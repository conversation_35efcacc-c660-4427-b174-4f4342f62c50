const WXAUTH = require('@/common/wxauth.js')
import store from '@/store'

async function autoLogin(force) {
  // 自动登陆
  // #ifdef MP-WEIXIN
  const isLogined = await WXAUTH.checkHasLogined()
  if (!isLogined) {
    await WXAUTH.authorize()
    await WXAUTH.bindSeller()
  }
  setTimeout(() => {
    uni.$emit('loginOK', {})
  }, 500)
  // #endif
  // #ifdef MP-QQ
  const isLogined = await QQAUTH.checkHasLogined()
  if (!isLogined) {
    await QQAUTH.authorize()
    await QQAUTH.bindSeller()
  }
  setTimeout(() => {
    uni.$emit('loginOK', {})
  }, 500)
  // #endif
  // #ifdef MP-TOUTIAO
  const isLogined = await TTAUTH.checkHasLogined()
  if (!isLogined) {
    await TTAUTH.authorize()
    await TTAUTH.bindSeller()
  }
  setTimeout(() => {
    uni.$emit('loginOK', {})
  }, 500)
  // #endif
  if (!force) {
    return
  }
  // #ifdef H5
  const isLogined = await this.checkHasLoginedH5()
  if (!isLogined) {
    // 判断是普通浏览器还是微信浏览器
    const ua = window.navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
      // 微信内置浏览器打开的
      // https://www.yuque.com/apifm/nu0f75/fpvc3m
      const res = await this.$wxapi.siteStatistics()
      const wxMpAppid = res.data.wxMpAppid
      let _domian = this.globalData.h5Domain + '/pages/index/index'
      _domian = encodeURIComponent(_domian)
      console.log(_domian);
      if (!this.globalData.goLogin) {
        this.globalData.goLogin = true
        window.parent.location.href = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +
          wxMpAppid + '&redirect_uri=' + _domian +
          '&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'
      }
    } else {
      // 其他浏览器打开的， 按需登陆，不能直接跳转到登陆界面
      uni.navigateTo({
        url: "/pages/login/login"
      })
    }
  }
  // #endif
}
async function checkHasLoginedH5() {
  const _this = this.$vm ? this.$vm : this
  if (!_this.token) {
    return false
  }
  // https://www.yuque.com/apifm/nu0f75/mp9f59
  const res = await _this.$wxapi.checkToken(_this.token)
  if (res.code != 0) {
    _this.$u.vuex('token', '')
    return false
  }
  return true
}
async function wxmpLogin(code) {
  // https://www.yuque.com/apifm/nu0f75/lh6cd3
  const res = await this.$wxapi.wxmpAuth({
    code
  })
  if (res.code == 0) {
    this.$u.vuex('token', res.data.token)
    this.$u.vuex('uid', res.data.uid)
    this.$u.vuex('openid', res.data.openid)
    setTimeout(() => {
      uni.$emit('loginOK', {})
    }, 500)
  }
}

module.exports = {
  autoLogin: autoLogin,
  checkHasLoginedH5: checkHasLoginedH5,
  wxmpLogin: wxmpLogin
}