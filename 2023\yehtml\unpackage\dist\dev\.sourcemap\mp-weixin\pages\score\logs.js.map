{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/score/logs.vue?438d", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/score/logs.vue?9839", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/score/logs.vue?2012", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/score/logs.vue?8d45", "uni-app:///pages/score/logs.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "page", "scorelogs", "created", "mounted", "onReady", "onLoad", "onShow", "onPullDownRefresh", "uni", "onReachBottom", "methods", "_scoreLogs", "title", "token", "res", "icon"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;;;AAGnD;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,+qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCUnrB;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC,6BAEA;EACAC,6BAEA;EACAC,6BAEA;EACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACA;IACA;IACAC;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAH;kBACAI;gBACA;gBACA;gBAAA;gBAAA,OACA;kBACAC;kBACAb;gBACA;cAAA;gBAHAc;gBAIAN;gBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;kBACA;oBACAA;sBACAI;sBACAG;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B", "file": "pages/score/logs.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/score/logs.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./logs.vue?vue&type=template&id=0087f645&scoped=true&\"\nvar renderjs\nimport script from \"./logs.vue?vue&type=script&lang=js&\"\nexport * from \"./logs.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0087f645\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/score/logs.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logs.vue?vue&type=template&id=0087f645&scoped=true&\"", "var components\ntry {\n  components = {\n    pageBoxEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/page-box-empty/page-box-empty\" */ \"@/components/page-box-empty/page-box-empty.vue\"\n      )\n    },\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.scorelogs || _vm.scorelogs.length == 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logs.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./logs.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<page-box-empty v-if=\"!scorelogs || scorelogs.length == 0\" title=\"暂无积分明细记录\" sub-title=\"可以去看看有那些想买的～\"\r\n\t\t\t:show-btn=\"false\" />\r\n\t\t<u-cell v-for=\"(item,index) in scorelogs\" :key=\"index\" :title=\"item.typeStr\" :label=\"item.dateAdd\"\r\n\t\t\t:value=\"item.score\"></u-cell>\t\t\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpage: 1,\r\n\t\t\t\tscorelogs: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\t\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis._scoreLogs()\r\n\t\t},\r\n\t\tonShow() {\r\n\r\n\t\t},\r\n\t\tonPullDownRefresh() {\r\n\t\t\tthis.page = 1\r\n\t\t\tthis._scoreLogs()\r\n\t\t\tuni.stopPullDownRefresh()\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tthis.page++\r\n\t\t\tthis._scoreLogs()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync _scoreLogs() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: ''\r\n\t\t\t\t})\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/khq7xu\r\n\t\t\t\tconst res = await this.$wxapi.scoreLogs({\r\n\t\t\t\t\ttoken: this.token,\r\n\t\t\t\t\tpage: this.page\r\n\t\t\t\t})\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tif(this.page == 1) {\r\n\t\t\t\t\t\tthis.scorelogs = res.data.result\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.scorelogs = this.scorelogs.concat(res.data.result)\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif(this.page > 1) {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '没有更多了～',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"], "sourceRoot": ""}