(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uview-ui/components/u-album/u-album"],{"0204":function(t,e,n){"use strict";(function(t){var i=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=i(n("7eb4")),u=i(n("ee10")),s=i(n("1bd5")),l={name:"u-album",mixins:[t.$u.mpMixin,t.$u.mixin,s.default],data:function(){return{singleWidth:0,singleHeight:0,singlePercent:.6}},watch:{urls:{immediate:!0,handler:function(t){1===t.length&&this.getImageRect()}}},computed:{imageStyle:function(){var e=this;return function(n,i){var r=e.space,u=e.rowCount,s=(e.multipleSize,e.urls,t.$u),l=s.addUnit,a=(s.addStyle,e.showUrls.length),o=(e.urls.length,{marginRight:l(r),marginBottom:l(r)});return n===a&&(o.marginBottom=0),(i===u||n===a&&i===e.showUrls[n-1].length)&&(o.marginRight=0),o}},showUrls:function(){var t=this,e=[];return this.urls.map((function(n,i){if(i+1<=t.maxCount){var r=Math.floor(i/t.rowCount);e[r]||(e[r]=[]),e[r].push(n)}})),e},imageWidth:function(){return t.$u.addUnit(1===this.urls.length?this.singleWidth:this.multipleSize)},imageHeight:function(){return t.$u.addUnit(1===this.urls.length?this.singleHeight:this.multipleSize)},albumWidth:function(){var t=0;return t=1===this.urls.length?this.singleWidth:this.showUrls[0].length*this.multipleSize+this.space*(this.showUrls[0].length-1),this.$emit("albumWidth",t),t}},methods:{onPreviewTap:function(e){var n=this,i=this.urls.map((function(t){return n.getSrc(t)}));t.previewImage({current:e,urls:i})},getSrc:function(e){return t.$u.test.object(e)?this.keyName&&e[this.keyName]||e.src:e},getImageRect:function(){var e=this,n=this.getSrc(this.urls[0]);t.getImageInfo({src:n,success:function(t){var n=t.width>=t.height;e.singleWidth=n?e.singleSize:t.width/t.height*e.singleSize,e.singleHeight=n?t.height/t.width*e.singleWidth:e.singleSize},fail:function(){e.getComponentWidth()}})},getComponentWidth:function(){var e=this;return(0,u.default)(r.default.mark((function n(){return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,t.$u.sleep(30);case 2:e.$uGetRect(".u-album__row").then((function(t){e.singleWidth=t.width*e.singlePercent}));case 3:case"end":return n.stop()}}),n)})))()}}};e.default=l}).call(this,n("df3c")["default"])},"6a8e":function(t,e,n){"use strict";n.r(e);var i=n("ccb2"),r=n("db47");for(var u in r)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(u);n("6edb");var s=n("828b"),l=Object(s["a"])(r["default"],i["b"],i["c"],!1,null,"1055b7c9",null,!1,i["a"],void 0);e["default"]=l.exports},"6edb":function(t,e,n){"use strict";var i=n("99c1"),r=n.n(i);r.a},"99c1":function(t,e,n){},ccb2:function(t,e,n){"use strict";n.d(e,"b",(function(){return r})),n.d(e,"c",(function(){return u})),n.d(e,"a",(function(){return i}));var i={"u-Text":function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u--text/u--text")]).then(n.bind(null,"b648"))}},r=function(){var t=this,e=t.$createElement,n=(t._self._c,t.__map(t.showUrls,(function(e,n){var i=t.__get_orig(e),r=t.urls.length,u=t.__map(e,(function(e,i){var r=t.__get_orig(e),u=t.__get_style([t.imageStyle(n+1,i+1)]),s=t.getSrc(e),l=t.showMore&&t.urls.length>t.rowCount*t.showUrls.length&&n===t.showUrls.length-1&&i===t.showUrls[t.showUrls.length-1].length-1,a=l?t.urls.length:null;return{$orig:r,s0:u,m0:s,g1:l,g2:a}}));return{$orig:i,g0:r,l0:u}})));t._isMounted||(t.e0=function(e,n){var i=arguments[arguments.length-1].currentTarget.dataset,r=i.eventParams||i["event-params"];n=r.item;t.previewFullImage&&t.onPreviewTap(t.getSrc(n))}),t.$mp.data=Object.assign({},{$root:{l1:n}})},u=[]},db47:function(t,e,n){"use strict";n.r(e);var i=n("0204"),r=n.n(i);for(var u in i)["default"].indexOf(u)<0&&function(t){n.d(e,t,(function(){return i[t]}))}(u);e["default"]=r.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uview-ui/components/u-album/u-album-create-component',
    {
        'uni_modules/uview-ui/components/u-album/u-album-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("6a8e"))
        })
    },
    [['uni_modules/uview-ui/components/u-album/u-album-create-component']]
]);
