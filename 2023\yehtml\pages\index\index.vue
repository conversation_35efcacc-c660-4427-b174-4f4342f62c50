<template>
	<view class="index container-wrapper">
		<view class="top-box" :style="headerMarginTopStyle">
			<view class="t">
				<image mode="aspectFit" class="logo" src="/static/images/logo-navbar.png"></image>
			</view>
			<view class="location">| 南宁</view>
			<view class="search" @click="goSearch">
				<u-search height="55rpx" bgColor="#FFFFFF" placeholder="输入关键词搜索" v-model="kw" :showAction="false"
					:disabled="true"></u-search>
			</view>
			<!--  #ifdef  MP-WEIXIN || MP-BAIDU || MP-TOUTIAO || MP-QQ -->
			<view class="mp-btn" :style="menuButtonInfoStyle"></view>
			<!--  #endif -->
		</view>
		<view class="swiper">
			<u-swiper v-if="banners" :list="banners" indicator circular keyName="picUrl" height="375rpx" bg-color="none"
				@click="tapBanner">
			</u-swiper>
		</view>

		<view class="main-wrapper">
			<!-- 添加公告栏 -->
			<view class="notice-bar-wrap">
				<u-notice-bar
					:text="['营业中，欢迎大家下单，同城2小时内送达；当日下单时间区间：昨日19:00至当日19:00，当日超过19:00下单，将于次日配送。当日配送时间区间：当日10:00至当日21:00。']"
					mode="column"
					color="#fa3534"
					bgColor="#fdf6ec"
					:show-icon="true"
				></u-notice-bar>
			</view>
			
			<view class="feature">
				<u-row gutter="0" justify="space-between">
					<u-col span="3" @click="featureClick('pzbz')">
						<u-icon size="14" label-size="12" name="checkmark-circle" label="品质保证"></u-icon>
					</u-col>
					<u-col span="3" @click="featureClick('tgfp')">
						<u-icon size="14" label-size="12" name="rmb-circle" label="提供发票"></u-icon>
					</u-col>
					<u-col span="3" @click="featureClick('cspf')">
						<u-icon size="14" label-size="12" name="info-circle" label="超时赔付"></u-icon>
					</u-col>
					<u-col span="3" @click="featureClick('jssd')">
						<u-icon size="14" label-size="12" name="car" label="极速送达"></u-icon>
					</u-col>
				</u-row>
			</view>

			<view class="category-container">
				<view class="category-box">
					<view class="category-list" v-for="(item, index) in categories" wx:key="index">
						<view class="category-column" @click="categoryClick(item)">
							<image mode="aspectFill" class="category-imgbox" :src="item.icon"></image>
							<view class="category-title">{{item.name}}</view>
						</view>
					</view>
				</view>
			</view>

			<view class="shortcuts-account">
				<u-row gutter="4">
					<u-col span="4" textAlign="center" @click="featureClick('sw')">
						<view><img src="https://ye.niutouren.vip/static/images/index/shortcuts/1.png" /></view>
						<view>实体礼品卡</view>
					</u-col>
					<u-col span="4" textAlign="center" @click="featureClick('sz')">
						<view><img src="https://ye.niutouren.vip/static/images/index/shortcuts/2.png" /></view>
						<view>数字礼品卡</view>
					</u-col>
					<u-col span="4" textAlign="center" @click="featureClick('sq')">
						<view><img src="https://ye.niutouren.vip/static/images/index/shortcuts/3.png" /></view>
						<view>山泉礼品卡</view>
					</u-col>
				</u-row>
			</view>

			<view v-if="shichiGoods" class="miaoshaGoods">
				<view class="ttt">
					<view class="content">
						<view class="left">
							<text>限数试吃</text>
							<text class="sub">试吃补贴，评价返利</text>
						</view>
						<view class="more" @click="gotoActivity('xssc')"><u-icon name="arrow-right"></u-icon></view>
					</view>
				</view>

				<view>
					<three3 :list="shichiGoods"></three3>
				</view>
			</view>

			<!-- <view v-if="miaoshaGoods && apiUserInfoMap.userLevel.level < 1" class="miaoshaGoods">
        <view class="ttt">
          <view class="content">
            <view class="left">
              <text>限数抢购</text>
              <text class="sub">库存尾货 数量有限 超值抢购</text>
            </view>
            <view class="more" @click="gotoActivity('xsqg')"><u-icon name="arrow-right"></u-icon></view>
          </view>
        </view>

        <view>
          <three2 :list="miaoshaGoods"></three2>
        </view>
      </view> -->

			<view v-if="goodsRecommend" class="miaoshaGoods">
				<view class="ttt">
					<view class="content">
						<view class="left">
							<text>限时预购</text>
						</view>
						<view class="more" @click="gotoActivity('xsyg')"><u-icon name="arrow-right"></u-icon></view>
					</view>
				</view>

				<view>
					<three1 :list="goodsRecommend"></three1>
				</view>
			</view>

			<view style="background: #F8F8F8;padding: 0 0 5px 0;">
				<view class="tab1">
					<u-row customStyle="margin-bottom: 10px">
						<u-col :span="tabSpan" justify="center" textAlign="center">
							<div class="tab line" :class="{ active: activeTab === 'tab1' }" @click="changeTab('tab1')">
								<div style="height: 30rpx;">&nbsp;</div>
								<div class="title">土鲜多</div>
								<div class="dsc">深山好货</div>
							</div>
						</u-col>
						<u-col :span="tabSpan" justify="center" textAlign="center">
							<div class="tab line" :class="{ active: activeTab === 'tab2' }" @click="changeTab('tab2')">
								<div style="height: 30rpx;">&nbsp;</div>
								<div class="title">泉水多</div>
								<div class="dsc">甘甜健康</div>
							</div>
						</u-col>
						<u-col :span="tabSpan" justify="center" textAlign="center">
							<div class="tab line" :class="{ active: activeTab === 'tab3' }" @click="changeTab('tab3')">
								<div style="height: 30rpx;">&nbsp;</div>
								<div class="title">好礼多</div>
								<div class="dsc">礼品多多</div>
							</div>
						</u-col>
						<u-col v-if="showEnterpriseTab" span="3" justify="center" textAlign="center">
							<div class="tab" :class="{ active: activeTab === 'tab4' }" @click="changeTab('tab4')">
								<div style="height: 30rpx;">&nbsp;</div>
								<div class="title">企业购</div>
								<div class="dsc">企业定制</div>
							</div>
						</u-col>
					</u-row>
				</view>
			</view>

			<view v-if="activeTab === 'tab1'">
				<view v-if="goods" class="goodsRecommend">
					<view class="goods-container">
						<list1 :list="goods" type="goods"></list1>
					</view>
				</view>
			</view>

			<view v-if="activeTab === 'tab2'">
				<view v-if="goodsSQS" class="goodsRecommend">
					<view class="goods-container">
						<list1 :list="goodsSQS" type="goods"></list1>
					</view>
				</view>
			</view>

			<view v-if="activeTab === 'tab3'">
				<view v-if="goodsHLD" class="goodsRecommend">
					<view class="goods-container">
						<list1 :list="goodsHLD" type="goods"></list1>
					</view>
				</view>
			</view>

			<view v-if="activeTab === 'tab4'">
				<view v-if="goods" class="goodsRecommend">
					<view class="goods-container">
						<list1 :list="goods" type="goods"></list1>
					</view>
				</view>
			</view>

		</view>

		<tabbar :tabIndex="tabIndex"></tabbar>

		<u-overlay v-if="adPosition['indexPop']" :show="adPositionIndexPop">
			<view class="adPositionIndexPop">
				<image :src="adPosition['indexPop'].val" mode="widthFix" @click="goUrl(adPosition['indexPop'].url)"></image>
				<view class="close" @click="adPositionIndexPop = false">
					<u-icon name="close-circle-fill" color="#eee" size="80rpx"></u-icon>
				</view>
			</view>
		</u-overlay>
	</view>
</template>

<script>
	import empty from 'empty-value'
	import tabbar from '@/components/tabbar'
	import list1 from '@/components/list/list1'
	import three1 from '@/components/list/three1'
	import three2 from '@/components/list/three2'
	import three3 from '@/components/list/three3'
	import tab1 from '@/components/tabs/tab1'

	const TOOLS = require('@/common/tools')
	// #ifdef H5
	const ua = window.navigator.userAgent.toLowerCase()
	if (ua.match(/MicroMessenger/i) == 'micromessenger') {
		const jweixin = require('jweixin-module')
		jweixin.ready(() => {
			// 需在用户可能点击分享按钮前就先调用
			jweixin.updateAppMessageShareData({
				title: '野贝农土鲜商城', // 分享标题
				desc: '野贝农土鲜商城', // 分享描述
				link: 'https://ye.niutouren.com', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
				imgUrl: 'https://dcdn.it120.cc/2024/05/17/2e030caf-c248-4386-84dd-28cea6951572.jpeg', // 分享图标
				success: function() {
					// 设置成功
				}
			})
			jweixin.updateTimelineShareData({
				title: '野贝农土鲜商城', // 分享标题
				desc: '野贝农土鲜商城', // 分享描述
				link: 'https://ye.niutouren.com', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致
				imgUrl: 'https://dcdn.it120.cc/2024/05/17/2e030caf-c248-4386-84dd-28cea6951572.jpeg', // 分享图标
				success: function() {
					// 设置成功
				}
			})
		})
	}
	// #endif
	export default {
		components: {
			tabbar,
			list1,
			three1,
			three2,
			three3,
			tab1
		},
		data() {
			return {
				tabIndex: 2,
				apiUserInfoMap: undefined,

				background: ['color1', 'color2', 'color3'],
				indicatorDots: true,
				autoplay: true,
				interval: 2000,
				duration: 500,

				headerMarginTopStyle: 'margin-top:0',
				kw: '',
				menuButtonInfoStyle: '',
				shopInfo: undefined,
				banners: undefined,
				goodsDynamic: undefined,
				categories: undefined,
				categories2: undefined,
				categories3: undefined,
				notice: undefined,
				adPosition: {},
				miaoshaGoods: undefined,
				shichiGoods: undefined,
				goodsRecommend: undefined,
				goodsHLD: undefined,
				goodsSQS: undefined,
				goodsYBC: undefined,
				kanjiaList: undefined,
				pingtuanList: undefined,
				page: 1,
				goods: [],
				adPositionIndexPop: false,
				timeData: {},

				activeTab: 'tab1',

				promotionGoods: [],
				promotionGoodsBak: [],
				promotionInsert: true,
			}
		},
		onLoad(e) {
			// #ifdef  MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
			uni.showShareMenu({
				withShareTicket: true,
			})
			// #endif
			// #ifdef  MP-WEIXIN || MP-BAIDU || MP-TOUTIAO || MP-QQ
			const systemInfo = uni.getSystemInfoSync()
			const menuButtonInfo = uni.getMenuButtonBoundingClientRect()
			this.menuButtonInfoStyle =
				`width: ${systemInfo.screenWidth - menuButtonInfo.left}px;height: ${menuButtonInfo.height}px`
			this.headerMarginTopStyle = `margin-top:${menuButtonInfo.top}px`
			// #endif
			// 读取小程序码中的邀请人编号
			if (e && e.scene) {
				const scene = decodeURIComponent(e.scene)
				if (scene) {
					this.$u.vuex('referrer', scene.substring(11))
				}
			}
			uni.setNavigationBarTitle({
				title: this.sysconfigMap.mallName
			})
			this._userDetail()
			this._banners()
			this._categories()
			this._notice()
			this._adPosition()
			this._miaoshaGoods()
			this._shichiGoods()
			this._goodsRecommend()
			this._kanjiaList()
			this._pingtuanList()
			this._goodsPromotion()
			this._goods()
			this._goodsHLD()
			this._goodsSQS()
			// #ifdef H5
			const ua = window.navigator.userAgent.toLowerCase()
			if (ua.match(/MicroMessenger/i) == 'micromessenger') {
				this.jssdkSign()
			}
			// #endif
		},
		onShow() {
			this.shopInfo = uni.getStorageSync('shopInfo')
			this._goodsDynamic()
			TOOLS.showTabBarBadge()

			if (this.activeTab === 'tab1') {
				const refreshIndex = uni.getStorageSync('refreshIndex')
				if (refreshIndex) {
					this.onPullDownRefresh()
					uni.removeStorageSync('refreshIndex')
				}
			}
		},
		created() {},
		onShareAppMessage() {
			return {
				title: '"' + this.sysconfigMap.mallName + '" ' + this.sysconfigMap.share_profile,
				path: '/pages/index/index?inviter_id=' + this.uid
			}
		},
		onReachBottom() {
			this.page += 1
			this._goods()
		},
		onPullDownRefresh() {
			this.page = 1
			this._banners()
			this._categories()
			this._notice()
			this._adPosition()
			this._miaoshaGoods()
			this._goodsRecommend()
			this._kanjiaList()
			this._pingtuanList()
			this._goods()
			uni.stopPullDownRefresh()
		},
		methods: {
			async _goodsPromotion() {
				await uni.$u.http.post('https://ye.niutouren.vip/api/order', {
					'type': 'index_good_promotion',
				}).then(res => {
					if (!empty(res)) {
						this.promotionGoods = uni.$u.deepClone(res)
					}

					//console.log('long is', this.longList)
				}).catch(err => {
					//
				})
			},
			async _userDetail() {
				// https://www.yuque.com/apifm/nu0f75/zgf8pu
				const res = await this.$wxapi.userDetail(this.token)
				if (res.code == 0) {
					this.apiUserInfoMap = res.data
				}
			},
			async jssdkSign() {
				const res = await this.$wxapi.jssdkSign(window.location.href)
				if (res.code === 0) {
					jweixin.config({
						debug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
						appId: res.data.appid, // 必填，公众号的唯一标识
						timestamp: res.data.timestamp, // 必填，生成签名的时间戳
						nonceStr: res.data.noncestr, // 必填，生成签名的随机串
						signature: res.data.sign, // 必填，签名
						jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'] // 必填，需要使用的JS接口列表
					})
				}
			},
			goSearch() {
				uni.navigateTo({
					url: '/packageFx/search/index'
				})
			},
			async _banners() {
				// https://www.yuque.com/apifm/nu0f75/ms21ki
				const res = await this.$wxapi.banners({
					type: 'index'
				})
				if (res.code == 0) {
					this.banners = res.data
				} else {
					uni.showToast({
						title: '后台未维护Banner数据',
						icon: 'none'
					})
				}
			},
			tapBanner(index) {
				const linkUrl = this.banners[index].linkUrl
				if (linkUrl) {
					uni.navigateTo({
						url: linkUrl
					})
				}
			},
			async _goodsDynamic() {
				// https://www.yuque.com/apifm/nu0f75/xehbuk
				const res = await this.$wxapi.goodsDynamic(0)
				if (res.code == 0) {
					this.goodsDynamic = []
					res.data.forEach(ele => {
						this.goodsDynamic.push(`${ele.nick}购买了${ele.goodsName}`)
					})
				}
			},
			async _categories() {
				const res = await this.$wxapi.goodsCategory()
				if (res.code === 0) {
					const categorizedData = res.data.reduce((acc, curr) => {
						if (curr.key === 'YBN1') {
							acc.categories.push(curr)
						} else if (curr.key === 'YBN2') {
							acc.categories2.push(curr)
						} else if (curr.key === 'YBN3') {
							acc.categories3.push(curr)
						}
						return acc
					}, {
						categories: [],
						categories2: [],
						categories3: []
					})

					this.categories = categorizedData.categories
					this.categories2 = categorizedData.categories2
					this.categories3 = categorizedData.categories3
				}
			},
			// 移除套餐、活动等
			removeItemsFromArray(arr) {
				const idsToRemove = [390765, 390766, 391088, 391089, 455910, 412599, 417010, 424167]
				const filteredArr = arr.filter(item => !idsToRemove.includes(item.id))
				return filteredArr
			},
			categoryClick(category) {
				if (category.vopCid1 || category.vopCid2) {
					uni.navigateTo({
						url: '/pages/goods/list-vop?cid1=' + (category.vopCid1 ? category.vopCid1 : '') +
							'&cid2=' + (category.vopCid2 ? category.vopCid2 : ''),
					})
				} else {
					uni.navigateTo({
						url: '/pages/goods/list?categoryId=' + category.id,
					})
				}
			},
			async _notice() {
				// https://www.yuque.com/apifm/nu0f75/zanb9r
				const res = await this.$wxapi.noticeLastOne()
				if (res.code == 0) {
					this.notice = res.data
				}
			},
			async _adPosition() {
				// https://www.yuque.com/apifm/nu0f75/ypi79p
				const res = await this.$wxapi.adPositionBatch('indexPop,index-live-pic')
				if (res.code == 0) {
					res.data.forEach(ele => {
						this.adPosition[ele.key] = ele
						if (ele.key == 'indexPop') {
							this.adPositionIndexPop = true
						}
					})
				}
			},
			gotoActivity(type) {
				uni.navigateTo({
					url: '/pages/promotion/list?type=' + type
				})
			},
			goUrl(url) {
				this.adPositionIndexPop = false
				if (url) {
					uni.navigateTo({
						url
					})
				}
			},
			noticeclick(e) {
				console.log(e)
			},
			async _shichiGoods() {
				// https://www.yuque.com/apifm/nu0f75/wg5t98
				const res = await this.$wxapi.goodsv2({
					categoryId: '463740',
					showExtJson: true
				})
				if (res.code == 0) {
					res.data.result.forEach(ele => {
						const _now = new Date().getTime()
						if (ele.dateStart) {
							ele.dateStartInt = new Date(ele.dateStart.replace(/-/g, '/')).getTime() - _now
						}
						if (ele.dateEnd) {
							ele.dateEndInt = new Date(ele.dateEnd.replace(/-/g, '/')).getTime() - _now
						}

						if (!empty(res.data.extJsonMap)) {
							if (ele.id in res.data.extJsonMap) {
								ele.ext = res.data.extJsonMap[ele.id]
							}
						}
					})

					let shichiGoods = []
					shichiGoods = this.rotateArray(res.data.result)
					this.shichiGoods = shichiGoods
				}
			},
			async _miaoshaGoods() {
				// https://www.yuque.com/apifm/nu0f75/wg5t98
				const res = await this.$wxapi.goodsv2({
					categoryId: '391089'
				})
				if (res.code == 0) {
					res.data.result.forEach(ele => {
						const _now = new Date().getTime()
						if (ele.dateStart) {
							ele.dateStartInt = new Date(ele.dateStart.replace(/-/g, '/')).getTime() - _now
						}
						if (ele.dateEnd) {
							ele.dateEndInt = new Date(ele.dateEnd.replace(/-/g, '/')).getTime() - _now
						}
					})

					let miaoshaGoods = []
					miaoshaGoods = this.rotateArray(res.data.result)
					this.miaoshaGoods = miaoshaGoods
				}
			},
			async _kanjiaList() {
				// https://www.yuque.com/apifm/nu0f75/wg5t98
				const res = await this.$wxapi.goodsv2({
					kanjia: true
				})
				if (res.code == 0) {
					const kanjiaGoodsIds = []
					res.data.result.forEach(ele => {
						kanjiaGoodsIds.push(ele.id)
					})
					// https://www.yuque.com/apifm/nu0f75/xs42ih
					const goodsKanjiaSetRes = await this.$wxapi.kanjiaSet(kanjiaGoodsIds.join())
					if (goodsKanjiaSetRes.code == 0) {
						res.data.result.forEach(ele => {
							const _process = goodsKanjiaSetRes.data.find(_set => {
								return _set.goodsId == ele.id
							})
							if (_process) {
								ele.process = 100 * _process.numberBuy / _process.number
								ele.process = ele.process.toFixed(0)
							}
						})
						this.kanjiaList = res.data.result
					}
				}
			},
			async _pingtuanList() {
				// https://www.yuque.com/apifm/nu0f75/wg5t98
				const res = await this.$wxapi.goodsv2({
					pingtuan: true
				})
				if (res.code == 0) {
					this.pingtuanList = res.data.result
				}
			},
			async _goods() {
				if (this.page > 1) {
					uni.showToast({
						title: '正在加载',
						icon: 'none'
					})
				}

				// https://www.yuque.com/apifm/nu0f75/wg5t98
				const res = await this.$wxapi.goodsv2({
					page: this.page,
					pageSize: 10,
					categoryId: '383896,383897,383898,383899,383900,383901,383902,383903,383904,383905,474858',
					showExtJson: true
				})
				if (res.code == 0) {
					let _goods = []
					let goods = []
					let goodsNew = []

					_goods = res.data.result
					_goods = this.shuffle(_goods)

					if (empty(this.promotionGoods)) {
						this.promotionGoods = uni.$u.deepClone(this.promotionGoodsBak)
					}

					let promotionGoods = this.promotionGoods

					if (!empty(_goods) && !empty(promotionGoods)) {
						goodsNew = this.insertPromotionGoods(_goods, promotionGoods)
					}

					if (this.page > 1) {
						_goods = this.goods.concat(goodsNew)
					} else {
						_goods = goodsNew
					}

					if (!empty(_goods)) {
						_goods.forEach(async (good, index) => {
							if (!empty(good)) {
								good.image = good.pic
								good.title = good.name

								if (!empty(res.data.extJsonMap)) {
									if (good.id in res.data.extJsonMap) {
										good.ext = res.data.extJsonMap[good.id]
										if (!empty(good.ext['delivery_type'])) {
											if (good.ext['delivery_type'] === '预购') {
												good.deliveryTypeUrl =
													'https://ye.niutouren.vip/static/images/goods/delivery-type-yg.png'
											}
											if (good.ext['delivery_type'] === '当日达') {
												good.deliveryTypeUrl =
													'https://ye.niutouren.vip/static/images/goods/delivery-type-drd.png'
											}
											if (good.ext['delivery_type'] === '次日达') {
												good.deliveryTypeUrl =
													'https://ye.niutouren.vip/static/images/goods/delivery-type-crd.png'
											}
											if (good.ext['delivery_type'] === '后日达') {
												good.deliveryTypeUrl =
													'https://ye.niutouren.vip/static/images/goods/delivery-type-hrd.png'
											}
										}
									}
								}

								goods.push(good)
							}
						})
					}

					this.goods = goods
				} else {
					/* if (this.page != 1) {
					  uni.showToast({
					    title: '没有更多了～',
					    icon: 'none'
					  })
					} */
				}
			},
			insertPromotionGoods(goods, promotionGoods) {
				let first = promotionGoods.shift()
				goods.unshift(first)
				let second = promotionGoods.shift()
				goods.splice(5, 0, second)
				let third = promotionGoods.shift()
				goods.splice(9, 0, third)

				return goods
			},
			async _goodsRecommend() {
				// https://www.yuque.com/apifm/nu0f75/wg5t98
				const res = await this.$wxapi.goodsv2({
					categoryId: '391088',
					showExtJson: true
				})
				if (res.code == 0) {
					let _goods = []
					let goods = []
					let extJsonMap = []

					_goods = res.data.result
					if (!empty(_goods)) {
						_goods.forEach(async (good, index) => {
							good.image = good.pic
							good.title = good.name

							if (!empty(res.data.extJsonMap)) {
								if (good.id in res.data.extJsonMap) {
									good.ext = res.data.extJsonMap[good.id]
									if (!empty(good.ext['deadline'])) {
										good.ext['deadlineDifference'] = TOOLS.translateTimeDifference(good.ext['deadline'])
									}
								}
							}

							goods.push(good)
						})
					}

					this.goodsRecommend = this.rotateArray(goods)
				}
			},
			async _goodsHLD() {
				// https://www.yuque.com/apifm/nu0f75/wg5t98
				const res = await this.$wxapi.goodsv2({
					categoryId: '455908,455909,455910,455911,455912,455913,489436,455913,455912'
				})
				if (res.code == 0) {
					let goods = []
					let goodsNew = []
					let _goods = []

					_goods = res.data.result
					_goods = this.shuffle(_goods)

					if (empty(this.promotionGoods)) {
						this.promotionGoods = uni.$u.deepClone(this.promotionGoodsBak)
					}
					let promotionGoods = this.promotionGoods
					if (!empty(_goods) && !empty(promotionGoods)) {
						goodsNew = this.insertPromotionGoods(_goods, promotionGoods)
					}

					if (!empty(_goods)) {
						_goods.forEach(async (good, index) => {
							if (!empty(good)) {
								good.image = good.pic
								good.title = good.name
								goods.push(good)
							}
						})
					}

					this.goodsHLD = goods
				}
			},
			async _goodsSQS() {
				// https://www.yuque.com/apifm/nu0f75/wg5t98
				const res = await this.$wxapi.goodsv2({
					categoryId: '424167,463423'
				})
				if (res.code == 0) {
					let goods = []
					let goodsNew = []
					let _goods = []

					_goods = res.data.result
					_goods = this.shuffle(_goods)

					if (empty(this.promotionGoods)) {
						this.promotionGoods = uni.$u.deepClone(this.promotionGoodsBak)
					}
					let promotionGoods = this.promotionGoods
					if (!empty(_goods) && !empty(promotionGoods)) {
						goodsNew = this.insertPromotionGoods(_goods, promotionGoods)
					}

					if (!empty(_goods)) {
						_goods.forEach(async (good, index) => {
							if (!empty(good)) {
								good.image = good.pic
								good.title = good.name
								goods.push(good)
							}
						})
					}

					this.goodsSQS = goods
				}
			},
			goCoupons() {
				uni.switchTab({
					url: '/packageFx/coupons/index'
				})
			},
			shuffle(array) {
				let currentIndex = array.length,
					randomIndex;

				while (currentIndex != 0) {
					randomIndex = Math.floor(Math.random() * currentIndex);
					currentIndex--;

					[array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]]
				}

				return array
			},
			rotateArray(arr) {
				let timer = setInterval(() => {
					let firstElement = arr.shift()
					arr.push(firstElement)
				}, 10000)

				return arr
			},
			changeTab(tab) {
				this.activeTab = tab
			},
			alertTab() {
				uni.showToast({
					title: '敬请期待',
					icon: 'error'
				})
			},
			featureClick(key) {
				let aboutPage = true
				if (key === 'sw') {
					aboutPage = false
					// 实体礼品卡 - 直接跳转到实物礼品卡列表
					const physicalParams = {
						pic: encodeURIComponent('https://ye.niutouren.vip/static/images/coupons/card2.jpg'),
						tag: encodeURIComponent('指定抵扣,可自用,可赠送'),
						title: encodeURIComponent('实物礼品卡'),
						type: encodeURIComponent('physical')
					}
					uni.navigateTo({
						url: `/packageFx/coupons/list?pic=${physicalParams.pic}&tag=${physicalParams.tag}&title=${physicalParams.title}&type=${physicalParams.type}`
					})
				}
				if (key === 'sz') {
					aboutPage = false
					// 数字礼品卡 - 直接跳转到消费礼品卡列表
					const virtualParams = {
						pic: encodeURIComponent('https://ye.niutouren.vip/static/images/coupons/card1.jpg'),
						tag: encodeURIComponent('全品通用,可自用,可赠送'),
						title: encodeURIComponent('消费礼品卡'),
						type: encodeURIComponent('virtual')
					}
					uni.navigateTo({
						url: `/packageFx/coupons/list?pic=${virtualParams.pic}&tag=${virtualParams.tag}&title=${virtualParams.title}&type=${virtualParams.type}`
					})
				}
				if (key === 'sq') {
					aboutPage = false
					// 山泉礼品卡 - 直接跳转到水卡列表
					const waterParams = {
						pic: encodeURIComponent('https://dcdn.it120.cc/2024/05/05/77302a3c-066e-426d-a36e-da545df8fccf.png'),
						tag: encodeURIComponent('购水抵扣,可自用,可赠送'),
						title: encodeURIComponent('水卡'),
						type: encodeURIComponent('water')
					}
					uni.navigateTo({
						url: `/packageFx/coupons/list?pic=${waterParams.pic}&tag=${waterParams.tag}&title=${waterParams.title}&type=${waterParams.type}`
					})
				}

				if (aboutPage) {
					uni.navigateTo({
						url: '/packageFx/about/about?key=' + key
					})
				}
			}
		},
		computed: {
			// 计算 span 值
			tabSpan() {
				return this.apiUserInfoMap?.userLevel?.id === 33080 ? 3 : 4
			},
			// 判断是否显示企业购标签
			showEnterpriseTab() {
				return this.apiUserInfoMap?.userLevel?.id === 33080
			}
		}
	}
</script>
<style scoped lang="scss">
	.index.container-wrapper {
		background: linear-gradient(90deg, #35641e, #80a933) fixed;
	}

	.index {
		.top-box {
			padding: 0 8rpx;
			display: flex;
			align-items: center;

			.t {
				padding-top: 8rpx;
				padding-left: 8rpx;
			}

			.t .logo {
				width: 94px;
				height: 25px;
				margin-top: 8rpx;
			}

			.search {
				padding: 0 8rpx;
				flex: 1;
				background: none;
			}

			.location {
				font-size: 24rpx;
				margin: 0 16rpx;
				margin-top: 22rpx;
				color: #fff;
			}
		}

		.swiper {
			margin: 20rpx 0;

			.notice {
				position: absolute;
				bottom: 46rpx;
				left: 24rpx;
				width: 668rpx;
				color: #fff;
				font-size: 24rpx;
				opacity: 0.8;
				border-radius: 32rpx;
			}
		}

		.main-wrapper {
			min-height: 100vh;
			background: #f7f8fa;
			border-top-left-radius: 10px;
			border-top-right-radius: 10px;
			position: relative;
			margin-top: -40px;
		}

		.feature {
			margin-left: 38rpx;
			margin-right: 20rpx;
			padding-top: 20rpx;
			text-align: center;
			font-size: 26rpx;
		}

		.shortcuts-account {
			background: #FFFFFF;
			margin-left: 36rpx;
			margin-right: 36rpx;
			padding: 6rpx 6rpx 16rpx 6rpx;
			border-radius: 6px;
			text-align: center;
			font-size: 20rpx;
			vertical-align: bottom;
		}

		.shortcuts-account img {
			width: 60rpx;
			height: 60rpx;
			background-repeat: no-repeat;
			display: inline-block;
			vertical-align: middle;
		}

		.category-container {
			padding: 0 0 10px 0;
			position: relative;

			.category-box {
				display: flex;
				flex-wrap: wrap;

				margin-left: 0;
				margin-right: 0;
				border-radius: 10px;
				padding: 20rpx 0;
				position: inherit;
			}

			.category-list {
				width: 20%;
				text-align: center;
				display: inline-block;
				overflow: hidden;
			}

			.category-column {
				width: 100%;
				margin-top: 20rpx;
				overflow: hidden;
			}

			.category-imgbox {
				width: 100rpx;
				height: 100rpx;
			}

			.category-title {
				font-size: 26rpx;
				text-align: center;
			}
		}

		.live-pic {
			margin-top: 16rpx;
		}

		.ttt {
			display: flex;
			align-items: left;
			margin-top: 24rpx;

			.content {
				display: flex;
				align-items: left;
				justify-content: space-between;
				padding: 0 16rpx;
				width: 100%;

				text {
					margin-left: 16rpx;
					color: #333;
					font-size: 46rpx;
					font-weight: bold;
				}

				text.sub {
					margin-left: 16rpx;
					color: #333;
					font-size: 24rpx;
					font-weight: bold;
					margin-top: 22rpx;
				}

				.delivery-time {
					margin-left: 16rpx;
					color: #333;
					font-size: 24rpx;
					font-weight: bold;
					margin-top: 22rpx;
				}

				.more {
					margin-top: 20rpx;
					margin-right: 10rpx;
				}
			}
		}

		.miaoshaGoods {
			.miaosha-goods-list {
				margin: 20rpx;
				border-radius: 16rpx;
				display: flex;
				padding: 20rpx;
			}

			.miaosha-goods-list .image {
				width: 260rpx;
				height: 260rpx;
				flex-shrink: 0;
				border-radius: 16rpx;
			}

			.miaosha-goods-list .r {
				margin-left: 32rpx;
			}

			.miaosha-goods-list .r .goods-title {
				color: #333;
				font-size: 28rpx;
			}

			.miaosha-goods-list .r .label {
				color: #e64340;
				font-size: 24rpx;
				display: flex;
				align-items: flex-start;
				margin-top: 8rpx;
			}

			.miaosha-goods-list .r .label text {
				margin-left: 8rpx;
			}

			.miaosha-goods-list .count-down {
				background: rgba(250, 195, 198, 0.3);
				border-radius: 5rpx;
				font-size: 14rpx;
				color: red;
				font-weight: 400;
				padding: 6rpx 16rpx;
				margin-top: 6rpx;
				text-align: center;
				border-radius: 10rpx;
			}

			.miaosha-price-btn {
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.miaosha-price-btn .msbtn {
				width: 170rpx;
				height: 60rpx;
				background: linear-gradient(156deg, #FF7863 0%, #FF211A 100%);
				border-radius: 34rpx;
				border: none !important;
				line-height: 60rpx !important;
				font-size: 13px !important;
			}

			.miaosha-price-btn .price {
				color: #e64340;
				font-size: 40rpx;
				margin-top: 12rpx;
				padding-right: 32rpx;
			}

			.miaosha-price-btn .price text {
				color: #666666;
				font-size: 26rpx;
				text-decoration: line-through;
			}
		}

		.goods-container {
			padding: 0 24rpx;
		}

		.goods-box {
			width: 339rpx;
			background-color: #fff;
			overflow: hidden;
			margin-top: 24rpx;
			border-radius: 5px;
			border: 1px solid #D1D1D1;
			padding-bottom: 10rpx;
		}

		.goods-box .img-box {
			width: 339rpx;
			height: 339rpx;
			overflow: hidden;
		}

		.goods-box .img-box image {
			width: 339rpx;
			height: 339rpx;
		}

		.goods-box .goods-title {
			padding: 0 4rpx;
		}

		.goods-box .goods-price-container {
			display: flex;
			align-items: baseline;
		}

		.goods-box .goods-price {
			overflow: hidden;
			font-size: 34rpx;
			color: #F20C32;
			margin-left: 24rpx;
		}

		.goods-box .goods-price2 {
			overflow: hidden;
			font-size: 26rpx;
			color: #aaa;
			text-decoration: line-through;
			margin-left: 20rpx;
		}

		.coupons-float {
			position: fixed;
			right: 15rpx;
			bottom: 180rpx;
			width: 80rpx;
			height: 80rpx;
			background-color: #fff;
			text-align: center;
			border-radius: 50%;
			border: 1rpx solid #ddd;
		}

		.coupons-float image {
			width: 60rpx;
			height: 60rpx;
			margin-top: 10rpx;
		}

		.adPositionIndexPop {
			width: 100vw;
			height: 100vh;
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
		}

		.adPositionIndexPop image {
			width: 505rpx;
		}

		.adPositionIndexPop .close {
			margin-top: 32rpx;
		}
	}

	.blank {
		height: 32rpx;
	}
</style>
<style lang="scss">
	.tab1 {
		margin: 0 12px;
	}
	
	.notice-bar-wrap {
		padding: 5rpx 10rpx;
		margin-bottom: 10rpx;
	}

	.tab1 .tab.line {
		position: relative;
		width: 100%;
	}

	.tab1 .tab.line::after {
		content: "";
		position: absolute;
		top: 20%;
		right: 0;
		bottom: 10%;
		width: 1px;
		background-color: #d3d3d3;
	}

	.tab1 .tab .title {
		font-size: 40rpx;
		font-weight: bolder;
		margin-bottom: 20rpx;
	}

	.tab1 .tab.active .title {
		margin-top: 10rpx;
	}

	.tab1 .tab .dsc {
		font-size: 30rpx;
		height: 60rpx;
	}

	.tab1 .tab.active {
		background-image: url("/static/images/tab-bg.png");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
	}

	.tab1 .tab.active .dsc {
		background-image: url("/static/images/tab-dsc-bg.png");
		background-size: contain;
		background-repeat: no-repeat;
		background-position: center;
		font-size: 24rpx;
		line-height: 58rpx;
		color: #FFFFFF;
		margin: 0 16rpx;
	}

	.swiper {
		height: 420rpx;
	}
</style>