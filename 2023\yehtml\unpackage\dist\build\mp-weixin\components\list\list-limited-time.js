(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/list/list-limited-time"],{"25a1":function(n,t,e){"use strict";e.r(t);var i=e("95a7"),o=e.n(i);for(var u in i)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(u);t["default"]=o.a},"95a7":function(n,t,e){"use strict";(function(n){var i=e("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;i(e("bc37"));var o={components:{listGoodsItemLimitedTime:function(){e.e("components/list-item/list-goods-item-limited-time").then(function(){return resolve(e("7bf8"))}.bind(null,e)).catch(e.oe)}},props:{list:{type:Array,default:[]},type:{type:String,default:""}},onReady:function(){},data:function(){return{}},watch:{list:function(n){}},methods:{imageClick:function(t){"goods"===this.type&&n.navigateTo({url:"/pages/goods/detail?id="+t.id})}}};t.default=o}).call(this,e("df3c")["default"])},b8bd:function(n,t,e){"use strict";e.r(t);var i=e("f9a5"),o=e("25a1");for(var u in o)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(u);e("ce2f");var c=e("828b"),l=Object(c["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=l.exports},c833:function(n,t,e){},ce2f:function(n,t,e){"use strict";var i=e("c833"),o=e.n(i);o.a},f9a5:function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return u})),e.d(t,"a",(function(){return i}));var i={uRow:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-row/u-row")]).then(e.bind(null,"f632"))},uCol:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-col/u-col")]).then(e.bind(null,"44b6"))},uImage:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-image/u-image")]).then(e.bind(null,"73f7"))}},o=function(){var n=this.$createElement;this._self._c},u=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/list/list-limited-time-create-component',
    {
        'components/list/list-limited-time-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("b8bd"))
        })
    },
    [['components/list/list-limited-time-create-component']]
]);
