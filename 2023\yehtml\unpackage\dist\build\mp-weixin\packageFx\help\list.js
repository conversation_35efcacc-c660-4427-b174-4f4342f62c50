(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["packageFx/help/list"],{"0df7":function(e,t,n){"use strict";(function(e){var c=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=c(n("7eb4")),a=c(n("ee10")),u={data:function(){return{category:void 0,current:0,scrolltop:0,cmsArticles:void 0}},created:function(){},mounted:function(){},onReady:function(){},onLoad:function(e){this.cmsCategories()},onShow:function(){},methods:{cmsCategories:function(){var e=this;return(0,a.default)(r.default.mark((function t(){var n,c;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$wxapi.cmsCategories();case 2:n=t.sent,0==n.code&&(c=n.data.filter((function(e){return"qa"==e.type})),e.category=c,c&&c.length>0&&e.articles(c[0].id));case 4:case"end":return t.stop()}}),t)})))()},articles:function(t){var n=this;return(0,a.default)(r.default.mark((function c(){var a;return r.default.wrap((function(c){while(1)switch(c.prev=c.next){case 0:return e.showLoading({title:""}),c.next=3,n.$wxapi.cmsArticles({categoryId:t});case 3:a=c.sent,e.hideLoading(),0==a.code?n.cmsArticles=a.data:n.cmsArticles=null;case 6:case"end":return c.stop()}}),c)})))()},categoryChange:function(e){this.current=e;var t=this.category[e];this.articles(t.id)}}};t.default=u}).call(this,n("3223")["default"])},"43f1":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return a})),n.d(t,"a",(function(){return c}));var c={pageBoxEmpty:function(){return n.e("components/page-box-empty/page-box-empty").then(n.bind(null,"bc43"))},uCell:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-cell/u-cell")]).then(n.bind(null,"819c"))}},r=function(){var e=this.$createElement;this._self._c},a=[]},"6bdc":function(e,t,n){},"73bc":function(e,t,n){"use strict";var c=n("6bdc"),r=n.n(c);r.a},ca8d:function(e,t,n){"use strict";n.r(t);var c=n("43f1"),r=n("e518");for(var a in r)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(a);n("73bc");var u=n("828b"),i=Object(u["a"])(r["default"],c["b"],c["c"],!1,null,"14a1f4a5",null,!1,c["a"],void 0);t["default"]=i.exports},e518:function(e,t,n){"use strict";n.r(t);var c=n("0df7"),r=n.n(c);for(var a in c)["default"].indexOf(a)<0&&function(e){n.d(t,e,(function(){return c[e]}))}(a);t["default"]=r.a},f103:function(e,t,n){"use strict";(function(e,t){var c=n("47a9");n("96bd");c(n("3240"));var r=c(n("ca8d"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(r.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["f103","common/runtime","common/vendor"]]]);