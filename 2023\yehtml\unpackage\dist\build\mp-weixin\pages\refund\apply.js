(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/refund/apply"],{"0163":function(e,t,n){},"0ced":function(e,t,n){"use strict";var r=n("0163"),o=n.n(r);o.a},"19f6":function(e,t,n){"use strict";n.d(t,"b",(function(){return o})),n.d(t,"c",(function(){return u})),n.d(t,"a",(function(){return r}));var r={uForm:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-form/u-form")]).then(n.bind(null,"29b8"))},uFormItem:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-form-item/u-form-item")]).then(n.bind(null,"218e"))},uInput:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-input/u-input")]).then(n.bind(null,"17d5"))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(null,"5f3a"))},uPicker:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-picker/u-picker")]).then(n.bind(null,"070b"))},uNumberBox:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-number-box/u-number-box")]).then(n.bind(null,"cc6c"))},uRadioGroup:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-radio-group/u-radio-group")]).then(n.bind(null,"5b4b"))},uRadio:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-radio/u-radio")]).then(n.bind(null,"e9f9"))},uCell:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-cell/u-cell")]).then(n.bind(null,"819c"))},uTextarea:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-textarea/u-textarea")]).then(n.bind(null,"19a3"))},uUpload:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-upload/u-upload")]).then(n.bind(null,"783f"))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-button/u-button")]).then(n.bind(null,"9edc"))}},o=function(){var e=this,t=e.$createElement;e._self._c;e._isMounted||(e.e0=function(t){e.goodsPickerShow=!0},e.e1=function(t){e.goodsPickerShow=!1})},u=[]},"5cd5":function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var o=r(n("7eb4")),u=r(n("7ca3")),i=r(n("ee10"));function s(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),n.push.apply(n,r)}return n}function a(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{};t%2?s(Object(n),!0).forEach((function(t){(0,u.default)(e,t,n[t])})):Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(n)):s(Object(n)).forEach((function(t){Object.defineProperty(e,t,Object.getOwnPropertyDescriptor(n,t))}))}return e}var c={data:function(){return{orderDetail:void 0,goodsPickerShow:!1,curGoods:void 0,curGoodsMaxNumber:0,rules:{type:[{type:"number",required:!0,message:"不能为空",trigger:["change","blur"]}],logisticsStatus:[{type:"number",required:!0,message:"不能为空",trigger:["change","blur"]}],reason:[{required:!0,message:"不能为空",trigger:["change","blur"]}],address:[{required:!0,message:"不能为空",trigger:["change","blur"]}],skuName:[{required:!0,message:"不能为空",trigger:["change","blur"]}],goodsBackType:[{required:!0,message:"不能为空",trigger:["change","blur"]}],packageDesc:[{required:!0,message:"不能为空",trigger:["change","blur"]}]},form:{type:0,logisticsStatus:0,orderId:void 0,reasonId:"",reason:void 0,remark:"",skuId:void 0,skuName:void 0,skuNum:void 0,goodsBackType:void 0,packageDesc:"10",queryType:"10"},reasons:["不喜欢/不想要","空包裹","未按约定时间发货","快递/物流一直未送达","货物破损已拒签","退运费","规格尺寸与商品页面描述不符","功能/效果不符","质量问题","少件/漏发","包装/商品破损","发票问题"],pics:[],orderSet:void 0,orderType:0,supportAfsTypeList:void 0,joycityPointsSearchAfsApplyReasonList:void 0,afsGoodsId:void 0,goodsBackTypes:void 0}},onReady:function(){this.$refs.uForm.setRules(this.rules)},onLoad:function(t){this.orderType=e.getStorageSync("orderType"),this.supportAfsTypeList=e.getStorageSync("supportAfsTypeList"),this.afsGoodsId=e.getStorageSync("afsGoodsId"),this.form.orderId=t.orderId,this._orderDetail(t.orderId),this._orderSet(),5==this.orderType&&(this.form.type=this.supportAfsTypeList[0],this._joycityPointsSearchAfsApplyReasonList())},mounted:function(){},methods:{_orderDetail:function(t){var n=this;return(0,i.default)(o.default.mark((function r(){var u;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,n.$wxapi.orderDetail(n.token,t);case 2:if(u=r.sent,0==u.code){r.next=7;break}return e.showToast({title:u.msg,icon:"none"}),e.navigateBack(),r.abrupt("return");case 7:n.orderDetail=u.data;case 8:case"end":return r.stop()}}),r)})))()},_orderSet:function(){var e=this;return(0,i.default)(o.default.mark((function t(){var n;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$wxapi.orderSet();case 2:n=t.sent,0==n.code&&(e.orderSet=n.data);case 4:case"end":return t.stop()}}),t)})))()},typeChange:function(e){this._joycityPointsSearchAfsApplyReasonList(),this.form.reason=null},_joycityPointsSearchAfsApplyReasonList:function(t){var n=this;return(0,i.default)(o.default.mark((function t(){var r;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,n.$wxapi.joycityPointsSearchAfsApplyReasonList({token:n.token,afsType:n.form.type,orderId:n.form.orderId,goodsId:n.afsGoodsId});case 2:if(r=t.sent,0==r.code){t.next=7;break}return e.showToast({title:r.msg,icon:"none"}),n.joycityPointsSearchAfsApplyReasonList=null,t.abrupt("return");case 7:n.joycityPointsSearchAfsApplyReasonList=r.data;case 8:case"end":return t.stop()}}),t)})))()},deletePic:function(e){this.pics.splice(e.index,1)},afterReadPic:function(e){var t=this;return(0,i.default)(o.default.mark((function n(){return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:t.pics=t.pics.concat(e.file);case 1:case"end":return n.stop()}}),n)})))()},submit:function(){var t=this;if(5==this.orderType){var n=this.joycityPointsSearchAfsApplyReasonList.find((function(e){return e.applyReasonName==t.form.reason}));if(!n)return void e.showToast({title:"请选择售后原因",icon:"none"});if(this.form.reasonId=n.applyReasonId,!this.form.remark)return void e.showToast({title:"备注信息不能为空",icon:"none"});if(!this.pics||0==this.pics.length)return void e.showToast({title:"请拍照并上传照片",icon:"none"})}3!=this.orderType||this.curGoods?this.$refs.uForm.validate().then((function(e){t._submit()})).catch((function(t){e.showToast({title:"表单请填写完整",icon:"none"})})):e.showToast({title:"请选择售后商品",icon:"none"})},_submit:function(){var t=this;return(0,i.default)(o.default.mark((function n(){var r,u,i,s,c,d;return o.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e.showLoading({title:""}),r=[],!(t.pics&&t.pics.length>0)){n.next=17;break}u=0;case 4:if(!(u<t.pics.length)){n.next=17;break}return i=t.pics[u],n.next=8,t.$wxapi.uploadFile(t.token,i.url);case 8:if(s=n.sent,0==s.code){n.next=13;break}return e.hideLoading(),e.showToast({title:s.msg,icon:"none"}),n.abrupt("return");case 13:r.push(s.data.url);case 14:u++,n.next=4;break;case 17:return n.next=19,t.$wxapi.refundApply(a(a({token:t.token,amount:0},t.form),{},{pic:r.join()}));case 19:c=n.sent,e.hideLoading(),0!=c.code?e.showToast({title:c.msg,icon:"none"}):(e.showToast({title:"提交成功，请耐心等待客服处理"}),d=e.getStorageSync("refundApplyedOrderIds"),d||(d=[]),d.push(t.form.orderId),e.setStorageSync("refundApplyedOrderIds",d),e.navigateBack());case 22:case"end":return n.stop()}}),n)})))()},cp:function(t){e.setClipboardData({data:t,success:function(){e.showToast({title:"已复制"})}})},goodsPickerSelect:function(t){var n=this;return(0,i.default)(o.default.mark((function r(){var u,i;return o.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(u=t.value[0],3!=n.orderType){r.next=29;break}return r.next=4,n.$wxapi.jdvopQueryCanRefundNumber({token:n.token,jdOrderId:n.orderDetail.orderInfo.orderNumberOuter,skuId:u.supplyGoodsId,queryType:10});case 4:if(i=r.sent,0==i.code){r.next=8;break}return e.showToast({title:i.msg,icon:"none"}),r.abrupt("return");case 8:if(!(1*i.data<1)){r.next=11;break}return e.showToast({title:"当前商品无法申请售后",icon:"none"}),r.abrupt("return");case 11:return n.form.skuNum=1*i.data,n.curGoodsMaxNumber=1*i.data,r.next=15,n.$wxapi.jdvopQueryRefundType({token:n.token,jdOrderId:n.orderDetail.orderInfo.orderNumberOuter,skuId:u.supplyGoodsId,queryType:10});case 15:if(i=r.sent,0==i.code){r.next=19;break}return e.showToast({title:i.msg,icon:"none"}),r.abrupt("return");case 19:return n.supportAfsTypeList=i.data,n.form.type=i.data[0].code,r.next=23,n.$wxapi.jdvopQueryGoodsBackType({token:n.token,jdOrderId:n.orderDetail.orderInfo.orderNumberOuter,skuId:u.supplyGoodsId,queryType:10});case 23:if(i=r.sent,0==i.code){r.next=27;break}return e.showToast({title:i.msg,icon:"none"}),r.abrupt("return");case 27:n.goodsBackTypes=i.data,n.form.goodsBackType=i.data[0].code;case 29:n.curGoods=t.value[0],n.goodsPickerShow=!1,n.form.skuId=n.curGoods.goodsId,n.form.skuName=n.curGoods.goodsName;case 33:case"end":return r.stop()}}),r)})))()}}};t.default=c}).call(this,n("df3c")["default"])},7346:function(e,t,n){"use strict";n.r(t);var r=n("19f6"),o=n("abc7");for(var u in o)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return o[e]}))}(u);n("0ced");var i=n("828b"),s=Object(i["a"])(o["default"],r["b"],r["c"],!1,null,"03bcb0ab",null,!1,r["a"],void 0);t["default"]=s.exports},abc7:function(e,t,n){"use strict";n.r(t);var r=n("5cd5"),o=n.n(r);for(var u in r)["default"].indexOf(u)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(u);t["default"]=o.a},ca59:function(e,t,n){"use strict";(function(e,t){var r=n("47a9");n("96bd");r(n("3240"));var o=r(n("7346"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(o.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["ca59","common/runtime","common/vendor"]]]);