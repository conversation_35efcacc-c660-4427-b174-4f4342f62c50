<template>
  <view class="page">
    <view class="poster" v-if="!posterShow">
      <lPainter :board="posterObj" ref="painter"></lPainter>
      <view class="footer-btn">
        <view class="">
          <u-button @click="goBack" :plain="true" type="info" text="返回"></u-button>
        </view>
        <view>
          <u-button @click="toSave" type="warning" text="保存" color="#2F4C42"></u-button>
        </view>
      </view>
    </view>
    <!-- 生成的图片 -->
    <uni-popup type="center" ref="posterImg" :maskClick="false">
      <view class="poster-img">
        <text @click="closePopup"></text>
        <image :src="path" mode="heightFix" @click="previewImg"></image>
        <view style="text-align: left;margin-left: 1em;">
          点击长按图片保存到手机
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script>
  import empty from 'empty-value'
  import uniPopup from "../../components/uni-popup/uni-popup.vue";
  import lPainter from '../../components/lime-painter/index.vue'
  export default {
    components: {
      lPainter,
      uniPopup
    },
    data() {
      return {
        userInfo: [],
        qrcode: '',
        minPriceOriginal: '',
        tags: '',
        goodsId: '',
        name: '',
        unit: '',
        posterShow: false, //显示海报
        path: '', //生成的图片地址
        posterObj: {}, //画板数据
        check_idx: 0, //底部选中的下标
      }
    },
    onShow() {
      this._userDetail()
    },
    onLoad(query) {
      if (!empty(query.id)) {
        this.goodsId = decodeURIComponent(query.id)
        this.pic = decodeURIComponent(query.pic)
        this.minPriceOriginal = decodeURIComponent(query.minPriceOriginal) + '/' + decodeURIComponent(query.unit)
        this.tags = decodeURIComponent(query.tags)
        this.name = decodeURIComponent(query.name)
      } else {
        this.goodsId = 1780169
        this.pic = 'https://dcdn.it120.cc/2024/05/17/bee3152e-d59c-4192-a54f-426ca2cf421b.jpeg'
        this.minPriceOriginal = '125/只'
        this.tags = '地标产品、清水养殖、五谷喂养'
        this.name = '御江青头鸭'
      }
    },
    methods: {
      async _userDetail() {
        // https://www.yuque.com/apifm/nu0f75/zgf8pu
        const res = await this.$wxapi.userDetail(this.token)
        if (res.code == 0) {
          let userInfo = res.data
          this.userInfo = userInfo

          const qrcodeRes = await this.$wxapi.wxaQrcode({
            scene: this.goodsId + ',' + userInfo.base.id,
            page: 'pages/goods/detail',
            is_hyaline: true,
            autoColor: true,
            expireHours: 1,
            check_path: __wxConfig.envVersion == 'release' ? true : false
          })

          if (qrcodeRes.code === 0) {
            this.posterObj = {
              width: '670rpx',
              height: '1165rpx',
              background: '#fff',
              borderRadius: '16rpx',
              views: [{
                  type: 'image',
                  src: 'https://ye.niutouren.vip/static/images/poster/bg.png',
                  css: {
                    left: '0',
                    top: '0',
                    width: '100%',
                    height: '100%'
                  }
                },
                {
                  type: 'image',
                  src: userInfo.base.avatarUrl,
                  css: {
                    left: '60rpx',
                    top: '120rpx',
                    borderRadius: '50%',
                    width: '80rpx',
                    height: '80rpx'
                  }
                },
                {
                  type: 'text',
                  text: userInfo.base.nick,
                  css: {
                    fontSize: '28rpx',
                    fontWeight: 'bold',
                    color: '#1A2033',
                    lineHeight: '28rpx',
                    left: '170rpx',
                    top: '130rpx'
                  }
                },
                {
                  type: 'text',
                  text: '邀请您品味品质生活',
                  css: {
                    fontSize: '24rpx',
                    color: '#1A2033',
                    lineHeight: '24rpx',
                    left: '170rpx',
                    top: '170rpx'
                  }
                },
                {
                  type: 'image',
                  src: this.pic,
                  css: {
                    left: '60rpx',
                    top: '230rpx',
                    width: '546rpx',
                    height: '364rpx',
                    borderRadius: '16rpx'
                  }
                },
                {
                  type: 'image',
                  src: qrcodeRes.data,
                  css: {
                    left: '400rpx',
                    top: '631rpx',
                    width: '200rpx',
                    height: '200rpx',
                  },
                }, {
                  type: 'text',
                  text: '长按/扫描打开小程序',
                  css: {
                    fontSize: '24rpx',
                    color: '#1A2033',
                    lineHeight: '45rpx',
                    left: '380rpx',
                    top: '850rpx',
                    width: '606rpx'
                  }
                }, {
                  type: 'text',
                  text: this.name,
                  css: {
                    fontSize: '48rpx',
                    color: '#1A2033',
                    lineHeight: '45rpx',
                    fontWeight: 'bold',
                    left: '60rpx',
                    top: '690rpx',
                    width: '606rpx'
                  }
                }, {
                  type: 'text',
                  text: this.tags,
                  css: {
                    fontSize: '24rpx',
                    color: '#1A2033',
                    lineHeight: '45rpx',
                    left: '60rpx',
                    top: '750rpx',
                    width: '606rpx'
                  }
                }, {
                  type: 'text',
                  text: this.minPriceOriginal,
                  css: {
                    fontSize: '60rpx',
                    color: '#e64340',
                    lineHeight: '45rpx',
                    fontWeight: 'bold',
                    fontStyle: 'italic',
                    left: '80rpx',
                    top: '820rpx',
                    width: '606rpx'
                  }
                }, {
                  type: 'text',
                  text: '¥',
                  css: {
                    fontSize: '24rpx',
                    color: '#e64340',
                    lineHeight: '45rpx',
                    fontStyle: 'italic',
                    left: '60rpx',
                    top: '830rpx',
                    width: '100rpx'
                  }
                },
              ]
            }
          }
        }
      },
      previewImg() {
        // #ifdef H5
        return;
        // #endif
        uni.previewImage({
          current: this.path,
          urls: [this.path]
        });
      },
      closePopup() {
        this.$refs.posterImg.close();
        this.posterShow = false;
      },
      toSave() {
        uni.showLoading({
          title: '海报生成中',
        })
        const painter = this.$refs.painter;
        painter.canvasToTempFilePath().then(res => {
          this.path = res.tempFilePath;
          this.$refs.posterImg.open();
          this.posterShow = true;
          uni.hideLoading()
        });
      },
      goBack() {
        uni.navigateBack({
          delta: 1
        });
      }
    }
  }
</script>

<style>
  page {
    background: #999;
  }
</style>
<style lang="scss" scoped>
  .page {
    /deep/ .uni-transition {
      background-color: rgba(0, 0, 0, 0.6)
    }

    .poster-img {
      width: 670rpx;
      height: 928rpx;
      position: relative;

      text {
        background: url('https://s.yun-live.com/images/20210201/5c4ef9d86bc5eec90f2f915683d9db08.png') no-repeat;
        background-size: 100% 100%;
        display: inline-block;
        width: 50rpx;
        height: 50rpx;
        position: absolute;
        top: -60rpx;
        right: 0;
      }

      image {
        width: 90%;
        height: 90%;
      }

      view {
        font-size: 32rpx;
        font-family: PingFang-SC-Bold, PingFang-SC;
        font-weight: bold;
        color: #FFFFFF;
        line-height: 32rpx;
        text-align: center;
        margin-top: 28rpx;
      }
    }

    .poster {
      padding: 24rpx 40rpx;

      .footer-btn {
        margin-top: 24rpx;
        display: flex;
        align-items: center;
        justify-content: space-between;

        view {
          width: 320rpx;
        }
      }
    }
  }
</style>