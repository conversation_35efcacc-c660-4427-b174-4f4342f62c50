(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uview-ui/components/u-gap/u-gap"],{"08bb":function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return u})),e.d(n,"a",(function(){}));var i=function(){var t=this.$createElement,n=(this._self._c,this.__get_style([this.gapStyle]));this.$mp.data=Object.assign({},{$root:{s0:n}})},u=[]},"222c":function(t,n,e){"use strict";e.r(n);var i=e("29ee"),u=e.n(i);for(var a in i)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(a);n["default"]=u.a},"29ee":function(t,n,e){"use strict";(function(t){var i=e("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=i(e("7983")),a={name:"u-gap",mixins:[t.$u.mpMixin,t.$u.mixin,u.default],computed:{gapStyle:function(){var n={backgroundColor:this.bgColor,height:t.$u.addUnit(this.height),marginTop:t.$u.addUnit(this.marginTop),marginBottom:t.$u.addUnit(this.marginBottom)};return t.$u.deepMerge(n,t.$u.addStyle(this.customStyle))}}};n.default=a}).call(this,e("df3c")["default"])},"2ca3":function(t,n,e){"use strict";var i=e("764a"),u=e.n(i);u.a},4895:function(t,n,e){"use strict";e.r(n);var i=e("08bb"),u=e("222c");for(var a in u)["default"].indexOf(a)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(a);e("2ca3");var o=e("828b"),r=Object(o["a"])(u["default"],i["b"],i["c"],!1,null,"72836044",null,!1,i["a"],void 0);n["default"]=r.exports},"764a":function(t,n,e){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uview-ui/components/u-gap/u-gap-create-component',
    {
        'uni_modules/uview-ui/components/u-gap/u-gap-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("4895"))
        })
    },
    [['uni_modules/uview-ui/components/u-gap/u-gap-create-component']]
]);
