(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/my/components/user-info"],{"49ab":function(n,e,t){"use strict";var a=t("8671"),u=t.n(a);u.a},"83cd":function(n,e,t){"use strict";t.r(e);var a=t("9cf7"),u=t.n(a);for(var r in a)["default"].indexOf(r)<0&&function(n){t.d(e,n,(function(){return a[n]}))}(r);e["default"]=u.a},8671:function(n,e,t){},"8e46":function(n,e,t){"use strict";t.d(e,"b",(function(){return u})),t.d(e,"c",(function(){return r})),t.d(e,"a",(function(){return a}));var a={uAvatar:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-avatar/u-avatar")]).then(t.bind(null,"72a3"))}},u=function(){var n=this.$createElement;this._self._c},r=[]},"9cf7":function(n,e,t){"use strict";(function(n){var a=t("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var u=a(t("7eb4")),r=a(t("ee10")),o=t("b543"),i={name:"user-info",props:{data:{type:Object,default:function(){return{}}},needLogin:{type:Boolean,default:!1}},data:function(){return{}},methods:{onChooseAvatar:function(e){var t=this;return(0,r.default)(u.default.mark((function a(){var r,o,i;return u.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(t,r=e.detail.avatarUrl,!r){a.next=12;break}return a.next=5,t.$wxapi.uploadFile(t.token,r);case 5:if(o=a.sent,0!=o.code){a.next=12;break}return i={token:t.token,avatarUrl:o.data.url},a.next=10,t.$wxapi.modifyUserInfo(i);case 10:a.sent,n.navigateTo({url:"/pages/my/info"});case 12:case"end":return a.stop()}}),a)})))()},goLogin:function(){o.autoLogin()},editNick:function(){n.navigateTo({url:"/pages/my/info"})}}};e.default=i}).call(this,t("df3c")["default"])},ce8f:function(n,e,t){"use strict";t.r(e);var a=t("8e46"),u=t("83cd");for(var r in u)["default"].indexOf(r)<0&&function(n){t.d(e,n,(function(){return u[n]}))}(r);t("49ab");var o=t("828b"),i=Object(o["a"])(u["default"],a["b"],a["c"],!1,null,"6035df06",null,!1,a["a"],void 0);e["default"]=i.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'pages/my/components/user-info-create-component',
    {
        'pages/my/components/user-info-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("ce8f"))
        })
    },
    [['pages/my/components/user-info-create-component']]
]);
