{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?5953", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?05a2", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?53dc", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?c0bc", "uni-app:///uni_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?c073", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue?f63f"], "names": ["name", "mixins", "data", "isActive", "parentData", "value", "activeColor", "inactiveColor", "created", "methods", "init", "uni", "updateParentData", "updateFromParent", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsI;AACtI;AACiE;AACL;AACsC;;;AAGlG;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,mFAAM;AACR,EAAE,oGAAM;AACR,EAAE,6GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAAwqB,CAAgB,wrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC6C5rB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAbA,eAcA;EACAA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;UACA;QACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7GA;AAAA;AAAA;AAAA;AAA+xC,CAAgB,mvCAAG,EAAC,C;;;;;;;;;;;ACAnzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-tabbar-item.vue?vue&type=template&id=0d9729bf&scoped=true&\"\nvar renderjs\nimport script from \"./u-tabbar-item.vue?vue&type=script&lang=js&\"\nexport * from \"./u-tabbar-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-tabbar-item.vue?vue&type=style&index=0&id=0d9729bf&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0d9729bf\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-tabbar-item/u-tabbar-item.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-tabbar-item.vue?vue&type=template&id=0d9729bf&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uBadge: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-badge/u-badge\" */ \"@/uni_modules/uview-ui/components/u-badge/u-badge.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.$u.addStyle(_vm.customStyle)])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-tabbar-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-tabbar-item.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t    class=\"u-tabbar-item\"\r\n\t    :style=\"[$u.addStyle(customStyle)]\"\r\n\t    @tap=\"clickHandler\"\r\n\t>\r\n\t\t<view class=\"u-tabbar-item__icon\">\r\n\t\t\t<u-icon\r\n\t\t\t    v-if=\"icon\"\r\n\t\t\t    :name=\"icon\"\r\n\t\t\t    :color=\"isActive? parentData.activeColor : parentData.inactiveColor\"\r\n\t\t\t    :size=\"20\"\r\n\t\t\t></u-icon>\r\n\t\t\t<template v-else>\r\n\t\t\t\t<slot\r\n\t\t\t\t    v-if=\"isActive\"\r\n\t\t\t\t    name=\"active-icon\"\r\n\t\t\t\t/>\r\n\t\t\t\t<slot\r\n\t\t\t\t    v-else\r\n\t\t\t\t    name=\"inactive-icon\"\r\n\t\t\t\t/>\r\n\t\t\t</template>\r\n\t\t\t<u-badge\r\n\t\t\t\tabsolute\r\n\t\t\t\t:offset=\"[0, dot ? '34rpx' : badge > 9 ? '14rpx' : '20rpx']\"\r\n\t\t\t    :customStyle=\"badgeStyle\"\r\n\t\t\t    :isDot=\"dot\"\r\n\t\t\t    :value=\"badge || (dot ? 1 : null)\"\r\n\t\t\t    :show=\"dot || badge > 0\"\r\n\t\t\t></u-badge>\r\n\t\t</view>\r\n\t\t\r\n\t\t<slot name=\"text\">\r\n\t\t\t<text\r\n\t\t\t    class=\"u-tabbar-item__text\"\r\n\t\t\t    :style=\"{\r\n\t\t\t\t\tcolor: isActive? parentData.activeColor : parentData.inactiveColor\r\n\t\t\t\t}\"\r\n\t\t\t>{{ text }}</text>\r\n\t\t</slot>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t/**\r\n\t * TabbarItem 底部导航栏子组件\r\n\t * @description 此组件提供了自定义tabbar的能力。\r\n\t * @tutorial https://www.uviewui.com/components/tabbar.html\r\n\t * @property {String | Number}\tname\t\titem标签的名称，作为与u-tabbar的value参数匹配的标识符\r\n\t * @property {String}\t\t\ticon\t\tuView内置图标或者绝对路径的图片\r\n\t * @property {String | Number}\tbadge\t\t右上角的角标提示信息\r\n\t * @property {Boolean}\t\t\tdot\t\t\t是否显示圆点，将会覆盖badge参数（默认 false ）\r\n\t * @property {String}\t\t\ttext\t\t描述文本\r\n\t * @property {Object | String}\tbadgeStyle\t控制徽标的位置，对象或者字符串形式，可以设置top和right属性（默认 'top: 6px;right:2px;' ）\r\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\r\n\t * \r\n\t * @example <u-tabbar :value=\"value2\" :placeholder=\"false\" @change=\"name => value2 = name\" :fixed=\"false\" :safeAreaInsetBottom=\"false\"><u-tabbar-item text=\"首页\" icon=\"home\" dot ></u-tabbar-item></u-tabbar>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-tabbar-item',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tisActive: false, // 是否处于激活状态\r\n\t\t\t\tparentData: {\r\n\t\t\t\t\tvalue: null,\r\n\t\t\t\t\tactiveColor: '',\r\n\t\t\t\t\tinactiveColor: ''\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit() {\r\n\t\t\t\t// 支付宝小程序不支持provide/inject，所以使用这个方法获取整个父组件，在created定义，避免循环引用\r\n\t\t\t\tthis.updateParentData()\r\n\t\t\t\tif (!this.parent) {\r\n\t\t\t\t\tuni.$u.error('u-tabbar-item必须搭配u-tabbar组件使用')\r\n\t\t\t\t}\r\n\t\t\t\t// 本子组件在u-tabbar的children数组中的索引\r\n\t\t\t\tconst index = this.parent.children.indexOf(this)\r\n\t\t\t\t// 判断本组件的name(如果没有定义name，就用index索引)是否等于父组件的value参数\r\n\t\t\t\tthis.isActive = (this.name || index) === this.parentData.value\r\n\t\t\t},\r\n\t\t\tupdateParentData() {\r\n\t\t\t\t// 此方法在mixin中\r\n\t\t\t\tthis.getParentData('u-tabbar')\r\n\t\t\t},\r\n\t\t\t// 此方法将会被父组件u-tabbar调用\r\n\t\t\tupdateFromParent() {\r\n\t\t\t\t// 重新初始化\r\n\t\t\t\tthis.init()\r\n\t\t\t},\r\n\t\t\tclickHandler() {\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tconst index = this.parent.children.indexOf(this)\r\n\t\t\t\t\tconst name = this.name || index\r\n\t\t\t\t\t// 点击的item为非激活的item才发出change事件\r\n\t\t\t\t\tif (name !== this.parent.value) {\r\n\t\t\t\t\t\tthis.parent.$emit('change', name)\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$emit('click', name)\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-tabbar-item {\r\n\t\t@include flex(column);\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tflex: 1;\r\n\t\t\r\n\t\t&__icon {\r\n\t\t\t@include flex;\r\n\t\t\tposition: relative;\r\n\t\t\twidth: 150rpx;\r\n\t\t\tjustify-content: center;\r\n\t\t}\r\n\r\n\t\t&__text {\r\n\t\t\tmargin-top: 2px;\r\n\t\t\tfont-size: 12px;\r\n\t\t\tcolor: $u-content-color;\r\n\t\t}\r\n\t}\r\n\r\n\t/* #ifdef MP */\r\n\t// 由于小程序都使用shadow DOM形式实现，需要给影子宿主设置flex: 1才能让其撑开\r\n\t:host {\r\n\t\tflex: 1\r\n\t}\r\n\t/* #endif */\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-tabbar-item.vue?vue&type=style&index=0&id=0d9729bf&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-tabbar-item.vue?vue&type=style&index=0&id=0d9729bf&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688737418\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}