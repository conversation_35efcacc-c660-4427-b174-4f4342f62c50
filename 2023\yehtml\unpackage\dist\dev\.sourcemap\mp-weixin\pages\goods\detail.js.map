{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/detail.vue?685a", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/detail.vue?143c", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/detail.vue?9668", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/detail.vue?3a26", "uni-app:///pages/goods/detail.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/detail.vue?440a", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/detail.vue?8e98"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "l<PERSON><PERSON><PERSON>", "data", "tabs", "viewId", "name", "curViewId", "timeData", "goodsDetail", "mySales", "goodsType", "jdGoodsDetail", "wxintroduction", "faved", "showGoodsPop", "page", "reputationList", "videoMp4Src", "posterData", "curG<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "cur<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "myHelpDetail", "kjid", "userInfo", "posterObj", "afterDeadline", "deliveryTypeUrl", "cardPage", "maxTotal", "minTotal", "shichiPrice", "longActiveTab", "longPage", "longButton", "longImage", "longList", "computed", "filteredTags", "onLoad", "e", "onShow", "onShareAppMessage", "d", "title", "path", "imageUrl", "_data", "created", "methods", "_back", "uni", "_userDetail", "res", "should<PERSON><PERSON><PERSON>", "_goodsLong", "_goodsDetail", "goodsId", "icon", "setTimeout", "currentTimestamp", "deadlineTimestamp", "basicInfo", "yyId", "yyIdStr", "supplyType", "pic", "stores", "pics", "jdvopGoodsDetail", "jdvopGoodsSkuImages", "ele", "joycityPointsGoodsDetail", "goCart", "url", "goodsFavCheck", "token", "type", "addFav", "getApp", "extJsonStr", "goodsName", "tabclick", "_showGoodsAfterDeadline", "_showGoodsPop", "kanji<PERSON><PERSON>", "scrolltolower", "_reputationList", "pageSize", "getVideoSrc", "drawSharePic", "_pic", "_minPriceOriginal", "_tags", "_id", "_name", "_unit", "kanjiaSet", "kanji<PERSON><PERSON>ress", "kjJoinUid", "joinK<PERSON>jia", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "console", "kanjiaInfo", "content", "showCancel", "confirmText", "kanjiaHelpDetail", "_pingtuanSet", "onChangeTimeData", "longButtonSet", "changeTab", "checkImage", "src", "success", "fail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,wVAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,aAAa,gUAEN;AACP,KAAK;AACL;AACA,aAAa,8SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3JA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACiWrrB;AAEA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MAEA;MACAC;MAAA;MACAC;MAEAC;MAEAC;MAEA;MACAC;MACAC;MACAC;MAEA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;UACA;UACA;YAAA;UAAA;QACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACA;IACA;IACA;MACA;IACA;IACA;IACA;MACA;MACA;QACAC;QACA;MACA;IACA;IACA;IACA;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IAEA;MACAF;QACAC;QACAC;MACA;IACA;IAEA;MACA;MAEAF;QACAC;QACAC;QACAC;MACA;IACA;IAEA;MACAC;MACAA;IACA;IACA;EACA;EACAC,6BAEA;EACAC;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAJ;kBACA;kBACA;gBACA;kBACA;oBACA;kBACA;;kBAEA;gBACA;kBACA;gBAAA,CACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,KACAC;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAJ;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAP;kBACAc;gBACA;gBACAC;kBACAR;gBACA;gBAAA;cAAA;gBAGA;gBACA;gBACA;kBACA;oBACA;kBACA;kBACA;oBACA,oGACA;oBAEAS;oBACAC;oBAEA;sBACA;oBACA;kBACA;kBACA;oBACA,8GACA;kBACA;kBAEA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;kBACA;kBAGA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;gBAEA;kBACA;gBACA;gBACA;kBACA;gBACA;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;gBACA;kBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACA5D;oBACA6D;kBACA;kBACAC;gBACA;cAAA;gBAGA;gBACA;gBACA;gBACA;kBACA;gBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAhB;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAP;kBACAc;gBACA;gBACAC;kBACAR;gBACA;gBAAA;cAAA;gBAGA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAjB;gBACA;kBACAe;kBACAA;oBACAG;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAnB;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAP;kBACAc;gBACA;gBACAC;kBACAR;gBACA;gBAAA;cAAA;gBAGAiB;gBACAf;kBACAe;oBACAF;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAO;MACA;QACAtB;MACA;MACAA;QACAuB;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAxE;kBACAyE;kBACAC;kBACApB;gBACA;gBACA;kBACAtD;kBACAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAkD;gBACA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAyB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACA5B;kBACAuB;gBACA;gBAAA;cAAA;gBAGAvE;kBACAyE;kBACAC;kBACApB;gBACA;gBACA;kBACAtD;kBACAA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAkD;gBACA;kBACA;gBACA;kBACAF;oBACAP;oBACAc;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAsB;kBACAd;kBACAe;kBACAhB;gBACA;gBACA9D;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAkD;gBACA;kBACA;gBACA;kBACAF;oBACAP;oBACAc;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAwB;MACA;IACA;IACAC;MACAhC;QACAP;QACAc;MACA;IACA;IACA;IACA0B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACA9B;kBACAzC;kBACAwE;gBACA;cAAA;gBAJAnC;gBAKA;kBACAA;oBACA;sBACA;oBAAA;oBAEA;sBACAkB;oBACA;oBACA;sBACAA;oBACA;sBACAA;oBACA;sBACAA;oBACA;kBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAkB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAApC;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAqC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;gBACAC;gBAEA7C;kBACAuB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAuB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA5C;gBACA;kBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA6C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACA;kBACAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA9C;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA+C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,IAEA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAGAjD;kBACAP;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAS;gBACAF;gBAAA,MACAE;kBAAA;kBAAA;gBAAA;gBACA0B;gBAAA;cAAA;gBAGA;kBACA;kBACA;kBACA;gBACA;kBACA5B;oBACAP;oBACAc;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA2C;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAA;gBAAA;gBAAA,OACA,8FACAC;cAAA;gBADAlD;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACAF;kBACAP;kBACAc;gBACA;gBAAA;cAAA;gBAGA;gBACAP;kBACAP;kBACA4D;kBACAC;kBACAC;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA,kFACAtF;cAAA;gBADAgC;gBAEA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAuD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAvD;gBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAwD;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACApH;QACAqH;QACAC;UACA;QACA;QACAC;UACAb;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC15BA;AAAA;AAAA;AAAA;AAAwxC,CAAgB,4uCAAG,EAAC,C;;;;;;;;;;;ACA5yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/goods/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/goods/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=f0a9f5ba&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=f0a9f5ba&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f0a9f5ba\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods/detail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=f0a9f5ba&scoped=true&\"", "var components\ntry {\n  components = {\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-swiper/u-swiper\" */ \"@/uni_modules/uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uGap: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-gap/u-gap\" */ \"@/uni_modules/uview-ui/components/u-gap/u-gap.vue\"\n      )\n    },\n    uCellGroup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell-group/u-cell-group\" */ \"@/uni_modules/uview-ui/components/u-cell-group/u-cell-group.vue\"\n      )\n    },\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n    uLineProgress: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-line-progress/u-line-progress\" */ \"@/uni_modules/uview-ui/components/u-line-progress/u-line-progress.vue\"\n      )\n    },\n    uRow: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-row/u-row\" */ \"@/uni_modules/uview-ui/components/u-row/u-row.vue\"\n      )\n    },\n    uCol: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-col/u-col\" */ \"@/uni_modules/uview-ui/components/u-col/u-col.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-image/u-image\" */ \"@/uni_modules/uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n    uParse: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-parse/u-parse\" */ \"@/uni_modules/uview-ui/components/u-parse/u-parse.vue\"\n      )\n    },\n    uList: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-list/u-list\" */ \"@/uni_modules/uview-ui/components/u-list/u-list.vue\"\n      )\n    },\n    uListItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-list-item/u-list-item\" */ \"@/uni_modules/uview-ui/components/u-list-item/u-list-item.vue\"\n      )\n    },\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-avatar/u-avatar\" */ \"@/uni_modules/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-empty/u-empty\" */ \"@/uni_modules/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uBadge: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-badge/u-badge\" */ \"@/uni_modules/uview-ui/components/u-badge/u-badge.vue\"\n      )\n    },\n    goodsPop: function () {\n      return import(\n        /* webpackChunkName: \"components/goods-pop/goods-pop\" */ \"@/components/goods-pop/goods-pop.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 =\n    _vm.goodsDetail && _vm.goodsType !== \"XSSC\"\n      ? _vm.__map(_vm.filteredTags, function (tag, index) {\n          var $orig = _vm.__get_orig(tag)\n          var m0 = _vm.shouldHighlight(tag)\n          var m1 = _vm.shouldHighlight(tag)\n          return {\n            $orig: $orig,\n            m0: m0,\n            m1: m1,\n          }\n        })\n      : null\n  var g0 = _vm.goodsDetail\n    ? _vm.curKanjiaprogress &&\n      _vm.curKanjiaprogress.helps &&\n      _vm.curKanjiaprogress.helps.length > 0\n    : null\n  var g1 =\n    _vm.goodsDetail && _vm.longPage === 1 && _vm.longActiveTab === \"tab1\"\n      ? _vm.longList.length\n      : null\n  var g2 =\n    _vm.goodsDetail && _vm.longPage === 1 && _vm.longActiveTab === \"tab1\"\n      ? _vm.longList.length\n      : null\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showGoodsPop = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <view cla ss=\"goods-detail\" v-if=\"goodsDetail\">\r\n      <scroll-view class=\"main\" scroll-y :scroll-into-view=\"curViewId\" enable-back-to-top scroll-with-animation @scrolltolower=\"scrolltolower\">\r\n        <view id=\"basic\">\r\n          <view class=\"goods-images\">\r\n            <video v-if=\"videoMp4Src\" :src=\"videoMp4Src\" autoplay=\"true\" loop=\"true\" object-fit=\"cover\" style='width:750rpx;height:750rpx;'></video>\r\n            <u-swiper v-else :list=\"goodsDetail.pics\" keyName=\"pic\" radius=\"0\" indicator circular height=\"750rpx\"></u-swiper>\r\n            <view v-if=\"deliveryTypeUrl !== ''\" class=\"delivery-type\">\r\n              <img style=\"width: 100px;height: 56px;\" :src=\"deliveryTypeUrl\">\r\n            </view>\r\n          </view>\r\n          <view class=\"price-share\">\r\n            <view v-if=\"curKanjiaprogress\" class=\"price-score price-score2\">\r\n              <view class=\"item\">\r\n                <text>¥1</text>{{ curKanjiaprogress.kanjiaInfo.curPrice }}\r\n              </view>\r\n              <view class=\"item original\">\r\n                <text>¥1</text>{{ curGoodsKanjia.originalPrice }}\r\n              </view>\r\n            </view>\r\n            <view v-else class=\"price-score price-score2\">\r\n              <view v-if=\"goodsType !== 'XSSC'\" class=\"price-info\">\r\n                <view class=\"price-right\">\r\n                  <span class=\"prefix\">￥</span>\r\n                  <span>{{ goodsDetail.basicInfo.minPrice }}</span>\r\n                </view>\r\n                <view class=\"price-left\">\r\n                  <view class=\"line1\">\r\n                    <view class=\"original-price\">\r\n                      <span>原价￥{{ goodsDetail.basicInfo.originalPrice }}</span> / {{ goodsDetail.basicInfo.unit }}\r\n                    </view>\r\n                    <view class=\"sales\" v-if=\"mySales > 10\">销量：{{ mySales }}</view>\r\n                  </view>\r\n\r\n                  <view v-if=\"goodsType === 'XSQG'\">\r\n                    <view class=\"only\">\r\n                      仅剩<span>{{ goodsDetail.basicInfo.stores }}</span>{{ goodsDetail.basicInfo.unit }}</view>\r\n                  </view>\r\n\r\n                  <view v-if=\"goodsType === 'XSTG'\">\r\n                    <view class=\"delivery-time\" style=\"display: inline-block;\">到货时间：{{ goodsDetail.delivery_time }}</view><br />\r\n                    <view class=\"only\">\r\n                      仅剩<span>{{ goodsDetail.basicInfo.stores }}</span>{{ goodsDetail.basicInfo.unit }}</view>\r\n                  </view>\r\n                </view>\r\n              </view>\r\n              <view v-if=\"goodsType === 'XSSC'\" class=\"price-info\">\r\n                <view class=\"price-right\">\r\n                  <view class=\"original-price\" style=\"text-decoration: none;\">\r\n                    <span>原价￥{{ goodsDetail.basicInfo.originalPrice }}</span><br />\r\n                    <span>试吃价</span>\r\n                  </view>\r\n                  <span class=\"prefix\">￥</span>\r\n                  <span>{{ shichiPrice }}</span>\r\n                </view>\r\n                <view class=\"price-left\">\r\n                  <view class=\"max-total\">试吃总数 {{ maxTotal }}只</view><br />\r\n                  <view class=\"max-total\">试吃起数 {{ minTotal }}只</view><br />\r\n                  <view class=\"only\">\r\n                    剩余<span>{{ goodsDetail.basicInfo.stores }}</span>{{ goodsDetail.basicInfo.unit }}</view>\r\n                  <view class=\"delivery-time\" style=\"display: inline-block;\">到货时间 {{ goodsDetail.delivery_time }}</view><br />\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"goods-title u-line-3 pt16\">\r\n            <view class=\"box-left\">\r\n              <text>{{ goodsDetail.basicInfo.name }}</text>\r\n              <view v-if=\"goodsType !== 'XSSC'\" class=\"goods-tags\">\r\n                <span v-for=\"(tag, index) in filteredTags\" :key=\"index\" :style=\"{ color: shouldHighlight(tag) ? '#e64340' : '', border: shouldHighlight(tag) ? '1px solid #e64340' : '', display: 'inline-block', 'margin-right': '5px' }\">\r\n                  {{ tag }}\r\n                </span>\r\n              </view>\r\n            </view>\r\n            <!-- #ifdef MP -->\r\n            <view class=\"btns\">\r\n              <view class=\"icon-btn\">&nbsp;</view>\r\n              <view class=\"icon-btn\" @click=\"longButtonSet(1)\">\r\n                <u-icon name=\"weixin-circle-fill\" color=\"#1BD66C\" size=\"48rpx\"></u-icon>\r\n                <text>接龙</text>\r\n                <button class=\"share\" open-type=\"share\"></button>\r\n              </view>\r\n              <view class=\"icon-btn\" @click=\"longButtonSet(0)\">\r\n                <u-icon name=\"share-square\" size=\"48rpx\"></u-icon>\r\n                <text>分享</text>\r\n                <button class=\"share\" open-type=\"share\"></button>\r\n              </view>\r\n              <view class=\"icon-btn\" @click=\"drawSharePic\">\r\n                <u-icon name=\"moments\" size=\"48rpx\"></u-icon>\r\n                <text>海报</text>\r\n              </view>\r\n            </view>\r\n            <!-- #endif -->\r\n          </view>\r\n          <view v-if=\"goodsDetail.basicInfo.characteristic\" class=\"title-sub\">\r\n            {{ goodsDetail.basicInfo.characteristic }}\r\n          </view>\r\n          <view class=\"commission\" v-if=\"goodsDetail.basicInfo.commissionType == 1\">分享有赏，好友下单后可得\r\n            {{goodsDetail.basicInfo.commission}} 积分奖励\r\n          </view>\r\n          <view class=\"commission\" v-if=\"goodsDetail.basicInfo.commissionType == 2\">分享有赏，好友下单后可得\r\n            {{goodsDetail.basicInfo.commission}}元 现金奖励\r\n          </view>\r\n        </view>\r\n\r\n        <block v-if=\"curGoodsKanjia\">\r\n          <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n          <view class=\"label-title\">\r\n            <view class=\"icon\"></view>商品砍价设置\r\n          </view>\r\n          <u-cell-group>\r\n            <u-cell title=\"数量\" :value=\"curGoodsKanjia.number + '份'\"></u-cell>\r\n            <u-cell title=\"已售\" :value=\"curGoodsKanjia.numberBuy + '份'\"></u-cell>\r\n            <u-cell title=\"原价\" :value=\"curGoodsKanjia.originalPrice\"></u-cell>\r\n            <u-cell title=\"底价\" :value=\"curGoodsKanjia.minPrice\"></u-cell>\r\n            <u-cell title=\"截止\" :value=\"curGoodsKanjia.dateEnd\"></u-cell>\r\n          </u-cell-group>\r\n        </block>\r\n        <block v-if=\"curKanjiaprogress\">\r\n          <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n          <view class=\"label-title\">\r\n            <view class=\"icon\"></view>当前砍价状态\r\n          </view>\r\n          <u-cell-group>\r\n            <u-cell title=\"帮砍人数\" :value=\"curKanjiaprogress.kanjiaInfo.helpNumber\"></u-cell>\r\n            <u-cell title=\"状态\" :value=\"curKanjiaprogress.kanjiaInfo.statusStr\"></u-cell>\r\n            <u-cell title=\"参与时间\" :value=\"curKanjiaprogress.kanjiaInfo.dateAdd\"></u-cell>\r\n          </u-cell-group>\r\n        </block>\r\n        <view v-if=\"curKanjiaprogress && curKanjiaprogress.kanjiaInfo.uid != uid\" class=\"curKanjiaJoin\">\r\n          帮<text>{{curKanjiaprogress.joiner.nick}}</text> 砍价吧！\r\n        </view>\r\n        <view v-if=\"curGoodsKanjia && curKanjiaprogress\" class=\"curKanjiaprogress\">\r\n          <u-line-progress :percentage=\"100 * (curGoodsKanjia.originalPrice - curKanjiaprogress.kanjiaInfo.curPrice) / (curGoodsKanjia.originalPrice - curGoodsKanjia.minPrice)\" activeColor=\"#ff0000\"></u-line-progress>\r\n          <view class=\"curKanjiaprogress-bar\">// 砍价完成进度 //</view>\r\n        </view>\r\n\r\n        <block v-if=\"curKanjiaprogress && curKanjiaprogress.helps && curKanjiaprogress.helps.length>0\">\r\n          <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n          <view class=\"label-title\">\r\n            <view class=\"icon\"></view>好友助力明细\r\n          </view>\r\n          <view class=\"kjlj\" v-for=\"(item,index) in curKanjiaprogress.helps\" :key=\"index\">\r\n            <image class=\"kjlj-l\" :src=\"item.avatarUrl\" mode=\"aspectFill\" />\r\n            <u-cell class=\"kjlj-r\" :label=\"item.nick + ' ' + item.dateAdd + '帮砍'\" size=\"large\">\r\n              <view slot=\"title\" class=\"price-score\">\r\n                <view class=\"item\"><text>¥</text>{{ item.cutPrice }}</view>\r\n              </view>\r\n            </u-cell>\r\n          </view>\r\n        </block>\r\n\r\n        <view v-if=\"goodsType === 'XSSC'\" class=\"wrapper-xssc-rule\">\r\n          <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n          <view class=\"label-title\">\r\n            <view class=\"icon\"></view>试吃规则\r\n          </view>\r\n          <view class=\"content-rule\">\r\n            <ul>\r\n              <li>1. 试吃价包含宰杀费</li>\r\n              <li>2. 每个试吃产品每个用户只能购买一次</li>\r\n              <li>3. 试吃产品暂时以原价购买</li>\r\n              <li>4. 试吃后进行评论，自动返还超出试吃价的部分<br />（即 原价 - 试吃价）</li>\r\n              <li>5. 试吃后若不参与真实评论达3次，则取消之后的试吃资格</li>\r\n            </ul>\r\n          </view>\r\n        </view>\r\n\r\n        <view v-if=\"longPage === 1\">\r\n          <view class=\"tab1\">\r\n            <u-row customStyle=\"margin-bottom: 10px\">\r\n              <u-col span=\"4\" justify=\"center\" textAlign=\"center\">\r\n                <div class=\"tab line\" :class=\"{ active: longActiveTab === 'tab1' }\" @click=\"changeTab('tab1')\">\r\n                  <div style=\"height: 30rpx;\">&nbsp;</div>\r\n                  <div class=\"title\">接龙</div>\r\n                </div>\r\n              </u-col>\r\n              <u-col span=\"4\" justify=\"center\" textAlign=\"center\">\r\n                <div class=\"tab line\" :class=\"{ active: longActiveTab === 'tab2' }\" @click=\"changeTab('tab2')\">\r\n                  <div style=\"height: 30rpx;\">&nbsp;</div>\r\n                  <div class=\"title\">详情</div>\r\n                </div>\r\n              </u-col>\r\n            </u-row>\r\n          </view>\r\n\r\n          <view v-if=\"longActiveTab === 'tab2'\" id=\"content\">\r\n            <view class=\"content\">\r\n              <view v-if=\"wxintroduction\">\r\n                <u-image v-for=\"(item,index) in wxintroduction\" :src=\"item\" mode=\"widthFix\" width=\"750rpx\" height=\"auto\"></u-image>\r\n              </view>\r\n              <u-parse v-else :content=\"goodsDetail.content\"></u-parse>\r\n            </view>\r\n          </view>\r\n\r\n          <view v-if=\"longActiveTab === 'tab1'\" style=\"margin: 0 20rpx;\">\r\n            <u-list v-if=\"longList.length > 0\">\r\n              <u-list-item v-for=\"(good, index) in longList\">\r\n                <u-cell :title=\"'用户' + good.account.userBase.nick + ' 于' + good.datePay + '购买成功'\">\r\n                  <u-avatar slot=\"icon\" size=\"35\" :src=\"good.account.userBase.avatarUrl\" customStyle=\"margin: -3px 5px -3px 0\"></u-avatar>\r\n                </u-cell>\r\n              </u-list-item>\r\n            </u-list>\r\n\r\n            <view v-if=\"!longList.length > 0\" style=\"min-height: 600rpx;\">\r\n              <u-empty mode=\"data\" icon=\"http://cdn.uviewui.com/uview/empty/data.png\" text=\"暂无订单，您可以成为第一个接龙龙头哦\">\r\n              </u-empty>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <view v-if=\"longPage === 0\">\r\n          <view v-if=\"goodsType !== 'XSSC'\" class=\"wrapper-comment\">\r\n            <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n            <view class=\"label-title\">\r\n              <view class=\"icon\"></view>评论\r\n            </view>\r\n            <view class=\"content-comment\">\r\n              <view>暂无评论</view>\r\n            </view>\r\n          </view>\r\n\r\n          <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n          <view class=\"label-title\">\r\n            <view class=\"icon\"></view>详细介绍\r\n          </view>\r\n          <view id=\"content\">\r\n            <view class=\"content\">\r\n              <view v-if=\"wxintroduction\">\r\n                <u-image v-for=\"(item,index) in wxintroduction\" :src=\"item\" mode=\"widthFix\" width=\"750rpx\" height=\"auto\"></u-image>\r\n              </view>\r\n              <u-parse v-else :content=\"goodsDetail.content\"></u-parse>\r\n            </view>\r\n          </view>\r\n        </view>\r\n\r\n        <!-- <view id=\"reputation\">\r\n          <u-divider v-if=\"reputationList\" text=\"用户评价\"></u-divider>\r\n          <view v-if=\"reputationList\" class=\"reputation-box\">\r\n            <view v-for=\"(item,index) in reputationList\" :key=\"index\" class=\"album\">\r\n              <view class=\"album__avatar\">\r\n                <u-image class=\"image\" :src=\"item.user.avatarUrl\" shape=\"circle\" width=\"120rpx\" height=\"120rpx\"></u-image>\r\n              </view>\r\n              <view class=\"album__content\">\r\n                <u-text :text=\"item.user.nick\" type=\"primary\" bold size=\"17\"></u-text>\r\n                <u-rate v-model=\"item.goods.goodReputation\"></u-rate>\r\n                <u-text margin=\"8rpx 0 8rpx 0\" size=\"26rpx\" color=\"#666666\" :text=\"item.goods.goodReputationRemark\"></u-text>\r\n                <u-text margin=\"0 0 8rpx 0\" size=\"24rpx\" color=\"#666666\" :text=\"item.goods.dateReputation\"></u-text>\r\n                <view style=\"height: 32rpx;\"></view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view> -->\r\n      </scroll-view>\r\n      <view v-if=\"curGoodsKanjia\">\r\n        <view v-if=\"curKanjiaprogress && curKanjiaprogress.kanjiaInfo.uid == uid\" class=\"bottom-btns\">\r\n          <!--  #ifdef MP-WEIXIN\t|| MP-BAIDU -->\r\n          <view class=\"icon-btn\">\r\n            <u-icon name=\"chat\" size=\"48rpx\"></u-icon>\r\n            <text>客服</text>\r\n            <button open-type='contact' :send-message-title=\"goodsDetail.basicInfo.name\" :send-message-img=\"goodsDetail.basicInfo.pic\" :send-message-path=\"'/pages/goods/detail?id='+goodsDetail.basicInfo.id\" show-message-card></button>\r\n          </view>\r\n          <!--  #endif -->\r\n          <view class=\"icon-btn\" @click=\"addFav\">\r\n            <u-icon :name=\"faved ? 'heart-fill' : 'heart'\" size=\"48rpx\"></u-icon>\r\n            <text>收藏</text>\r\n          </view>\r\n          <view class=\"btn\">\r\n            <u-button class=\"half-l\" text=\"邀请好友助力\" shape=\"circle\" color=\"linear-gradient(90deg,#ffd01e, #ff8917)\" open-type='share'>\r\n            </u-button>\r\n          </view>\r\n          <view class=\"btn\">\r\n            <u-button class=\"half-r\" text=\"用现价购买\" shape=\"circle\" color=\"linear-gradient(90deg, #ff6034, #ee0a24)\" @click=\"kanjiabuy\">\r\n            </u-button>\r\n          </view>\r\n        </view>\r\n        <view v-else-if=\"curKanjiaprogress && curKanjiaprogress.kanjiaInfo.uid != uid\" class=\"bottom-btns\">\r\n          <view class=\"icon-btn\">\r\n            <u-icon name=\"share\" size=\"48rpx\"></u-icon>\r\n            <text>邀请</text>\r\n            <button open-type='share'></button>\r\n          </view>\r\n          <view class=\"btn\">\r\n            <u-button class=\"half-l\" :text=\"myHelpDetail ? '您已助力' : '帮忙砍一刀'\" shape=\"circle\" :disabled=\"myHelpDetail ? true : false\" color=\"linear-gradient(90deg,#ffd01e, #ff8917)\" @click=\"helpKanjia\">\r\n            </u-button>\r\n          </view>\r\n          <view class=\"btn\">\r\n            <u-button class=\"half-r\" text=\"我也要参与\" shape=\"circle\" color=\"linear-gradient(90deg, #ff6034, #ee0a24)\" @click=\"joinKanjia\">\r\n            </u-button>\r\n          </view>\r\n        </view>\r\n        <view v-else class=\"bottom-btns\">\r\n          <!--  #ifdef MP-WEIXIN\t|| MP-BAIDU -->\r\n          <view class=\"icon-btn\">\r\n            <u-icon name=\"chat\" size=\"48rpx\"></u-icon>\r\n            <text>客服</text>\r\n            <button open-type='contact' :send-message-title=\"goodsDetail.basicInfo.name\" :send-message-img=\"goodsDetail.basicInfo.pic\" :send-message-path=\"'/pages/goods/detail?id='+goodsDetail.basicInfo.id\" show-message-card></button>\r\n          </view>\r\n          <!--  #endif -->\r\n          <view class=\"icon-btn\" @click=\"addFav\">\r\n            <u-icon :name=\"faved ? 'heart-fill' : 'heart'\" size=\"48rpx\"></u-icon>\r\n            <text>收藏</text>\r\n          </view>\r\n          <view class=\"btn\">\r\n            <u-button class=\"half-l\" text=\"发起砍价\" shape=\"circle\" color=\"linear-gradient(90deg,#ffd01e, #ff8917)\" @click=\"joinKanjia\">\r\n            </u-button>\r\n          </view>\r\n          <view class=\"btn\">\r\n            <u-button class=\"half-r\" text=\"原价购买\" shape=\"circle\" color=\"linear-gradient(90deg, #ff6034, #ee0a24)\" @click=\"_showGoodsPop\">\r\n            </u-button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <view v-else class=\"bottom-btns\">\r\n        <!--  #ifdef MP-WEIXIN\t|| MP-BAIDU -->\r\n        <view v-if=\"!cardPage\" class=\"icon-btn\">\r\n          <u-icon name=\"chat\" size=\"48rpx\"></u-icon>\r\n          <text>客服</text>\r\n        </view>\r\n        <!--  #endif -->\r\n        <view v-if=\"!cardPage\" class=\"icon-btn\" @click=\"goCart\">\r\n          <u-icon name=\"shopping-cart\" size=\"48rpx\"></u-icon>\r\n          <text>购物车</text>\r\n          <u-badge type=\"error\" :value=\"cartNumber\" absolute :offset=\"[-10, -10]\"></u-badge>\r\n        </view>\r\n        <view class=\"icon-btn\" @click=\"addFav\">\r\n          <u-icon :name=\"faved ? 'heart-fill' : 'heart'\" size=\"48rpx\"></u-icon>\r\n          <text>收藏</text>\r\n        </view>\r\n        <view v-if=\"cardPage\" class=\"btn\" style=\"margin-right: 10rpx;\">\r\n          <view>\r\n            <u-button class=\"half-l\" text=\"返回礼品列表\" shape=\"circle\" @click=\"_back\"></u-button>\r\n          </view>\r\n        </view>\r\n        <view v-if=\"!cardPage\" class=\"btn\" style=\"margin-right: 10rpx;\">\r\n          <u-button v-if=\"goodsDetail.category.id !== 391088 && goodsDetail.category.id !== 463740\" class=\"half-l\" text=\"加入购物车\" shape=\"circle\" color=\"linear-gradient(90deg,#ffd01e, #ff8917)\" @click=\"_showGoodsPop\"></u-button>\r\n        </view>\r\n        <view v-if=\"!cardPage\" class=\"btn\">\r\n          <u-button v-if=\"goodsDetail.category.id !== 391088 && goodsDetail.category.id !== 463740\" class=\"half-r\" text=\"立即购买\" shape=\"circle\" color=\"linear-gradient(90deg, #ff6034, #ee0a24)\" @click=\"_showGoodsPop\"></u-button>\r\n          <u-button v-if=\"goodsDetail.category.id === 463740 && goodsDetail.category.id === 463740\" class=\"half-r\" text=\"立即申请\" shape=\"circle\" color=\"linear-gradient(90deg, #ff6034, #ee0a24)\" @click=\"_showGoodsPop\"></u-button>\r\n          <view v-if=\"goodsDetail.category.id === 391088\">\r\n            <u-button v-if=\"afterDeadline === false\" class=\"half-r\" text=\"立即预购\" shape=\"circle\" color=\"linear-gradient(90deg, #ff6034, #ee0a24)\" @click=\"_showGoodsPop\"></u-button>\r\n            <u-button v-if=\"afterDeadline === true\" class=\"half-r\" text=\"立即预购\" color=\"#777777\" shape=\"circle\" @click=\"_showGoodsAfterDeadline\"></u-button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <goods-pop :show=\"showGoodsPop\" :goodsDetail=\"goodsDetail\" :kjid=\"kjid\" @close=\"showGoodsPop = false\"></goods-pop>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import empty from 'empty-value'\r\n  import lPainter from '../../components/lime-painter/index.vue'\r\n  import moment from 'moment'\r\n  const TOOLS = require('@/common/tools')\r\n\r\n  export default {\r\n    components: {\r\n      lPainter\r\n    },\r\n    data() {\r\n      return {\r\n        tabs: [{\r\n            viewId: 'basic',\r\n            name: '商品信息',\r\n          },\r\n          {\r\n            viewId: 'content',\r\n            name: '详细介绍',\r\n          },\r\n          {\r\n            viewId: 'reputation',\r\n            name: '用户评价',\r\n          },\r\n        ],\r\n        curViewId: 'basic',\r\n        timeData: {},\r\n        goodsDetail: undefined,\r\n        mySales: 0,\r\n        goodsType: undefined,\r\n        jdGoodsDetail: undefined,\r\n        wxintroduction: undefined,\r\n        faved: false,\r\n        showGoodsPop: false,\r\n        page: 1,\r\n        reputationList: null,\r\n        videoMp4Src: undefined,\r\n        // 海报模板数据\r\n        posterData: {},\r\n        curGoodsKanjia: undefined,\r\n        curKanjiaprogress: undefined,\r\n        myHelpDetail: undefined,\r\n        kjid: undefined,\r\n\r\n        userInfo: undefined,\r\n\r\n        // 海报\r\n        posterObj: {}, //画板数据\r\n        afterDeadline: false,\r\n\r\n        deliveryTypeUrl: '',\r\n\r\n        cardPage: 0,\r\n\r\n        // 试吃\r\n        maxTotal: 0,\r\n        minTotal: 0,\r\n        shichiPrice: 0,\r\n\r\n        // 接龙\r\n        longActiveTab: 'tab1',\r\n        longPage: 0,\r\n        longButton: 0,\r\n        longImage: 'https://ye.niutouren.vip/static/images/long/long.png',\r\n        longList: []\r\n      }\r\n    },\r\n    computed: {\r\n      filteredTags() {\r\n        if (!empty(this.goodsDetail)) {\r\n          let goodsTags = this.goodsDetail.basicInfo.tags\r\n          if (goodsTags) {\r\n            const tags = goodsTags.split(/[, ]+/);\r\n            return tags.filter(tag => tag.trim() !== '');\r\n          }\r\n        }\r\n        return [];\r\n      }\r\n    },\r\n    onLoad(e) {\r\n      if (e && e.card) {\r\n        this.cardPage = 1\r\n      }\r\n      if (e && e.inviter_id) {\r\n        this.$u.vuex('referrer', e.inviter_id)\r\n      }\r\n      // 读取小程序码中的邀请人编号\r\n      if (e && e.scene) {\r\n        const scene = decodeURIComponent(e.scene) // 处理扫码进商品详情页面的逻辑\r\n        if (scene && scene.split(',').length >= 2) {\r\n          e.id = scene.split(',')[0]\r\n          this.$u.vuex('referrer', scene.split(',')[1])\r\n        }\r\n      }\r\n      this._goodsDetail(e.id, e.supplyType, e.yyId)\r\n      if (e && e.long_page === \"1\") {\r\n        this.longPage = 1\r\n        this._goodsLong(e.id)\r\n      }\r\n    },\r\n    onShow() {\r\n      this._userDetail()\r\n    },\r\n    onShareAppMessage(e) {\r\n      let d\r\n      d = {\r\n        title: this.goodsDetail.basicInfo.name,\r\n        path: '/pages/goods/detail?id=' + this.goodsDetail.basicInfo.id + '&inviter_id=' + this.uid\r\n      }\r\n\r\n      if (this.goodsType === 'XSSC') {\r\n        d = {\r\n          title: this.goodsDetail.basicInfo.name + ' 试吃啦!',\r\n          path: '/pages/goods/detail?id=' + this.goodsDetail.basicInfo.id + '&inviter_id=' + this.uid\r\n        }\r\n      }\r\n\r\n      if (this.longButton === 1) {\r\n        this.checkImage('https://ye.niutouren.vip/static/images/long/' + this.goodsDetail.basicInfo.id + '.png')\r\n\r\n        d = {\r\n          title: this.goodsDetail.basicInfo.name + ' 低价接龙啦!',\r\n          path: '/pages/goods/detail?id=' + this.goodsDetail.basicInfo.id + '&inviter_id=' + this.uid + '&long_page=1',\r\n          imageUrl: this.longImage\r\n        }\r\n      }\r\n\r\n      if (this.kjJoinUid) {\r\n        _data.title = this.curKanjiaprogress.joiner.nick + '邀请您帮TA砍价'\r\n        _data.path += '&kjJoinUid=' + this.kjJoinUid\r\n      }\r\n      return d\r\n    },\r\n    created() {\r\n\r\n    },\r\n    methods: {\r\n      async _back() {\r\n        uni.navigateBack()\r\n      },\r\n      async _userDetail() {\r\n        // https://www.yuque.com/apifm/nu0f75/zgf8pu\r\n        const res = await this.$wxapi.userDetail(this.token)\r\n        if (res.code == 0) {\r\n          this.userInfo = res.data\r\n        }\r\n      },\r\n      shouldHighlight(tag) {\r\n        return /[A-Za-z]\\d+/.test(tag);\r\n      },\r\n      async _goodsLong(goodsId) {\r\n        await uni.$u.http.post('https://ye.niutouren.vip/api/order', {\r\n          'type': 'long',\r\n          'good_id': goodsId,\r\n        }).then(res => {\r\n          if (!empty(res)) {\r\n            this.longList = res\r\n          }\r\n\r\n          //console.log('long is', this.longList)\r\n        }).catch(err => {\r\n          //\r\n        })\r\n      },\r\n      async _goodsDetail(goodsId, supplyType, yyId) {\r\n        if (goodsId) {\r\n          // https://www.yuque.com/apifm/nu0f75/vuml8a\r\n          const res = await this.$wxapi.goodsDetail(goodsId, this.token)\r\n          if (res.code != 0) {\r\n            uni.showToast({\r\n              title: res.msg,\r\n              icon: 'none'\r\n            })\r\n            setTimeout(() => {\r\n              uni.navigateBack()\r\n            }, 3000)\r\n            return\r\n          }\r\n          this.goodsDetail = res.data\r\n          this.mySales = this.goodsDetail.basicInfo.numberSells\r\n          if (!empty(res.data.extJson)) {\r\n            if (res.data.extJson['sales_base']) {\r\n              this.mySales = this.mySales + parseFloat(res.data.extJson['sales_base'])\r\n            }\r\n            if (res.data.extJson['deadline']) {\r\n              this.goodsDetail.deadline = moment(res.data.extJson['deadline'], 'YYYYMMDD').format(\r\n                'YYYY年MM月DD日 HH:mm')\r\n\r\n              let currentTimestamp = moment().unix()\r\n              let deadlineTimestamp = moment(res.data.extJson['deadline'], 'YYYY-MM-DD').unix()\r\n\r\n              if (currentTimestamp > deadlineTimestamp) {\r\n                this.afterDeadline = true\r\n              }\r\n            }\r\n            if (res.data.extJson['delivery_time']) {\r\n              this.goodsDetail.delivery_time = moment(res.data.extJson['delivery_time'], 'YYYYMMDD').format(\r\n                'YYYY年MM月DD日 HH:mm')\r\n            }\r\n\r\n            if (res.data.extJson['delivery_type']) {\r\n              if (res.data.extJson['delivery_type'] === '预购') {\r\n                this.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-yg.png'\r\n              }\r\n              if (res.data.extJson['delivery_type'] === '当日达') {\r\n                this.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-drd.png'\r\n              }\r\n              if (res.data.extJson['delivery_type'] === '次日达') {\r\n                this.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-crd.png'\r\n              }\r\n              if (res.data.extJson['delivery_type'] === '后日达') {\r\n                this.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-hrd.png'\r\n              }\r\n            }\r\n\r\n\r\n            if (res.data.extJson['max_total']) {\r\n              this.maxTotal = res.data.extJson['max_total']\r\n            }\r\n            if (res.data.extJson['min_total']) {\r\n              this.minTotal = res.data.extJson['min_total']\r\n            }\r\n            if (res.data.extJson['shichiPrice']) {\r\n              this.shichiPrice = res.data.extJson['shichiPrice']\r\n            }\r\n          }\r\n\r\n          if (!empty(res.data.category.key)) {\r\n            this.goodsType = res.data.category.key\r\n          }\r\n          if (res.data.basicInfo.videoId) {\r\n            this.getVideoSrc(res.data.basicInfo.videoId)\r\n          }\r\n          if (res.data.basicInfo.kanjia) {\r\n            this.kanjiaSet()\r\n          }\r\n        } else {\r\n          // 不是api工厂商品\r\n          this.goodsDetail = {\r\n            basicInfo: {\r\n              yyId: yyId,\r\n              yyIdStr: yyId,\r\n              supplyType: supplyType,\r\n              pic: '',\r\n              name: '',\r\n              stores: 999999\r\n            },\r\n            pics: []\r\n          }\r\n        }\r\n\r\n        // 检测是否收藏\r\n        this.goodsFavCheck()\r\n        this._reputationList()\r\n        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {\r\n          this.jdvopGoodsDetail(this.goodsDetail.basicInfo.yyId)\r\n        }\r\n        if (this.goodsDetail.basicInfo.supplyType == 'jdJoycityPoints') {\r\n          this.joycityPointsGoodsDetail(this.goodsDetail.basicInfo.yyIdStr)\r\n        }\r\n      },\r\n      async jdvopGoodsDetail(skuId) {\r\n        // https://www.yuque.com/apifm/nu0f75/ar77dc\r\n        const res = await this.$wxapi.jdvopGoodsDetail(skuId)\r\n        if (res.code != 0) {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n          setTimeout(() => {\r\n            uni.navigateBack()\r\n          }, 3000)\r\n          return\r\n        }\r\n        this.jdGoodsDetail = res.data\r\n        this.goodsDetail.basicInfo.minPrice = this.jdGoodsDetail.price.priceSale\r\n        this.goodsDetail.basicInfo.originalPrice = this.jdGoodsDetail.price.priceJd\r\n        this.goodsDetail.basicInfo.name = this.jdGoodsDetail.price.skuName\r\n        this.goodsDetail.basicInfo.pic = this.jdGoodsDetail.imageDomain + this.jdGoodsDetail.price.pic\r\n        if (this.jdGoodsDetail.info.wxintroduction) {\r\n          this.wxintroduction = JSON.parse(this.jdGoodsDetail.info.wxintroduction)\r\n        }\r\n        this.jdvopGoodsSkuImages(skuId)\r\n      },\r\n      async jdvopGoodsSkuImages(skuId) {\r\n        // https://www.yuque.com/apifm/nu0f75/pvcu30\r\n        const res = await this.$wxapi.jdvopGoodsSkuImages(skuId)\r\n        if (res.code == 0) {\r\n          const pics = res.data\r\n          pics.forEach(ele => {\r\n            ele.pic = this.jdGoodsDetail.imageDomain + ele.path\r\n          })\r\n          this.goodsDetail.pics = pics\r\n        }\r\n      },\r\n      async joycityPointsGoodsDetail(skuId) {\r\n        const res = await this.$wxapi.joycityPointsGoodsDetail(skuId)\r\n        if (res.code != 0) {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n          setTimeout(() => {\r\n            uni.navigateBack()\r\n          }, 3000)\r\n          return\r\n        }\r\n        const pics = []\r\n        res.data.providerImgUrls.split(',').forEach(ele => {\r\n          pics.push({\r\n            pic: ele\r\n          })\r\n        })\r\n        this.goodsDetail.pics = pics\r\n        this.goodsDetail.basicInfo.name = res.data.goodsName\r\n        this.goodsDetail.basicInfo.minPrice = 0\r\n        this.goodsDetail.basicInfo.minScore = res.data.goodsPrice\r\n        this.goodsDetail.basicInfo.originalPrice = res.data.suggestedPrice\r\n        if (res.data.pics && res.data.pics.length > 0) {\r\n          this.wxintroduction = res.data.pics\r\n        } else {\r\n          this.goodsDetail.content = res.data.usageGuide\r\n        }\r\n      },\r\n      goCart() {\r\n        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {\r\n          uni.setStorageSync('cart_tabIndex', 1)\r\n        }\r\n        uni.navigateTo({\r\n          url: \"/pages/cart/index\"\r\n        })\r\n      },\r\n      async goodsFavCheck() {\r\n        const data = {\r\n          token: this.token,\r\n          type: 0,\r\n          goodsId: this.goodsDetail.basicInfo.id\r\n        }\r\n        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {\r\n          data.type = 1\r\n          data.goodsId = this.goodsDetail.basicInfo.yyId\r\n        }\r\n        // https://www.yuque.com/apifm/nu0f75/ugf7y9\r\n        const res = await this.$wxapi.goodsFavCheckV2(data)\r\n        if (res.code == 0) {\r\n          this.faved = true\r\n        } else {\r\n          this.faved = false\r\n        }\r\n      },\r\n      async addFav() {\r\n        if (!await getApp().checkHasLoginedH5()) {\r\n          uni.navigateTo({\r\n            url: \"/pages/login/login\"\r\n          })\r\n          return\r\n        }\r\n        const data = {\r\n          token: this.token,\r\n          type: 0,\r\n          goodsId: this.goodsDetail.basicInfo.id\r\n        }\r\n        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {\r\n          data.type = 1\r\n          data.goodsId = this.goodsDetail.basicInfo.yyId\r\n        }\r\n        if (this.faved) {\r\n          // 取消收藏 https://www.yuque.com/apifm/nu0f75/zy4sil\r\n          const res = await this.$wxapi.goodsFavDeleteV2(data)\r\n          if (res.code == 0) {\r\n            this.faved = false\r\n          } else {\r\n            uni.showToast({\r\n              title: res.msg,\r\n              icon: 'none'\r\n            })\r\n          }\r\n        } else {\r\n          const extJsonStr = {\r\n            pic: this.goodsDetail.basicInfo.pic,\r\n            goodsName: this.goodsDetail.basicInfo.name,\r\n            supplyType: this.goodsDetail.basicInfo.supplyType\r\n          }\r\n          data.extJsonStr = JSON.stringify(extJsonStr)\r\n          // 加入收藏 https://www.yuque.com/apifm/nu0f75/mr1471\r\n          const res = await this.$wxapi.goodsFavAdd(data)\r\n          if (res.code == 0) {\r\n            this.faved = true\r\n          } else {\r\n            uni.showToast({\r\n              title: res.msg,\r\n              icon: 'none'\r\n            })\r\n          }\r\n        }\r\n      },\r\n      tabclick(e) {\r\n        this.curViewId = this.tabs[e.index].viewId\r\n      },\r\n      _showGoodsAfterDeadline() {\r\n        uni.showToast({\r\n          title: '已过截止时间',\r\n          icon: 'error'\r\n        })\r\n      },\r\n      // 弹出商品购买弹窗\r\n      async _showGoodsPop() {\r\n        this.showGoodsPop = true\r\n        this.kjid = null\r\n      },\r\n      kanjiabuy() {\r\n        // 砍价用现在的价格购买\r\n        this.goodsDetail.basicInfo.minPrice = this.curKanjiaprogress.kanjiaInfo.curPrice\r\n        this.kjid = this.curGoodsKanjia.id\r\n        this.showGoodsPop = true\r\n      },\r\n      scrolltolower() {\r\n        this.page += 1\r\n        this._reputationList()\r\n      },\r\n      async _reputationList() {\r\n        // https://www.yuque.com/apifm/nu0f75/cusiow\r\n        const res = await this.$wxapi.goodsReputationV2({\r\n          goodsId: this.goodsDetail.basicInfo.id,\r\n          page: this.page,\r\n          pageSize: 10\r\n        })\r\n        if (res.code == 0) {\r\n          res.data.result.forEach(ele => {\r\n            if (ele.user && !ele.user.avatarUrl) {\r\n              //ele.user.avatarUrl = '/static/images/empty.jpg'\r\n            }\r\n            if (ele.user && !ele.user.nick) {\r\n              ele.user.nick = '匿名用户'\r\n            }\r\n            if (ele.goods.goodReputation == 0) {\r\n              ele.goods.goodReputation = 1\r\n            } else if (ele.goods.goodReputation == 1) {\r\n              ele.goods.goodReputation = 3\r\n            } else if (ele.goods.goodReputation == 2) {\r\n              ele.goods.goodReputation = 5\r\n            }\r\n          })\r\n          if (this.page == 1) {\r\n            this.reputationList = res.data.result\r\n          } else {\r\n            this.reputationList = this.reputationList.concat(res.data.result)\r\n          }\r\n        }\r\n      },\r\n      async getVideoSrc(videoId) {\r\n        const res = await this.$wxapi.videoDetail(videoId)\r\n        if (res.code == 0) {\r\n          this.videoMp4Src = res.data.fdMp4\r\n        }\r\n      },\r\n      async drawSharePic() {\r\n        const _pic = this.goodsDetail.basicInfo.pic;\r\n        const _minPriceOriginal = this.goodsDetail.basicInfo.minPriceOriginal;\r\n        const _tags = this.goodsDetail.basicInfo.tags;\r\n        const _id = this.goodsDetail.basicInfo.id;\r\n        const _name = this.goodsDetail.basicInfo.name;\r\n        const _unit = this.goodsDetail.basicInfo.unit;\r\n\r\n        uni.navigateTo({\r\n          url: '/packageFx/partner/poster?pic=' + encodeURIComponent(_pic) + '&minPriceOriginal=' + encodeURIComponent(_minPriceOriginal) + '&tags=' + encodeURIComponent(_tags) + '&id=' + encodeURIComponent(_id) + '&unit=' + encodeURIComponent(_unit) + '&name=' + encodeURIComponent(_name)\r\n        });\r\n      },\r\n      async kanjiaSet() {\r\n        const res = await this.$wxapi.kanjiaSet(this.goodsDetail.basicInfo.id)\r\n        if (res.code == 0) {\r\n          this.curGoodsKanjia = res.data[0]\r\n          this.curGoodsKanjia.dateEnd = this.curGoodsKanjia.dateEnd.replace(/00:00:00/g, '')\r\n          this.curGoodsKanjia.dateEnd = this.curGoodsKanjia.dateEnd.replace(/ /g, '')\r\n          this.kanjiaprogress()\r\n        }\r\n      },\r\n      async kanjiaprogress() {\r\n        let kjJoinUid = this.kjJoinUid\r\n        if (!kjJoinUid) {\r\n          kjJoinUid = this.uid\r\n        }\r\n        const res = await this.$wxapi.kanjiaDetail(this.curGoodsKanjia.id, kjJoinUid)\r\n        if (res.code == 0) {\r\n          this.curKanjiaprogress = res.data\r\n          this.kanjiaHelpDetail()\r\n        }\r\n      },\r\n      async joinKanjia() {\r\n        // 报名参加砍价活动\r\n        if (!this.curGoodsKanjia) {\r\n          return\r\n        }\r\n        uni.showLoading({\r\n          title: '加载中'\r\n        })\r\n        const res = await this.$wxapi.kanjiaJoin(this.token, this.curGoodsKanjia.id)\r\n        uni.hideLoading()\r\n        if (res.code == 2000) {\r\n          getApp().autoLogin(true)\r\n          return\r\n        }\r\n        if (res.code == 0) {\r\n          this.$u.vuex('kjJoinUid', this.uid)\r\n          this.myHelpDetail = null\r\n          this.kanjiaSet()\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n        }\r\n      },\r\n      async helpKanjia() {\r\n        console.log(this.curGoodsKanjia);\r\n        console.log(this.token);\r\n        const res = await this.$wxapi.kanjiaHelp(this.token, this.curGoodsKanjia.id, this.curKanjiaprogress\r\n          .kanjiaInfo.uid, '')\r\n        if (res.code != 0) {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n          return;\r\n        }\r\n        this.myHelpDetail = res.data\r\n        uni.showModal({\r\n          title: '成功',\r\n          content: '成功帮TA砍掉 ' + res.data.cutPrice + ' 元',\r\n          showCancel: false,\r\n          confirmText: '知道了'\r\n        })\r\n        this.kanjiaSet()\r\n      },\r\n      async kanjiaHelpDetail() {\r\n        const res = await this.$wxapi.kanjiaHelpDetail(this.token, this.curGoodsKanjia.id, this\r\n          .curKanjiaprogress.kanjiaInfo.uid)\r\n        if (res.code == 0) {\r\n          this.myHelpDetail = res.data\r\n        }\r\n      },\r\n      async _pingtuanSet() {\r\n        const res = await this.$wxapi.pingtuanSet(this.goodsDetail.basicInfo.id)\r\n        if (res.code == 0) {\r\n          this.pingtuanSet = res.data\r\n          // 如果是拼团商品， 默认显示拼团价格\r\n          this.goodsDetail.basicInfo.minPrice = this.goodsDetail.basicInfo.pingtuanPrice\r\n        }\r\n      },\r\n      onChangeTimeData(e) {\r\n        this.timeData = e\r\n      },\r\n      longButtonSet(e) {\r\n        this.longButton = e\r\n      },\r\n      changeTab(tab) {\r\n        this.longActiveTab = tab\r\n      },\r\n      checkImage(imageUrl) {\r\n        wx.getImageInfo({\r\n          src: imageUrl,\r\n          success: res => {\r\n            this.longImage = imageUrl\r\n          },\r\n          fail: err => {\r\n            console.log(imageUrl + ' does not exist')\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n</script>\r\n<style scoped lang=\"scss\">\r\n  .goods-detail {\r\n    width: 100vw;\r\n    height: calc(100vh);\r\n    overflow: hidden;\r\n    display: flex;\r\n    flex-direction: column;\r\n\r\n    .main {\r\n      flex: 1;\r\n      overflow: hidden;\r\n    }\r\n  }\r\n\r\n  .price-share {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    padding: 8rpx 32rpx;\r\n    align-items: center;\r\n\r\n    .price {\r\n      color: #e64340;\r\n      font-size: 64rpx;\r\n      margin-top: 12rpx;\r\n      padding-right: 32rpx;\r\n\r\n      text {\r\n        margin-left: 16rpx;\r\n        color: #666666;\r\n        font-size: 26rpx;\r\n        text-decoration: line-through;\r\n      }\r\n    }\r\n  }\r\n\r\n  .title {\r\n    padding: 0rpx 32rpx;\r\n    color: #293539;\r\n    position: relative;\r\n  }\r\n\r\n  .title-sub {\r\n    padding: 16rpx 32rpx;\r\n    color: #666666;\r\n    font-size: 26rpx;\r\n  }\r\n\r\n  .commission {\r\n    padding: 16rpx 32rpx;\r\n    color: #e64340;\r\n    font-size: 24rpx;\r\n  }\r\n\r\n  .amount {\r\n    text-align: center;\r\n    font-size: 68rpx;\r\n    color: #a78845;\r\n    margin: 64rpx 0;\r\n\r\n    text {\r\n      font-size: 48rpx;\r\n      padding-right: 3px;\r\n    }\r\n  }\r\n\r\n  .content {\r\n    margin-top: 32rpx;\r\n    padding-bottom: 200rpx;\r\n\r\n    image {\r\n      height: auto;\r\n    }\r\n\r\n    div {\r\n      height: auto;\r\n    }\r\n  }\r\n\r\n  .bottom-btns {\r\n    display: flex;\r\n    background: #FFF;\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 0;\r\n    right: 0;\r\n    padding: 16rpx 32rpx 16rpx 32rpx;\r\n    border-top: 1px solid #DCDBDD;\r\n\r\n    .icon-btn {\r\n      position: relative;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      font-size: 24rpx;\r\n      color: #333;\r\n      margin-right: 32rpx;\r\n\r\n      button {\r\n        position: absolute;\r\n        height: 100%;\r\n        width: 100%;\r\n        opacity: 0;\r\n        z-index: 99;\r\n      }\r\n    }\r\n\r\n    .btn {\r\n      flex: 1;\r\n\r\n      .half-l {\r\n        border-top-right-radius: 0;\r\n        border-bottom-right-radius: 0;\r\n      }\r\n\r\n      .half-r {\r\n        border-top-left-radius: 0;\r\n        border-bottom-left-radius: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  .reputation-box {\r\n    padding: 32rpx;\r\n  }\r\n\r\n  .album {\r\n    @include flex;\r\n    align-items: flex-start;\r\n\r\n    &__avatar {\r\n      padding: 5px;\r\n      border-radius: 3px;\r\n    }\r\n\r\n    &__content {\r\n      margin-left: 10px;\r\n      flex: 1;\r\n    }\r\n  }\r\n\r\n  .haibaopop {\r\n    width: 100vw;\r\n    height: 100vh;\r\n  }\r\n\r\n  .goods-images {\r\n    position: relative;\r\n  }\r\n\r\n  .delivery-type {\r\n    position: absolute;\r\n    left: 0;\r\n    bottom: -20rpx;\r\n  }\r\n\r\n  .goods-title {\r\n    padding: 20rpx 32rpx;\r\n    position: relative;\r\n    overflow: inherit;\r\n\r\n    .box-left {\r\n      max-width: 70%;\r\n      min-height: 100rpx;\r\n\r\n      text {\r\n        font-weight: bold;\r\n        font-size: 42rpx;\r\n      }\r\n    }\r\n\r\n    .btns {\r\n      position: absolute;\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      top: -80rpx;\r\n      right: 60rpx;\r\n      width: 240rpx;\r\n      justify-content: flex-end;\r\n\r\n      text {\r\n        font-size: 24rpx;\r\n        font-weight: normal;\r\n      }\r\n\r\n      .icon-btn {\r\n        position: relative;\r\n        display: flex;\r\n        width: 60rpx;\r\n        flex-direction: column;\r\n        align-items: center;\r\n        font-size: 24rpx;\r\n        color: #333;\r\n        margin-left: 48rpx;\r\n        margin-bottom: 20rpx;\r\n\r\n        .share {\r\n          position: absolute;\r\n          width: 100%;\r\n          min-height: 80rpx;\r\n          opacity: 0;\r\n        }\r\n      }\r\n\r\n      .btn {\r\n        flex: 1;\r\n\r\n        .half-l {\r\n          border-top-right-radius: 0;\r\n          border-bottom-right-radius: 0;\r\n        }\r\n\r\n        .half-r {\r\n          border-top-left-radius: 0;\r\n          border-bottom-left-radius: 0;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  .curKanjiaJoin {\r\n    padding: 32rpx;\r\n    font-size: 28rpx;\r\n\r\n    text {\r\n      color: #e64340;\r\n      font-weight: bold;\r\n      padding: 0 8rpx;\r\n      font-size: 32rpx;\r\n    }\r\n  }\r\n\r\n  .curKanjiaprogress {\r\n    padding: 32rpx;\r\n  }\r\n\r\n  .price-score2 {\r\n    .item {\r\n      font-size: 64rpx;\r\n    }\r\n  }\r\n\r\n  .curKanjiaprogress-bar {\r\n    text-align: center;\r\n    font-size: 20rpx;\r\n    color: #999;\r\n    margin-top: 20rpx;\r\n  }\r\n\r\n  .kjlj {\r\n    display: flex;\r\n    background-color: #ffffff;\r\n    padding: 0 32rpx;\r\n    align-items: center;\r\n\r\n    .kjlj-l {\r\n      width: 88rpx;\r\n      height: 88rpx;\r\n      border-radius: 50%;\r\n      flex-shrink: 0;\r\n    }\r\n\r\n    .kjlj-r {\r\n      flex: 1;\r\n    }\r\n  }\r\n\r\n  .price-info {\r\n    font-weight: bolder;\r\n  }\r\n\r\n  .price-info .price-left {\r\n    display: inline-block;\r\n    font-weight: normal;\r\n    text-align: left;\r\n  }\r\n\r\n  .price-info .price-left .line1 {\r\n    display: flex;\r\n    flex-wrap: nowrap;\r\n  }\r\n\r\n  .price-info .price-left .sales {\r\n    font-size: 22rpx;\r\n    color: #324A43;\r\n    margin-left: 10rpx;\r\n  }\r\n\r\n  .price-info .price-right {\r\n    font-size: 70rpx;\r\n    display: inline-block;\r\n    letter-spacing: -2px;\r\n    margin-right: 20rpx;\r\n  }\r\n\r\n  .price-info .prefix,\r\n  .price-info .suffix {\r\n    font-size: 32rpx;\r\n  }\r\n\r\n  .price-info .original-price {\r\n    font-size: 20rpx;\r\n    text-decoration: line-through;\r\n    line-height: 32rpx;\r\n    color: #858996;\r\n    letter-spacing: 0;\r\n    font-weight: normal;\r\n  }\r\n\r\n  .max-total,\r\n  .min-total {\r\n    font-size: 22rpx;\r\n    line-height: 24rpx;\r\n    color: #858996;\r\n    margin: 10rpx 0;\r\n  }\r\n\r\n  .only {\r\n    font-size: 22rpx;\r\n    line-height: 24rpx;\r\n    color: #858996;\r\n  }\r\n\r\n  .only span {\r\n    background-color: #324A43;\r\n    border-radius: 4px;\r\n    display: inline-block;\r\n    text-align: center;\r\n    width: 16px;\r\n    height: 16px;\r\n    line-height: 16px;\r\n    margin: 0 8rpx;\r\n    color: #FFFFFF;\r\n  }\r\n\r\n  .delivery-time {\r\n    font-size: 22rpx;\r\n    color: #324A43;\r\n  }\r\n\r\n  .price-score text.time__custom__item {\r\n    font-size: 18rpx;\r\n  }\r\n\r\n\r\n  .poster {\r\n    padding: 24rpx 40rpx;\r\n\r\n    .footer-btn {\r\n      margin-top: 24rpx;\r\n      display: flex;\r\n      align-items: center;\r\n      justify-content: space-between;\r\n\r\n      view {\r\n        width: 319rpx;\r\n        height: 66rpx;\r\n        border-radius: 40rpx;\r\n        border: 1px solid #e6c70a;\r\n        font-size: 26rpx;\r\n        font-family: PingFangSC-Regular, PingFang SC;\r\n        font-weight: 400;\r\n        color: #e6c70a;\r\n        line-height: 66rpx;\r\n        text-align: center;\r\n      }\r\n\r\n      .save {\r\n        background: #e6c70a;\r\n        color: #FFFFFF;\r\n      }\r\n    }\r\n  }\r\n\r\n  .poster-btn {\r\n    position: fixed;\r\n    bottom: 0;\r\n    width: 100%;\r\n    height: 140rpx;\r\n    background: #FFFFFF;\r\n    border-radius: 32rpx 32rpx 0px 0px;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-around;\r\n\r\n    >view {\r\n      width: 80rpx;\r\n      height: 80rpx;\r\n      position: relative;\r\n      border-radius: 10rpx;\r\n      border: 4rpx solid #fff;\r\n\r\n      &.is-check {\r\n        border: 4rpx solid #e6c70a;\r\n      }\r\n\r\n      image {\r\n        width: 100%;\r\n        height: 100%;\r\n      }\r\n\r\n      view {\r\n        width: 30rpx;\r\n        height: 30rpx;\r\n        position: absolute;\r\n        right: -15rpx;\r\n        bottom: -15rpx;\r\n        background: url('https://s.yun-live.com/images/20210201/311c01265c1aa508418f6bae10d67602.png') no-repeat;\r\n        background-size: 100% 100%;\r\n      }\r\n    }\r\n  }\r\n\r\n  .wrapper-xssc-rule .content-rule {\r\n    margin-bottom: 60rpx;\r\n  }\r\n\r\n  .wrapper-xssc-rule ul {\r\n    font-size: 24rpx;\r\n    color: #324A43;\r\n    margin: 0 30rpx;\r\n  }\r\n\r\n  .content-comment {\r\n    margin: 0 30rpx 30rpx 30rpx;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .tab1 {\r\n    margin: 0;\r\n    border-top-left-radius: 20rpx;\r\n    border-top-right-radius: 20rpx;\r\n    border-bottom-left-radius: 20rpx;\r\n    border-bottom-right-radius: 20rpx;\r\n    background-image: linear-gradient(to bottom, #f4f4f4, #ffffff);\r\n  }\r\n\r\n  .tab1 .tab.line {\r\n    position: relative;\r\n    width: 100%;\r\n    padding-bottom: 2rpx;\r\n  }\r\n\r\n  .tab1 .tab.line::after {\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 20%;\r\n    right: 0;\r\n    bottom: 10%;\r\n    width: 1px;\r\n    background-color: #f1f1f1;\r\n  }\r\n\r\n  .tab1 .tab .title {\r\n    font-size: 40rpx;\r\n    font-weight: bolder;\r\n    margin-bottom: 20rpx;\r\n    color: #858996;\r\n  }\r\n\r\n  .tab1 .tab.active .title {\r\n    color: #293539;\r\n  }\r\n\r\n  .tab1 .tab .dsc {\r\n    font-size: 30rpx;\r\n    height: 60rpx;\r\n  }\r\n\r\n  .tab1 .tab.active {\r\n    background-size: contain;\r\n    background-repeat: no-repeat;\r\n    background-position: center;\r\n  }\r\n\r\n  .tab1 .tab.active .dsc {\r\n    background-size: contain;\r\n    background-repeat: no-repeat;\r\n    background-position: center;\r\n    font-size: 32rpx;\r\n    line-height: 50rpx;\r\n    color: #FFFFFF;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&id=f0a9f5ba&scoped=true&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&id=f0a9f5ba&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692294006\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}