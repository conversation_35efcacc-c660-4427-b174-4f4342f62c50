@charset "UTF-8";
/**
 * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量
 * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可
 * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 
 */
/* 变量 */
view.data-v-542a922e, scroll-view.data-v-542a922e, swiper-item.data-v-542a922e {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-tabbar.data-v-542a922e {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: center;
}
.u-tabbar__content.data-v-542a922e {
  display: flex;
  flex-direction: column;
  background-color: #fff;
}
.u-tabbar__content__item-wrapper.data-v-542a922e {
  height: 50px;
  display: flex;
  flex-direction: row;
}
.u-tabbar--fixed.data-v-542a922e {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
}

