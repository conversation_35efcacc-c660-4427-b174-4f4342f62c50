{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u--text/u--text.vue?f70d", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u--text/u--text.vue?f467", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u--text/u--text.vue?0cda", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u--text/u--text.vue?c98f", "uni-app:///uni_modules/uview-ui/components/u--text/u--text.vue"], "names": ["name", "mixins", "components", "uvText"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;;;AAGtD;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkqB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACmCtrB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAA;EACAC;EACAC;IACAC;EACA;AACA;AAAA,2B", "file": "uni_modules/uview-ui/components/u--text/u--text.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u--text.vue?vue&type=template&id=ab0c7f22&\"\nvar renderjs\nimport script from \"./u--text.vue?vue&type=script&lang=js&\"\nexport * from \"./u--text.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u--text/u--text.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u--text.vue?vue&type=template&id=ab0c7f22&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u--text.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u--text.vue?vue&type=script&lang=js&\"", "<template>\r\n    <uvText\r\n        :type=\"type\"\r\n        :show=\"show\"\r\n        :text=\"text\"\r\n        :prefixIcon=\"prefixIcon\"\r\n        :suffixIcon=\"suffixIcon\"\r\n        :mode=\"mode\"\r\n        :href=\"href\"\r\n        :format=\"format\"\r\n        :call=\"call\"\r\n        :openType=\"openType\"\r\n        :bold=\"bold\"\r\n        :block=\"block\"\r\n        :lines=\"lines\"\r\n        :color=\"color\"\r\n\t\t:decoration=\"decoration\"\r\n        :size=\"size\"\r\n        :iconStyle=\"iconStyle\"\r\n        :margin=\"margin\"\r\n        :lineHeight=\"lineHeight\"\r\n        :align=\"align\"\r\n        :wordWrap=\"wordWrap\"\r\n        :customStyle=\"customStyle\"\r\n        @click=\"$emit('click')\"\r\n    ></uvText>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * 此组件存在的理由是，在nvue下，u-text被uni-app官方占用了，u-text在nvue中相当于input组件\r\n * 所以在nvue下，取名为u--input，内部其实还是u-text.vue，只不过做一层中转\r\n * 不使用v-bind=\"$attrs\"，而是分开独立写传参，是因为微信小程序不支持此写法\r\n */\r\nimport uvText from \"../u-text/u-text.vue\";\r\nimport props from \"../u-text/props.js\";\r\nexport default {\r\n    name: \"u--text\",\r\n    mixins: [uni.$u.mpMixin, props, uni.$u.mixin],\r\n    components: {\r\n        uvText,\r\n    },\r\n};\r\n</script>\r\n"], "sourceRoot": ""}