<template>
  <view class="container-wrapper">
    <view class="main-wrapper">
      <view class="top-image-wrapper">
        <div class="top-image">&nbsp;</div>
      </view>
      <view class="main">
        <view v-if="type === 'virtual'">
          <view class="title">选择卡面</view>
          <view class="card">
            <view v-for="(image, index) in images" :key="index" @tap="cardImgSelect(index)">
              <img :src="image.src" :class="{ active: selectedIndex === index }">
            </view>
          </view>
        </view>
        <view class="title">选择礼品卡</view>
        <view class="food-wrapper" ref="foodsWrapper">
          <view v-if="Object.keys(list).length != 0" class="food" v-for="(food, index) in list.foods" :key="index" :style="{ 'border': food.count > 0 ? '1px #21A921 solid' : '1px #e4e4e4 solid' }">
            <image @click="goto(food)" v-if="type === 'physical'" class="food-img physical" :src="food.img" mode=""></image>
            <image @click="goto(food)" v-if="type === 'virtual'" class="food-img virtual" :src="food.img" mode=""></image>
            <image @click="goto(food)" v-if="type === 'water'" class="food-img water" :src="food.img" mode=""></image>
            <view class="food-info">
              <text class="food-title" @click="goto(food)">{{food.name}}</text>
              <text class="food-dsc" @click="goto(food)">{{food.description}}</text>

              <view v-if="food.price" class="price-score">
                <view class="item">
                  <view><text>¥</text>{{food.price}}<span v-if="food.originalPrice" class="original-pice">原价￥{{ food.originalPrice }}</span></view>
                </view>
              </view>

              <view class="food-btn">
                <cartcontrol :food="food" @add="addCart" @dec="decreaseCart" @input="inputCart"></cartcontrol>
              </view>
            </view>
          </view>
        </view>
        <shopcart :goods="goods" @add="addCart" @dec="decreaseCart" @input="inputCart" @delAll="delAll"></shopcart>
      </view>
    </view>
  </view>
</template>

<script>
  import shopcart from '@/components/shopcart.vue';
  import cartcontrol from '@/components/cartcontrol.vue'
  import Vue from 'vue'

  export default {
    data() {
      return {
        type: '',
        list: [],
        food: {},
        goods: [],
        selectedIndex: -1,
        images: [{
            src: 'https://ye.niutouren.vip/static/images/coupons/ln1.jpg'
          },
          {
            src: 'https://ye.niutouren.vip/static/images/coupons/ln2.jpg'
          },
          {
            src: 'https://ye.niutouren.vip/static/images/coupons/ln3.jpg'
          },
        ],
      }
    },
    components: {
      shopcart,
      cartcontrol
    },
    onLoad(query) {
      const {
        pic,
        tag,
        title,
        type
      } = query
      this.type = type

      if (type === 'virtual') {
        this.goods = [{
          'name': 'newyear',
          'foods': [{
              'name': '100元',
              'price': 100,
              'originalPrice': '',
              'description': '礼品卡100元',
              'sellCount': 100,
              'img': 'https://ye.niutouren.vip/static/images/coupons/100.png'
            }, {
              'name': '200元',
              'price': 200,
              'originalPrice': '',
              'description': '礼品卡200元',
              'sellCount': 100,
              'img': 'https://ye.niutouren.vip/static/images/coupons/200.png'
            },
            {
              'name': '500元',
              'price': 500,
              'originalPrice': '',
              'description': '礼品卡500元',
              'sellCount': 100,
              'img': 'https://ye.niutouren.vip/static/images/coupons/500.png'
            },
            {
              'name': '1000元',
              'price': 1000,
              'originalPrice': '',
              'description': '礼品卡1000元',
              'sellCount': 100,
              'img': 'https://ye.niutouren.vip/static/images/coupons/1000.png'
            },
            {
              'name': '2000元',
              'price': 2000,
              'originalPrice': '',
              'description': '礼品卡2000元',
              'sellCount': 100,
              'img': 'https://ye.niutouren.vip/static/images/coupons/2000.png'
            }
          ]
        }]

        this.select(0, 'newyear')
      }

      if (type === 'physical') {
        this.cardDataGet(type)
      }

      if (type === 'water') {
        this.cardDataGet(type)
      }
    },
    methods: {
      async cardDataGet(type) {
        let that = this

        await uni.$u.http.post('https://ye.niutouren.vip/api/card', {
          type: type,
        }).then(res => {
          that.goods = [{
            name: 'newyear',
            foods: res.goods.map(item => ({
              name: item.name,
              price: item.minPrice,
              originalPrice: item.originalPrice,
              description: '',
              sellCount: item.numberSells,
              img: item.pic,
              id: item.id
            }))
          }]

          this.select(0, 'newyear')
        }).catch(err => {
          //
        })
      },
      goto(item) {
        if (item.id) {
          uni.navigateTo({
            url: '/pages/goods/detail?card=1&id=' + item.id
          })
        }
      },
      cardImgSelect(index) {
        this.selectedIndex = index
      },
      select(index, name) {
        this.food = {}
        this.goods.forEach(item => {
          if (item.name === name) {
            this.list = item
          }
        })
        this.currentIndex = index;
      },
      addCart: function(item) {
        if (item.count >= 0) {
          item.count++
          this.goods.forEach((good) => {
            good.foods.forEach((food) => {
              if (item.name == food.name)
                food.count = item.count
            })
          })
        } else {
          this.goods.forEach((good) => {
            good.foods.forEach((food) => {
              if (item.name == food.name)
                Vue.set(food, 'count', 1)
            })
          })
        }
      },
      decreaseCart(item) {
        if (item.count) {
          item.count--
          this.goods.forEach((good) => {
            good.foods.forEach((food) => {
              if (item.name == food.name)
                food.count = item.count
            })
          })
        }
      },
      inputCart: function(item) {
        if (item.count >= 0) {
          item.count++
          this.goods.forEach((good) => {
            good.foods.forEach((food) => {
              if (item.name == food.name)
                food.count = item.count + -1
            })
          })
        } else {
          this.goods.forEach((good) => {
            good.foods.forEach((food) => {
              if (item.name == food.name)
                Vue.set(food, 'count', 1)
            })
          })
        }
      },
      delAll() {
        this.goods.forEach((good) => {
          good.foods.forEach((food) => {
            if (food.count) {
              food.count = 0
            }
          })
        })
      }
    }
  }
</script>

<style>
  .container-wrapper {
    padding: 0;
    margin: 0;
    background: #ffffff;
    height: 100vh;
  }

  .main-wrapper {
    position: relative;
  }

  .top-image {
    width: 100%;
    height: 220px;
    background-image: url('https://ye.niutouren.vip/static/images/coupons/bg.png');
    background-size: cover;
    background-position: bottom center;
  }

  .main {
    background: #FFFFFF;
    margin: 0;
    margin-top: -120rpx;
    padding: 30rpx;
    min-height: 500rpx;
    padding-bottom: 200rpx;
  }

  .main .title {
    color: #2b2b2b;
    font-size: 30rpx;
    margin: 30rpx 0 20rpx 0;
  }

  .main .card {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .main .card img {
    width: 200rpx;
    height: 257rpx;
    border-radius: 10rpx;
  }

  .main .card img.active {
    border: 6rpx solid #5EA537;
  }

  .menu-wrapper {
    text-align: center;
    width: 22%;
    display: flex;
    flex-direction: column;
    background: #f3f5f7;
  }

  .menu-item {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;
    height: 50px;
  }

  .food-wrapper {
    display: flex;
    flex-wrap: wrap;
  }

  .food {
    width: 48%;
    border-radius: 20rpx;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .food:nth-child(odd) {
    margin-right: 2%;
  }

  .food-img {
    width: 113px;
    height: 75px;
    margin-top: 6px;
  }

  .food-img.physical,
  .food-img.water {
    width: 130px;
    height: 130px;
    border-radius: 10px;
    margin-top: 6px;
  }

  .food-info {
    margin-left: 10px;
    margin-right: 16px;
    color: #858996;
    font-size: 24rpx;
    display: flex;
    flex-direction: column;
    flex: 2;
  }

  .food-title {
    padding: 2px 0;
    font-size: 30rpx;
    margin: 10rpx 0 0 0;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .food-dsc {
    color: #858996;
    font-size: 24rpx;
  }

  .food-btn {
    display: flex;
    flex-direction: row;
    margin: 20rpx 0 20rpx 60rpx;
  }

  .food-price {
    color: #f01414;
    font-size: 16px;
  }

  .original-pice {
    font-size: 20rpx;
    font-weight: normal;
    margin-top: 10rpx;
    margin-left: 6rpx;
    text-decoration: line-through;
    color: #858996;
  }
</style>