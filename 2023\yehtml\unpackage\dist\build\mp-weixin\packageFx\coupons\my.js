(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["packageFx/coupons/my"],{"1c5e":function(e,t,n){"use strict";(function(e,t){var r=n("47a9");n("96bd");r(n("3240"));var a=r(n("43b3"));e.__webpack_require_UNI_MP_PLUGIN__=n,t(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])},"24f8":function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"c",(function(){return o})),n.d(t,"a",(function(){return r}));var r={uSticky:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-sticky/u-sticky")]).then(n.bind(null,"2a0c"))},uSubsection:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-subsection/u-subsection")]).then(n.bind(null,"1992"))},pageBoxEmpty:function(){return n.e("components/page-box-empty/page-box-empty").then(n.bind(null,"bc43"))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-button/u-button")]).then(n.bind(null,"9edc"))},uModal:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-modal/u-modal")]).then(n.bind(null,"6909"))},uTextarea:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-textarea/u-textarea")]).then(n.bind(null,"19a3"))},uOverlay:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-overlay/u-overlay")]).then(n.bind(null,"3cf8"))}},a=function(){var e=this.$createElement,t=(this._self._c,!this.coupons||0==this.coupons.length);this.$mp.data=Object.assign({},{$root:{g0:t}})},o=[]},"34f9":function(e,t,n){"use strict";var r=n("adf1"),a=n.n(r);a.a},"43b3":function(e,t,n){"use strict";n.r(t);var r=n("24f8"),a=n("c277");for(var o in a)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(o);n("34f9");var u=n("828b"),s=Object(u["a"])(a["default"],r["b"],r["c"],!1,null,"158f176e",null,!1,r["a"],void 0);t["default"]=s.exports},"9d68":function(e,t,n){"use strict";(function(e){var r=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=r(n("7eb4")),o=r(n("ee10")),u=r(n("34cf")),s=r(n("bc37"));function i(e,t){var n="undefined"!==typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(!n){if(Array.isArray(e)||(n=function(e,t){if(!e)return;if("string"===typeof e)return c(e,t);var n=Object.prototype.toString.call(e).slice(8,-1);"Object"===n&&e.constructor&&(n=e.constructor.name);if("Map"===n||"Set"===n)return Array.from(e);if("Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return c(e,t)}(e))||t&&e&&"number"===typeof e.length){n&&(e=n);var r=0,a=function(){};return{s:a,n:function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}},e:function(e){throw e},f:a}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var o,u=!0,s=!1;return{s:function(){n=n.call(e)},n:function(){var e=n.next();return u=e.done,e},e:function(e){s=!0,o=e},f:function(){try{u||null==n.return||n.return()}finally{if(s)throw o}}}}function c(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}var d=n("0cdf"),l={components:{listCardPhysicalTip:function(){n.e("components/list/list-card-physical-tip").then(function(){return resolve(n("fd79"))}.bind(null,n)).catch(n.oe)}},data:function(){return{cardPhysicalPop:!1,cardPhysical:{},goods:[],shareSendRemarksPop:!1,remarks:"",remarksPlaceholder:"示例: 刘先生, 小李给您送一只土鸡试试味道",remarksButton:"确认赠送",sharePath:"",shareImageUrl:"",shareRemarks:"",sharePositionIndexPop:!1,sharePositionOwnPop:!1,shareCurrentID:void 0,shareCurrentPic:void 0,shareCurrentName:void 0,shareCardID:void 0,shareCardKey:void 0,shareCardItem:void 0,shareCardGoodID:void 0,tabs:[{name:"可用",status:"0"},{name:"已赠",status:"1, 2, 3"}],current:0,coupons:[],curItem:void 0,couponPwd:void 0}},onLoad:function(e){var t=decodeURIComponent(e.q),n=null;if(t.includes("ye.niutouren.vip/qr")){var r=t.indexOf("?");if(-1!==r){var a,o=t.slice(r+1),c=o.split("&"),d=i(c);try{for(d.s();!(a=d.n()).done;){var l=a.value,f=l.split("="),h=(0,u.default)(f,2),p=h[0],m=h[1];if("id"===decodeURIComponent(p)){n=decodeURIComponent(m);break}}}catch(b){d.e(b)}finally{d.f()}}}(0,s.default)(n)||this.cardPhysicalGet(n);var v=e.cardID,w=e.cardKey,x=e.remarks,y=e.goodID,k=e.pic,g=e.referrerUid?e.referrerUid:0;(0,s.default)(v)||(0,s.default)(w)||(this.shareCardID=v,this.shareCardKey=w,this.shareRemarks=x,this.shareCardGoodID=y,this.shareCurrentPic=k,this.sharePath="/packageFx/coupons/my?cardID="+v+"&cardKey="+w+"&goodID="+y,String(g)===String(this.uid)?this.cardStatusGet(v,!0):this.cardStatusGet(v,!1,!0)),this._myCoupons(0)},onShow:function(){},methods:{sendRemarks:function(){this.shareSendRemarksPop=!0},sendRemarksCancel:function(){this.shareSendRemarksPop=!1},sendRemarksInput:function(e){this.remarks=e},cardPhysicalGet:function(t){var n=this;return(0,o.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$u.http.post("https://ye.niutouren.vip/api/qr",{id:2}).then((function(e){console.log("card res is",e),(0,s.default)(e.id)||(n._goods(),n.cardPhysicalPop=!0)}));case 2:case"end":return t.stop()}}),t)})))()},cardStatusGet:function(t){var n=arguments,r=this;return(0,o.default)(a.default.mark((function o(){var u,i,c;return a.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return u=n.length>1&&void 0!==n[1]&&n[1],i=n.length>2&&void 0!==n[2]&&n[2],n.length>3&&void 0!==n[3]&&n[3],c="",a.next=6,e.$u.http.post("https://ye.niutouren.vip/api/json",{refId:t,type:"card_share",action:"list"}).then((function(e){if(0==e.code&&!(0,s.default)(e.data.result)){var t=JSON.parse(e.data.result[0].content);(0,s.default)(t.status)||(-1===t.status&&(c="该礼品卡已被拒绝"),1===t.status&&(c="该礼品卡已经赠送，对方还未接收"),2===t.status&&(c="该礼品卡已经被领取"),i&&1===t.status&&(r.sharePositionIndexPop=!0),i&&1!==t.status&&(r.shareRemarks=c,r.sharePositionOwnPop=!0),u&&(r.shareRemarks=c,r.sharePositionOwnPop=!0))}}));case 6:return a.abrupt("return",c);case 7:case"end":return a.stop()}}),o)})))()},_couponsShareChange:function(t,n){return(0,o.default)(a.default.mark((function r(){return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,e.$u.http.post("https://ye.niutouren.vip/api/json",{refId:t,type:"card_share",action:"list"}).then((function(t){if(0==t.code&&!(0,s.default)(t.data.result)){var r=t.data.result[0];e.$u.http.post("https://ye.niutouren.vip/api/json",{id:r.id,type:"card_share",action:"set",content:JSON.stringify({status:n})})}}));case 2:case"end":return r.stop()}}),r)})))()},currentIDGet:function(e){var t=this;return(0,o.default)(a.default.mark((function n(){return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:t.shareCurrentID=e.id,t.shareCurrentPic=e.pic,t.shareCurrentName=e.name,t._couponsShareOpen();case 4:case"end":return n.stop()}}),n)})))()},couponsUse:function(t){return(0,o.default)(a.default.mark((function n(){var r;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:r=[{goodsId:t.type,goodsName:t.name,number:1,pic:t.pic,price:t.money,score:0,sku:[],additions:[],goodsType:0,kjid:""}],e.setStorageSync("goodsList",r),e.navigateTo({url:"/pages/pay/order?mod=buy&card="+t.id});case 3:case"end":return n.stop()}}),n)})))()},onShareAppMessage:function(e){if(this.shareSendRemarksPop=!1,""!==this.sharePath)return this.$wxapi.jsonSet({token:this.token,type:"card_share",refId:this.shareCurrentID,content:JSON.stringify({status:1})}),{title:this.remarks,path:this.sharePath+"&remarks="+this.remarks+"&pic="+this.shareCurrentPic+"&referrerUid="+this.uid,imageUrl:this.shareCurrentPic}},_couponsShareOpen:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var n;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$wxapi.couponsShareOpen(e.token,e.shareCurrentID);case 2:n=t.sent,0==n.code&&(e.sharePath="/packageFx/coupons/my?cardID="+e.shareCurrentID+"&cardKey="+n.data);case 4:case"end":return t.stop()}}),t)})))()},_sharedMessageClose:function(){var e=this;return(0,o.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.sharePositionOwnPop=!1;case 1:case"end":return t.stop()}}),t)})))()},_sharedYes:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var n;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$wxapi.couponsShareFetch(e.token,e.shareCardID,e.shareCardKey,e.shareCardGoodID);case 2:n=t.sent,0==n.code&&(e.sharePositionIndexPop=!1,e._couponsShareChange(e.shareCardID,2),e._myCoupons(0));case 4:case"end":return t.stop()}}),t)})))()},_sharedNo:function(){var e=this;return(0,o.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.sharePositionIndexPop=!1,e._couponsShareChange(e.shareCardID,-1);case 2:case"end":return t.stop()}}),t)})))()},_cardPhysicalYes:function(){return(0,o.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:console.log("is ok");case 1:case"end":return e.stop()}}),e)})))()},_cardPhysicalNo:function(){var e=this;return(0,o.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.cardPhysicalPop=!1;case 1:case"end":return t.stop()}}),t)})))()},_sharedForward:function(){var e=this;return(0,o.default)(a.default.mark((function t(){return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.sharePositionIndexPop=!1,e.remarksPlaceholder="转发：代我接收该卡，填写地址就可以领取",e.remarksButton="确认转发",e.sendRemarks();case 4:case"end":return t.stop()}}),t)})))()},tabchange:function(e){var t=this;return(0,o.default)(a.default.mark((function n(){return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:t.current=e,0==t.current&&t._myCoupons(0),1==t.current&&t._myCoupons("1, 2, 3");case 3:case"end":return n.stop()}}),n)})))()},_myCoupons:function(e){var t=this;return(0,o.default)(a.default.mark((function n(){var r;return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t.coupons=null,n.next=3,t.$wxapi.myCoupons({token:t.token,status:e});case 3:r=n.sent,0==r.code&&(console.log("coupons is",r.data),r.data.forEach(function(){var e=(0,o.default)(a.default.mark((function e(n){var o;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n.dateEnd&&(n.dateEnd=n.dateEnd.split("")[0]),n.cardSend=0,o="",e.next=5,t.cardStatusGet(n.id);case 5:o=e.sent,""!==o&&(n.cardMessage=o,n.cardSend=1),t.coupons=r.data;case 8:case"end":return e.stop()}}),e)})));return function(t){return e.apply(this,arguments)}}()));case 5:case"end":return n.stop()}}),n)})))()},getCounpon:function(t,n){var r=this;return(0,o.default)(a.default.mark((function o(){var u;return a.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.next=2,getApp().checkHasLoginedH5();case 2:if(a.sent){a.next=5;break}return e.navigateTo({url:"/pages/login/login"}),a.abrupt("return");case 5:if(r.curItem=t,!t.pwd||n){a.next=9;break}return r.couponPwd="",a.abrupt("return");case 9:return a.next=11,r.$wxapi.fetchCoupons({id:t.id,token:r.token,pwd:r.couponPwd?r.couponPwd:""});case 11:if(u=a.sent,700!=u.code){a.next=15;break}return n?e.showToast({title:"口令输入有误",icon:"none"}):e.showToast({title:"礼品卡不存在",icon:"none"}),a.abrupt("return");case 15:if(20001!=u.code&&20002!=u.code){a.next=18;break}return e.showModal({title:"错误",content:"来晚了",showCancel:!1}),a.abrupt("return");case 18:if(20003!=u.code){a.next=21;break}return e.showModal({title:"错误",content:"你领过了，别贪心哦~",showCancel:!1}),a.abrupt("return");case 21:if(30001!=u.code){a.next=24;break}return e.showModal({title:"错误",content:"您的积分不足",showCancel:!1}),a.abrupt("return");case 24:if(20004!=u.code){a.next=27;break}return e.showModal({title:"错误",content:"已过期~",showCancel:!1}),a.abrupt("return");case 27:0==u.code?e.showToast({title:"领取成功",icon:"success"}):e.showModal({title:"错误",content:u.msg,showCancel:!1});case 28:case"end":return a.stop()}}),o)})))()},goIndex:function(){e.switchTab({url:"../index/index"})},goDetail:function(t){e.navigateTo({url:"/pages/goods/detail?id="+t+"&card=1"})},_goods:function(){var e=this;return(0,o.default)(a.default.mark((function t(){var n,r,u;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return 463740,t.next=3,e.$wxapi.goodsv2({categoryId:463740,showExtJson:!0,pageSize:1});case 3:n=t.sent,0==n.code&&(r=[],u=[],[],r=n.data.result,(0,s.default)(r)||r.forEach(function(){var e=(0,o.default)(a.default.mark((function e(t,r){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.image=t.pic,t.title=t.name,(0,s.default)(n.data.extJsonMap)||t.id in n.data.extJsonMap&&(t.ext=n.data.extJsonMap[t.id],(0,s.default)(t.ext["deadline"])||(t.ext["deadline"]=d.translateTimeDifference(t.ext["deadline"])),t.ext["max_total"]&&(t.ext["max_total"]=t.ext["max_total"]),t.ext["min_total"]&&(t.ext["min_total"]=t.ext["min_total"]),t.ext["shichiPrice"]&&(t.ext["shichiPrice"]=t.ext["shichiPrice"])),u.push(t);case 4:case"end":return e.stop()}}),e)})));return function(t,n){return e.apply(this,arguments)}}()),e.goods=u);case 5:case"end":return t.stop()}}),t)})))()}}};t.default=l}).call(this,n("df3c")["default"])},adf1:function(e,t,n){},c277:function(e,t,n){"use strict";n.r(t);var r=n("9d68"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(o);t["default"]=a.a}},[["1c5e","common/runtime","common/vendor"]]]);