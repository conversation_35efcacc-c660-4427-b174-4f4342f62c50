{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-picker/u-picker.vue?9fda", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-picker/u-picker.vue?71e5", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-picker/u-picker.vue?a46c", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-picker/u-picker.vue?4fe4", "uni-app:///uni_modules/uview-ui/components/u-picker/u-picker.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-picker/u-picker.vue?7051", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-picker/u-picker.vue?fbe9"], "names": ["name", "mixins", "data", "lastIndex", "innerIndex", "innerColumns", "columnIndex", "watch", "defaultIndex", "immediate", "handler", "columns", "methods", "getItemText", "<PERSON><PERSON><PERSON><PERSON>", "cancel", "confirm", "indexs", "value", "values", "<PERSON><PERSON><PERSON><PERSON>", "e", "index", "setIndexs", "setLastIndex", "setColumnValues", "tmpIndex", "getColumnValues", "uni", "setColumns", "getIndexs", "getV<PERSON>ues"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,oTAEN;AACP,KAAK;AACL;AACA,aAAa,kVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC8EvrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAvBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,eAwBA;EACAA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;EACA;EACAE;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;QACAC;QACAC;UAAA;QAAA;QACAC;MACA;IACA;IACA;IACAC;MACA,IACAF,QACAG,SADAH;MAEA;QACAZ;MACA;MACA;QACA;QACA;UAAA;UACA;UACAA;UACA;UACAgB;UACA;QACA;MACA;;MACA;MACA;MACA;MACA;MACA;MAEA;QAKAJ;UAAA;QAAA;QACAI;QACAL;QACA;QACAE;QACAb;MACA;IACA;IACA;IACAiB;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;UACAC;QACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAH;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA,CACA;MACA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxOA;AAAA;AAAA;AAAA;AAA0xC,CAAgB,8uCAAG,EAAC,C;;;;;;;;;;;ACA9yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-picker/u-picker.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-picker.vue?vue&type=template&id=f45a262e&scoped=true&\"\nvar renderjs\nimport script from \"./u-picker.vue?vue&type=script&lang=js&\"\nexport * from \"./u-picker.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-picker.vue?vue&type=style&index=0&id=f45a262e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"f45a262e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-picker/u-picker.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-picker.vue?vue&type=template&id=f45a262e&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uToolbar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-toolbar/u-toolbar\" */ \"@/uni_modules/uview-ui/components/u-toolbar/u-toolbar.vue\"\n      )\n    },\n    uLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-loading-icon/u-loading-icon\" */ \"@/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.$u.addUnit(_vm.visibleItemCount * _vm.itemHeight)\n  var g1 = _vm.$u.addUnit(_vm.itemHeight)\n  var l1 = _vm.__map(_vm.innerColumns, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g2 = _vm.$u.test.array(item)\n    var g3 = g2 ? _vm.$u.addUnit(_vm.itemHeight) : null\n    var g4 = g2 ? _vm.$u.addUnit(_vm.itemHeight) : null\n    var l0 = _vm.__map(item, function (item1, index1) {\n      var $orig = _vm.__get_orig(item1)\n      var m0 = g2 ? _vm.getItemText(item1) : null\n      return {\n        $orig: $orig,\n        m0: m0,\n      }\n    })\n    return {\n      $orig: $orig,\n      g2: g2,\n      g3: g3,\n      g4: g4,\n      l0: l0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-picker.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-picker.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<u-popup\r\n\t\t:show=\"show\"\r\n\t\t@close=\"closeHandler\"\r\n\t>\r\n\t\t<view class=\"u-picker\">\r\n\t\t\t<u-toolbar\r\n\t\t\t\tv-if=\"showToolbar\"\r\n\t\t\t\t:cancelColor=\"cancelColor\"\r\n\t\t\t\t:confirmColor=\"confirmColor\"\r\n\t\t\t\t:cancelText=\"cancelText\"\r\n\t\t\t\t:confirmText=\"confirmText\"\r\n\t\t\t\t:title=\"title\"\r\n\t\t\t\t@cancel=\"cancel\"\r\n\t\t\t\t@confirm=\"confirm\"\r\n\t\t\t></u-toolbar>\r\n\t\t\t<picker-view\r\n\t\t\t\tclass=\"u-picker__view\"\r\n\t\t\t\t:indicatorStyle=\"`height: ${$u.addUnit(itemHeight)}`\"\r\n\t\t\t\t:value=\"innerIndex\"\r\n\t\t\t\t:immediateChange=\"immediateChange\"\r\n\t\t\t\t:style=\"{\r\n\t\t\t\t\theight: `${$u.addUnit(visibleItemCount * itemHeight)}`\r\n\t\t\t\t}\"\r\n\t\t\t\t@change=\"changeHandler\"\r\n\t\t\t>\r\n\t\t\t\t<picker-view-column\r\n\t\t\t\t\tv-for=\"(item, index) in innerColumns\"\r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\tclass=\"u-picker__view__column\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<text\r\n\t\t\t\t\t\tv-if=\"$u.test.array(item)\"\r\n\t\t\t\t\t\tclass=\"u-picker__view__column__item u-line-1\"\r\n\t\t\t\t\t\tv-for=\"(item1, index1) in item\"\r\n\t\t\t\t\t\t:key=\"index1\"\r\n\t\t\t\t\t\t:style=\"{\r\n\t\t\t\t\t\t\theight: $u.addUnit(itemHeight),\r\n\t\t\t\t\t\t\tlineHeight: $u.addUnit(itemHeight),\r\n\t\t\t\t\t\t\tfontWeight: index1 === innerIndex[index] ? 'bold' : 'normal'\r\n\t\t\t\t\t\t}\"\r\n\t\t\t\t\t>{{ getItemText(item1) }}</text>\r\n\t\t\t\t</picker-view-column>\r\n\t\t\t</picker-view>\r\n\t\t\t<view\r\n\t\t\t\tv-if=\"loading\"\r\n\t\t\t\tclass=\"u-picker--loading\"\r\n\t\t\t>\r\n\t\t\t\t<u-loading-icon mode=\"circle\"></u-loading-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</u-popup>\r\n</template>\r\n\r\n<script>\r\n/**\r\n * u-picker\r\n * @description 选择器\r\n * @property {Boolean}\t\t\tshow\t\t\t\t是否显示picker弹窗（默认 false ）\r\n * @property {Boolean}\t\t\tshowToolbar\t\t\t是否显示顶部的操作栏（默认 true ）\r\n * @property {String}\t\t\ttitle\t\t\t\t顶部标题\r\n * @property {Array}\t\t\tcolumns\t\t\t\t对象数组，设置每一列的数据\r\n * @property {Boolean}\t\t\tloading\t\t\t\t是否显示加载中状态（默认 false ）\r\n * @property {String | Number}\titemHeight\t\t\t各列中，单个选项的高度（默认 44 ）\r\n * @property {String}\t\t\tcancelText\t\t\t取消按钮的文字（默认 '取消' ）\r\n * @property {String}\t\t\tconfirmText\t\t\t确认按钮的文字（默认 '确定' ）\r\n * @property {String}\t\t\tcancelColor\t\t\t取消按钮的颜色（默认 '#909193' ）\r\n * @property {String}\t\t\tconfirmColor\t\t确认按钮的颜色（默认 '#3c9cff' ）\r\n * @property {String | Number}\tvisibleItemCount\t每列中可见选项的数量（默认 5 ）\r\n * @property {String}\t\t\tkeyName\t\t\t\t选项对象中，需要展示的属性键名（默认 'text' ）\r\n * @property {Boolean}\t\t\tcloseOnClickOverlay\t是否允许点击遮罩关闭选择器（默认 false ）\r\n * @property {Array}\t\t\tdefaultIndex\t\t各列的默认索引\r\n * @property {Boolean}\t\t\timmediateChange\t\t是否在手指松开时立即触发change事件（默认 false ）\r\n * @event {Function} close\t\t关闭选择器时触发\r\n * @event {Function} cancel\t\t点击取消按钮触发\r\n * @event {Function} change\t\t当选择值变化时触发\r\n * @event {Function} confirm\t点击确定按钮，返回当前选择的值\r\n */\r\nimport props from './props.js';\r\nexport default {\r\n\tname: 'u-picker',\r\n\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\t// 上一次选择的列索引\r\n\t\t\tlastIndex: [],\r\n\t\t\t// 索引值 ，对应picker-view的value\r\n\t\t\tinnerIndex: [],\r\n\t\t\t// 各列的值\r\n\t\t\tinnerColumns: [],\r\n\t\t\t// 上一次的变化列索引\r\n\t\t\tcolumnIndex: 0,\r\n\t\t}\r\n\t},\r\n\twatch: {\r\n\t\t// 监听默认索引的变化，重新设置对应的值\r\n\t\tdefaultIndex: {\r\n\t\t\timmediate: true,\r\n\t\t\thandler(n) {\r\n\t\t\t\tthis.setIndexs(n, true)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 监听columns参数的变化\r\n\t\tcolumns: {\r\n\t\t\timmediate: true,\r\n\t\t\thandler(n) {\r\n\t\t\t\tthis.setColumns(n)\r\n\t\t\t}\r\n\t\t},\r\n\t},\r\n\tmethods: {\r\n\t\t// 获取item需要显示的文字，判别为对象还是文本\r\n\t\tgetItemText(item) {\r\n\t\t\tif (uni.$u.test.object(item)) {\r\n\t\t\t\treturn item[this.keyName]\r\n\t\t\t} else {\r\n\t\t\t\treturn item\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 关闭选择器\r\n\t\tcloseHandler() {\r\n\t\t\tif (this.closeOnClickOverlay) {\r\n\t\t\t\tthis.$emit('close')\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 点击工具栏的取消按钮\r\n\t\tcancel() {\r\n\t\t\tthis.$emit('cancel')\r\n\t\t},\r\n\t\t// 点击工具栏的确定按钮\r\n\t\tconfirm() {\r\n\t\t\tthis.$emit('confirm', {\r\n\t\t\t\tindexs: this.innerIndex,\r\n\t\t\t\tvalue: this.innerColumns.map((item, index) => item[this.innerIndex[index]]),\r\n\t\t\t\tvalues: this.innerColumns\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 选择器某一列的数据发生变化时触发\r\n\t\tchangeHandler(e) {\r\n\t\t\tconst {\r\n\t\t\t\tvalue\r\n\t\t\t} = e.detail\r\n\t\t\tlet index = 0,\r\n\t\t\t\tcolumnIndex = 0\r\n\t\t\t// 通过对比前后两次的列索引，得出当前变化的是哪一列\r\n\t\t\tfor (let i = 0; i < value.length; i++) {\r\n\t\t\t\tlet item = value[i]\r\n\t\t\t\tif (item !== (this.lastIndex[i] || 0)) { // 把undefined转为合法假值0\r\n\t\t\t\t\t// 设置columnIndex为当前变化列的索引\r\n\t\t\t\t\tcolumnIndex = i\r\n\t\t\t\t\t// index则为变化列中的变化项的索引\r\n\t\t\t\t\tindex = item\r\n\t\t\t\t\tbreak // 终止循环，即使少一次循环，也是性能的提升\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\tthis.columnIndex = columnIndex\r\n\t\t\tconst values = this.innerColumns\r\n\t\t\t// 将当前的各项变化索引，设置为\"上一次\"的索引变化值\r\n\t\t\tthis.setLastIndex(value)\r\n\t\t\tthis.setIndexs(value)\r\n\r\n\t\t\tthis.$emit('change', {\r\n\t\t\t\t// #ifndef MP-WEIXIN || MP-LARK\r\n\t\t\t\t// 微信小程序不能传递this，会因为循环引用而报错\r\n\t\t\t\tpicker: this,\r\n\t\t\t\t// #endif\r\n\t\t\t\tvalue: this.innerColumns.map((item, index) => item[value[index]]),\r\n\t\t\t\tindex,\r\n\t\t\t\tindexs: value,\r\n\t\t\t\t// values为当前变化列的数组内容\r\n\t\t\t\tvalues,\r\n\t\t\t\tcolumnIndex\r\n\t\t\t})\r\n\t\t},\r\n\t\t// 设置index索引，此方法可被外部调用设置\r\n\t\tsetIndexs(index, setLastIndex) {\r\n\t\t\tthis.innerIndex = uni.$u.deepClone(index)\r\n\t\t\tif (setLastIndex) {\r\n\t\t\t\tthis.setLastIndex(index)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 记录上一次的各列索引位置\r\n\t\tsetLastIndex(index) {\r\n\t\t\t// 当能进入此方法，意味着当前设置的各列默认索引，即为“上一次”的选中值，需要记录，是因为changeHandler中\r\n\t\t\t// 需要拿前后的变化值进行对比，得出当前发生改变的是哪一列\r\n\t\t\tthis.lastIndex = uni.$u.deepClone(index)\r\n\t\t},\r\n\t\t// 设置对应列选项的所有值\r\n\t\tsetColumnValues(columnIndex, values) {\r\n\t\t\t// 替换innerColumns数组中columnIndex索引的值为values，使用的是数组的splice方法\r\n\t\t\tthis.innerColumns.splice(columnIndex, 1, values)\r\n\t\t\t// 拷贝一份原有的innerIndex做临时变量，将大于当前变化列的所有的列的默认索引设置为0\r\n\t\t\tlet tmpIndex = uni.$u.deepClone(this.innerIndex)\r\n\t\t\tfor (let i = 0; i < this.innerColumns.length; i++) {\r\n\t\t\t\tif (i > this.columnIndex) {\r\n\t\t\t\t\ttmpIndex[i] = 0\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t// 一次性赋值，不能单个修改，否则无效\r\n\t\t\tthis.setIndexs(tmpIndex)\r\n\t\t},\r\n\t\t// 获取对应列的所有选项\r\n\t\tgetColumnValues(columnIndex) {\r\n\t\t\t// 进行同步阻塞，因为外部得到change事件之后，可能需要执行setColumnValues更新列的值\r\n\t\t\t// 索引如果在外部change的回调中调用getColumnValues的话，可能无法得到变更后的列值，这里进行一定延时，保证值的准确性\r\n\t\t\t(async () => {\r\n\t\t\t\tawait uni.$u.sleep()\r\n\t\t\t})()\r\n\t\t\treturn this.innerColumns[columnIndex]\r\n\t\t},\r\n\t\t// 设置整体各列的columns的值\r\n\t\tsetColumns(columns) {\r\n\t\t\tthis.innerColumns = uni.$u.deepClone(columns)\r\n\t\t\t// 如果在设置各列数据时，没有被设置默认的各列索引defaultIndex，那么用0去填充它，数组长度为列的数量\r\n\t\t\tif (this.innerIndex.length === 0) {\r\n\t\t\t\tthis.innerIndex = new Array(columns.length).fill(0)\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 获取各列选中值对应的索引\r\n\t\tgetIndexs() {\r\n\t\t\treturn this.innerIndex\r\n\t\t},\r\n\t\t// 获取各列选中的值\r\n\t\tgetValues() {\r\n\t\t\t// 进行同步阻塞，因为外部得到change事件之后，可能需要执行setColumnValues更新列的值\r\n\t\t\t// 索引如果在外部change的回调中调用getValues的话，可能无法得到变更后的列值，这里进行一定延时，保证值的准确性\r\n\t\t\t(async () => {\r\n\t\t\t\tawait uni.$u.sleep()\r\n\t\t\t})()\r\n\t\t\treturn this.innerColumns.map((item, index) => item[this.innerIndex[index]])\r\n\t\t}\r\n\t},\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-picker {\r\n\t\tposition: relative;\r\n\r\n\t\t&__view {\r\n\r\n\t\t\t&__column {\r\n\t\t\t\t@include flex;\r\n\t\t\t\tflex: 1;\r\n\t\t\t\tjustify-content: center;\r\n\r\n\t\t\t\t&__item {\r\n\t\t\t\t\t@include flex;\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\t\t\t\t\tfont-size: 16px;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\tdisplay: block;\r\n\t\t\t\t\t/* #endif */\r\n\t\t\t\t\tcolor: $u-main-color;\r\n\r\n\t\t\t\t\t&--disabled {\r\n\t\t\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t\t\tcursor: not-allowed;\r\n\t\t\t\t\t\t/* #endif */\r\n\t\t\t\t\t\topacity: 0.35;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&--loading {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tright: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tbottom: 0;\r\n\t\t\t@include flex;\r\n\t\t\tjustify-content: center;\r\n\t\t\talign-items: center;\r\n\t\t\tbackground-color: rgba(255, 255, 255, 0.87);\r\n\t\t\tz-index: 1000;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-picker.vue?vue&type=style&index=0&id=f45a262e&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-picker.vue?vue&type=style&index=0&id=f45a262e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688734657\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}