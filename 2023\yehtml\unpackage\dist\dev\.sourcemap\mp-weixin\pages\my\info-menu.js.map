{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/info-menu.vue?1bdd", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/info-menu.vue?3855", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/info-menu.vue?d67a", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/info-menu.vue?425e", "uni-app:///pages/my/info-menu.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userMobile", "apiUserInfoMap", "created", "mounted", "onReady", "onLoad", "onShow", "methods", "userDetail", "res", "getPhoneNumber", "console", "_getPhoneNumber", "uni", "title", "icon", "duration", "content", "showCancel"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;;;AAGxD;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAoqB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCiBxrB;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC,6BAEA;EACAC,6BAEA;EACAC,6BAEA;EACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACAC;QACA;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAH;gBACA;kBACAI;oBACAC;oBACAC;oBACAC;kBACA;kBACA;kBACA;gBACA;kBACAH;oBACAC;oBACAG;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B", "file": "pages/my/info-menu.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/info-menu.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./info-menu.vue?vue&type=template&id=711699f4&scoped=true&\"\nvar renderjs\nimport script from \"./info-menu.vue?vue&type=script&lang=js&\"\nexport * from \"./info-menu.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"711699f4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/info-menu.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info-menu.vue?vue&type=template&id=711699f4&scoped=true&\"", "var components\ntry {\n  components = {\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info-menu.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info-menu.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <u-cell v-if=\"userMobile\" title=\"手机号码\" :value=\"userMobile\" />\r\n    <u-cell wx:else title=\"绑定手机\">\r\n      <view slot=\"value\" class=\"mobile-btn\">\r\n        <u-button type=\"success\" size=\"mini\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\">立即绑定\r\n        </u-button>\r\n      </view>\r\n    </u-cell>\r\n    <u-cell title=\"收货地址\" is-link url=\"/packageFx/address/index\" />\r\n    <u-cell title=\"我的收藏\" is-link url=\"/pages/goods/fav\" />\r\n    <u-cell title=\"修改资料\" is-link url=\"/pages/my/info\" />\r\n\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    data() {\r\n      return {\r\n        userMobile: undefined,\r\n        apiUserInfoMap: undefined\r\n      }\r\n    },\r\n    created() {\r\n\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    onReady() {\r\n\r\n    },\r\n    onLoad(e) {\r\n      this.userDetail()\r\n    },\r\n    onShow() {\r\n\r\n    },\r\n    methods: {\r\n      async userDetail() {\r\n        const res = await this.$wxapi.userDetail(this.token)\r\n        if (res.code == 0 && (res.data.base.nick || res.data.base.nickavatarUrl)) {\r\n          this.userMobile = res.data.base.mobile\r\n          this.apiUserInfoMap = res.data\r\n        }\r\n      },\r\n      getPhoneNumber(e) {\r\n        if (!e.detail.errMsg || e.detail.errMsg != \"getPhoneNumber:ok\") {\r\n          // wx.showModal({\r\n          // \ttitle: '提示',\r\n          // \tcontent: e.detail.errMsg,\r\n          // \tshowCancel: false\r\n          // })\r\n          console.error(e)\r\n          return;\r\n        }\r\n        this._getPhoneNumber(e)\r\n      },\r\n      async _getPhoneNumber(e) {\r\n        const res = await this.$wxapi.bindMobileWxappV2(this.token, e.detail.code)\r\n        if (res.code == 0) {\r\n          uni.showToast({\r\n            title: '绑定成功',\r\n            icon: 'success',\r\n            duration: 2000\r\n          })\r\n          this.$u.vuex('mobile', res.data)\r\n          this.userDetail()\r\n        } else {\r\n          uni.showModal({\r\n            title: '提示',\r\n            content: res.msg,\r\n            showCancel: false\r\n          })\r\n        }\r\n      }\r\n    }\r\n  }\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n</style>"], "sourceRoot": ""}