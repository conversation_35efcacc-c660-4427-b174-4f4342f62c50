<view><view class="container"><block wx:if="{{$root.g0}}"><u-empty vue-id="17172ea8-1" mode="car" text="暂无订单" icon="http://cdn.uviewui.com/uview/empty/car.png" bind:__l="__l"></u-empty></block><view class="order-list" hidden="{{$root.g1>0?false:true}}"><block wx:for="{{orderList}}" wx:for-item="item" wx:for-index="index"><view class="a-order"><u-cell vue-id="{{'17172ea8-2-'+index}}" icon="integral-fill" title="{{'订单号：'+item.orderNumber}}" value="{{item.statusStr}}" label="{{'购买用户: '+item.buyerUserNick}}" bind:__l="__l"></u-cell><block wx:for="{{item.goodsList}}" wx:for-item="g" wx:for-index="index2"><u-cell vue-id="{{'17172ea8-3-'+index+'-'+index2}}" title="{{g.goodsName}}" value="{{g.amountSingle}}" label="{{g.number}}" bind:__l="__l" vue-slots="{{['icon','label','right-icon']}}"><u-avatar vue-id="{{('17172ea8-4-'+index+'-'+index2)+','+('17172ea8-3-'+index+'-'+index2)}}" slot="icon" shape="square" size="35" src="{{g.pic?g.pic:'error'}}" customStyle="margin: -3px 5px -3px 0" bind:__l="__l"></u-avatar><view slot="label"><view class="u-font-14 u-tips-color">{{g.commisionRecord.bili+"% 返佣"}}</view><view class="u-font-14 u-tips-color">{{g.commisionRecord.money+" "+(g.commisionRecord.unit==0?'元':'积分')}}</view><block wx:if="{{g.commisionRecord.isSettlement}}"><text style="color:green;margin-left:10px;font-size:14px;">已结算</text></block><block wx:else><block wx:if="{{item.status!=-1}}"><text style="color:gray;margin-left:10px;font-size:14px;">待结算</text></block></block></view><view slot="right-icon">{{"x "+item.stores}}</view></u-cell></block><view class="goods-price">{{"共 "+item.goodsNumber+' 件商品 合计：'}}<block wx:if="{{item.score<=0}}"><text class="p">{{"¥ "+item.amountReal}}</text></block><block wx:if="{{item.score>0}}"><text class="p">{{"¥ "+item.amountReal+" + "+item.score+" 积分"}}</text></block>，累计佣金<block wx:if="{{item.score<=0}}"><text class="p">{{item.totalCommision}}</text></block></view><block wx:if="{{userInviter[item.goodsList[0].commisionRecord.uids]}}"><u-cell vue-id="{{'17172ea8-5-'+index}}" title="{{'销售员: '+userInviter[item.goodsList[0].commisionRecord.uids].nick}}" bind:__l="__l"></u-cell></block><view class="goods-info"><view class="goods-des"><block wx:if="{{item.remark&&item.remark!=''}}"><view class="remark">{{item.remark}}</view></block><view style="font-size:24rpx;color:#666;">{{"下单日期："+item.dateAdd+''}}</view></view></view></view></block></view><view class="safeAreaOldMarginBttom safeAreaNewMarginBttom"></view></view></view>