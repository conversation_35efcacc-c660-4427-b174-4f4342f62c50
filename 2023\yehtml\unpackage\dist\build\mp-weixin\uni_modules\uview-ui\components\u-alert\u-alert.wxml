<u-transition vue-id="9d61241a-1" mode="fade" show="{{show}}" class="data-v-62fa04c8" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="{{['u-alert','data-v-62fa04c8','u-alert--'+type+'--'+effect]}}" style="{{$root.s0}}" catchtap="__e"><block wx:if="{{showIcon}}"><view class="u-alert__icon data-v-62fa04c8"><u-icon vue-id="{{('9d61241a-2')+','+('9d61241a-1')}}" name="{{iconName}}" size="18" color="{{iconColor}}" class="data-v-62fa04c8" bind:__l="__l"></u-icon></view></block><view class="u-alert__content data-v-62fa04c8" style="{{'padding-right:'+(closable?'20px':0)+';'}}"><block wx:if="{{title}}"><text class="{{['u-alert__content__title','data-v-62fa04c8',effect==='dark'?'u-alert__text--dark':'u-alert__text--'+type+'--light']}}" style="{{'font-size:'+($root.g0)+';'+('text-align:'+(center?'center':'left')+';')}}">{{title}}</text></block><block wx:if="{{description}}"><text class="{{['u-alert__content__desc','data-v-62fa04c8',effect==='dark'?'u-alert__text--dark':'u-alert__text--'+type+'--light']}}" style="{{'font-size:'+($root.g1)+';'+('text-align:'+(center?'center':'left')+';')}}">{{description}}</text></block></view><block wx:if="{{closable}}"><view data-event-opts="{{[['tap',[['closeHandler',['$event']]]]]}}" class="u-alert__close data-v-62fa04c8" catchtap="__e"><u-icon vue-id="{{('9d61241a-3')+','+('9d61241a-1')}}" name="close" color="{{iconColor}}" size="15" class="data-v-62fa04c8" bind:__l="__l"></u-icon></view></block></view></u-transition>