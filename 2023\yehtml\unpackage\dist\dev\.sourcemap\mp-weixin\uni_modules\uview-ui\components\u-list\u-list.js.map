{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-list/u-list.vue?63b8", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-list/u-list.vue?b448", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-list/u-list.vue?9634", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-list/u-list.vue?9cde", "uni-app:///uni_modules/uview-ui/components/u-list/u-list.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-list/u-list.vue?4a29", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-list/u-list.vue?99be"], "names": ["name", "mixins", "watch", "scrollIntoView", "data", "innerScrollTop", "offset", "sys", "computed", "listStyle", "addUnit", "provide", "uList", "created", "mounted", "methods", "updateOffsetFromChild", "onScroll", "scrollTop", "scrollIntoViewById", "scrolltolower", "uni", "scrolltoupper"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvBA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACuCrrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AArBA,eAsBA;EACAA;EACAC;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;EACA;EACAC;EACAC;IACAC;MACA;IACA;IACAC;MACA;MAKAC;MAEA;MACA;IACA;IACAC,qDASA;IACA;IACAC;MAAA;MACAC;QACA;MACA;IACA;IAEA;IACAC;MAAA;MACAD;QACA;QACA;QACA;MACA;IACA;EAEA;AACA;AAAA,2B;;;;;;;;;;;;;AClJA;AAAA;AAAA;AAAA;AAAwxC,CAAgB,4uCAAG,EAAC,C;;;;;;;;;;;ACA5yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-list/u-list.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-list.vue?vue&type=template&id=27d76bae&scoped=true&\"\nvar renderjs\nimport script from \"./u-list.vue?vue&type=script&lang=js&\"\nexport * from \"./u-list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-list.vue?vue&type=style&index=0&id=27d76bae&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"27d76bae\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-list/u-list.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-list.vue?vue&type=template&id=27d76bae&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.listStyle])\n  var m0 = Number(_vm.scrollTop)\n  var m1 = Number(_vm.lowerThreshold)\n  var m2 = Number(_vm.upperThreshold)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        m0: m0,\n        m1: m1,\n        m2: m2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-list.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- #ifdef APP-NVUE -->\r\n\t<list\r\n\t\tclass=\"u-list\"\r\n\t\t:enableBackToTop=\"enableBackToTop\"\r\n\t\t:loadmoreoffset=\"lowerThreshold\"\r\n\t\t:showScrollbar=\"showScrollbar\"\r\n\t\t:style=\"[listStyle]\"\r\n\t\t:offset-accuracy=\"Number(offsetAccuracy)\"\r\n\t\t@scroll=\"onScroll\"\r\n\t\t@loadmore=\"scrolltolower\"\r\n\t>\r\n\t\t<slot />\r\n\t</list>\r\n\t<!-- #endif -->\r\n\t<!-- #ifndef APP-NVUE -->\r\n\t<scroll-view\r\n\t\tclass=\"u-list\"\r\n\t\t:scroll-into-view=\"scrollIntoView\"\r\n\t\t:style=\"[listStyle]\"\r\n\t\tscroll-y\r\n\t\t:scroll-top=\"Number(scrollTop)\"\r\n\t\t:lower-threshold=\"Number(lowerThreshold)\"\r\n\t\t:upper-threshold=\"Number(upperThreshold)\"\r\n\t\t:show-scrollbar=\"showScrollbar\"\r\n\t\t:enable-back-to-top=\"enableBackToTop\"\r\n\t\t:scroll-with-animation=\"scrollWithAnimation\"\r\n\t\t@scroll=\"onScroll\"\r\n\t\t@scrolltolower=\"scrolltolower\"\r\n\t\t@scrolltoupper=\"scrolltoupper\"\r\n\t>\r\n\t\t<view>\r\n\t\t\t<slot />\r\n\t\t</view>\r\n\t</scroll-view>\r\n\t<!-- #endif -->\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t// #ifdef APP-NVUE\r\n\tconst dom = uni.requireNativePlugin('dom')\r\n\t// #endif\r\n\t/**\r\n\t * List 列表\r\n\t * @description 该组件为高性能列表组件\r\n\t * @tutorial https://www.uviewui.com/components/list.html\r\n\t * @property {Boolean}\t\t\tshowScrollbar\t\t控制是否出现滚动条，仅nvue有效 （默认 false ）\r\n\t * @property {String ｜ Number}\tlowerThreshold\t\t距底部多少时触发scrolltolower事件 （默认 50 ）\r\n\t * @property {String ｜ Number}\tupperThreshold\t\t距顶部多少时触发scrolltoupper事件，非nvue有效 （默认 0 ）\r\n\t * @property {String ｜ Number}\tscrollTop\t\t\t设置竖向滚动条位置（默认 0 ）\r\n\t * @property {String ｜ Number}\toffsetAccuracy\t\t控制 onscroll 事件触发的频率，仅nvue有效（默认 10 ）\r\n\t * @property {Boolean}\t\t\tenableFlex\t\t\t启用 flexbox 布局。开启后，当前节点声明了display: flex就会成为flex container，并作用于其孩子节点，仅微信小程序有效（默认 false ）\r\n\t * @property {Boolean}\t\t\tpagingEnabled\t\t是否按分页模式显示List，（默认 false ）\r\n\t * @property {Boolean}\t\t\tscrollable\t\t\t是否允许List滚动（默认 true ）\r\n\t * @property {String}\t\t\tscrollIntoView\t\t值应为某子元素id（id不能以数字开头）\r\n\t * @property {Boolean}\t\t\tscrollWithAnimation\t在设置滚动条位置时使用动画过渡 （默认 false ）\r\n\t * @property {Boolean}\t\t\tenableBackToTop\t\tiOS点击顶部状态栏、安卓双击标题栏时，滚动条返回顶部，只对微信小程序有效 （默认 false ）\r\n\t * @property {String ｜ Number}\theight\t\t\t\t列表的高度 （默认 0 ）\r\n\t * @property {String ｜ Number}\twidth\t\t\t\t列表宽度 （默认 0 ）\r\n\t * @property {String ｜ Number}\tpreLoadScreen\t\t列表前后预渲染的屏数，1代表一个屏幕的高度，1.5代表1个半屏幕高度  （默认 1 ）\r\n\t * @property {Object}\t\t\tcustomStyle\t\t\t定义需要用到的外部样式\r\n\t *\r\n\t * @example <u-list @scrolltolower=\"scrolltolower\"></u-list>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-list',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\twatch: {\r\n\t\t\tscrollIntoView(n) {\r\n\t\t\t\tthis.scrollIntoViewById(n)\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 记录内部滚动的距离\r\n\t\t\t\tinnerScrollTop: 0,\r\n\t\t\t\t// vue下，scroll-view在上拉加载时的偏移值\r\n\t\t\t\toffset: 0,\r\n\t\t\t\tsys: uni.$u.sys()\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tlistStyle() {\r\n\t\t\t\tconst style = {},\r\n\t\t\t\t\taddUnit = uni.$u.addUnit\r\n\t\t\t\tif (this.width != 0) style.width = addUnit(this.width)\r\n\t\t\t\tif (this.height != 0) style.height = addUnit(this.height)\r\n\t\t\t\t// 如果没有定义列表高度，则默认使用屏幕高度\r\n\t\t\t\tif (!style.height) style.height = addUnit(this.sys.windowHeight, 'px')\r\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\r\n\t\t\t}\r\n\t\t},\r\n\t\tprovide() {\r\n\t\t\treturn {\r\n\t\t\t\tuList: this\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.refs = []\r\n\t\t\tthis.children = []\r\n\t\t\tthis.anchors = []\r\n\t\t},\r\n\t\tmounted() {},\r\n\t\tmethods: {\r\n\t\t\tupdateOffsetFromChild(top) {\r\n\t\t\t\tthis.offset = top\r\n\t\t\t},\r\n\t\t\tonScroll(e) {\r\n\t\t\t\tlet scrollTop = 0\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tscrollTop = e.contentOffset.y\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tscrollTop = e.detail.scrollTop\r\n\t\t\t\t// #endif\r\n\t\t\t\tthis.innerScrollTop = scrollTop\r\n\t\t\t\tthis.$emit('scroll', Math.abs(scrollTop))\r\n\t\t\t},\r\n\t\t\tscrollIntoViewById(id) {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 根据id参数，找到所有u-list-item中匹配的节点，再通过dom模块滚动到对应的位置\r\n\t\t\t\tconst item = this.refs.find(item => item.$refs[id] ? true : false)\r\n\t\t\t\tdom.scrollToElement(item.$refs[id], {\r\n\t\t\t\t\t// 是否需要滚动动画\r\n\t\t\t\t\tanimated: this.scrollWithAnimation\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 滚动到底部触发事件\r\n\t\t\tscrolltolower(e) {\r\n\t\t\t\tuni.$u.sleep(30).then(() => {\r\n\t\t\t\t\tthis.$emit('scrolltolower')\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\t// 滚动到底部时触发，非nvue有效\r\n\t\t\tscrolltoupper(e) {\r\n\t\t\t\tuni.$u.sleep(30).then(() => {\r\n\t\t\t\t\tthis.$emit('scrolltoupper')\r\n\t\t\t\t\t// 这一句很重要，能绝对保证在性功能障碍的webview，滚动条到顶时，取消偏移值，让页面置顶\r\n\t\t\t\t\tthis.offset = 0\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t\t// #endif\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-list {\r\n\t\t@include flex(column);\r\n\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-list.vue?vue&type=style&index=0&id=27d76bae&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-list.vue?vue&type=style&index=0&id=27d76bae&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692294167\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}