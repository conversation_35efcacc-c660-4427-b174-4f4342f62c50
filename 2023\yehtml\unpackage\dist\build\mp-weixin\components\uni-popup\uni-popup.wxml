<block wx:if="{{showPopup}}"><view data-event-opts="{{[['touchmove',[['clear',['$event']]]]]}}" class="{{['uni-popup','data-v-8effeed8',popupstyle]}}" catchtouchmove="__e"><block wx:if="{{maskShow}}"><uni-transition vue-id="37594ee2-1" mode-class="{{['fade']}}" styles="{{maskClass}}" duration="{{duration}}" show="{{showTrans}}" data-event-opts="{{[['^click',[['onTap']]]]}}" bind:click="__e" class="data-v-8effeed8" bind:__l="__l"></uni-transition></block><uni-transition vue-id="37594ee2-2" mode-class="{{ani}}" styles="{{transClass}}" duration="{{duration}}" show="{{showTrans}}" data-event-opts="{{[['^click',[['onTap']]]]}}" bind:click="__e" class="data-v-8effeed8" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['clear',['$event']]]]]}}" class="uni-popup__wrapper-box data-v-8effeed8" catchtap="__e"><slot></slot></view></uni-transition></view></block>