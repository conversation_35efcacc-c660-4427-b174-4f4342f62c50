(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/list/three1"],{"26f3":function(n,t,e){"use strict";var i=e("b47a"),o=e.n(i);o.a},"3d59":function(n,t,e){"use strict";(function(n){var i=e("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;i(e("bc37"));var o=i(e("f0c3")),u={components:{},props:{list:{type:Array,default:[]},type:{type:String,default:""}},created:function(){console.log("this list",this.list);var n=(0,o.default)().unix(),t=(0,o.default)(this.list[0].ext["deadline"],"YYYY-MM-DD").unix();this.deliveryTimeTip=n>t?"预购已经结束":"正在预购中"},data:function(){return{timeData:{},deliveryTimeTip:""}},watch:{list:function(n){console.log("watch list is",n)}},methods:{goDetail:function(t){n.navigateTo({url:"/pages/goods/detail?id="+t.id})},onChangeTimeData:function(n){this.timeData=n},handleTags:function(n){var t=n.replace(/[,，]/g," ");return t}}};t.default=u}).call(this,e("df3c")["default"])},5860:function(n,t,e){"use strict";e.r(t);var i=e("7839"),o=e("de15");for(var u in o)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(u);e("26f3");var l=e("828b"),a=Object(l["a"])(o["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);t["default"]=a.exports},7839:function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return u})),e.d(t,"a",(function(){return i}));var i={uRow:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-row/u-row")]).then(e.bind(null,"f632"))},uCol:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-col/u-col")]).then(e.bind(null,"44b6"))},uCountDown:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-count-down/u-count-down")]).then(e.bind(null,"7dea"))},uSkeleton:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-skeleton/u-skeleton")]).then(e.bind(null,"58b2"))}},o=function(){var n=this.$createElement,t=(this._self._c,this.list[0].name?this.handleTags(this.list[0].tags):null);this.$mp.data=Object.assign({},{$root:{m0:t}})},u=[]},b47a:function(n,t,e){},de15:function(n,t,e){"use strict";e.r(t);var i=e("3d59"),o=e.n(i);for(var u in i)["default"].indexOf(u)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(u);t["default"]=o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/list/three1-create-component',
    {
        'components/list/three1-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("5860"))
        })
    },
    [['components/list/three1-create-component']]
]);
