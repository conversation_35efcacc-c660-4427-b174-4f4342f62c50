(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["packageFx/my/grade"],{2454:function(t,e,o){"use strict";(function(t,e){var n=o("47a9");o("96bd");n(o("3240"));var i=n(o("8e2b"));t.__webpack_require_UNI_MP_PLUGIN__=o,e(i.default)}).call(this,o("3223")["default"],o("df3c")["createPage"])},"2cca":function(t,e,o){"use strict";o.r(e);var n=o("4bf9"),i=o.n(n);for(var c in n)["default"].indexOf(c)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(c);e["default"]=i.a},"4bf9":function(t,e,o){"use strict";(function(t){var n=o("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var i=n(o("7eb4")),c=n(o("ee10")),s={components:{zbTooltip:function(){o.e("uni_modules/zb-tooltip/components/zb-tooltip/zb-tooltip").then(function(){return resolve(o("dffd"))}.bind(null,o)).catch(o.oe)}},data:function(){return{circular:!1,indicatorDots:!1,autoplay:!1,interval:2e3,duration:500,indicatorColor:"#e9e9e9",indicatorActiveColor:"red",leftRightMargin:"50rpx",current:0,memberLevel:"member1",levelId:5596,userDetail:void 0,balance:0,memberLevelDetail:[],activeColor:["#dbdfeb","#ffe3cb","#f7dbac"],memberName:["轻享","心享","尊享"],memberEqual:!1,equity:[{icon:"",text:"专属特价",desc:"全场9.5折(预购、抢购等活动除外)",position:"bottom-start"},{icon:"",text:"优先配送",desc:"优先配送免排队，好食材快人一步",position:"bottom-start"},{icon:"",text:"专属客服",desc:"贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题",position:"bottom-start"},{icon:"",text:"新品先购",desc:"野贝农上市新品之前，优先获得选购权",position:"bottom-end"}],open:[{icon:"",text:"购买轻享会员年卡",desc:"费用为188元/年",class:"icon-color-1"},{icon:"",text:"预存",desc:"在野贝农小程序平台充值预存1000元",class:"icon-color-1"}],faq:[{icon:"",text:"预存的费用有限制吗？",desc:"没有限制，您可以购买野贝农平台所有商品",class:"icon-color-1"},{icon:"",text:"预存的费用能提现吗？",desc:"不能，根据国家法律，充值的费用是不允许提现的",class:"icon-color-4"}]}},onLoad:function(t){this._getUserAmount(),this._userLevelDetail()},methods:{_userDetail:function(){var t=this;return(0,c.default)(i.default.mark((function e(){var o;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$wxapi.userDetail(t.token);case 2:o=e.sent,0==o.code&&(t.userDetail=o.data.base,console.log("this.userDetail is",t.userDetail));case 4:case"end":return e.stop()}}),e)})))()},userLevelBuy:function(t){var e;switch(t){case 0:e=553;break;case 1:e=554;break;case 2:e=555;break}this._userLevelBuy(e)},_userLevelBuy:function(e){var o=this;return(0,c.default)(i.default.mark((function n(){var c,s;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,o.$wxapi.userLevelBuy(o.token,e);case 2:c=n.sent,s=!0,n.t0=e,n.next=553===n.t0?7:554===n.t0?9:555===n.t0?11:13;break;case 7:return o.balance<1e3&&(t.showToast({title:"余额不足1000",icon:"error"}),s=!1),n.abrupt("break",13);case 9:return o.balance<3e3&&(t.showToast({title:"余额不足3000",icon:"error"}),s=!1),n.abrupt("break",13);case 11:return o.balance<6e3&&(t.showToast({title:"余额不足6000",icon:"error"}),s=!1),n.abrupt("break",13);case 13:!0===s&&(0==c.code?t.showToast({title:"升级成功",icon:"none"}):2e4==c.code&&t.showToast({title:c.msg,icon:"none"}));case 14:case"end":return n.stop()}}),n)})))()},_getUserAmount:function(){var t=this;return(0,c.default)(i.default.mark((function e(){var o;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$wxapi.userAmount(t.token);case 2:o=e.sent,0==o.code&&(t.balance=o.data.balance.toFixed(2));case 4:case"end":return e.stop()}}),e)})))()},_userLevelDetail:function(){var t=this;return(0,c.default)(i.default.mark((function e(){var o;return i.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$wxapi.userLevelDetail(t.levelId);case 2:o=e.sent,0==o.code&&(t.memberLevelDetail=o.data,console.log("memberLevelDetail is",t.memberLevelDetail),o.data.info.level,t.memberEqual=!1);case 4:case"end":return e.stop()}}),e)})))()},tipClick:function(t){},changeItem:function(t){this.current=t.detail.current,this.memberLevel="member"+(t.detail.current+1),0===this.current&&(this.levelId=5596,this.equity=[{icon:"",text:"专属特价",desc:"全场9.5折(预购、抢购等活动除外)",position:"bottom-start"},{icon:"",text:"优先配送",desc:"优先配送免排队，好食材快人一步",position:"bottom-start"},{icon:"",text:"专属客服",desc:"贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题",position:"bottom-start"},{icon:"",text:"新品先购",desc:"野贝农上市新品之前，优先获得选购权",position:"bottom-end"}],this.open=[{icon:"",text:"购买轻享会员年卡",desc:"费用为188元/年",class:"icon-color-1"},{icon:"",text:"预存",desc:"在野贝农小程序平台充值预存1000元",class:"icon-color-1"}]),1===this.current&&(this.levelId=5597,this.equity=[{icon:"",text:"专属特价",desc:"全场9折(预购、抢购等活动除外)",position:"bottom-start"},{icon:"",text:"优先配送",desc:"优先配送免排队，好食材快人一步",position:"bottom-start"},{icon:"",text:"专属客服",desc:"贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题",position:"bottom-start"},{icon:"",text:"新品先购",desc:"野贝农上市新品之前，优先获得选购权",position:"bottom-end"},{icon:"",text:"免费试吃",desc:"野贝农新品上市前，都会邀请会员免费试吃",position:"bottom-start"},{icon:"",text:"线下聚会",desc:"有资格参加我们野贝农组织的线下活动：美食艺术家活动",position:"bottom-start"}],this.open=[{icon:"",text:"购买轻享会员年卡",desc:"费用为288元/年",class:"icon-color-1"},{icon:"",text:"预存",desc:"在野贝农小程序平台充值预存3000元",class:"icon-color-1"}]),2===this.current&&(this.levelId=5598,this.equity=[{icon:"",text:"专属特价",desc:"全场8.8折(预购、抢购等活动除外)",position:"bottom-start"},{icon:"",text:"优先配送",desc:"优先配送免排队，好食材快人一步",position:"bottom-start"},{icon:"",text:"专属客服",desc:"贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题",position:"bottom-start"},{icon:"",text:"新品先购",desc:"野贝农上市新品之前，优先获得选购权",position:"bottom-end"},{icon:"",text:"免费试吃",desc:"野贝农新品上市前，都会邀请会员免费试吃",position:"bottom-start"},{icon:"",text:"线下聚会",desc:"有资格参加我们野贝农组织的线下活动：美食艺术家活动",position:"bottom-start"},{icon:"",text:"免费游玩",desc:"作为我们的最高级别的会员，可以受邀免费参加我们深山养殖地游玩活动",position:"bottom-start"},{icon:"",text:"成为合伙人",desc:"做为我们的最高级别的会员，会获得成为我们野贝农渠道合伙人的资格",position:"bottom-end"}],this.open=[{icon:"",text:"购买轻享会员年卡",desc:"费用为388元/年",class:"icon-color-1"},{icon:"",text:"预存",desc:"在野贝农小程序平台充值预存6000元",class:"icon-color-1"}]),this._userLevelDetail()}}};e.default=s}).call(this,o("df3c")["default"])},"8cb8":function(t,e,o){},"8e2b":function(t,e,o){"use strict";o.r(e);var n=o("dc87"),i=o("2cca");for(var c in i)["default"].indexOf(c)<0&&function(t){o.d(e,t,(function(){return i[t]}))}(c);o("b54d"),o("d054");var s=o("828b"),r=Object(s["a"])(i["default"],n["b"],n["c"],!1,null,"1d43bd1d",null,!1,n["a"],void 0);e["default"]=r.exports},b54d:function(t,e,o){"use strict";var n=o("d5e5"),i=o.n(n);i.a},d054:function(t,e,o){"use strict";var n=o("8cb8"),i=o.n(n);i.a},d5e5:function(t,e,o){},dc87:function(t,e,o){"use strict";o.d(e,"b",(function(){return i})),o.d(e,"c",(function(){return c})),o.d(e,"a",(function(){return n}));var n={zbTooltip:function(){return o.e("uni_modules/zb-tooltip/components/zb-tooltip/zb-tooltip").then(o.bind(null,"dffd"))}},i=function(){var t=this.$createElement,e=(this._self._c,this.equity.length);this.$mp.data=Object.assign({},{$root:{g0:e}})},c=[]}},[["2454","common/runtime","common/vendor"]]]);