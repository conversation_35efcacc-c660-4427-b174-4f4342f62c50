<u-popup vue-id="348b313d-1" show="{{show}}" data-event-opts="{{[['^close',[['closeHandler']]]]}}" bind:close="__e" class="data-v-df234be4" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-picker data-v-df234be4"><block wx:if="{{showToolbar}}"><u-toolbar vue-id="{{('348b313d-2')+','+('348b313d-1')}}" cancelColor="{{cancelColor}}" confirmColor="{{confirmColor}}" cancelText="{{cancelText}}" confirmText="{{confirmText}}" title="{{title}}" data-event-opts="{{[['^cancel',[['cancel']]],['^confirm',[['confirm']]]]}}" bind:cancel="__e" bind:confirm="__e" class="data-v-df234be4" bind:__l="__l"></u-toolbar></block><picker-view class="u-picker__view data-v-df234be4" style="{{'height:'+(''+$root.g0)+';'}}" indicatorStyle="{{'height: '+$root.g1}}" value="{{innerIndex}}" immediateChange="{{immediateChange}}" data-event-opts="{{[['change',[['changeHandler',['$event']]]]]}}" bindchange="__e"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><picker-view-column class="u-picker__view__column data-v-df234be4"><block wx:for="{{item.l0}}" wx:for-item="item1" wx:for-index="index1" wx:key="index1"><block wx:if="{{item.g2}}"><text class="u-picker__view__column__item u-line-1 data-v-df234be4" style="{{'height:'+(item.g3)+';'+('line-height:'+(item.g4)+';')+('font-weight:'+(index1===innerIndex[index]?'bold':'normal')+';')}}">{{item1.m0}}</text></block></block></picker-view-column></block></picker-view><block wx:if="{{loading}}"><view class="u-picker--loading data-v-df234be4"><u-loading-icon vue-id="{{('348b313d-3')+','+('348b313d-1')}}" mode="circle" class="data-v-df234be4" bind:__l="__l"></u-loading-icon></view></block></view></u-popup>