{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list-card-physical-tip.vue?0ce9", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list-card-physical-tip.vue?cb65", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list-card-physical-tip.vue?8235", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list-card-physical-tip.vue?5e27", "uni-app:///components/list/list-card-physical-tip.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list-card-physical-tip.vue?5dc0", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list-card-physical-tip.vue?3e79"], "names": ["components", "listCardPhysicalTip", "props", "list", "type", "default", "onReady", "data", "watch", "methods", "imageClick", "uni", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC0E;AACL;AACa;;;AAGlF;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,4FAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3CA;AAAA;AAAA;AAAA;AAAirB,CAAgB,isBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACmBrsB;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAA;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAD;MACAA;MACAC;IACA;EACA;EACAC;EACAC;IACA;EACA;EACAC;IACAL;MACA;IAAA;EAEA;EACAM;IACAC;MACA;MACA;QACAC;UACAC;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAA69B,CAAgB,g9BAAG,EAAC,C;;;;;;;;;;;ACAj/B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/list/list-card-physical-tip.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./list-card-physical-tip.vue?vue&type=template&id=45f45979&\"\nvar renderjs\nimport script from \"./list-card-physical-tip.vue?vue&type=script&lang=js&\"\nexport * from \"./list-card-physical-tip.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list-card-physical-tip.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/list/list-card-physical-tip.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-card-physical-tip.vue?vue&type=template&id=45f45979&\"", "var components\ntry {\n  components = {\n    uRow: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-row/u-row\" */ \"@/uni_modules/uview-ui/components/u-row/u-row.vue\"\n      )\n    },\n    uCol: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-col/u-col\" */ \"@/uni_modules/uview-ui/components/u-col/u-col.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-image/u-image\" */ \"@/uni_modules/uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-card-physical-tip.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-card-physical-tip.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"list2\">\r\n    <view v-for=\"(item, index) in list\" :key=\"index\" slot=\"slot{{index}}\">\r\n      <view class=\"item-wrapper\">\r\n        <u-row justify=\"space-between\" gutter=\"10\">\r\n          <u-col span=\"4\">\r\n            <u-image width=\"180rpx\" height=\"180rpx\" :src=\"item.pic\" radius=\"10\"></u-image>\r\n          </u-col>\r\n          <u-col span=\"8\">\r\n            <listCardPhysicalTip :item=\"item\"></listCardPhysicalTip>\r\n          </u-col>\r\n        </u-row>\r\n      </view>\r\n    </view>\r\n  </view>\r\n\r\n</template>\r\n\r\n<script>\r\n  import empty from 'empty-value'\r\n  import listCardPhysicalTip from '@/components/list-item/list-card-physical-tip.vue'\r\n\r\n  export default {\r\n    components: {\r\n      listCardPhysicalTip\r\n    },\r\n    props: {\r\n      list: {\r\n        type: Array,\r\n        default: [],\r\n      },\r\n      type: {\r\n        type: String,\r\n        default: '',\r\n      },\r\n    },\r\n    onReady() {},\r\n    data() {\r\n      return {}\r\n    },\r\n    watch: {\r\n      list: function(val) {\r\n        //console.log('watch list is', val)\r\n      }\r\n    },\r\n    methods: {\r\n      imageClick(item) {\r\n        let that = this\r\n        if (that.type === 'goods') {\r\n          uni.navigateTo({\r\n            url: '/pages/goods/detail?id=' + item.id\r\n          })\r\n        }\r\n      }\r\n    },\r\n  }\r\n</script>\r\n<style>\r\n  .list2 {\r\n    padding: 20rpx 0;\r\n  }\r\n\r\n  .item-wrapper {\r\n    border-radius: 20rpx;\r\n    background: #FFFFFF;\r\n    color: #2C3E50;\r\n    padding: 16rpx;\r\n    margin-bottom: 20rpx;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-card-physical-tip.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-card-physical-tip.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688710564\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}