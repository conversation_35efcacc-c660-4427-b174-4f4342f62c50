{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-col/u-col.vue?2035", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-col/u-col.vue?cf63", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-col/u-col.vue?c4ea", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-col/u-col.vue?79ba", "uni-app:///uni_modules/uview-ui/components/u-col/u-col.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-col/u-col.vue?dac9", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-col/u-col.vue?042d"], "names": ["name", "mixins", "data", "width", "parentData", "gutter", "gridNum", "computed", "uJustify", "uAlignItem", "colStyle", "paddingLeft", "paddingRight", "alignItems", "justifyContent", "textAlign", "flex", "marginLeft", "mounted", "methods", "init", "updateParentData", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,grBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACeprB;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,eAaA;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;QACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACA,yFACA,+FACA;IACA;IACAC;MACA;MACA,mDACA;IACA;IACAC;MACA;QACA;QACAC;QACAC;QACAC;QACAC;QACAC;QAEA;QACAC;QACAC;MAOA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1FA;AAAA;AAAA;AAAA;AAAuxC,CAAgB,2uCAAG,EAAC,C;;;;;;;;;;;ACA3yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-col/u-col.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-col.vue?vue&type=template&id=498e50fb&scoped=true&\"\nvar renderjs\nimport script from \"./u-col.vue?vue&type=script&lang=js&\"\nexport * from \"./u-col.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-col.vue?vue&type=style&index=0&id=498e50fb&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"498e50fb\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-col/u-col.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-col.vue?vue&type=template&id=498e50fb&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.colStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-col.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-col.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t    class=\"u-col\"\r\n\t\tref=\"u-col\"\r\n\t    :class=\"[\r\n\t\t\t'u-col-' + span\r\n\t\t]\"\r\n\t    :style=\"[colStyle]\"\r\n\t    @tap=\"clickHandler\"\r\n\t>\r\n\t\t<slot></slot>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t/**\r\n\t * CodeInput 栅格系统的列 \r\n\t * @description 该组件一般用于Layout 布局 通过基础的 12 分栏，迅速简便地创建布局\r\n\t * @tutorial https://www.uviewui.com/components/Layout.html\r\n\t * @property {String | Number}\tspan\t\t栅格占据的列数，总12等份 (默认 12 ) \r\n\t * @property {String | Number}\toffset\t\t分栏左边偏移，计算方式与span相同 (默认 0 ) \r\n\t * @property {String}\t\t\tjustify\t\t水平排列方式，可选值为`start`(或`flex-start`)、`end`(或`flex-end`)、`center`、`around`(或`space-around`)、`between`(或`space-between`)  (默认 'start' ) \r\n\t * @property {String}\t\t\talign\t\t垂直对齐方式，可选值为top、center、bottom、stretch (默认 'stretch' ) \r\n\t * @property {String}\t\t\ttextAlign\t文字水平对齐方式 (默认 'left' ) \r\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\r\n\t * @event {Function}\tclick\tcol被点击，会阻止事件冒泡到row\r\n\t * @example\t <u-col  span=\"3\" offset=\"3\" > <view class=\"demo-layout bg-purple\"></view> </u-col>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-col',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\twidth: 0,\r\n\t\t\t\tparentData: {\r\n\t\t\t\t\tgutter: 0\r\n\t\t\t\t},\r\n\t\t\t\tgridNum: 12\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tuJustify() {\r\n\t\t\t\tif (this.justify == 'end' || this.justify == 'start') return 'flex-' + this.justify\r\n\t\t\t\telse if (this.justify == 'around' || this.justify == 'between') return 'space-' + this.justify\r\n\t\t\t\telse return this.justify\r\n\t\t\t},\r\n\t\t\tuAlignItem() {\r\n\t\t\t\tif (this.align == 'top') return 'flex-start'\r\n\t\t\t\tif (this.align == 'bottom') return 'flex-end'\r\n\t\t\t\telse return this.align\r\n\t\t\t},\r\n\t\t\tcolStyle() {\r\n\t\t\t\tconst style = {\r\n\t\t\t\t\t// 这里写成\"padding: 0 10px\"的形式是因为nvue的需要\r\n\t\t\t\t\tpaddingLeft: uni.$u.addUnit(uni.$u.getPx(this.parentData.gutter)/2),\r\n\t\t\t\t\tpaddingRight: uni.$u.addUnit(uni.$u.getPx(this.parentData.gutter)/2),\r\n\t\t\t\t\talignItems: this.uAlignItem,\r\n\t\t\t\t\tjustifyContent: this.uJustify,\r\n\t\t\t\t\ttextAlign: this.textAlign,\r\n\t\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t\t// 在非nvue上，使用百分比形式\r\n\t\t\t\t\tflex: `0 0 ${100 / this.gridNum * this.span}%`,\r\n\t\t\t\t\tmarginLeft: 100 / 12 * this.offset + '%',\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\t// 在nvue上，由于无法使用百分比单位，这里需要获取父组件的宽度，再计算得出该有对应的百分比尺寸\r\n\t\t\t\t\twidth: uni.$u.addUnit(Math.floor(this.width / this.gridNum * Number(this.span))),\r\n\t\t\t\t\tmarginLeft: uni.$u.addUnit(Math.floor(this.width / this.gridNum * Number(this.offset))),\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync init() {\r\n\t\t\t\t// 支付宝小程序不支持provide/inject，所以使用这个方法获取整个父组件，在created定义，避免循环引用\r\n\t\t\t\tthis.updateParentData()\r\n\t\t\t\tthis.width = await this.parent.getComponentWidth()\r\n\t\t\t},\r\n\t\t\tupdateParentData() {\r\n\t\t\t\tthis.getParentData('u-row')\r\n\t\t\t},\r\n\t\t\tclickHandler(e) {\r\n\t\t\t\tthis.$emit('click');\r\n\t\t\t}\r\n\t\t},\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-col {\r\n\t\tpadding: 0;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing:border-box;\r\n\t\t/* #endif */\r\n\t\t/* #ifdef MP */\r\n\t\tdisplay: block;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t// nvue下百分比无效\r\n\t/* #ifndef APP-NVUE */\r\n\t.u-col-0 {\r\n\t\twidth: 0;\r\n\t}\r\n\r\n\t.u-col-1 {\r\n\t\twidth: calc(100%/12);\r\n\t}\r\n\r\n\t.u-col-2 {\r\n\t\twidth: calc(100%/12 * 2);\r\n\t}\r\n\r\n\t.u-col-3 {\r\n\t\twidth: calc(100%/12 * 3);\r\n\t}\r\n\r\n\t.u-col-4 {\r\n\t\twidth: calc(100%/12 * 4);\r\n\t}\r\n\r\n\t.u-col-5 {\r\n\t\twidth: calc(100%/12 * 5);\r\n\t}\r\n\r\n\t.u-col-6 {\r\n\t\twidth: calc(100%/12 * 6);\r\n\t}\r\n\r\n\t.u-col-7 {\r\n\t\twidth: calc(100%/12 * 7);\r\n\t}\r\n\r\n\t.u-col-8 {\r\n\t\twidth: calc(100%/12 * 8);\r\n\t}\r\n\r\n\t.u-col-9 {\r\n\t\twidth: calc(100%/12 * 9);\r\n\t}\r\n\r\n\t.u-col-10 {\r\n\t\twidth: calc(100%/12 * 10);\r\n\t}\r\n\r\n\t.u-col-11 {\r\n\t\twidth: calc(100%/12 * 11);\r\n\t}\r\n\r\n\t.u-col-12 {\r\n\t\twidth: calc(100%/12 * 12);\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-col.vue?vue&type=style&index=0&id=498e50fb&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-col.vue?vue&type=style&index=0&id=498e50fb&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688733914\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}