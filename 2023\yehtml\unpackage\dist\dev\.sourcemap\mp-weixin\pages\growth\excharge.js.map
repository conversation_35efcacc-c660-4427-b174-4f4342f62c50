{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/growth/excharge.vue?e0c6", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/growth/excharge.vue?ee40", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/growth/excharge.vue?4399", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/growth/excharge.vue?2960", "uni-app:///pages/growth/excharge.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "rules", "deductionScore", "required", "type", "message", "trigger", "form", "created", "mounted", "onReady", "onLoad", "onShow", "methods", "submit", "uni", "title", "icon", "_submit", "res", "console", "setTimeout"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;;;AAGvD;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,aAAa,gUAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCgBvrB;EACAC;IACA;MACAC;QACAC;UACAC;UACAC;UACAC;UACA;UACAC;QACA;MACA;MACAC;QACAL;MACA;IACA;EACA;EACAM,6BAEA;EACAC,6BAEA;EACAC;IACA;EACA;EACAC,4BAEA;EACAC,2BAEA;EACAC;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACA;gBACA;kBACAC;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAC;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBACAJ;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAGAF;kBACAC;gBACA;gBACAK;kBACAN;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B", "file": "pages/growth/excharge.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/growth/excharge.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./excharge.vue?vue&type=template&id=493b8586&scoped=true&\"\nvar renderjs\nimport script from \"./excharge.vue?vue&type=script&lang=js&\"\nexport * from \"./excharge.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"493b8586\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/growth/excharge.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./excharge.vue?vue&type=template&id=493b8586&scoped=true&\"", "var components\ntry {\n  components = {\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./excharge.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./excharge.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"form-box\">\r\n\t\t\t<u-form ref=\"uForm\" label-width=\"130rpx\" :model=\"form\">\r\n\t\t\t\t<u-form-item label=\"消耗积分\" prop=\"deductionScore\" required>\r\n\t\t\t\t\t<u-input v-model=\"form.deductionScore\" type=\"number\" clearable focus placeholder=\"请输入积分数量\"></u-input>\r\n\t\t\t\t</u-form-item>\r\n\t\t\t</u-form>\r\n\t\t</view>\r\n\t\t<view class=\"submit-btn\">\r\n\t\t\t<u-button type=\"success\" @click=\"submit\">立即兑换</u-button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\trules: {\r\n\t\t\t\t\tdeductionScore: [{\r\n\t\t\t\t\t\trequired: true,\r\n\t\t\t\t\t\ttype: 'number',\r\n\t\t\t\t\t\tmessage: '不能为空',\r\n\t\t\t\t\t\t// 可以单个或者同时写两个触发验证方式 \r\n\t\t\t\t\t\ttrigger: ['change', 'blur'],\r\n\t\t\t\t\t}],\r\n\t\t\t\t},\r\n\t\t\t\tform: {\r\n\t\t\t\t\tdeductionScore: undefined\r\n\t\t\t\t},\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\tthis.$refs.uForm.setRules(this.rules);\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\t\r\n\t\t},\r\n\t\tonShow() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync submit() {\r\n\t\t\t\tthis.$refs.uForm.validate().then(res => {\r\n\t\t\t\t\tthis._submit()\r\n\t\t\t\t}).catch(errors => {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '表单请填写完整',\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync _submit() {\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/sq3tzp\r\n\t\t\t\tconst res = await this.$wxapi.exchangeScoreToGrowth(this.token, this.form.deductionScore)\r\n\t\t\t\tconsole.log(res);\r\n\t\t\t\tif(res.code != 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '兑换成功'\r\n\t\t\t\t})\r\n\t\t\t\tsetTimeout(function() {\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t}, 1000);\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\r\n</style>\r\n"], "sourceRoot": ""}