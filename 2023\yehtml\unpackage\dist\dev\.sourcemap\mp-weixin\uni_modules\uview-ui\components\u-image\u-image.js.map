{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-image/u-image.vue?7dc2", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-image/u-image.vue?d293", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-image/u-image.vue?0f98", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-image/u-image.vue?a02c", "uni-app:///uni_modules/uview-ui/components/u-image/u-image.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-image/u-image.vue?02e8", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-image/u-image.vue?a343"], "names": ["name", "mixins", "data", "isError", "loading", "opacity", "durationTime", "backgroundStyle", "show", "watch", "src", "immediate", "handler", "computed", "wrapStyle", "style", "mounted", "methods", "onClick", "onError<PERSON><PERSON><PERSON>", "onLoadHandler", "removeBgColor", "backgroundColor"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sUAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9EA;AAAA;AAAA;AAAA;AAAkqB,CAAgB,krBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACkEtrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAzBA,eA0BA;EACAA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;UACA;UACA;QAEA;UACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACAC;MACAA;MACA;MACAA;MACA;MACAA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IAEA;EACA;EACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjMA;AAAA;AAAA;AAAA;AAAyxC,CAAgB,6uCAAG,EAAC,C;;;;;;;;;;;ACA7yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-image/u-image.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-image.vue?vue&type=template&id=042b391e&scoped=true&\"\nvar renderjs\nimport script from \"./u-image.vue?vue&type=script&lang=js&\"\nexport * from \"./u-image.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-image.vue?vue&type=style&index=0&id=042b391e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"042b391e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-image/u-image.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-image.vue?vue&type=template&id=042b391e&scoped=true&\"", "var components\ntry {\n  components = {\n    uTransition: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-transition/u-transition\" */ \"@/uni_modules/uview-ui/components/u-transition/u-transition.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.wrapStyle, _vm.backgroundStyle])\n  var g0 =\n    !_vm.isError && !(_vm.shape == \"circle\") ? _vm.$u.addUnit(_vm.radius) : null\n  var g1 = !_vm.isError ? _vm.$u.addUnit(_vm.width) : null\n  var g2 = !_vm.isError ? _vm.$u.addUnit(_vm.height) : null\n  var g3 =\n    _vm.showLoading && _vm.loading && !(_vm.shape == \"circle\")\n      ? _vm.$u.addUnit(_vm.radius)\n      : null\n  var g4 = _vm.showLoading && _vm.loading ? _vm.$u.addUnit(_vm.width) : null\n  var g5 = _vm.showLoading && _vm.loading ? _vm.$u.addUnit(_vm.height) : null\n  var g6 =\n    _vm.showError && _vm.isError && !_vm.loading && !(_vm.shape == \"circle\")\n      ? _vm.$u.addUnit(_vm.radius)\n      : null\n  var g7 =\n    _vm.showError && _vm.isError && !_vm.loading\n      ? _vm.$u.addUnit(_vm.width)\n      : null\n  var g8 =\n    _vm.showError && _vm.isError && !_vm.loading\n      ? _vm.$u.addUnit(_vm.height)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1,\n        g2: g2,\n        g3: g3,\n        g4: g4,\n        g5: g5,\n        g6: g6,\n        g7: g7,\n        g8: g8,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-image.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-image.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<u-transition\r\n\t\tmode=\"fade\"\r\n\t\t:show=\"show\"\r\n\t\t:duration=\"fade ? 1000 : 0\"\r\n\t>\r\n\t\t<view\r\n\t\t\tclass=\"u-image\"\r\n\t\t\t@tap=\"onClick\"\r\n\t\t\t:style=\"[wrapStyle, backgroundStyle]\"\r\n\t\t>\r\n\t\t\t<image\r\n\t\t\t\tv-if=\"!isError\"\r\n\t\t\t\t:src=\"src\"\r\n\t\t\t\t:mode=\"mode\"\r\n\t\t\t\t@error=\"onErrorHandler\"\r\n\t\t\t\t@load=\"onLoadHandler\"\r\n\t\t\t\t:show-menu-by-longpress=\"showMenuByLongpress\"\r\n\t\t\t\t:lazy-load=\"lazyLoad\"\r\n\t\t\t\tclass=\"u-image__image\"\r\n\t\t\t\t:style=\"{\r\n\t\t\t\t\tborderRadius: shape == 'circle' ? '10000px' : $u.addUnit(radius),\r\n\t\t\t\t\twidth: $u.addUnit(width),\r\n\t\t\t\t\theight: $u.addUnit(height)\r\n\t\t\t\t}\"\r\n\t\t\t></image>\r\n\t\t\t<view\r\n\t\t\t\tv-if=\"showLoading && loading\"\r\n\t\t\t\tclass=\"u-image__loading\"\r\n\t\t\t\t:style=\"{\r\n\t\t\t\t\tborderRadius: shape == 'circle' ? '50%' : $u.addUnit(radius),\r\n\t\t\t\t\tbackgroundColor: this.bgColor,\r\n\t\t\t\t\twidth: $u.addUnit(width),\r\n\t\t\t\t\theight: $u.addUnit(height)\r\n\t\t\t\t}\"\r\n\t\t\t>\r\n\t\t\t\t<slot name=\"loading\">\r\n\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t:name=\"loadingIcon\"\r\n\t\t\t\t\t\t:width=\"width\"\r\n\t\t\t\t\t\t:height=\"height\"\r\n\t\t\t\t\t></u-icon>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t\t<view\r\n\t\t\t\tv-if=\"showError && isError && !loading\"\r\n\t\t\t\tclass=\"u-image__error\"\r\n\t\t\t\t:style=\"{\r\n\t\t\t\t\tborderRadius: shape == 'circle' ? '50%' : $u.addUnit(radius),\r\n\t\t\t\t\twidth: $u.addUnit(width),\r\n\t\t\t\t\theight: $u.addUnit(height)\r\n\t\t\t\t}\"\r\n\t\t\t>\r\n\t\t\t\t<slot name=\"error\">\r\n\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t:name=\"errorIcon\"\r\n\t\t\t\t\t\t:width=\"width\"\r\n\t\t\t\t\t\t:height=\"height\"\r\n\t\t\t\t\t></u-icon>\r\n\t\t\t\t</slot>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</u-transition>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t/**\r\n\t * Image 图片\r\n\t * @description 此组件为uni-app的image组件的加强版，在继承了原有功能外，还支持淡入动画、加载中、加载失败提示、圆角值和形状等。\r\n\t * @tutorial https://uviewui.com/components/image.html\r\n\t * @property {String}\t\t\tsrc \t\t\t\t图片地址\r\n\t * @property {String}\t\t\tmode \t\t\t\t裁剪模式，见官网说明 （默认 'aspectFill' ）\r\n\t * @property {String | Number}\twidth \t\t\t\t宽度，单位任意，如果为数值，则为px单位 （默认 '300' ）\r\n\t * @property {String | Number}\theight \t\t\t\t高度，单位任意，如果为数值，则为px单位 （默认 '225' ）\r\n\t * @property {String}\t\t\tshape \t\t\t\t图片形状，circle-圆形，square-方形 （默认 'square' ）\r\n\t * @property {String | Number}\tradius\t\t \t\t圆角值，单位任意，如果为数值，则为px单位 （默认 0 ）\r\n\t * @property {Boolean}\t\t\tlazyLoad\t\t\t是否懒加载，仅微信小程序、App、百度小程序、字节跳动小程序有效 （默认 true ）\r\n\t * @property {Boolean}\t\t\tshowMenuByLongpress\t是否开启长按图片显示识别小程序码菜单，仅微信小程序有效 （默认 true ）\r\n\t * @property {String}\t\t\tloadingIcon \t\t加载中的图标，或者小图片 （默认 'photo' ）\r\n\t * @property {String}\t\t\terrorIcon \t\t\t加载失败的图标，或者小图片 （默认 'error-circle' ）\r\n\t * @property {Boolean}\t\t\tshowLoading \t\t是否显示加载中的图标或者自定义的slot （默认 true ）\r\n\t * @property {Boolean}\t\t\tshowError \t\t\t是否显示加载错误的图标或者自定义的slot （默认 true ）\r\n\t * @property {Boolean}\t\t\tfade \t\t\t\t是否需要淡入效果 （默认 true ）\r\n\t * @property {Boolean}\t\t\twebp \t\t\t\t只支持网络资源，只对微信小程序有效 （默认 false ）\r\n\t * @property {String | Number}\tduration \t\t\t搭配fade参数的过渡时间，单位ms （默认 500 ）\r\n\t * @property {String}\t\t\tbgColor \t\t\t背景颜色，用于深色页面加载图片时，为了和背景色融合  (默认 '#f3f4f6' )\r\n\t * @property {Object}\t\t\tcustomStyle  \t\t定义需要用到的外部样式\r\n\t * @event {Function}\tclick\t点击图片时触发\r\n\t * @event {Function}\terror\t图片加载失败时触发\r\n\t * @event {Function} load 图片加载成功时触发\r\n\t * @example <u-image width=\"100%\" height=\"300px\" :src=\"src\"></u-image>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-image',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// 图片是否加载错误，如果是，则显示错误占位图\r\n\t\t\t\tisError: false,\r\n\t\t\t\t// 初始化组件时，默认为加载中状态\r\n\t\t\t\tloading: true,\r\n\t\t\t\t// 不透明度，为了实现淡入淡出的效果\r\n\t\t\t\topacity: 1,\r\n\t\t\t\t// 过渡时间，因为props的值无法修改，故需要一个中间值\r\n\t\t\t\tdurationTime: this.duration,\r\n\t\t\t\t// 图片加载完成时，去掉背景颜色，因为如果是png图片，就会显示灰色的背景\r\n\t\t\t\tbackgroundStyle: {},\r\n\t\t\t\t// 用于fade模式的控制组件显示与否\r\n\t\t\t\tshow: false\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tsrc: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(n) {\r\n\t\t\t\t\tif (!n) {\r\n\t\t\t\t\t\t// 如果传入null或者''，或者false，或者undefined，标记为错误状态\r\n\t\t\t\t\t\tthis.isError = true\r\n\t\t\t\t\t\t\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.isError = false;\r\n\t\t\t\t\t\tthis.loading = true;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\twrapStyle() {\r\n\t\t\t\tlet style = {};\r\n\t\t\t\t// 通过调用addUnit()方法，如果有单位，如百分比，px单位等，直接返回，如果是纯粹的数值，则加上rpx单位\r\n\t\t\t\tstyle.width = this.$u.addUnit(this.width);\r\n\t\t\t\tstyle.height = this.$u.addUnit(this.height);\r\n\t\t\t\t// 如果是显示圆形，设置一个很多的半径值即可\r\n\t\t\t\tstyle.borderRadius = this.shape == 'circle' ? '10000px' : uni.$u.addUnit(this.radius)\r\n\t\t\t\t// 如果设置圆角，必须要有hidden，否则可能圆角无效\r\n\t\t\t\tstyle.overflow = this.borderRadius > 0 ? 'hidden' : 'visible'\r\n\t\t\t\t// if (this.fade) {\r\n\t\t\t\t// \tstyle.opacity = this.opacity\r\n\t\t\t\t// \t// nvue下，这几个属性必须要分开写\r\n\t\t\t\t// \tstyle.transitionDuration = `${this.durationTime}ms`\r\n\t\t\t\t// \tstyle.transitionTimingFunction = 'ease-in-out'\r\n\t\t\t\t// \tstyle.transitionProperty = 'opacity'\r\n\t\t\t\t// }\r\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle));\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.show = true\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 点击图片\r\n\t\t\tonClick() {\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t},\r\n\t\t\t// 图片加载失败\r\n\t\t\tonErrorHandler(err) {\r\n\t\t\t\tthis.loading = false\r\n\t\t\t\tthis.isError = true\r\n\t\t\t\tthis.$emit('error', err)\r\n\t\t\t},\r\n\t\t\t// 图片加载完成，标记loading结束\r\n\t\t\tonLoadHandler(event) {\r\n\t\t\t\tthis.loading = false\r\n\t\t\t\tthis.isError = false\r\n\t\t\t\tthis.$emit('load', event)\r\n\t\t\t\tthis.removeBgColor()\r\n\t\t\t\t// 如果不需要动画效果，就不执行下方代码，同时移除加载时的背景颜色\r\n\t\t\t\t// 否则无需fade效果时，png图片依然能看到下方的背景色\r\n\t\t\t\t// if (!this.fade) return this.removeBgColor();\r\n\t\t\t\t// // 原来opacity为1(不透明，是为了显示占位图)，改成0(透明，意味着该元素显示的是背景颜色，默认的灰色)，再改成1，是为了获得过渡效果\r\n\t\t\t\t// this.opacity = 0;\r\n\t\t\t\t// // 这里设置为0，是为了图片展示到背景全透明这个过程时间为0，延时之后延时之后重新设置为duration，是为了获得背景透明(灰色)\r\n\t\t\t\t// // 到图片展示的过程中的淡入效果\r\n\t\t\t\t// this.durationTime = 0;\r\n\t\t\t\t// // 延时50ms，否则在浏览器H5，过渡效果无效\r\n\t\t\t\t// setTimeout(() => {\r\n\t\t\t\t// \tthis.durationTime = this.duration;\r\n\t\t\t\t// \tthis.opacity = 1;\r\n\t\t\t\t// \tsetTimeout(() => {\r\n\t\t\t\t// \t\tthis.removeBgColor();\r\n\t\t\t\t// \t}, this.durationTime);\r\n\t\t\t\t// }, 50);\r\n\t\t\t},\r\n\t\t\t// 移除图片的背景色\r\n\t\t\tremoveBgColor() {\r\n\t\t\t\t// 淡入动画过渡完成后，将背景设置为透明色，否则png图片会看到灰色的背景\r\n\t\t\t\tthis.backgroundStyle = {\r\n\t\t\t\t\tbackgroundColor: 'transparent'\r\n\t\t\t\t};\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import '../../libs/css/components.scss';\r\n\r\n\t$u-image-error-top:0px !default;\r\n\t$u-image-error-left:0px !default;\r\n\t$u-image-error-width:100% !default;\r\n\t$u-image-error-hight:100% !default;\r\n\t$u-image-error-background-color:$u-bg-color !default;\r\n\t$u-image-error-color:$u-tips-color !default;\r\n\t$u-image-error-font-size: 46rpx !default;\r\n\r\n\t.u-image {\r\n\t\tposition: relative;\r\n\t\ttransition: opacity 0.5s ease-in-out;\r\n\r\n\t\t&__image {\r\n\t\t\twidth: 100%;\r\n\t\t\theight: 100%;\r\n\t\t}\r\n\r\n\t\t&__loading,\r\n\t\t&__error {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: $u-image-error-top;\r\n\t\t\tleft: $u-image-error-left;\r\n\t\t\twidth: $u-image-error-width;\r\n\t\t\theight: $u-image-error-hight;\r\n\t\t\t@include flex;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tbackground-color: $u-image-error-background-color;\r\n\t\t\tcolor: $u-image-error-color;\r\n\t\t\tfont-size: $u-image-error-font-size;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-image.vue?vue&type=style&index=0&id=042b391e&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-image.vue?vue&type=style&index=0&id=042b391e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692292698\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}