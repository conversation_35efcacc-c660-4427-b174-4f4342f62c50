<view class="waterfalls-flow data-v-4735c36b"><block wx:for="{{$root.l1}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="waterfalls-flow-column data-v-4735c36b" style="{{'width:'+(w)+';'+('margin-left:'+(index==0?0:m)+';')}}" id="{{'waterfalls_flow_column_'+(index+1)}}" msg="{{msg}}"><block wx:for="{{item.l0}}" wx:for-item="item2" wx:for-index="index2" wx:key="index2"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item2})}}" class="{{['data-v-4735c36b','column-value',[(item2.o)?'column-value-show':'']]}}" style="{{item.s0}}" catchtap="__e"><block wx:if="{{data.seat==1}}"><view class="inner data-v-4735c36b"><slot name="slot{{item2.index}}"></slot></view></block><image class="{{['data-v-4735c36b','img',[(item2[hideImageKey]==true||item2[hideImageKey]==1)?'img-hide':''],[(!item2[data.imageKey])?'img-error':'']]}}" src="{{item2[data.imageKey]}}" mode="widthFix" data-event-opts="{{[['load',[['e1',['$event']]]],['error',[['e2',['$event']]]],['tap',[['e3',['$event']]]]]}}" data-event-params="{{({item2,index,item2,index,item2})}}" bindload="__e" binderror="__e" catchtap="__e"></image><block wx:if="{{data.seat==2}}"><view class="inner data-v-4735c36b"><slot name="slot{{item2.index}}"></slot></view></block></view></block></view></block></view>