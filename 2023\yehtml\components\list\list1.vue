<template>
  <view class="list1">
    <view>
      <custom-waterfalls-flow ref="waterfallsFlowRef" :value="list" :column="2" :columnSpace="1.5" :seat="2" @imageClick="imageClick()">
        <view v-for="(item, index) in list" :key="index" slot="slot{{index}}">
          <view class="badge-box">
            <view v-if="(item.categoryId === 383898 || item.categoryId === 383899 || item.categoryId === 383900 || item.categoryId === 383901 || item.categoryId === 383902 || item.categoryId === 383903 || item.categoryId === 383904 || item.categoryId === 383905) && item.promotionInsert !== 1" class="badge"></view>
          </view>
          <view v-if="item.deliveryTypeUrl" class="delivery-type">
            <img style="width: 45px;height: 25px;" :src="item.deliveryTypeUrl">
          </view>
          <view v-if="item.promotionInsert === 1">
            <listPromotion :item="item"></listPromotion>
          </view>
          <view v-if="item.promotionInsert !== 1">
            <listGoodsItem1 :item="item"></listGoodsItem1>
          </view>
        </view>
      </custom-waterfalls-flow>
    </view>
  </view>
</template>

<script>
  import empty from 'empty-value'
  import listGoodsItem1 from '@/components/list-item/list-goods-item1.vue'
  import listPromotion from '@/components/list-item/list-promotion.vue'

  const TOOLS = require('@/common/tools')
  export default {
    components: {
      listGoodsItem1,
      listPromotion
    },
    props: {
      list: {
        type: Array,
        default: [],
      },
      type: {
        type: String,
        default: '',
      },
    },
    onReady() {},
    data() {
      return {}
    },
    watch: {
      list: function(val) {
        //console.log('watch list is', val)
      }
    },
    methods: {
      imageClick(item) {
        if (!empty(item.promotionInsert)) {
          let that = this
          uni.navigateTo({
            url: item.path
          })
        } else {
          const categoryIds = [383898, 383899, 383900, 383901, 383902, 383903, 383904, 383905]
          if (categoryIds.includes(item.categoryId)) {
            TOOLS.softOpeningTip()
          } else {
            uni.navigateTo({
              url: '/pages/goods/detail?id=' + item.id
            })
          }
        }
      }
    },
  }
</script>
<style>
  .list1 {
    padding: 20rpx 0;
  }

  .badge-box {
    position: relative;
    top: -180px;
    left: 70%;
    margin-bottom: -50px;
    width: 50px;
    height: 50px;
  }

  .badge {
    background: url("https://ye.niutouren.vip/static/images/list/stock-up.png") no-repeat;
    background-size: 50px 50px;
    background-position: center;
    width: 50px;
    height: 50px;
  }

  .delivery-type {
    position: relative;
    bottom: 0;
    left: 0;
    margin-top: -136rpx;
  }
</style>