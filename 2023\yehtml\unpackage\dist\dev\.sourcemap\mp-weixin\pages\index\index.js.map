{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?94d7", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?f23b", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?b05d", "uni-app:///pages/index/index.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?f128", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?9980", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?8a46", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?6e15"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "list1", "three1", "three2", "three3", "tab1", "data", "tabIndex", "apiUserInfoMap", "background", "indicatorDots", "autoplay", "interval", "duration", "headerMarginTopStyle", "kw", "menuButtonInfoStyle", "shopInfo", "banners", "goodsDynamic", "categories", "categories2", "categories3", "notice", "adPosition", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "shichiGoods", "goodsRecommend", "goodsHLD", "goodsSQS", "goodsYBC", "kanjiaList", "pingtuanList", "page", "goods", "adPositionIndexPop", "timeData", "activeTab", "promotionGoods", "promotionGoodsBak", "promotionInsert", "onLoad", "uni", "withShareTicket", "systemInfo", "title", "onShow", "TOOLS", "created", "onShareAppMessage", "path", "onReachBottom", "onPullDownRefresh", "methods", "_goodsPromotion", "_userDetail", "res", "jssdkSign", "j<PERSON><PERSON>", "debug", "appId", "timestamp", "nonceStr", "signature", "jsApiList", "goSearch", "url", "_banners", "type", "icon", "tapBanner", "_goodsDynamic", "_categories", "categorizedData", "acc", "removeItemsFromArray", "categoryClick", "_notice", "_adPosition", "gotoActivity", "goUrl", "noticeclick", "console", "_shichiGoods", "categoryId", "showExtJson", "ele", "_miaoshaGoods", "_kanjiaList", "kanjia", "kanjiaGoodsIds", "goodsKanjiaSetRes", "_pingtuanList", "pingtuan", "_goods", "pageSize", "orderBy", "goodsNew", "good", "insertPromotionGoods", "_goodsRecommend", "extJsonMap", "_goodsHLD", "_goodsSQS", "goCoupons", "shuffle", "randomIndex", "currentIndex", "array", "rotateArray", "arr", "changeTab", "alertTab", "featureClick", "aboutPage", "pic", "tag", "computed", "tabSpan", "showEnterpriseTab"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;AACxB;;;AAGlE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,mTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpEA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,grBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACgNprB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQA;AAAA,eA4BA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MAEAC;MACAC;MACAC;IACA;EACA;EACAC;IAEAC;MACAC;IACA;IAGA;IACA;IACA,4CACAC;IACA;;IAEA;IACA;MACA;MACA;QACA;MACA;IACA;IACAF;MACAG;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAOA;EACAC;IACA;IACA;IACAC;IAEA;MACA;MACA;QACA;QACAL;MACA;IACA;EACA;EACAM;EACAC;IACA;MACAJ;MACAK;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAV;EACA;EACAW;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAZ;kBACA;gBACA;kBACA;oBACA;kBACA;;kBAEA;gBACA;kBACA;gBAAA,CACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAD;gBACA;kBACAE;oBACAC;oBAAA;oBACAC;oBAAA;oBACAC;oBAAA;oBACAC;oBAAA;oBACAC;oBAAA;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACAvB;QACAwB;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAC;gBACA;cAAA;gBAFAZ;gBAGA;kBACA;gBACA;kBACAd;oBACAG;oBACAwB;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;QACA5B;UACAwB;QACA;MACA;IACA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAf;gBACA;kBACA;kBACAA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAgB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAhB;gBACA;kBACAiB;oBACA;sBACAC;oBACA;sBACAA;oBACA;sBACAA;oBACA;oBACA;kBACA;oBACAtD;oBACAC;oBACAC;kBACA;kBAEA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAqD;MACA;MACA;QAAA;MAAA;MACA;IACA;IACAC;MACA;QACAlC;UACAwB,kFACA;QACA;MACA;QACAxB;UACAwB;QACA;MACA;IACA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAArB;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAsB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAtB;gBACA;kBACAA;oBACA;oBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAuB;MACArC;QACAwB;MACA;IACA;IACAc;MACA;MACA;QACAtC;UACAwB;QACA;MACA;IACA;IACAe;MACAC;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAC;gBACA;cAAA;gBAHA7B;gBAIA;kBACAA;oBACA;oBACA;sBACA8B;oBACA;oBACA;sBACAA;oBACA;oBAEA;sBACA;wBACAA;sBACA;oBACA;kBACA;kBAEA5D;kBACAA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA6D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAH;gBACA;cAAA;gBAFA5B;gBAGA;kBACAA;oBACA;oBACA;sBACA8B;oBACA;oBACA;sBACAA;oBACA;kBACA;kBAEA7D;kBACAA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA+D;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAC;gBACA;cAAA;gBAFAjC;gBAAA,MAGAA;kBAAA;kBAAA;gBAAA;gBACAkC;gBACAlC;kBACAkC;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACAnC;oBACA;sBACA;oBACA;oBACA;sBACA8B;sBACAA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAC;gBACA;cAAA;gBAFArC;gBAGA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAsC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;kBACApD;oBACAG;oBACAwB;kBACA;gBACA;;gBAEA;gBAAA;gBAAA,OACA;kBACApC;kBACA8D;kBACAX;kBACAC;kBACAW;gBACA;cAAA;gBANAxC;gBAOA;kBACAsC;kBACA5D;kBACA+D;kBAEAH;kBACAA;kBAEA;oBACA;kBACA;kBAEAxD;kBAEA;oBACA2D;kBACA;kBAEA;oBACAH;kBACA;oBACAA;kBACA;kBAEA;oBACAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCACA;kCACAI;kCACAA;kCAEA;oCACA;sCACAA;sCACA;wCACA;0CACAA,uBACA;wCACA;wCACA;0CACAA,uBACA;wCACA;wCACA;0CACAA,uBACA;wCACA;wCACA;0CACAA,uBACA;wCACA;sCACA;oCACA;kCACA;kCAEAhE;gCACA;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CACA;sBAAA;wBAAA;sBAAA;oBAAA;kBACA;kBAEA;gBACA;kBACA;AACA;AACA;AACA;AACA;AACA;gBALA;cAMA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAiE;MACA;MACAjE;MACA;MACAA;MACA;MACAA;MAEA;IACA;IACAkE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAhB;kBACAC;gBACA;cAAA;gBAHA7B;gBAIA;kBACAsC;kBACA5D;kBACAmE;kBAEAP;kBACA;oBACAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCACAI;gCACAA;gCAEA;kCACA;oCACAA;oCACA;sCACAA;oCACA;kCACA;gCACA;gCAEAhE;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CACA;sBAAA;wBAAA;sBAAA;oBAAA;kBACA;kBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAoE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAlB;kBACAY;gBACA;cAAA;gBAHAxC;gBAIA;kBACAtB;kBACA+D;kBACAH;kBAEAA;kBACAA;kBAEA;oBACA;kBACA;kBACAxD;kBACA;oBACA2D;kBACA;kBAEA;oBACAH;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCACA;kCACAI;kCACAA;kCACAhE;gCACA;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CACA;sBAAA;wBAAA;sBAAA;oBAAA;kBACA;kBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAqE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAnB;gBACA;cAAA;gBAFA5B;gBAGA;kBACAtB;kBACA+D;kBACAH;kBAEAA;kBACAA;kBAEA;oBACA;kBACA;kBACAxD;kBACA;oBACA2D;kBACA;kBAEA;oBACAH;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCACA;kCACAI;kCACAA;kCACAhE;gCACA;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CACA;sBAAA;wBAAA;sBAAA;oBAAA;kBACA;kBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAsE;MACA9D;QACAwB;MACA;IACA;IACAuC;MACA;QACAC;MAEA;QACAA;QACAC;QAAA,YAEA;QAAAC;QAAAA;MACA;MAEA;IACA;IACAC;MACA;QACA;QACAC;MACA;MAEA;IACA;IACAC;MACA;IACA;IACAC;MACAtE;QACAG;QACAwB;MACA;IACA;IACA4C;MACA;MACA;QACAC;QACA;QACA;UACAC;UACAC;UACAvE;UACAuB;QACA;QACA1B;UACAwB;QACA;MACA;MACA;QACAgD;QACA;QACA;UACAC;UACAC;UACAvE;UACAuB;QACA;QACA1B;UACAwB;QACA;MACA;MACA;QACAgD;QACA;QACA;UACAC;UACAC;UACAvE;UACAuB;QACA;QACA1B;UACAwB;QACA;MACA;MAEA;QACAxB;UACAwB;QACA;MACA;IACA;EACA;EACAmD;IACA;IACAC;MAAA;MACA;IACA;IACA;IACAC;MAAA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACl4BA;AAAA;AAAA;AAAA;AAAuxC,CAAgB,2uCAAG,EAAC,C;;;;;;;;;;;ACA3yC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA+vC,CAAgB,mtCAAG,EAAC,C;;;;;;;;;;;ACAnxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\"\nimport style1 from \"./index.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57280228\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=57280228&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-search/u-search\" */ \"@/uni_modules/uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-swiper/u-swiper\" */ \"@/uni_modules/uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uNoticeBar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-notice-bar/u-notice-bar\" */ \"@/uni_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue\"\n      )\n    },\n    uRow: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-row/u-row\" */ \"@/uni_modules/uview-ui/components/u-row/u-row.vue\"\n      )\n    },\n    uCol: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-col/u-col\" */ \"@/uni_modules/uview-ui/components/u-col/u-col.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uOverlay: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-overlay/u-overlay\" */ \"@/uni_modules/uview-ui/components/u-overlay/u-overlay.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.adPositionIndexPop = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\n\t<view class=\"index container-wrapper\">\n\t\t<view class=\"top-box\" :style=\"headerMarginTopStyle\">\n\t\t\t<view class=\"t\">\n\t\t\t\t<image mode=\"aspectFit\" class=\"logo\" src=\"/static/images/logo-navbar.png\"></image>\n\t\t\t</view>\n\t\t\t<view class=\"location\">| 南宁</view>\n\t\t\t<view class=\"search\" @click=\"goSearch\">\n\t\t\t\t<u-search height=\"55rpx\" bgColor=\"#FFFFFF\" placeholder=\"输入关键词搜索\" v-model=\"kw\" :showAction=\"false\"\n\t\t\t\t\t:disabled=\"true\"></u-search>\n\t\t\t</view>\n\t\t\t<!--  #ifdef  MP-WEIXIN || MP-BAIDU || MP-TOUTIAO || MP-QQ -->\n\t\t\t<view class=\"mp-btn\" :style=\"menuButtonInfoStyle\"></view>\n\t\t\t<!--  #endif -->\n\t\t</view>\n\t\t<view class=\"swiper\">\n\t\t\t<u-swiper v-if=\"banners\" :list=\"banners\" indicator circular keyName=\"picUrl\" height=\"375rpx\" bg-color=\"none\"\n\t\t\t\t@click=\"tapBanner\">\n\t\t\t</u-swiper>\n\t\t</view>\n\n\t\t<view class=\"main-wrapper\">\n\t\t\t<!-- 添加公告栏 -->\n\t\t\t<view class=\"notice-bar-wrap\">\n\t\t\t\t<u-notice-bar\n\t\t\t\t\t:text=\"['营业中，欢迎大家下单，同城2小时内送达；当日下单时间区间：昨日19:00至当日19:00，当日超过19:00下单，将于次日配送。当日配送时间区间：当日10:00至当日21:00。']\"\n\t\t\t\t\tmode=\"column\"\n\t\t\t\t\tcolor=\"#fa3534\"\n\t\t\t\t\tbgColor=\"#fdf6ec\"\n\t\t\t\t\t:show-icon=\"true\"\n\t\t\t\t></u-notice-bar>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"feature\">\n\t\t\t\t<u-row gutter=\"0\" justify=\"space-between\">\n\t\t\t\t\t<u-col span=\"3\" @click=\"featureClick('pzbz')\">\n\t\t\t\t\t\t<u-icon size=\"14\" label-size=\"12\" name=\"checkmark-circle\" label=\"品质保证\"></u-icon>\n\t\t\t\t\t</u-col>\n\t\t\t\t\t<u-col span=\"3\" @click=\"featureClick('tgfp')\">\n\t\t\t\t\t\t<u-icon size=\"14\" label-size=\"12\" name=\"rmb-circle\" label=\"提供发票\"></u-icon>\n\t\t\t\t\t</u-col>\n\t\t\t\t\t<u-col span=\"3\" @click=\"featureClick('cspf')\">\n\t\t\t\t\t\t<u-icon size=\"14\" label-size=\"12\" name=\"info-circle\" label=\"超时赔付\"></u-icon>\n\t\t\t\t\t</u-col>\n\t\t\t\t\t<u-col span=\"3\" @click=\"featureClick('jssd')\">\n\t\t\t\t\t\t<u-icon size=\"14\" label-size=\"12\" name=\"car\" label=\"极速送达\"></u-icon>\n\t\t\t\t\t</u-col>\n\t\t\t\t</u-row>\n\t\t\t</view>\n\n\t\t\t<view class=\"category-container\">\n\t\t\t\t<view class=\"category-box\">\n\t\t\t\t\t<view class=\"category-list\" v-for=\"(item, index) in categories\" wx:key=\"index\">\n\t\t\t\t\t\t<view class=\"category-column\" @click=\"categoryClick(item)\">\n\t\t\t\t\t\t\t<image mode=\"aspectFill\" class=\"category-imgbox\" :src=\"item.icon\"></image>\n\t\t\t\t\t\t\t<view class=\"category-title\">{{item.name}}</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"shortcuts-account\">\n\t\t\t\t<u-row gutter=\"4\">\n\t\t\t\t\t<u-col span=\"4\" textAlign=\"center\" @click=\"featureClick('sw')\">\n\t\t\t\t\t\t<view><img src=\"https://ye.niutouren.vip/static/images/index/shortcuts/1.png\" /></view>\n\t\t\t\t\t\t<view>实体礼品卡</view>\n\t\t\t\t\t</u-col>\n\t\t\t\t\t<u-col span=\"4\" textAlign=\"center\" @click=\"featureClick('sz')\">\n\t\t\t\t\t\t<view><img src=\"https://ye.niutouren.vip/static/images/index/shortcuts/2.png\" /></view>\n\t\t\t\t\t\t<view>数字礼品卡</view>\n\t\t\t\t\t</u-col>\n\t\t\t\t\t<u-col span=\"4\" textAlign=\"center\" @click=\"featureClick('sq')\">\n\t\t\t\t\t\t<view><img src=\"https://ye.niutouren.vip/static/images/index/shortcuts/3.png\" /></view>\n\t\t\t\t\t\t<view>山泉礼品卡</view>\n\t\t\t\t\t</u-col>\n\t\t\t\t</u-row>\n\t\t\t</view>\n\n\t\t\t<view v-if=\"shichiGoods\" class=\"miaoshaGoods\">\n\t\t\t\t<view class=\"ttt\">\n\t\t\t\t\t<view class=\"content\">\n\t\t\t\t\t\t<view class=\"left\">\n\t\t\t\t\t\t\t<text>限数试吃</text>\n\t\t\t\t\t\t\t<text class=\"sub\">试吃补贴，评价返利</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"more\" @click=\"gotoActivity('xssc')\"><u-icon name=\"arrow-right\"></u-icon></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view>\n\t\t\t\t\t<three3 :list=\"shichiGoods\"></three3>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- <view v-if=\"miaoshaGoods && apiUserInfoMap.userLevel.level < 1\" class=\"miaoshaGoods\">\n        <view class=\"ttt\">\n          <view class=\"content\">\n            <view class=\"left\">\n              <text>限数抢购</text>\n              <text class=\"sub\">库存尾货 数量有限 超值抢购</text>\n            </view>\n            <view class=\"more\" @click=\"gotoActivity('xsqg')\"><u-icon name=\"arrow-right\"></u-icon></view>\n          </view>\n        </view>\n\n        <view>\n          <three2 :list=\"miaoshaGoods\"></three2>\n        </view>\n      </view> -->\n\n\t\t\t<view v-if=\"goodsRecommend\" class=\"miaoshaGoods\">\n\t\t\t\t<view class=\"ttt\">\n\t\t\t\t\t<view class=\"content\">\n\t\t\t\t\t\t<view class=\"left\">\n\t\t\t\t\t\t\t<text>限时预购</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"more\" @click=\"gotoActivity('xsyg')\"><u-icon name=\"arrow-right\"></u-icon></view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<view>\n\t\t\t\t\t<three1 :list=\"goodsRecommend\"></three1>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view style=\"background: #F8F8F8;padding: 0 0 5px 0;\">\n\t\t\t\t<view class=\"tab1\">\n\t\t\t\t\t<u-row customStyle=\"margin-bottom: 10px\">\n\t\t\t\t\t\t<u-col :span=\"tabSpan\" justify=\"center\" textAlign=\"center\">\n\t\t\t\t\t\t\t<div class=\"tab line\" :class=\"{ active: activeTab === 'tab1' }\" @click=\"changeTab('tab1')\">\n\t\t\t\t\t\t\t\t<div style=\"height: 30rpx;\">&nbsp;</div>\n\t\t\t\t\t\t\t\t<div class=\"title\">土鲜多</div>\n\t\t\t\t\t\t\t\t<div class=\"dsc\">深山好货</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</u-col>\n\t\t\t\t\t\t<u-col :span=\"tabSpan\" justify=\"center\" textAlign=\"center\">\n\t\t\t\t\t\t\t<div class=\"tab line\" :class=\"{ active: activeTab === 'tab2' }\" @click=\"changeTab('tab2')\">\n\t\t\t\t\t\t\t\t<div style=\"height: 30rpx;\">&nbsp;</div>\n\t\t\t\t\t\t\t\t<div class=\"title\">泉水多</div>\n\t\t\t\t\t\t\t\t<div class=\"dsc\">甘甜健康</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</u-col>\n\t\t\t\t\t\t<u-col :span=\"tabSpan\" justify=\"center\" textAlign=\"center\">\n\t\t\t\t\t\t\t<div class=\"tab line\" :class=\"{ active: activeTab === 'tab3' }\" @click=\"changeTab('tab3')\">\n\t\t\t\t\t\t\t\t<div style=\"height: 30rpx;\">&nbsp;</div>\n\t\t\t\t\t\t\t\t<div class=\"title\">好礼多</div>\n\t\t\t\t\t\t\t\t<div class=\"dsc\">礼品多多</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</u-col>\n\t\t\t\t\t\t<u-col v-if=\"showEnterpriseTab\" span=\"3\" justify=\"center\" textAlign=\"center\">\n\t\t\t\t\t\t\t<div class=\"tab\" :class=\"{ active: activeTab === 'tab4' }\" @click=\"changeTab('tab4')\">\n\t\t\t\t\t\t\t\t<div style=\"height: 30rpx;\">&nbsp;</div>\n\t\t\t\t\t\t\t\t<div class=\"title\">企业购</div>\n\t\t\t\t\t\t\t\t<div class=\"dsc\">企业定制</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</u-col>\n\t\t\t\t\t</u-row>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view v-if=\"activeTab === 'tab1'\">\n\t\t\t\t<view v-if=\"goods\" class=\"goodsRecommend\">\n\t\t\t\t\t<view class=\"goods-container\">\n\t\t\t\t\t\t<list1 :list=\"goods\" type=\"goods\"></list1>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view v-if=\"activeTab === 'tab2'\">\n\t\t\t\t<view v-if=\"goodsSQS\" class=\"goodsRecommend\">\n\t\t\t\t\t<view class=\"goods-container\">\n\t\t\t\t\t\t<list1 :list=\"goodsSQS\" type=\"goods\"></list1>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view v-if=\"activeTab === 'tab3'\">\n\t\t\t\t<view v-if=\"goodsHLD\" class=\"goodsRecommend\">\n\t\t\t\t\t<view class=\"goods-container\">\n\t\t\t\t\t\t<list1 :list=\"goodsHLD\" type=\"goods\"></list1>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view v-if=\"activeTab === 'tab4'\">\n\t\t\t\t<view v-if=\"goods\" class=\"goodsRecommend\">\n\t\t\t\t\t<view class=\"goods-container\">\n\t\t\t\t\t\t<list1 :list=\"goods\" type=\"goods\"></list1>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t</view>\n\n\t\t<tabbar :tabIndex=\"tabIndex\"></tabbar>\n\n\t\t<u-overlay v-if=\"adPosition['indexPop']\" :show=\"adPositionIndexPop\">\n\t\t\t<view class=\"adPositionIndexPop\">\n\t\t\t\t<image :src=\"adPosition['indexPop'].val\" mode=\"widthFix\" @click=\"goUrl(adPosition['indexPop'].url)\"></image>\n\t\t\t\t<view class=\"close\" @click=\"adPositionIndexPop = false\">\n\t\t\t\t\t<u-icon name=\"close-circle-fill\" color=\"#eee\" size=\"80rpx\"></u-icon>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</u-overlay>\n\t</view>\n</template>\n\n<script>\n\timport empty from 'empty-value'\n\timport tabbar from '@/components/tabbar'\n\timport list1 from '@/components/list/list1'\n\timport three1 from '@/components/list/three1'\n\timport three2 from '@/components/list/three2'\n\timport three3 from '@/components/list/three3'\n\timport tab1 from '@/components/tabs/tab1'\n\n\tconst TOOLS = require('@/common/tools')\n\t// #ifdef H5\n\tconst ua = window.navigator.userAgent.toLowerCase()\n\tif (ua.match(/MicroMessenger/i) == 'micromessenger') {\n\t\tconst jweixin = require('jweixin-module')\n\t\tjweixin.ready(() => {\n\t\t\t// 需在用户可能点击分享按钮前就先调用\n\t\t\tjweixin.updateAppMessageShareData({\n\t\t\t\ttitle: '野贝农土鲜商城', // 分享标题\n\t\t\t\tdesc: '野贝农土鲜商城', // 分享描述\n\t\t\t\tlink: 'https://ye.niutouren.com', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致\n\t\t\t\timgUrl: 'https://dcdn.it120.cc/2024/05/17/2e030caf-c248-4386-84dd-28cea6951572.jpeg', // 分享图标\n\t\t\t\tsuccess: function() {\n\t\t\t\t\t// 设置成功\n\t\t\t\t}\n\t\t\t})\n\t\t\tjweixin.updateTimelineShareData({\n\t\t\t\ttitle: '野贝农土鲜商城', // 分享标题\n\t\t\t\tdesc: '野贝农土鲜商城', // 分享描述\n\t\t\t\tlink: 'https://ye.niutouren.com', // 分享链接，该链接域名或路径必须与当前页面对应的公众号JS安全域名一致\n\t\t\t\timgUrl: 'https://dcdn.it120.cc/2024/05/17/2e030caf-c248-4386-84dd-28cea6951572.jpeg', // 分享图标\n\t\t\t\tsuccess: function() {\n\t\t\t\t\t// 设置成功\n\t\t\t\t}\n\t\t\t})\n\t\t})\n\t}\n\t// #endif\n\texport default {\n\t\tcomponents: {\n\t\t\ttabbar,\n\t\t\tlist1,\n\t\t\tthree1,\n\t\t\tthree2,\n\t\t\tthree3,\n\t\t\ttab1\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\ttabIndex: 2,\n\t\t\t\tapiUserInfoMap: undefined,\n\n\t\t\t\tbackground: ['color1', 'color2', 'color3'],\n\t\t\t\tindicatorDots: true,\n\t\t\t\tautoplay: true,\n\t\t\t\tinterval: 2000,\n\t\t\t\tduration: 500,\n\n\t\t\t\theaderMarginTopStyle: 'margin-top:0',\n\t\t\t\tkw: '',\n\t\t\t\tmenuButtonInfoStyle: '',\n\t\t\t\tshopInfo: undefined,\n\t\t\t\tbanners: undefined,\n\t\t\t\tgoodsDynamic: undefined,\n\t\t\t\tcategories: undefined,\n\t\t\t\tcategories2: undefined,\n\t\t\t\tcategories3: undefined,\n\t\t\t\tnotice: undefined,\n\t\t\t\tadPosition: {},\n\t\t\t\tmiaoshaGoods: undefined,\n\t\t\t\tshichiGoods: undefined,\n\t\t\t\tgoodsRecommend: undefined,\n\t\t\t\tgoodsHLD: undefined,\n\t\t\t\tgoodsSQS: undefined,\n\t\t\t\tgoodsYBC: undefined,\n\t\t\t\tkanjiaList: undefined,\n\t\t\t\tpingtuanList: undefined,\n\t\t\t\tpage: 1,\n\t\t\t\tgoods: [],\n\t\t\t\tadPositionIndexPop: false,\n\t\t\t\ttimeData: {},\n\n\t\t\t\tactiveTab: 'tab1',\n\n\t\t\t\tpromotionGoods: [],\n\t\t\t\tpromotionGoodsBak: [],\n\t\t\t\tpromotionInsert: true,\n\t\t\t}\n\t\t},\n\t\tonLoad(e) {\n\t\t\t// #ifdef  MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ\n\t\t\tuni.showShareMenu({\n\t\t\t\twithShareTicket: true,\n\t\t\t})\n\t\t\t// #endif\n\t\t\t// #ifdef  MP-WEIXIN || MP-BAIDU || MP-TOUTIAO || MP-QQ\n\t\t\tconst systemInfo = uni.getSystemInfoSync()\n\t\t\tconst menuButtonInfo = uni.getMenuButtonBoundingClientRect()\n\t\t\tthis.menuButtonInfoStyle =\n\t\t\t\t`width: ${systemInfo.screenWidth - menuButtonInfo.left}px;height: ${menuButtonInfo.height}px`\n\t\t\tthis.headerMarginTopStyle = `margin-top:${menuButtonInfo.top}px`\n\t\t\t// #endif\n\t\t\t// 读取小程序码中的邀请人编号\n\t\t\tif (e && e.scene) {\n\t\t\t\tconst scene = decodeURIComponent(e.scene)\n\t\t\t\tif (scene) {\n\t\t\t\t\tthis.$u.vuex('referrer', scene.substring(11))\n\t\t\t\t}\n\t\t\t}\n\t\t\tuni.setNavigationBarTitle({\n\t\t\t\ttitle: this.sysconfigMap.mallName\n\t\t\t})\n\t\t\tthis._userDetail()\n\t\t\tthis._banners()\n\t\t\tthis._categories()\n\t\t\tthis._notice()\n\t\t\tthis._adPosition()\n\t\t\tthis._miaoshaGoods()\n\t\t\tthis._shichiGoods()\n\t\t\tthis._goodsRecommend()\n\t\t\tthis._kanjiaList()\n\t\t\tthis._pingtuanList()\n\t\t\tthis._goodsPromotion()\n\t\t\tthis._goods()\n\t\t\tthis._goodsHLD()\n\t\t\tthis._goodsSQS()\n\t\t\t// #ifdef H5\n\t\t\tconst ua = window.navigator.userAgent.toLowerCase()\n\t\t\tif (ua.match(/MicroMessenger/i) == 'micromessenger') {\n\t\t\t\tthis.jssdkSign()\n\t\t\t}\n\t\t\t// #endif\n\t\t},\n\t\tonShow() {\n\t\t\tthis.shopInfo = uni.getStorageSync('shopInfo')\n\t\t\tthis._goodsDynamic()\n\t\t\tTOOLS.showTabBarBadge()\n\n\t\t\tif (this.activeTab === 'tab1') {\n\t\t\t\tconst refreshIndex = uni.getStorageSync('refreshIndex')\n\t\t\t\tif (refreshIndex) {\n\t\t\t\t\tthis.onPullDownRefresh()\n\t\t\t\t\tuni.removeStorageSync('refreshIndex')\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcreated() {},\n\t\tonShareAppMessage() {\n\t\t\treturn {\n\t\t\t\ttitle: '\"' + this.sysconfigMap.mallName + '\" ' + this.sysconfigMap.share_profile,\n\t\t\t\tpath: '/pages/index/index?inviter_id=' + this.uid\n\t\t\t}\n\t\t},\n\t\tonReachBottom() {\n\t\t\tthis.page += 1\n\t\t\tthis._goods()\n\t\t},\n\t\tonPullDownRefresh() {\n\t\t\tthis.page = 1\n\t\t\tthis._banners()\n\t\t\tthis._categories()\n\t\t\tthis._notice()\n\t\t\tthis._adPosition()\n\t\t\tthis._miaoshaGoods()\n\t\t\tthis._goodsRecommend()\n\t\t\tthis._kanjiaList()\n\t\t\tthis._pingtuanList()\n\t\t\tthis._goods()\n\t\t\tuni.stopPullDownRefresh()\n\t\t},\n\t\tmethods: {\n\t\t\tasync _goodsPromotion() {\n\t\t\t\tawait uni.$u.http.post('https://ye.niutouren.vip/api/order', {\n\t\t\t\t\t'type': 'index_good_promotion',\n\t\t\t\t}).then(res => {\n\t\t\t\t\tif (!empty(res)) {\n\t\t\t\t\t\tthis.promotionGoods = uni.$u.deepClone(res)\n\t\t\t\t\t}\n\n\t\t\t\t\t//console.log('long is', this.longList)\n\t\t\t\t}).catch(err => {\n\t\t\t\t\t//\n\t\t\t\t})\n\t\t\t},\n\t\t\tasync _userDetail() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/zgf8pu\n\t\t\t\tconst res = await this.$wxapi.userDetail(this.token)\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tthis.apiUserInfoMap = res.data\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync jssdkSign() {\n\t\t\t\tconst res = await this.$wxapi.jssdkSign(window.location.href)\n\t\t\t\tif (res.code === 0) {\n\t\t\t\t\tjweixin.config({\n\t\t\t\t\t\tdebug: false, // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。\n\t\t\t\t\t\tappId: res.data.appid, // 必填，公众号的唯一标识\n\t\t\t\t\t\ttimestamp: res.data.timestamp, // 必填，生成签名的时间戳\n\t\t\t\t\t\tnonceStr: res.data.noncestr, // 必填，生成签名的随机串\n\t\t\t\t\t\tsignature: res.data.sign, // 必填，签名\n\t\t\t\t\t\tjsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'] // 必填，需要使用的JS接口列表\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoSearch() {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/packageFx/search/index'\n\t\t\t\t})\n\t\t\t},\n\t\t\tasync _banners() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/ms21ki\n\t\t\t\tconst res = await this.$wxapi.banners({\n\t\t\t\t\ttype: 'index'\n\t\t\t\t})\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tthis.banners = res.data\n\t\t\t\t} else {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '后台未维护Banner数据',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\ttapBanner(index) {\n\t\t\t\tconst linkUrl = this.banners[index].linkUrl\n\t\t\t\tif (linkUrl) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: linkUrl\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync _goodsDynamic() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/xehbuk\n\t\t\t\tconst res = await this.$wxapi.goodsDynamic(0)\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tthis.goodsDynamic = []\n\t\t\t\t\tres.data.forEach(ele => {\n\t\t\t\t\t\tthis.goodsDynamic.push(`${ele.nick}购买了${ele.goodsName}`)\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync _categories() {\n\t\t\t\tconst res = await this.$wxapi.goodsCategory()\n\t\t\t\tif (res.code === 0) {\n\t\t\t\t\tconst categorizedData = res.data.reduce((acc, curr) => {\n\t\t\t\t\t\tif (curr.key === 'YBN1') {\n\t\t\t\t\t\t\tacc.categories.push(curr)\n\t\t\t\t\t\t} else if (curr.key === 'YBN2') {\n\t\t\t\t\t\t\tacc.categories2.push(curr)\n\t\t\t\t\t\t} else if (curr.key === 'YBN3') {\n\t\t\t\t\t\t\tacc.categories3.push(curr)\n\t\t\t\t\t\t}\n\t\t\t\t\t\treturn acc\n\t\t\t\t\t}, {\n\t\t\t\t\t\tcategories: [],\n\t\t\t\t\t\tcategories2: [],\n\t\t\t\t\t\tcategories3: []\n\t\t\t\t\t})\n\n\t\t\t\t\tthis.categories = categorizedData.categories\n\t\t\t\t\tthis.categories2 = categorizedData.categories2\n\t\t\t\t\tthis.categories3 = categorizedData.categories3\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 移除套餐、活动等\n\t\t\tremoveItemsFromArray(arr) {\n\t\t\t\tconst idsToRemove = [390765, 390766, 391088, 391089, 455910, 412599, 417010, 424167]\n\t\t\t\tconst filteredArr = arr.filter(item => !idsToRemove.includes(item.id))\n\t\t\t\treturn filteredArr\n\t\t\t},\n\t\t\tcategoryClick(category) {\n\t\t\t\tif (category.vopCid1 || category.vopCid2) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/goods/list-vop?cid1=' + (category.vopCid1 ? category.vopCid1 : '') +\n\t\t\t\t\t\t\t'&cid2=' + (category.vopCid2 ? category.vopCid2 : ''),\n\t\t\t\t\t})\n\t\t\t\t} else {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/pages/goods/list?categoryId=' + category.id,\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync _notice() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/zanb9r\n\t\t\t\tconst res = await this.$wxapi.noticeLastOne()\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tthis.notice = res.data\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync _adPosition() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/ypi79p\n\t\t\t\tconst res = await this.$wxapi.adPositionBatch('indexPop,index-live-pic')\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tres.data.forEach(ele => {\n\t\t\t\t\t\tthis.adPosition[ele.key] = ele\n\t\t\t\t\t\tif (ele.key == 'indexPop') {\n\t\t\t\t\t\t\tthis.adPositionIndexPop = true\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tgotoActivity(type) {\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/pages/promotion/list?type=' + type\n\t\t\t\t})\n\t\t\t},\n\t\t\tgoUrl(url) {\n\t\t\t\tthis.adPositionIndexPop = false\n\t\t\t\tif (url) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t},\n\t\t\tnoticeclick(e) {\n\t\t\t\tconsole.log(e)\n\t\t\t},\n\t\t\tasync _shichiGoods() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/wg5t98\n\t\t\t\tconst res = await this.$wxapi.goodsv2({\n\t\t\t\t\tcategoryId: '463740',\n\t\t\t\t\tshowExtJson: true\n\t\t\t\t})\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tres.data.result.forEach(ele => {\n\t\t\t\t\t\tconst _now = new Date().getTime()\n\t\t\t\t\t\tif (ele.dateStart) {\n\t\t\t\t\t\t\tele.dateStartInt = new Date(ele.dateStart.replace(/-/g, '/')).getTime() - _now\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (ele.dateEnd) {\n\t\t\t\t\t\t\tele.dateEndInt = new Date(ele.dateEnd.replace(/-/g, '/')).getTime() - _now\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif (!empty(res.data.extJsonMap)) {\n\t\t\t\t\t\t\tif (ele.id in res.data.extJsonMap) {\n\t\t\t\t\t\t\t\tele.ext = res.data.extJsonMap[ele.id]\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\n\t\t\t\t\tlet shichiGoods = []\n\t\t\t\t\tshichiGoods = this.rotateArray(res.data.result)\n\t\t\t\t\tthis.shichiGoods = shichiGoods\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync _miaoshaGoods() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/wg5t98\n\t\t\t\tconst res = await this.$wxapi.goodsv2({\n\t\t\t\t\tcategoryId: '391089'\n\t\t\t\t})\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tres.data.result.forEach(ele => {\n\t\t\t\t\t\tconst _now = new Date().getTime()\n\t\t\t\t\t\tif (ele.dateStart) {\n\t\t\t\t\t\t\tele.dateStartInt = new Date(ele.dateStart.replace(/-/g, '/')).getTime() - _now\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif (ele.dateEnd) {\n\t\t\t\t\t\t\tele.dateEndInt = new Date(ele.dateEnd.replace(/-/g, '/')).getTime() - _now\n\t\t\t\t\t\t}\n\t\t\t\t\t})\n\n\t\t\t\t\tlet miaoshaGoods = []\n\t\t\t\t\tmiaoshaGoods = this.rotateArray(res.data.result)\n\t\t\t\t\tthis.miaoshaGoods = miaoshaGoods\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync _kanjiaList() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/wg5t98\n\t\t\t\tconst res = await this.$wxapi.goodsv2({\n\t\t\t\t\tkanjia: true\n\t\t\t\t})\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tconst kanjiaGoodsIds = []\n\t\t\t\t\tres.data.result.forEach(ele => {\n\t\t\t\t\t\tkanjiaGoodsIds.push(ele.id)\n\t\t\t\t\t})\n\t\t\t\t\t// https://www.yuque.com/apifm/nu0f75/xs42ih\n\t\t\t\t\tconst goodsKanjiaSetRes = await this.$wxapi.kanjiaSet(kanjiaGoodsIds.join())\n\t\t\t\t\tif (goodsKanjiaSetRes.code == 0) {\n\t\t\t\t\t\tres.data.result.forEach(ele => {\n\t\t\t\t\t\t\tconst _process = goodsKanjiaSetRes.data.find(_set => {\n\t\t\t\t\t\t\t\treturn _set.goodsId == ele.id\n\t\t\t\t\t\t\t})\n\t\t\t\t\t\t\tif (_process) {\n\t\t\t\t\t\t\t\tele.process = 100 * _process.numberBuy / _process.number\n\t\t\t\t\t\t\t\tele.process = ele.process.toFixed(0)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t\tthis.kanjiaList = res.data.result\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync _pingtuanList() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/wg5t98\n\t\t\t\tconst res = await this.$wxapi.goodsv2({\n\t\t\t\t\tpingtuan: true\n\t\t\t\t})\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tthis.pingtuanList = res.data.result\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync _goods() {\n\t\t\t\tif (this.page > 1) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ttitle: '正在加载',\n\t\t\t\t\t\ticon: 'none'\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/wg5t98\n\t\t\t\tconst res = await this.$wxapi.goodsv2({\n\t\t\t\t\tpage: this.page,\n\t\t\t\t\tpageSize: 1,\n\t\t\t\t\tcategoryId: '383896,383897,383898,383899,383900,383901,383902,383903,383904,383905,474858',\n\t\t\t\t\tshowExtJson: true,\n\t\t\t\t\torderBy: 'ordersDown,addedDown' // 按订单数量降序，然后按发布时间降序\n\t\t\t\t})\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tlet _goods = []\n\t\t\t\t\tlet goods = []\n\t\t\t\t\tlet goodsNew = []\n\n\t\t\t\t\t_goods = res.data.result\n\t\t\t\t\t_goods = this.shuffle(_goods)\n\n\t\t\t\t\tif (empty(this.promotionGoods)) {\n\t\t\t\t\t\tthis.promotionGoods = uni.$u.deepClone(this.promotionGoodsBak)\n\t\t\t\t\t}\n\n\t\t\t\t\tlet promotionGoods = this.promotionGoods\n\n\t\t\t\t\tif (!empty(_goods) && !empty(promotionGoods)) {\n\t\t\t\t\t\tgoodsNew = this.insertPromotionGoods(_goods, promotionGoods)\n\t\t\t\t\t}\n\n\t\t\t\t\tif (this.page > 1) {\n\t\t\t\t\t\t_goods = this.goods.concat(goodsNew)\n\t\t\t\t\t} else {\n\t\t\t\t\t\t_goods = goodsNew\n\t\t\t\t\t}\n\n\t\t\t\t\tif (!empty(_goods)) {\n\t\t\t\t\t\t_goods.forEach(async (good, index) => {\n\t\t\t\t\t\t\tif (!empty(good)) {\n\t\t\t\t\t\t\t\tgood.image = good.pic\n\t\t\t\t\t\t\t\tgood.title = good.name\n\n\t\t\t\t\t\t\t\tif (!empty(res.data.extJsonMap)) {\n\t\t\t\t\t\t\t\t\tif (good.id in res.data.extJsonMap) {\n\t\t\t\t\t\t\t\t\t\tgood.ext = res.data.extJsonMap[good.id]\n\t\t\t\t\t\t\t\t\t\tif (!empty(good.ext['delivery_type'])) {\n\t\t\t\t\t\t\t\t\t\t\tif (good.ext['delivery_type'] === '预购') {\n\t\t\t\t\t\t\t\t\t\t\t\tgood.deliveryTypeUrl =\n\t\t\t\t\t\t\t\t\t\t\t\t\t'https://ye.niutouren.vip/static/images/goods/delivery-type-yg.png'\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tif (good.ext['delivery_type'] === '当日达') {\n\t\t\t\t\t\t\t\t\t\t\t\tgood.deliveryTypeUrl =\n\t\t\t\t\t\t\t\t\t\t\t\t\t'https://ye.niutouren.vip/static/images/goods/delivery-type-drd.png'\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tif (good.ext['delivery_type'] === '次日达') {\n\t\t\t\t\t\t\t\t\t\t\t\tgood.deliveryTypeUrl =\n\t\t\t\t\t\t\t\t\t\t\t\t\t'https://ye.niutouren.vip/static/images/goods/delivery-type-crd.png'\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t\tif (good.ext['delivery_type'] === '后日达') {\n\t\t\t\t\t\t\t\t\t\t\t\tgood.deliveryTypeUrl =\n\t\t\t\t\t\t\t\t\t\t\t\t\t'https://ye.niutouren.vip/static/images/goods/delivery-type-hrd.png'\n\t\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\t\tgoods.push(good)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.goods = goods\n\t\t\t\t} else {\n\t\t\t\t\t/* if (this.page != 1) {\n\t\t\t\t\t  uni.showToast({\n\t\t\t\t\t    title: '没有更多了～',\n\t\t\t\t\t    icon: 'none'\n\t\t\t\t\t  })\n\t\t\t\t\t} */\n\t\t\t\t}\n\t\t\t},\n\t\t\tinsertPromotionGoods(goods, promotionGoods) {\n\t\t\t\tlet first = promotionGoods.shift()\n\t\t\t\tgoods.unshift(first)\n\t\t\t\tlet second = promotionGoods.shift()\n\t\t\t\tgoods.splice(5, 0, second)\n\t\t\t\tlet third = promotionGoods.shift()\n\t\t\t\tgoods.splice(9, 0, third)\n\n\t\t\t\treturn goods\n\t\t\t},\n\t\t\tasync _goodsRecommend() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/wg5t98\n\t\t\t\tconst res = await this.$wxapi.goodsv2({\n\t\t\t\t\tcategoryId: '391088',\n\t\t\t\t\tshowExtJson: true\n\t\t\t\t})\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tlet _goods = []\n\t\t\t\t\tlet goods = []\n\t\t\t\t\tlet extJsonMap = []\n\n\t\t\t\t\t_goods = res.data.result\n\t\t\t\t\tif (!empty(_goods)) {\n\t\t\t\t\t\t_goods.forEach(async (good, index) => {\n\t\t\t\t\t\t\tgood.image = good.pic\n\t\t\t\t\t\t\tgood.title = good.name\n\n\t\t\t\t\t\t\tif (!empty(res.data.extJsonMap)) {\n\t\t\t\t\t\t\t\tif (good.id in res.data.extJsonMap) {\n\t\t\t\t\t\t\t\t\tgood.ext = res.data.extJsonMap[good.id]\n\t\t\t\t\t\t\t\t\tif (!empty(good.ext['deadline'])) {\n\t\t\t\t\t\t\t\t\t\tgood.ext['deadlineDifference'] = TOOLS.translateTimeDifference(good.ext['deadline'])\n\t\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tgoods.push(good)\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.goodsRecommend = this.rotateArray(goods)\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync _goodsHLD() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/wg5t98\n\t\t\t\tconst res = await this.$wxapi.goodsv2({\n\t\t\t\t\tcategoryId: '455908,455909,455910,455911,455912,455913,489436,455913,455912',\n\t\t\t\t\torderBy: 'ordersDown,addedDown' // 按订单数量降序，然后按发布时间降序\n\t\t\t\t})\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tlet goods = []\n\t\t\t\t\tlet goodsNew = []\n\t\t\t\t\tlet _goods = []\n\n\t\t\t\t\t_goods = res.data.result\n\t\t\t\t\t_goods = this.shuffle(_goods)\n\n\t\t\t\t\tif (empty(this.promotionGoods)) {\n\t\t\t\t\t\tthis.promotionGoods = uni.$u.deepClone(this.promotionGoodsBak)\n\t\t\t\t\t}\n\t\t\t\t\tlet promotionGoods = this.promotionGoods\n\t\t\t\t\tif (!empty(_goods) && !empty(promotionGoods)) {\n\t\t\t\t\t\tgoodsNew = this.insertPromotionGoods(_goods, promotionGoods)\n\t\t\t\t\t}\n\n\t\t\t\t\tif (!empty(_goods)) {\n\t\t\t\t\t\t_goods.forEach(async (good, index) => {\n\t\t\t\t\t\t\tif (!empty(good)) {\n\t\t\t\t\t\t\t\tgood.image = good.pic\n\t\t\t\t\t\t\t\tgood.title = good.name\n\t\t\t\t\t\t\t\tgoods.push(good)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.goodsHLD = goods\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync _goodsSQS() {\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/wg5t98\n\t\t\t\tconst res = await this.$wxapi.goodsv2({\n\t\t\t\t\tcategoryId: '424167,463423'\n\t\t\t\t})\n\t\t\t\tif (res.code == 0) {\n\t\t\t\t\tlet goods = []\n\t\t\t\t\tlet goodsNew = []\n\t\t\t\t\tlet _goods = []\n\n\t\t\t\t\t_goods = res.data.result\n\t\t\t\t\t_goods = this.shuffle(_goods)\n\n\t\t\t\t\tif (empty(this.promotionGoods)) {\n\t\t\t\t\t\tthis.promotionGoods = uni.$u.deepClone(this.promotionGoodsBak)\n\t\t\t\t\t}\n\t\t\t\t\tlet promotionGoods = this.promotionGoods\n\t\t\t\t\tif (!empty(_goods) && !empty(promotionGoods)) {\n\t\t\t\t\t\tgoodsNew = this.insertPromotionGoods(_goods, promotionGoods)\n\t\t\t\t\t}\n\n\t\t\t\t\tif (!empty(_goods)) {\n\t\t\t\t\t\t_goods.forEach(async (good, index) => {\n\t\t\t\t\t\t\tif (!empty(good)) {\n\t\t\t\t\t\t\t\tgood.image = good.pic\n\t\t\t\t\t\t\t\tgood.title = good.name\n\t\t\t\t\t\t\t\tgoods.push(good)\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t})\n\t\t\t\t\t}\n\n\t\t\t\t\tthis.goodsSQS = goods\n\t\t\t\t}\n\t\t\t},\n\t\t\tgoCoupons() {\n\t\t\t\tuni.switchTab({\n\t\t\t\t\turl: '/packageFx/coupons/index'\n\t\t\t\t})\n\t\t\t},\n\t\t\tshuffle(array) {\n\t\t\t\tlet currentIndex = array.length,\n\t\t\t\t\trandomIndex;\n\n\t\t\t\twhile (currentIndex != 0) {\n\t\t\t\t\trandomIndex = Math.floor(Math.random() * currentIndex);\n\t\t\t\t\tcurrentIndex--;\n\n\t\t\t\t\t[array[currentIndex], array[randomIndex]] = [array[randomIndex], array[currentIndex]]\n\t\t\t\t}\n\n\t\t\t\treturn array\n\t\t\t},\n\t\t\trotateArray(arr) {\n\t\t\t\tlet timer = setInterval(() => {\n\t\t\t\t\tlet firstElement = arr.shift()\n\t\t\t\t\tarr.push(firstElement)\n\t\t\t\t}, 10000)\n\n\t\t\t\treturn arr\n\t\t\t},\n\t\t\tchangeTab(tab) {\n\t\t\t\tthis.activeTab = tab\n\t\t\t},\n\t\t\talertTab() {\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '敬请期待',\n\t\t\t\t\ticon: 'error'\n\t\t\t\t})\n\t\t\t},\n\t\t\tfeatureClick(key) {\n\t\t\t\tlet aboutPage = true\n\t\t\t\tif (key === 'sw') {\n\t\t\t\t\taboutPage = false\n\t\t\t\t\t// 实体礼品卡 - 直接跳转到实物礼品卡列表\n\t\t\t\t\tconst physicalParams = {\n\t\t\t\t\t\tpic: encodeURIComponent('https://ye.niutouren.vip/static/images/coupons/card2.jpg'),\n\t\t\t\t\t\ttag: encodeURIComponent('指定抵扣,可自用,可赠送'),\n\t\t\t\t\t\ttitle: encodeURIComponent('实物礼品卡'),\n\t\t\t\t\t\ttype: encodeURIComponent('physical')\n\t\t\t\t\t}\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/packageFx/coupons/list?pic=${physicalParams.pic}&tag=${physicalParams.tag}&title=${physicalParams.title}&type=${physicalParams.type}`\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tif (key === 'sz') {\n\t\t\t\t\taboutPage = false\n\t\t\t\t\t// 数字礼品卡 - 直接跳转到消费礼品卡列表\n\t\t\t\t\tconst virtualParams = {\n\t\t\t\t\t\tpic: encodeURIComponent('https://ye.niutouren.vip/static/images/coupons/card1.jpg'),\n\t\t\t\t\t\ttag: encodeURIComponent('全品通用,可自用,可赠送'),\n\t\t\t\t\t\ttitle: encodeURIComponent('消费礼品卡'),\n\t\t\t\t\t\ttype: encodeURIComponent('virtual')\n\t\t\t\t\t}\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/packageFx/coupons/list?pic=${virtualParams.pic}&tag=${virtualParams.tag}&title=${virtualParams.title}&type=${virtualParams.type}`\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t\tif (key === 'sq') {\n\t\t\t\t\taboutPage = false\n\t\t\t\t\t// 山泉礼品卡 - 直接跳转到水卡列表\n\t\t\t\t\tconst waterParams = {\n\t\t\t\t\t\tpic: encodeURIComponent('https://dcdn.it120.cc/2024/05/05/77302a3c-066e-426d-a36e-da545df8fccf.png'),\n\t\t\t\t\t\ttag: encodeURIComponent('购水抵扣,可自用,可赠送'),\n\t\t\t\t\t\ttitle: encodeURIComponent('水卡'),\n\t\t\t\t\t\ttype: encodeURIComponent('water')\n\t\t\t\t\t}\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/packageFx/coupons/list?pic=${waterParams.pic}&tag=${waterParams.tag}&title=${waterParams.title}&type=${waterParams.type}`\n\t\t\t\t\t})\n\t\t\t\t}\n\n\t\t\t\tif (aboutPage) {\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: '/packageFx/about/about?key=' + key\n\t\t\t\t\t})\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\tcomputed: {\n\t\t\t// 计算 span 值\n\t\t\ttabSpan() {\n\t\t\t\treturn this.apiUserInfoMap?.userLevel?.id === 33080 ? 3 : 4\n\t\t\t},\n\t\t\t// 判断是否显示企业购标签\n\t\t\tshowEnterpriseTab() {\n\t\t\t\treturn this.apiUserInfoMap?.userLevel?.id === 33080\n\t\t\t}\n\t\t}\n\t}\n</script>\n<style scoped lang=\"scss\">\n\t.index.container-wrapper {\n\t\tbackground: linear-gradient(90deg, #35641e, #80a933) fixed;\n\t}\n\n\t.index {\n\t\t.top-box {\n\t\t\tpadding: 0 8rpx;\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\n\t\t\t.t {\n\t\t\t\tpadding-top: 8rpx;\n\t\t\t\tpadding-left: 8rpx;\n\t\t\t}\n\n\t\t\t.t .logo {\n\t\t\t\twidth: 94px;\n\t\t\t\theight: 25px;\n\t\t\t\tmargin-top: 8rpx;\n\t\t\t}\n\n\t\t\t.search {\n\t\t\t\tpadding: 0 8rpx;\n\t\t\t\tflex: 1;\n\t\t\t\tbackground: none;\n\t\t\t}\n\n\t\t\t.location {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tmargin: 0 16rpx;\n\t\t\t\tmargin-top: 22rpx;\n\t\t\t\tcolor: #fff;\n\t\t\t}\n\t\t}\n\n\t\t.swiper {\n\t\t\tmargin: 20rpx 0;\n\n\t\t\t.notice {\n\t\t\t\tposition: absolute;\n\t\t\t\tbottom: 46rpx;\n\t\t\t\tleft: 24rpx;\n\t\t\t\twidth: 668rpx;\n\t\t\t\tcolor: #fff;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\topacity: 0.8;\n\t\t\t\tborder-radius: 32rpx;\n\t\t\t}\n\t\t}\n\n\t\t.main-wrapper {\n\t\t\tmin-height: 100vh;\n\t\t\tbackground: #f7f8fa;\n\t\t\tborder-top-left-radius: 10px;\n\t\t\tborder-top-right-radius: 10px;\n\t\t\tposition: relative;\n\t\t\tmargin-top: -40px;\n\t\t}\n\n\t\t.feature {\n\t\t\tmargin-left: 38rpx;\n\t\t\tmargin-right: 20rpx;\n\t\t\tpadding-top: 20rpx;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 26rpx;\n\t\t}\n\n\t\t.shortcuts-account {\n\t\t\tbackground: #FFFFFF;\n\t\t\tmargin-left: 36rpx;\n\t\t\tmargin-right: 36rpx;\n\t\t\tpadding: 6rpx 6rpx 16rpx 6rpx;\n\t\t\tborder-radius: 6px;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 20rpx;\n\t\t\tvertical-align: bottom;\n\t\t}\n\n\t\t.shortcuts-account img {\n\t\t\twidth: 60rpx;\n\t\t\theight: 60rpx;\n\t\t\tbackground-repeat: no-repeat;\n\t\t\tdisplay: inline-block;\n\t\t\tvertical-align: middle;\n\t\t}\n\n\t\t.category-container {\n\t\t\tpadding: 0 0 10px 0;\n\t\t\tposition: relative;\n\n\t\t\t.category-box {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\n\t\t\t\tmargin-left: 0;\n\t\t\t\tmargin-right: 0;\n\t\t\t\tborder-radius: 10px;\n\t\t\t\tpadding: 20rpx 0;\n\t\t\t\tposition: inherit;\n\t\t\t}\n\n\t\t\t.category-list {\n\t\t\t\twidth: 20%;\n\t\t\t\ttext-align: center;\n\t\t\t\tdisplay: inline-block;\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\n\t\t\t.category-column {\n\t\t\t\twidth: 100%;\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\toverflow: hidden;\n\t\t\t}\n\n\t\t\t.category-imgbox {\n\t\t\t\twidth: 100rpx;\n\t\t\t\theight: 100rpx;\n\t\t\t}\n\n\t\t\t.category-title {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\n\t\t.live-pic {\n\t\t\tmargin-top: 16rpx;\n\t\t}\n\n\t\t.ttt {\n\t\t\tdisplay: flex;\n\t\t\talign-items: left;\n\t\t\tmargin-top: 24rpx;\n\n\t\t\t.content {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: left;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\tpadding: 0 16rpx;\n\t\t\t\twidth: 100%;\n\n\t\t\t\ttext {\n\t\t\t\t\tmargin-left: 16rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-size: 46rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t}\n\n\t\t\t\ttext.sub {\n\t\t\t\t\tmargin-left: 16rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tmargin-top: 22rpx;\n\t\t\t\t}\n\n\t\t\t\t.delivery-time {\n\t\t\t\t\tmargin-left: 16rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: bold;\n\t\t\t\t\tmargin-top: 22rpx;\n\t\t\t\t}\n\n\t\t\t\t.more {\n\t\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\tmargin-right: 10rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.miaoshaGoods {\n\t\t\t.miaosha-goods-list {\n\t\t\t\tmargin: 20rpx;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\tpadding: 20rpx;\n\t\t\t}\n\n\t\t\t.miaosha-goods-list .image {\n\t\t\t\twidth: 260rpx;\n\t\t\t\theight: 260rpx;\n\t\t\t\tflex-shrink: 0;\n\t\t\t\tborder-radius: 16rpx;\n\t\t\t}\n\n\t\t\t.miaosha-goods-list .r {\n\t\t\t\tmargin-left: 32rpx;\n\t\t\t}\n\n\t\t\t.miaosha-goods-list .r .goods-title {\n\t\t\t\tcolor: #333;\n\t\t\t\tfont-size: 28rpx;\n\t\t\t}\n\n\t\t\t.miaosha-goods-list .r .label {\n\t\t\t\tcolor: #e64340;\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: flex-start;\n\t\t\t\tmargin-top: 8rpx;\n\t\t\t}\n\n\t\t\t.miaosha-goods-list .r .label text {\n\t\t\t\tmargin-left: 8rpx;\n\t\t\t}\n\n\t\t\t.miaosha-goods-list .count-down {\n\t\t\t\tbackground: rgba(250, 195, 198, 0.3);\n\t\t\t\tborder-radius: 5rpx;\n\t\t\t\tfont-size: 14rpx;\n\t\t\t\tcolor: red;\n\t\t\t\tfont-weight: 400;\n\t\t\t\tpadding: 6rpx 16rpx;\n\t\t\t\tmargin-top: 6rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tborder-radius: 10rpx;\n\t\t\t}\n\n\t\t\t.miaosha-price-btn {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t}\n\n\t\t\t.miaosha-price-btn .msbtn {\n\t\t\t\twidth: 170rpx;\n\t\t\t\theight: 60rpx;\n\t\t\t\tbackground: linear-gradient(156deg, #FF7863 0%, #FF211A 100%);\n\t\t\t\tborder-radius: 34rpx;\n\t\t\t\tborder: none !important;\n\t\t\t\tline-height: 60rpx !important;\n\t\t\t\tfont-size: 13px !important;\n\t\t\t}\n\n\t\t\t.miaosha-price-btn .price {\n\t\t\t\tcolor: #e64340;\n\t\t\t\tfont-size: 40rpx;\n\t\t\t\tmargin-top: 12rpx;\n\t\t\t\tpadding-right: 32rpx;\n\t\t\t}\n\n\t\t\t.miaosha-price-btn .price text {\n\t\t\t\tcolor: #666666;\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\ttext-decoration: line-through;\n\t\t\t}\n\t\t}\n\n\t\t.goods-container {\n\t\t\tpadding: 0 24rpx;\n\t\t}\n\n\t\t.goods-box {\n\t\t\twidth: 339rpx;\n\t\t\tbackground-color: #fff;\n\t\t\toverflow: hidden;\n\t\t\tmargin-top: 24rpx;\n\t\t\tborder-radius: 5px;\n\t\t\tborder: 1px solid #D1D1D1;\n\t\t\tpadding-bottom: 10rpx;\n\t\t}\n\n\t\t.goods-box .img-box {\n\t\t\twidth: 339rpx;\n\t\t\theight: 339rpx;\n\t\t\toverflow: hidden;\n\t\t}\n\n\t\t.goods-box .img-box image {\n\t\t\twidth: 339rpx;\n\t\t\theight: 339rpx;\n\t\t}\n\n\t\t.goods-box .goods-title {\n\t\t\tpadding: 0 4rpx;\n\t\t}\n\n\t\t.goods-box .goods-price-container {\n\t\t\tdisplay: flex;\n\t\t\talign-items: baseline;\n\t\t}\n\n\t\t.goods-box .goods-price {\n\t\t\toverflow: hidden;\n\t\t\tfont-size: 34rpx;\n\t\t\tcolor: #F20C32;\n\t\t\tmargin-left: 24rpx;\n\t\t}\n\n\t\t.goods-box .goods-price2 {\n\t\t\toverflow: hidden;\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #aaa;\n\t\t\ttext-decoration: line-through;\n\t\t\tmargin-left: 20rpx;\n\t\t}\n\n\t\t.coupons-float {\n\t\t\tposition: fixed;\n\t\t\tright: 15rpx;\n\t\t\tbottom: 180rpx;\n\t\t\twidth: 80rpx;\n\t\t\theight: 80rpx;\n\t\t\tbackground-color: #fff;\n\t\t\ttext-align: center;\n\t\t\tborder-radius: 50%;\n\t\t\tborder: 1rpx solid #ddd;\n\t\t}\n\n\t\t.coupons-float image {\n\t\t\twidth: 60rpx;\n\t\t\theight: 60rpx;\n\t\t\tmargin-top: 10rpx;\n\t\t}\n\n\t\t.adPositionIndexPop {\n\t\t\twidth: 100vw;\n\t\t\theight: 100vh;\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.adPositionIndexPop image {\n\t\t\twidth: 505rpx;\n\t\t}\n\n\t\t.adPositionIndexPop .close {\n\t\t\tmargin-top: 32rpx;\n\t\t}\n\t}\n\n\t.blank {\n\t\theight: 32rpx;\n\t}\n</style>\n<style lang=\"scss\">\n\t.tab1 {\n\t\tmargin: 0 12px;\n\t}\n\t\n\t.notice-bar-wrap {\n\t\tpadding: 5rpx 10rpx;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.tab1 .tab.line {\n\t\tposition: relative;\n\t\twidth: 100%;\n\t}\n\n\t.tab1 .tab.line::after {\n\t\tcontent: \"\";\n\t\tposition: absolute;\n\t\ttop: 20%;\n\t\tright: 0;\n\t\tbottom: 10%;\n\t\twidth: 1px;\n\t\tbackground-color: #d3d3d3;\n\t}\n\n\t.tab1 .tab .title {\n\t\tfont-size: 40rpx;\n\t\tfont-weight: bolder;\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.tab1 .tab.active .title {\n\t\tmargin-top: 10rpx;\n\t}\n\n\t.tab1 .tab .dsc {\n\t\tfont-size: 30rpx;\n\t\theight: 60rpx;\n\t}\n\n\t.tab1 .tab.active {\n\t\tbackground-image: url(\"/static/images/tab-bg.png\");\n\t\tbackground-size: contain;\n\t\tbackground-repeat: no-repeat;\n\t\tbackground-position: center;\n\t}\n\n\t.tab1 .tab.active .dsc {\n\t\tbackground-image: url(\"/static/images/tab-dsc-bg.png\");\n\t\tbackground-size: contain;\n\t\tbackground-repeat: no-repeat;\n\t\tbackground-position: center;\n\t\tfont-size: 24rpx;\n\t\tline-height: 58rpx;\n\t\tcolor: #FFFFFF;\n\t\tmargin: 0 16rpx;\n\t}\n\n\t.swiper {\n\t\theight: 420rpx;\n\t}\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753691039928\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=1&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753691039915\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}