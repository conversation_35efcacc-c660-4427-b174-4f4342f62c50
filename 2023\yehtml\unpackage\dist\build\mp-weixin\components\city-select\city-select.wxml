<u-popup vue-id="23bc9af8-1" show="{{value}}" mode="bottom" round="32rpx" closeable="{{true}}" z-index="{{uZIndex}}" closeOnClickOverlay="{{maskCloseAble}}" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:if="{{value}}"><u-tabs vue-id="{{('23bc9af8-2')+','+('23bc9af8-1')}}" list="{{genTabsList}}" current="{{tabsIndex}}" data-event-opts="{{[['^change',[['tabsChange']]]]}}" bind:change="__e" bind:__l="__l"></u-tabs></block><view class="area-box"><view class="{{['u-flex',(isChange)?'change':'']}}"><view class="area-item"><view class="u-padding-10 u-bg-gray" style="height:100%;"><scroll-view style="height:100%;" scroll-y="{{true}}"><u-cell-group vue-id="{{('23bc9af8-3')+','+('23bc9af8-1')}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{provinces}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-cell vue-id="{{('23bc9af8-4-'+index)+','+('23bc9af8-3')}}" title="{{item.label}}" name="{{index}}" data-event-opts="{{[['^click',[['provinceChange']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['right-icon']}}"><view slot="right-icon"><block wx:if="{{isChooseP&&province==index}}"><u-icon vue-id="{{('23bc9af8-5-'+index)+','+('23bc9af8-4-'+index)}}" size="48rpx" name="checkbox-mark" color="green" bind:__l="__l"></u-icon></block></view></u-cell></block></u-cell-group></scroll-view></view></view><block wx:if="{{isChooseP}}"><view class="area-item"><view class="u-padding-10 u-bg-gray" style="height:100%;"><scroll-view style="height:100%;" scroll-y="{{true}}"><u-cell-group vue-id="{{('23bc9af8-6')+','+('23bc9af8-1')}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{citys}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-cell vue-id="{{('23bc9af8-7-'+index)+','+('23bc9af8-6')}}" title="{{item.label}}" name="{{index}}" data-event-opts="{{[['^click',[['cityChange']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['right-icon']}}"><view slot="right-icon"><block wx:if="{{isChooseC&&city==index}}"><u-icon vue-id="{{('23bc9af8-8-'+index)+','+('23bc9af8-7-'+index)}}" size="48rpx" name="checkbox-mark" color="green" bind:__l="__l"></u-icon></block></view></u-cell></block></u-cell-group></scroll-view></view></view></block><block wx:if="{{isChooseC&&level>=3}}"><view class="area-item"><view class="u-padding-10 u-bg-gray" style="height:100%;"><scroll-view style="height:100%;" scroll-y="{{true}}"><u-cell-group vue-id="{{('23bc9af8-9')+','+('23bc9af8-1')}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{areas}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-cell vue-id="{{('23bc9af8-10-'+index)+','+('23bc9af8-9')}}" title="{{item.label}}" name="{{index}}" data-event-opts="{{[['^click',[['areaChange']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['right-icon']}}"><view slot="right-icon"><block wx:if="{{isChooseA&&area==index}}"><u-icon vue-id="{{('23bc9af8-11-'+index)+','+('23bc9af8-10-'+index)}}" size="48rpx" name="checkbox-mark" color="green" bind:__l="__l"></u-icon></block></view></u-cell></block></u-cell-group></scroll-view></view></view></block><block wx:if="{{isChooseA&&level>=4}}"><view class="area-item"><view class="u-padding-10 u-bg-gray" style="height:100%;"><scroll-view style="height:100%;" scroll-y="{{true}}"><u-cell-group vue-id="{{('23bc9af8-12')+','+('23bc9af8-1')}}" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{streets}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-cell vue-id="{{('23bc9af8-13-'+index)+','+('23bc9af8-12')}}" title="{{item.label}}" name="{{index}}" data-event-opts="{{[['^click',[['streetChange']]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['right-icon']}}"><view slot="right-icon"><block wx:if="{{isChooseS&&street==index}}"><u-icon vue-id="{{('23bc9af8-14-'+index)+','+('23bc9af8-13-'+index)}}" size="48rpx" name="checkbox-mark" color="green" bind:__l="__l"></u-icon></block></view></u-cell></block></u-cell-group></scroll-view></view></view></block></view></view></u-popup>