{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/fav.vue?4c91", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/fav.vue?cd85", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/fav.vue?b091", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/fav.vue?c4c9", "uni-app:///pages/goods/fav.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/fav.vue?f2df", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/fav.vue?553e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "type", "title", "page", "goods", "created", "mounted", "onReady", "onLoad", "onShow", "onReachBottom", "methods", "_favList", "token", "res", "ele", "uni", "icon", "deleteFav", "content", "success", "console", "_deleteFav", "id", "goDetail", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,YAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA4H;AAC5H;AACuD;AACL;AACsC;;;AAGxF;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,yEAAM;AACR,EAAE,0FAAM;AACR,EAAE,mGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,8FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAA8pB,CAAgB,8qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoBlrB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC,6BAEA;EACAC,6BAEA;EACAC,6BAEA;EACAC;IACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;EACA;EACAC,2BAEA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAV;gBACA;cAAA;gBAHAW;gBAIA;kBACAA;oBACA;sBACA;sBACA;wBACAC;sBACA;sBACA;wBACAA;sBACA;sBACA;wBACAA;sBACA;oBACA;kBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;kBACA;oBACAC;sBACAd;sBACAe;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAF;kBACAd;kBACAiB;kBACAC;oBACA;sBACA;oBACA;sBACAC;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAtB;kBACAa;kBACAU;kBACAtB;gBACA,GACA;gBAAA;gBAAA,OACA;cAAA;gBAAAa;gBACA;kBACAE;oBACAd;kBACA;kBACA;gBACA;kBACAc;oBACAd;oBACAe;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAO;MACA;QACAR;UACAS;QACA;MACA;QACAT;UACAS;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzIA;AAAA;AAAA;AAAA;AAAqxC,CAAgB,yuCAAG,EAAC,C;;;;;;;;;;;ACAzyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/goods/fav.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/goods/fav.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./fav.vue?vue&type=template&id=4f06f7b9&scoped=true&\"\nvar renderjs\nimport script from \"./fav.vue?vue&type=script&lang=js&\"\nexport * from \"./fav.vue?vue&type=script&lang=js&\"\nimport style0 from \"./fav.vue?vue&type=style&index=0&id=4f06f7b9&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4f06f7b9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods/fav.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fav.vue?vue&type=template&id=4f06f7b9&scoped=true&\"", "var components\ntry {\n  components = {\n    pageBoxEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/page-box-empty/page-box-empty\" */ \"@/components/page-box-empty/page-box-empty.vue\"\n      )\n    },\n    uText: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-text/u-text\" */ \"@/uni_modules/uview-ui/components/u-text/u-text.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.goods || _vm.goods.length == 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fav.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fav.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <page-box-empty v-if=\"!goods || goods.length == 0\" :title=\"title\" sub-title=\"可以去看看有那些想买的～\" :show-btn=\"true\" />\r\n    <view class=\"goods-container\">\r\n      <view v-for=\"(item, index) in goods\" :key=\"index\" class=\"goods-box\">\r\n        <view class=\"img-box\">\r\n          <image :src=\"item.pic\" class=\"image\" mode=\"aspectFill\" lazy-load=\"true\" @click=\"goDetail(item)\" />\r\n        </view>\r\n        <view class=\"name\">\r\n          <u-text class=\"goods-title\" :text=\"item.goodsName\" :lines=\"3\" size=\"28rpx\" color=\"#333\" @click=\"goDetail(item)\"></u-text>\r\n        </view>\r\n        <view class=\"delete\">\r\n          <u-icon name=\"trash\" color=\"#F20C32\" size=\"48rpx\" @click=\"deleteFav(index, item)\"></u-icon>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    data() {\r\n      return {\r\n        type: 1,\r\n        title: '暂无收藏记录',\r\n        page: 1,\r\n        goods: []\r\n      }\r\n    },\r\n    created() {\r\n\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    onReady() {\r\n\r\n    },\r\n    onLoad(e) {\r\n      if (e && e.type) {\r\n        if (e.type === '2') {\r\n          this.title = '暂无获赠记录'\r\n        }\r\n        if (e.type === '3') {\r\n          this.title = '暂无推荐记录'\r\n        }\r\n      }\r\n      this._favList()\r\n    },\r\n    onShow() {\r\n\r\n    },\r\n    onReachBottom() {\r\n      this.page += 1\r\n      this._favList()\r\n    },\r\n    methods: {\r\n      async _favList() {\r\n        // https://www.yuque.com/apifm/nu0f75/cusiow\r\n        const res = await this.$wxapi.goodsFavList({\r\n          token: this.token,\r\n          page: this.page\r\n        })\r\n        if (res.code == 0) {\r\n          res.data.forEach(ele => {\r\n            if (ele.json) {\r\n              const jsonStr = JSON.parse(ele.json)\r\n              if (jsonStr.pic) {\r\n                ele.pic = jsonStr.pic\r\n              }\r\n              if (jsonStr.goodsName) {\r\n                ele.goodsName = jsonStr.goodsName\r\n              }\r\n              if (jsonStr.supplyType) {\r\n                ele.supplyType = jsonStr.supplyType\r\n              }\r\n            }\r\n          })\r\n          if (this.page == 1) {\r\n            this.goods = res.data\r\n          } else {\r\n            this.goods = this.goods.concat(res.data)\r\n          }\r\n        } else {\r\n          if (this.page != 1) {\r\n            uni.showToast({\r\n              title: '没有更多了～',\r\n              icon: 'none'\r\n            })\r\n          }\r\n        }\r\n      },\r\n      async deleteFav(index, item) {\r\n        uni.showModal({\r\n          title: '提示',\r\n          content: '确定要取消收藏吗？',\r\n          success: res => {\r\n            if (res.confirm) {\r\n              this._deleteFav(index, item)\r\n            } else if (res.cancel) {\r\n              console.log('用户点击取消');\r\n            }\r\n          }\r\n        })\r\n      },\r\n      async _deleteFav(index, item) {\r\n        const data = {\r\n          token: this.token,\r\n          id: item.id,\r\n          type: item.type\r\n        }\r\n        // https://www.yuque.com/apifm/nu0f75/zy4sil\r\n        const res = await this.$wxapi.goodsFavDeleteV2(data)\r\n        if (res.code == 0) {\r\n          uni.showToast({\r\n            title: '取消收藏'\r\n          })\r\n          this.goods.splice(index, 1)\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n        }\r\n      },\r\n      goDetail(item) {\r\n        if (item.supplyType == 'vop_jd') {\r\n          uni.navigateTo({\r\n            url: '/pages/goods/detail?supplyType=vop_jd&yyId=' + item.goodsId\r\n          })\r\n        } else {\r\n          uni.navigateTo({\r\n            url: '/pages/goods/detail?id=' + item.goodsId\r\n          })\r\n        }\r\n      }\r\n    }\r\n  }\r\n</script>\r\n<style scoped lang=\"scss\">\r\n  .goods-container {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    flex-wrap: wrap;\r\n    box-sizing: content-box;\r\n    padding: 0 24rpx;\r\n\r\n    .goods-box {\r\n      width: 339rpx;\r\n      background-color: #fff;\r\n      overflow: hidden;\r\n      margin-top: 24rpx;\r\n      border-radius: 5px;\r\n      border: 1px solid #D1D1D1;\r\n      padding-bottom: 10rpx;\r\n\r\n      .img-box {\r\n        width: 339rpx;\r\n        height: 339rpx;\r\n        overflow: hidden;\r\n\r\n        image {\r\n          width: 339rpx;\r\n          height: 339rpx;\r\n        }\r\n      }\r\n\r\n      .goods-title {\r\n        padding: 0 4rpx;\r\n      }\r\n\r\n      .goods-price-container {\r\n        display: flex;\r\n        align-items: baseline;\r\n      }\r\n\r\n      .goods-price {\r\n        overflow: hidden;\r\n        font-size: 34rpx;\r\n        color: #F20C32;\r\n        margin-left: 24rpx;\r\n      }\r\n\r\n      .goods-price2 {\r\n        overflow: hidden;\r\n        font-size: 26rpx;\r\n        color: #aaa;\r\n        text-decoration: line-through;\r\n        margin-left: 20rpx;\r\n      }\r\n    }\r\n\r\n  }\r\n\r\n  .delete {\r\n    padding: 16rpx;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .name {\r\n    height: 108rpx;\r\n    padding: 16rpx;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fav.vue?vue&type=style&index=0&id=4f06f7b9&scoped=true&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./fav.vue?vue&type=style&index=0&id=4f06f7b9&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692293972\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}