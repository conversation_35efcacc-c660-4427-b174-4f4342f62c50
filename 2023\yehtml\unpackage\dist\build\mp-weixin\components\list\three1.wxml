<view class="three1"><view class="delivery-time"><label class="text _span">到货时间</label><label class="date _span">{{list[0].ext.delivery_time}}</label></view><view><block wx:if="{{list[0].name}}"><u-row vue-id="4132d72d-1" customStyle="padding: 12px;margin-bottom: 10px;" gutter="12" bind:__l="__l" vue-slots="{{['default']}}"><u-col vue-id="{{('4132d72d-2')+','+('4132d72d-1')}}" span="6" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['goDetail',['$0'],['list.__$n0']]]]]}}" class="left" bindtap="__e"><block wx:if="{{deliveryTimeTip!==''}}"><view class="status-badge">{{deliveryTimeTip}}</view></block><view class="left-top" style="{{'background-image:'+('url('+list[0].pic+')')+';'}}"></view><view class="left-bottom"><view class="title">{{list[0].name}}</view><view class="dsc">{{$root.m0}}</view><view class="price"><view class="price-left"><view class="price-tip">特约预购价</view><view class="original-price"><label class="_span">{{"原价￥"+list[0].originalPrice}}</label>{{'/ '+list[0].unit}}</view></view><view class="price-right"><label class="prefix _span">￥</label><label class="_span">{{list[0].minPrice}}</label></view></view><view class="count-down"><view style="display:inline-block;">截止剩余：</view><view style="display:inline-block;"><u-count-down vue-id="{{('4132d72d-3')+','+('4132d72d-2')}}" time="{{list[0].ext['deadlineDifference']*1000}}" format="DD:HH:mm:ss" data-event-opts="{{[['^change',[['onChangeTimeData']]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="time"><view class="time__custom"><text class="time__custom__item">{{timeData.days}}</text></view><text class="time__doc">天</text><view class="time__custom"><text class="time__custom__item">{{timeData.hours>10?timeData.hours:'0'+timeData.hours}}</text></view><text class="time__doc">时</text><view class="time__custom"><text class="time__custom__item">{{timeData.minutes>10?timeData.minutes:'0'+timeData.minutes}}</text></view><text class="time__doc">分</text></view></u-count-down></view></view></view></view></u-col><u-col vue-id="{{('4132d72d-4')+','+('4132d72d-1')}}" span="6" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['goDetail',['$0'],['list.__$n1']]]]]}}" class="right" bindtap="__e"><view class="right-top" style="{{'background-image:'+('url('+list[1].pic+')')+';'}}"></view><view class="right-bottom"><view class="title">{{list[1].name}}</view><view class="price"><view class="price-left"><view class="price-tip">立即预购</view><view class="original-price"><label class="_span">{{"原价￥"+list[1].originalPrice}}</label>{{'/ '+list[1].unit}}</view></view><view class="price-right"><label class="prefix _span">￥</label><label class="_span">{{list[1].minPrice}}</label></view></view></view></view><view data-event-opts="{{[['tap',[['goDetail',['$0'],['list.__$n2']]]]]}}" class="right" style="margin-top:20rpx;" bindtap="__e"><view class="right-top" style="{{'background-image:'+('url('+list[2].pic+')')+';'}}"></view><view class="right-bottom"><view class="title">{{list[2].name}}</view><view class="price"><view class="price-left"><view class="price-tip">立即预购</view><view class="original-price"><label class="_span">{{"原价￥"+list[2].originalPrice}}</label>{{'/ '+list[2].unit}}</view></view><view class="price-right"><label class="prefix _span">￥</label><label class="_span">{{list[2].minPrice}}</label></view></view></view></view></u-col></u-row></block><block wx:if="{{!list[0].name}}"><view style="padding:12px;margin-bottom:10px;"><u-skeleton vue-id="4132d72d-5" rows="3" bind:__l="__l"></u-skeleton></view></block></view></view>