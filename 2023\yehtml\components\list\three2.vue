<template>
  <view class="three1">
    <view>
      <u-row v-if="list[1].name" customStyle="padding: 12px;margin-bottom: 10px;" gutter="12">
        <u-col span="6">
          <view class="right" @click="goDetail(list[1])">
            <view class="right-top" :style="{ backgroundImage: 'url(' + list[1].pic + ')' }"></view>
            <view class="right-bottom">
              <view class="title">{{ list[1].name }}</view>
              <view class="price">
                <view class="price-right">
                  <span class="prefix">￥</span>
                  <span>38</span>
                </view>
                <view class="price-left">
                  <view class="original-price"><span>原价￥{{ list[1].originalPrice }}</span> / {{ list[1].unit }}</view>
                  <view class="only">仅剩<span>{{ list[1].stores }}</span>只</view>
                </view>
              </view>
            </view>
          </view>
          <view class="right" style="margin-top: 20rpx;" @click="goDetail(list[2])">
            <view class="right-top" :style="{ backgroundImage: 'url(' + list[2].pic + ')' }"></view>
            <view class="right-bottom">
              <view class="title">{{ list[2].name }}</view>
              <view class="price">
                <view class="price-right">
                  <span class="prefix">￥</span>
                  <span>38</span>
                </view>
                <view class="price-left">
                  <view class="original-price"><span>原价￥{{ list[2].originalPrice }}</span> / {{ list[2].minPrice }}
                  </view>
                  <view class="only">仅剩<span>{{ list[2].stores }}</span>只</view>
                </view>
              </view>
            </view>
          </view>
        </u-col>
        <u-col span="6">
          <view class="left" @click="goDetail(list[0])">
            <view class="left-top" :style="{ backgroundImage: 'url(' + list[0].pic + ')' }"></view>
            <view class="left-bottom">
              <view class="title">{{ list[0].name }}</view>
              <view class="dsc">{{ handleTags(list[0].tags) }}</view>
              <view class="price">
                <view class="price-left">
                  <view class="price-tip">抢购价</view>
                  <view class="only">仅剩<span>{{ list[0].stores }}</span>只</view>
                </view>
                <view class="price-right">
                  <span class="prefix">￥</span>
                  <span>{{ list[0].minPrice }}</span>
                </view>
              </view>
            </view>
          </view>
        </u-col>
      </u-row>
      <view v-if="!list[0].name" style="padding: 12px;margin-bottom: 10px;">
        <u-skeleton rows="3"></u-skeleton>
      </view>
    </view>
  </view>
</template>

<script>
  import empty from 'empty-value'
  const TOOLS = require('@/common/tools')

  export default {
    components: {},
    props: {
      list: {
        type: Array,
        default: [],
      },
      type: {
        type: String,
        default: '',
      }
    },
    onReady() {},
    data() {
      return {
        timeData: {}
      }
    },
    watch: {
      list: function(val) {
        //console.log('watch list is', val)
      }
    },
    methods: {
      goDetail(item) {
        TOOLS.softOpeningTip()
        
        /* let that = this
        uni.navigateTo({
          url: '/pages/goods/detail?id=' + item.id
        }) */
      },
      onChangeTimeData(e) {
        this.timeData = e
      },
      handleTags(tag) {
        var result = tag.replace(/[,，]/g, ' ')
        return result
      }
    },
  }
</script>
<style>
  .only {
    font-size: 22rpx;
    line-height: 24rpx;
  }

  .only span {
    background-color: #324A43;
    border-radius: 4px;
    display: inline-block;
    text-align: center;
    width: 16px;
    height: 16px;
    line-height: 16px;
    margin: 0 8rpx;
  }

  .left {
    border-radius: 8rpx;
    overflow: hidden;
    text-align: center;
  }

  .left-top {
    height: 546rpx;
    background-size: cover;
    background-position: center;
  }

  .left-bottom {
    line-height: 50rpx;
    background: #87C232;
    background: radial-gradient(circle, #eead4d, #f2933a);
    color: #FFF;
    padding: 20rpx 10rpx;
  }

  .left-bottom .count-down {
    font-size: 22rpx;
    color: #324A43;
  }

  .left-bottom .title {
    font-size: 46rpx;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .left-bottom .dsc {
    font-size: 26rpx;
    color: #FFFFFF;
  }

  .left-bottom .price-tip {
    font-size: 22rpx;
    color: #345409;
    background: #f5f9c3;
    border-radius: 20rpx;
    padding: 0 8rpx;
    display: inline-block;
    line-height: 32rpx;
  }

  .left-bottom .price {
    font-weight: bolder;
  }

  .left-bottom .price .price-left {
    display: inline-block;
    font-weight: normal;
    text-align: right;
  }

  .left-bottom .price .price-right {
    font-size: 80rpx;
    display: inline-block;
    letter-spacing: -2px;
  }

  .left-bottom .price .prefix,
  .left-bottom .price .suffix {
    font-size: 40rpx;
  }

  .right {
    color: #FFFFFF;
    border-radius: 8rpx;
    overflow: hidden;
    text-align: center;
  }

  .right-top {
    height: 200rpx;
    background-size: cover;
    background-position: top;
  }

  .right-bottom {
    line-height: 50rpx;
    background: #87C232;
    background: radial-gradient(circle, #eead4d, #f2933a);
    color: #FFF;
    padding: 20rpx 10rpx;
  }

  .right-bottom .title {
    font-size: 46rpx;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    margin-bottom: 20rpx;
  }

  .right-bottom .price-tip {
    font-size: 20rpx;
    color: #345409;
    background: #f5f9c3;
    border-radius: 20rpx;
    padding: 0 8rpx;
    display: inline-block;
    line-height: 32rpx;
  }

  .right-bottom .price {
    font-weight: bolder;
  }

  .right-bottom .price .price-left {
    display: inline-block;
    font-weight: normal;
    text-align: left;
  }

  .right-bottom .price .price-right {
    font-size: 70rpx;
    display: inline-block;
    letter-spacing: -2px;
    margin-right: 20rpx;
  }

  .right-bottom .price .prefix,
  .right-bottom .price .suffix {
    font-size: 32rpx;
  }

  .right-bottom .price .original-price {
    font-size: 20rpx;
    text-decoration: line-through;
    line-height: 32rpx;
  }
</style>