{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-code/u-code.vue?b931", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-code/u-code.vue?5f35", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-code/u-code.vue?e754", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-code/u-code.vue?d191", "uni-app:///uni_modules/uview-ui/components/u-code/u-code.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-code/u-code.vue?929c", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-code/u-code.vue?e6cb"], "names": ["name", "mixins", "data", "secNum", "timer", "canGetCode", "mounted", "watch", "seconds", "immediate", "handler", "methods", "<PERSON><PERSON><PERSON><PERSON><PERSON>ning", "uni", "start", "clearInterval", "reset", "changeEvent", "setTimeToStorage", "key", "<PERSON><PERSON><PERSON><PERSON>", "clearTimeout"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACOrrB;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,eAgBA;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACAC;QACA;QACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;QACA;MACA;MACA;MACA;MACA;MACA;MACA;QACA;UACA;UACA;QACA;UACAA;UACA;UACA;UACA;UACA;UACA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACAD;MACA;MACA;IACA;IACAE;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;QACA;QACA;QACAL;UACAM;UACAjB;QACA;MACA;IACA;EACA;EACA;EACAkB;IACA;IACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3HA;AAAA;AAAA;AAAA;AAAwxC,CAAgB,4uCAAG,EAAC,C;;;;;;;;;;;ACA5yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-code/u-code.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-code.vue?vue&type=template&id=1d88f349&scoped=true&\"\nvar renderjs\nimport script from \"./u-code.vue?vue&type=script&lang=js&\"\nexport * from \"./u-code.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-code.vue?vue&type=style&index=0&id=1d88f349&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1d88f349\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-code/u-code.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-code.vue?vue&type=template&id=1d88f349&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-code.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-code.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-code\">\r\n\t\t<!-- 此组件功能由js完成，无需写html逻辑 -->\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t/**\r\n\t * Code 验证码输入框\r\n\t * @description 考虑到用户实际发送验证码的场景，可能是一个按钮，也可能是一段文字，提示语各有不同，所以本组件 不提供界面显示，只提供提示语，由用户将提示语嵌入到具体的场景\r\n\t * @tutorial https://www.uviewui.com/components/code.html\r\n\t * @property {String | Number}\tseconds\t\t\t倒计时所需的秒数（默认 60 ）\r\n\t * @property {String}\t\t\tstartText\t\t开始前的提示语，见官网说明（默认 '获取验证码' ）\r\n\t * @property {String}\t\t\tchangeText\t\t倒计时期间的提示语，必须带有字母\"x\"，见官网说明（默认 'X秒重新获取' ）\r\n\t * @property {String}\t\t\tendText\t\t\t倒计结束的提示语，见官网说明（默认 '重新获取' ）\r\n\t * @property {Boolean}\t\t\tkeepRunning\t\t是否在H5刷新或各端返回再进入时继续倒计时（ 默认false ）\r\n\t * @property {String}\t\t\tuniqueKey\t\t为了区分多个页面，或者一个页面多个倒计时组件本地存储的继续倒计时变了\r\n\t *\r\n\t * @event {Function}\tchange\t倒计时期间，每秒触发一次\r\n\t * @event {Function}\tstart\t开始倒计时触发\r\n\t * @event {Function}\tend\t\t结束倒计时触发\r\n\t * @example <u-code ref=\"uCode\" @change=\"codeChange\" seconds=\"20\"></u-code>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-code\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tsecNum: this.seconds,\r\n\t\t\t\ttimer: null,\r\n\t\t\t\tcanGetCode: true, // 是否可以执行验证码操作\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.checkKeepRunning()\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tseconds: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(n) {\r\n\t\t\t\t\tthis.secNum = n\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tcheckKeepRunning() {\r\n\t\t\t\t// 获取上一次退出页面(H5还包括刷新)时的时间戳，如果没有上次的保存，此值可能为空\r\n\t\t\t\tlet lastTimestamp = Number(uni.getStorageSync(this.uniqueKey + '_$uCountDownTimestamp'))\r\n\t\t\t\tif(!lastTimestamp) return this.changeEvent(this.startText)\r\n\t\t\t\t// 当前秒的时间戳\r\n\t\t\t\tlet nowTimestamp = Math.floor((+ new Date()) / 1000)\r\n\t\t\t\t// 判断当前的时间戳，是否小于上一次的本该按设定结束，却提前结束的时间戳\r\n\t\t\t\tif(this.keepRunning && lastTimestamp && lastTimestamp > nowTimestamp) {\r\n\t\t\t\t\t// 剩余尚未执行完的倒计秒数\r\n\t\t\t\t\tthis.secNum = lastTimestamp - nowTimestamp\r\n\t\t\t\t\t// 清除本地保存的变量\r\n\t\t\t\t\tuni.removeStorageSync(this.uniqueKey + '_$uCountDownTimestamp')\r\n\t\t\t\t\t// 开始倒计时\r\n\t\t\t\t\tthis.start()\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 如果不存在需要继续上一次的倒计时，执行正常的逻辑\r\n\t\t\t\t\tthis.changeEvent(this.startText)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 开始倒计时\r\n\t\t\tstart() {\r\n\t\t\t\t// 防止快速点击获取验证码的按钮而导致内部产生多个定时器导致混乱\r\n\t\t\t\tif(this.timer) {\r\n\t\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\t\tthis.timer = null\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('start')\r\n\t\t\t\tthis.canGetCode = false\r\n\t\t\t\t// 这里放这句，是为了一开始时就提示，否则要等setInterval的1秒后才会有提示\r\n\t\t\t\tthis.changeEvent(this.changeText.replace(/x|X/, this.secNum))\r\n\t\t\t\tthis.timer = setInterval(() => {\r\n\t\t\t\t\tif (--this.secNum) {\r\n\t\t\t\t\t\t// 用当前倒计时的秒数替换提示字符串中的\"x\"字母\r\n\t\t\t\t\t\tthis.changeEvent(this.changeText.replace(/x|X/, this.secNum))\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\t\t\tthis.timer = null\r\n\t\t\t\t\t\tthis.changeEvent(this.endText)\r\n\t\t\t\t\t\tthis.secNum = this.seconds\r\n\t\t\t\t\t\tthis.$emit('end')\r\n\t\t\t\t\t\tthis.canGetCode = true\r\n\t\t\t\t\t}\r\n\t\t\t\t}, 1000)\r\n        this.setTimeToStorage()\r\n      },\r\n\t\t\t// 重置，可以让用户再次获取验证码\r\n\t\t\treset() {\r\n\t\t\t\tthis.canGetCode = true\r\n\t\t\t\tclearInterval(this.timer)\r\n\t\t\t\tthis.secNum = this.seconds\r\n\t\t\t\tthis.changeEvent(this.endText)\r\n\t\t\t},\r\n\t\t\tchangeEvent(text) {\r\n\t\t\t\tthis.$emit('change', text)\r\n\t\t\t},\r\n\t\t\t// 保存时间戳，为了防止倒计时尚未结束，H5刷新或者各端的右上角返回上一页再进来\r\n\t\t\tsetTimeToStorage() {\r\n\t\t\t\tif(!this.keepRunning || !this.timer) return\r\n\t\t\t\t// 记录当前的时间戳，为了下次进入页面，如果还在倒计时内的话，继续倒计时\r\n\t\t\t\t// 倒计时尚未结束，结果大于0；倒计时已经开始，就会小于初始值，如果等于初始值，说明没有开始倒计时，无需处理\r\n\t\t\t\tif(this.secNum > 0 && this.secNum <= this.seconds) {\r\n\t\t\t\t\t// 获取当前时间戳(+ new Date()为特殊写法)，除以1000变成秒，再去除小数部分\r\n\t\t\t\t\tlet nowTimestamp = Math.floor((+ new Date()) / 1000)\r\n\t\t\t\t\t// 将本该结束时候的时间戳保存起来 => 当前时间戳 + 剩余的秒数\r\n\t\t\t\t\tuni.setStorage({\r\n\t\t\t\t\t\tkey: this.uniqueKey + '_$uCountDownTimestamp',\r\n\t\t\t\t\t\tdata: nowTimestamp + Number(this.secNum)\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\t// 组件销毁的时候，清除定时器，否则定时器会继续存在，系统不会自动清除\r\n\t\tbeforeDestroy() {\r\n\t\t\tthis.setTimeToStorage()\r\n\t\t\tclearTimeout(this.timer)\r\n\t\t\tthis.timer = null\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-code.vue?vue&type=style&index=0&id=1d88f349&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-code.vue?vue&type=style&index=0&id=1d88f349&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692292966\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}