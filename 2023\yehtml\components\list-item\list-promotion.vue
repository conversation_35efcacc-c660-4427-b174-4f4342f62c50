<template>
  <view class="promotion-box" :style="{background: item.bgColor, color: item.textColor}">
    <view class="title" @click="goDetail(item)">{{ item.title }}</view>
    <view class="dsc" @click="goDetail(item)">{{ item.dsc }}</view>
    <view @click="goDetail(item)" class="button" :style="{background: item.buttonBgColor, color: item.buttonColor}">{{ item.buttonText }}</view>
  </view>
</template>

<script>
  export default {
    props: {
      item: {
        type: Object,
        default: {},
      },
    },
    onReady() {},
    data() {
      return {}
    },
    methods: {
      goDetail(item) {
        uni.navigateTo({
          url: item.path
        })
      }
    },
  }
</script>
<style>
  .promotion-box {
    overflow: hidden;
    text-align: left;
    overflow: hidden;
    background: #FFF9D7;
    text-align: center;
    padding: 10rpx 0;
  }

  .title {
    font-size: 46rpx;
    font-weight: bold;
    padding: 0 4rpx;
  }

  .dsc {
    margin: 10rpx 0;
    font-size: 24rpx;
  }

  .button {
    display: inline-block;
    white-space: nowrap;
    padding: 6rpx 16rpx;
    border-radius: 2rpx 8rpx;
    font-size: 24rpx;
  }
</style>