{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/components/tabs/tab-water.vue?1801", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/tabs/tab-water.vue?d95f", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/tabs/tab-water.vue?4ef8", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/tabs/tab-water.vue?fc7b", "uni-app:///components/tabs/tab-water.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/tabs/tab-water.vue?7e34", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/tabs/tab-water.vue?6701"], "names": ["components", "list2", "data", "goods", "mounted", "watch", "list", "methods", "changeTab", "that", "_goods", "categoryId", "showExtJson", "res", "goodsNew", "good"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAsH;AACtH;AAC6D;AACL;AACa;;;AAGrE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,oFAAM;AACR,EAAE,6FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,wFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAoqB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACQxrB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAA;IACAC;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IAAA;EAEA;EACAC;IACAC;MACA;MACAC;MACAA;MACAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC,uBACA;gBAAA;gBAAA,OACA;kBACAA;kBACAC;gBACA;cAAA;gBAHAC;gBAIA;kBACAH;kBACAP;kBACAW;kBAEAJ;kBAEA;oBACAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCACAK;gCACAA;gCACAZ;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CACA;sBAAA;wBAAA;sBAAA;oBAAA;kBACA;kBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAg9B,CAAgB,m8BAAG,EAAC,C;;;;;;;;;;;ACAp+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/tabs/tab-water.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./tab-water.vue?vue&type=template&id=552b7e89&\"\nvar renderjs\nimport script from \"./tab-water.vue?vue&type=script&lang=js&\"\nexport * from \"./tab-water.vue?vue&type=script&lang=js&\"\nimport style0 from \"./tab-water.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/tabs/tab-water.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tab-water.vue?vue&type=template&id=552b7e89&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tab-water.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tab-water.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"tab-water\">\r\n    <list2 :list=\"goods\"></list2>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import list2 from '@/components/list/list2'\r\n  import empty from 'empty-value'\r\n\r\n  export default {\r\n    components: {\r\n      list2,\r\n    },\r\n    data() {\r\n      return {\r\n        goods: [],\r\n      }\r\n    },\r\n    mounted() {\r\n      this._goods()\r\n    },\r\n    watch: {\r\n      list: function(val) {\r\n        //console.log('watch list is', val)\r\n      }\r\n    },\r\n    methods: {\r\n      changeTab(tab) {\r\n        let that = this\r\n        that.activeTab = tab\r\n        that.goods = []\r\n        that._goods()\r\n      },\r\n      async _goods() {\r\n        let categoryId = '424167'\r\n        // https://www.yuque.com/apifm/nu0f75/wg5t98\r\n        const res = await this.$wxapi.goodsv2({\r\n          categoryId: categoryId,\r\n          showExtJson: true\r\n        })\r\n        if (res.code == 0) {\r\n          let _goods = []\r\n          let goods = []\r\n          let goodsNew = []\r\n\r\n          _goods = res.data.result\r\n\r\n          if (!empty(_goods)) {\r\n            _goods.forEach(async (good, index) => {\r\n              good.image = good.pic\r\n              good.title = good.name\r\n              goods.push(good)\r\n            })\r\n          }\r\n\r\n          this.goods = goods\r\n        }\r\n      },\r\n    },\r\n  }\r\n</script>\r\n<style>\r\n  .tab-water {\r\n    margin: 0 12px;\r\n  }\r\n\r\n  .tab.line {\r\n    position: relative;\r\n    width: 100%;\r\n  }\r\n\r\n  .tab.line::after {\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 20%;\r\n    right: 0;\r\n    bottom: 10%;\r\n    width: 1px;\r\n    background-color: #d3d3d3;\r\n  }\r\n\r\n  .tab .title {\r\n    font-size: 34rpx;\r\n    font-weight: bolder;\r\n    margin-bottom: 10rpx;\r\n  }\r\n\r\n  .tab .dsc {\r\n    font-size: 26rpx;\r\n    height: 60rpx;\r\n  }\r\n\r\n  .tab.active {\r\n    background-image: url(\"/static/images/tab-bg.png\");\r\n    background-size: contain;\r\n    background-repeat: no-repeat;\r\n    background-position: center;\r\n  }\r\n\r\n  .tab.active .dsc {\r\n    background-image: url(\"/static/images/tab-dsc-bg.png\");\r\n    background-size: contain;\r\n    background-repeat: no-repeat;\r\n    background-position: center;\r\n    font-size: 28rpx;\r\n    line-height: 56rpx;\r\n    color: #FFFFFF;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tab-water.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./tab-water.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688720165\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}