(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/list/three3"],{"0ea8":function(n,e,t){"use strict";t.r(e);var u=t("565d"),o=t("c8f8");for(var i in o)["default"].indexOf(i)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(i);t("7ad0");var a=t("828b"),r=Object(a["a"])(o["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);e["default"]=r.exports},2622:function(n,e,t){},5518:function(n,e,t){"use strict";(function(n){var u=t("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;u(t("bc37")),t("0cdf");var o={components:{},props:{list:{type:Array,default:[]},type:{type:String,default:""}},onReady:function(){},data:function(){return{timeData:{}}},watch:{list:function(n){}},methods:{goDetail:function(e){n.navigateTo({url:"/pages/goods/detail?id="+e.id})},onChangeTimeData:function(n){this.timeData=n},handleTags:function(n){var e=n.replace(/[,，]/g," ");return e}}};e.default=o}).call(this,t("df3c")["default"])},"565d":function(n,e,t){"use strict";t.d(e,"b",(function(){return o})),t.d(e,"c",(function(){return i})),t.d(e,"a",(function(){return u}));var u={uRow:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-row/u-row")]).then(t.bind(null,"f632"))},uCol:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-col/u-col")]).then(t.bind(null,"44b6"))},uSkeleton:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-skeleton/u-skeleton")]).then(t.bind(null,"58b2"))}},o=function(){var n=this.$createElement;this._self._c},i=[]},"7ad0":function(n,e,t){"use strict";var u=t("2622"),o=t.n(u);o.a},c8f8:function(n,e,t){"use strict";t.r(e);var u=t("5518"),o=t.n(u);for(var i in u)["default"].indexOf(i)<0&&function(n){t.d(e,n,(function(){return u[n]}))}(i);e["default"]=o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/list/three3-create-component',
    {
        'components/list/three3-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("0ea8"))
        })
    },
    [['components/list/three3-create-component']]
]);
