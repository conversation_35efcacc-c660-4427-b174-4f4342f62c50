(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/list-item/list-goods-item2"],{2172:function(t,n,e){"use strict";e.r(n);var i=e("4829"),u=e("f3b1");for(var o in u)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(o);e("ec8b");var r=e("828b"),a=Object(r["a"])(u["default"],i["b"],i["c"],!1,null,null,null,!1,i["a"],void 0);n["default"]=a.exports},"3f5e3":function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e={props:{item:{type:Object,default:{}}},onReady:function(){},data:function(){return{}},computed:{filteredTags:function(){if(this.item.tags){var t=this.item.tags.split(/[, ]+/);return t.filter((function(t){return""!==t.trim()}))}return[]}},methods:{goDetail:function(n){t.navigateTo({url:"/pages/goods/detail?id="+n.id})},shouldHighlight:function(t){return/[A-Za-z]\d+/.test(t)}}};n.default=e}).call(this,e("df3c")["default"])},4829:function(t,n,e){"use strict";e.d(n,"b",(function(){return u})),e.d(n,"c",(function(){return o})),e.d(n,"a",(function(){return i}));var i={uIcon:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-icon/u-icon")]).then(e.bind(null,"5f3a"))}},u=function(){var t=this,n=t.$createElement,e=(t._self._c,t.__map(t.filteredTags,(function(n,e){var i=t.__get_orig(n),u=t.shouldHighlight(n),o=t.shouldHighlight(n);return{$orig:i,m0:u,m1:o}})));t.$mp.data=Object.assign({},{$root:{l0:e}})},o=[]},ec8b:function(t,n,e){"use strict";var i=e("f0e4"),u=e.n(i);u.a},f0e4:function(t,n,e){},f3b1:function(t,n,e){"use strict";e.r(n);var i=e("3f5e3"),u=e.n(i);for(var o in i)["default"].indexOf(o)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(o);n["default"]=u.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/list-item/list-goods-item2-create-component',
    {
        'components/list-item/list-goods-item2-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("2172"))
        })
    },
    [['components/list-item/list-goods-item2-create-component']]
]);
