<template>
  <view>
    123
  </view>

</template>

<script>
  import empty from 'empty-value'
  import listGoodsItemCouponsIndex from '@/components/list-item/list-goods-item-coupons-index'

  export default {
    components: {
      listGoodsItemCouponsIndex
    },
    props: {
      list: {
        type: Array,
        default: [],
      },
      type: {
        type: String,
        default: '',
      },
    },
    onReady() {},
    data() {
      return {}
    },
    watch: {
      list: function(val) {
        //console.log('watch list is', val)
      }
    },
    methods: {
      //
    },
  }
</script>
<style>

</style>