@charset "UTF-8";
/**
 * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量
 * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可
 * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 
 */
/* 变量 */
view.data-v-50004b49, scroll-view.data-v-50004b49, swiper-item.data-v-50004b49 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-text.data-v-50004b49 {
  display: flex;
  flex-direction: row;
  align-items: center;
  flex-wrap: nowrap;
  flex: 1;
  width: 100%;
}
.u-text__price.data-v-50004b49 {
  font-size: 14px;
  color: #606266;
}
.u-text__value.data-v-50004b49 {
  font-size: 14px;
  display: flex;
  flex-direction: row;
  color: #606266;
  flex-wrap: wrap;
  text-overflow: ellipsis;
  align-items: center;
}
.u-text__value--primary.data-v-50004b49 {
  color: #3c9cff;
}
.u-text__value--warning.data-v-50004b49 {
  color: #f9ae3d;
}
.u-text__value--success.data-v-50004b49 {
  color: #5ac725;
}
.u-text__value--info.data-v-50004b49 {
  color: #909399;
}
.u-text__value--error.data-v-50004b49 {
  color: #f56c6c;
}
.u-text__value--main.data-v-50004b49 {
  color: #303133;
}
.u-text__value--content.data-v-50004b49 {
  color: #606266;
}
.u-text__value--tips.data-v-50004b49 {
  color: #858996;
}
.u-text__value--light.data-v-50004b49 {
  color: #c0c4cc;
}

