{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/components/goods-pop/goods-pop.vue?f9a8", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/goods-pop/goods-pop.vue?e819", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/goods-pop/goods-pop.vue?a814", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/goods-pop/goods-pop.vue?f003", "uni-app:///components/goods-pop/goods-pop.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/goods-pop/goods-pop.vue?d230", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/goods-pop/goods-pop.vue?162c"], "names": ["name", "props", "show", "type", "default", "goodsDetail", "skuList", "kjid", "data", "pic", "price", "score", "buyNumber", "min", "max", "propertyChildIds", "propertyChildNames", "goodsAddition", "faved", "properties", "lock", "watch", "deep", "immediate", "handler", "mounted", "methods", "_initData", "TOOLS", "close", "skuSelect", "p", "c", "_p", "ele", "ok", "i", "a", "_subPic", "additionSelect", "child", "_goodsAddition", "res", "calculateGoodsPrice", "token", "goodsId", "uni", "title", "icon", "big", "goCart", "url", "goodsFavCheck", "addFav", "getApp", "extJsonStr", "goodsName", "supplyType", "checkOk", "additionAllSelect", "addCart", "sku", "optionId", "optionValueId", "id", "pid", "number", "tobuy", "optionName", "optionValueName", "pname", "goodsType", "goodsList", "additions", "logisticsId"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,4RAEN;AACP,KAAK;AACL;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzEA;AAAA;AAAA;AAAA;AAAoqB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC4ExrB;AAAA,eACA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAC;MACAF;MACAC;IACA;IACAE;MACAH;MACAC;IACA;IACAG;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACAhB;MACAiB;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC,6BAEA;EACAC;IACAC;MACA;QACA;MACA;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;MACA;MACA;MACA;QACA;MACA;MACA;MACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACAX;gBACAY;gBACAC,8BACA;gBACA;kBACAC;kBACAA;oBACAC;kBACA;kBACAD;kBACAd;gBACA;gBACA;gBACAa;gBACAD;gBACAA;gBACAZ;gBACA;gBACAJ;gBACAC;gBACAG;kBACA;oBACAJ;oBACAC;kBACA;gBACA;gBACA;gBACA;gBACA;gBACAV;kBACA;kBACAS;oBACA;sBACAoB;oBACA;kBACA;kBACA;gBACA,IACA;gBAAA,uBACAC;kBACA;kBACA;oBACA;kBACA;kBACA;oBACAH;kBACA;oBACAA;kBACA;kBACAA;oBACAI;sBACA;oBACA;oBACA;sBACAL;oBACA;sBACAA;oBACA;kBACA;kBACAb;gBAAA;gBApBA;kBAAA;gBAqBA;gBACA;gBACA;kBACAmB;oBACA;kBACA;kBACA;oBACA;kBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAR;gBACAC;gBAAA,KACAA;kBAAA;kBAAA;gBAAA;gBACA;gBACAA;gBACAD;gBACA;gBAAA;cAAA;gBAGA;gBACA;kBACAA;oBACAS;kBACA;gBACA;gBACAR;gBACAD;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAU;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MAEA,2FACAxB;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBACA;kBACA;gBACA;kBACA;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAKA;kBACAyB;kBACAC;kBACA9B;gBACA;cAAA;gBAJA2B;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACAI;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAGA;gBACA;gBACA;kBACA;gBACA;kBACA;gBACA;gBACA;gBACA;kBACA;oBACAC;sBACA;wBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;QACAJ;MACA;MACAA;QACAK;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA5C;kBACAoC;kBACAzC;kBACA0C;gBACA;gBACA;kBACArC;kBACAA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAkC;gBACA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAW;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAR;kBACAK;gBACA;gBAAA;cAAA;gBAGA3C;kBACAoC;kBACAzC;kBACA0C;gBACA;gBACA;kBACArC;kBACAA;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAkC;gBACA;kBACA;gBACA;kBACAI;oBACAC;oBACAC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEAO;kBACA9C;kBACA+C;kBACAC;gBACA;gBACAjD;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAkC;gBACA;kBACA;gBACA;kBACAI;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAU;MACA,uFACAvC;QACA2B;UACAC;UACAC;QACA;QACA;MACA;MACA;MACA;QACA;UACA;YACA;cACA;YACA;YACA;cACAW;YACA;UACA;QACA;MACA;MACA;QACAb;UACAC;UACAC;QACA;QACA;MACA;MACA;IACA;IACAY;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAN;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAR;kBACAK;gBACA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACAL;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAGAa;gBACA;kBACA;oBACAA;sBACAC;sBACAC;oBACA;kBACA;gBACA;gBACA9C;gBACA;kBACA;oBACAiB;sBACA;wBACAjB;0BACA+C;0BACAC;wBACA;sBACA;oBACA;kBACA;gBACA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACArB;kBACAC;kBACAqB;gBACA;cAAA;gBAJAxB;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAOA;cAAA;gBAAAA;cAAA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACAI;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAGA;gBACAF;kBACAC;gBACA;gBACAnB;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAuC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAb;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACAR;kBACAK;gBACA;gBAAA;cAAA;gBAAA,IAGA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACAL;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAGAa;gBACA;kBACA;oBACAA;sBACAC;sBACAC;sBACAK;sBACAC;oBACA;kBACA;gBACA;gBACApD;gBACA;kBACA;oBACAiB;sBACA;wBACAjB;0BACA+C;0BACAC;0BACAjE;0BACAsE;wBACA;sBACA;oBACA;kBACA;gBACA;gBACAC;gBACA1B;gBACA;kBACA0B;kBACA1B;gBACA;gBACA;kBACA0B;kBACA1B;gBACA;gBACA2B;kBACA3B;kBACAW;kBACAU;kBACAzD;kBACAC;kBACAC;kBACAkD;kBAAA;kBACAY;kBAAA;kBACAF;kBACAhE;kBACAmE;gBACA;gBAEA5B;gBACAA;kBACAK;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AAEA;AAAA,2B;;;;;;;;;;;;;ACjiBA;AAAA;AAAA;AAAA;AAA2xC,CAAgB,+uCAAG,EAAC,C;;;;;;;;;;;ACA/yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/goods-pop/goods-pop.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./goods-pop.vue?vue&type=template&id=56f7457a&scoped=true&\"\nvar renderjs\nimport script from \"./goods-pop.vue?vue&type=script&lang=js&\"\nexport * from \"./goods-pop.vue?vue&type=script&lang=js&\"\nimport style0 from \"./goods-pop.vue?vue&type=style&index=0&id=56f7457a&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"56f7457a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/goods-pop/goods-pop.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goods-pop.vue?vue&type=template&id=56f7457a&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-image/u-image\" */ \"@/uni_modules/uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n    uText: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-text/u-text\" */ \"@/uni_modules/uview-ui/components/u-text/u-text.vue\"\n      )\n    },\n    uLine: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-line/u-line\" */ \"@/uni_modules/uview-ui/components/u-line/u-line.vue\"\n      )\n    },\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tag/u-tag\" */ \"@/uni_modules/uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n    uNumberBox: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-number-box/u-number-box\" */ \"@/uni_modules/uview-ui/components/u-number-box/u-number-box.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uBadge: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-badge/u-badge\" */ \"@/uni_modules/uview-ui/components/u-badge/u-badge.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goods-pop.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goods-pop.vue?vue&type=script&lang=js&\"", "<template>\r\n  <u-popup v-if=\"show\" :show=\"show\" mode=\"bottom\" round=\"32rpx\" :customStyle=\"{maxHeight: '80vh', overflow: 'scroll'}\" @close=\"close\">\r\n    <view class=\"goodsList-pop\">\r\n      <u-image showLoading lazyLoad :src=\"pic\" radius=\"16rpx\" width=\"240rpx\" height=\"240rpx\"></u-image>\r\n      <view class=\"goods-info\">\r\n        <u-text class=\"t\" :lines=\"3\" :text=\"goodsDetail.basicInfo.name\"></u-text>\r\n        <view v-if=\"goodsDetail.basicInfo.numberSells\" class=\"t2\">已售:{{ goodsDetail.basicInfo.numberSells }}\r\n        </view>\r\n        <view class=\"price\">\r\n          <view v-if=\"price\"><text>¥</text>{{ price }}</view>\r\n          <view v-if=\"score\">\r\n            <text>￠</text>{{ score }}\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <u-line dashed margin=\"32rpx\"></u-line>\r\n    <view v-for=\"(item,index) in properties\" :key=\"item.id\" v-if=\"!item.hidden\" class=\"skuList\">\r\n      <view class=\"t\">{{ item.name }}</view>\r\n      <view class=\"items\">\r\n        <view v-for=\"(item2,index2) in item.childsCurGoods\" :key=\"item2.id\" class=\"item\">\r\n          <u-tag :show=\"!item2.hidden\" :type=\"item2.selected ? 'error' : 'info'\" :plain=\"item2.selected ? false : true\" :text=\"item2.name\" @click=\"skuSelect(index, index2)\"></u-tag>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <view v-for=\"(item,index) in goodsAddition\" :key=\"item.id\" class=\"skuList\">\r\n      <view class=\"t\">{{ item.name }}</view>\r\n      <view class=\"items\">\r\n        <view v-for=\"(item2,index2) in item.items\" :key=\"item2.id\" class=\"item\">\r\n          <u-tag :type=\"item2.selected ? 'error' : 'info'\" :plain=\"item2.selected ? false : true\" :text=\"item2.name\" @click=\"additionSelect(index, index2)\"></u-tag>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <u-line v-if=\"goodsAddition || properties\" dashed margin=\"32rpx\"></u-line>\r\n    <block v-if=\"!kjid\">\r\n      <view class=\"buy-number\">\r\n        <text>购买数量</text>\r\n        <u-number-box v-model=\"buyNumber\" :min=\"min\" :max=\"max\" integer></u-number-box>\r\n      </view>\r\n      <u-line dashed margin=\"32rpx\"></u-line>\r\n    </block>\r\n    <view v-if=\"kjid\">\r\n      <view class=\"btn\">\r\n        <u-button text=\"立即购买\" shape=\"circle\" color=\"linear-gradient(90deg, #ff6034, #ee0a24, #ff6034)\" @click=\"tobuy\"></u-button>\r\n      </view>\r\n    </view>\r\n    <view v-else class=\"btns\">\r\n      <!--  #ifdef MP-WEIXIN\t|| MP-BAIDU -->\r\n      <view class=\"icon-btn\">\r\n        <u-icon name=\"chat\" size=\"48rpx\"></u-icon>\r\n        <text>客服</text>\r\n        <button open-type='contact' :send-message-title=\"goodsDetail.basicInfo.name\" :send-message-img=\"goodsDetail.basicInfo.pic\" :send-message-path=\"'/pages/goods/detail?id='+goodsDetail.basicInfo.id\" show-message-card></button>\r\n      </view>\r\n      <!--  #endif -->\r\n      <view class=\"icon-btn\" @click=\"goCart\">\r\n        <u-icon name=\"shopping-cart\" size=\"48rpx\"></u-icon>\r\n        <text>购物车</text>\r\n        <u-badge type=\"error\" :value=\"cartNumber\" absolute :offset=\"[-10, -10]\"></u-badge>\r\n      </view>\r\n      <view class=\"icon-btn\" @click=\"addFav\">\r\n        <u-icon :name=\"faved ? 'heart-fill' : 'heart'\" size=\"48rpx\"></u-icon>\r\n        <text>收藏</text>\r\n      </view>\r\n      <view class=\"btn\">\r\n        <u-button v-if=\"goodsDetail.category.id !== 391088\" class=\"half-l\" text=\"加入购物车\" shape=\"circle\" color=\"linear-gradient(90deg,#ffd01e, #ff8917)\" @click=\"addCart\">\r\n        </u-button>\r\n      </view>\r\n      <view class=\"btn\" style=\"margin-left: 10rpx\">\r\n        <u-button class=\"half-r\" text=\"立即购买\" shape=\"circle\" color=\"linear-gradient(90deg, #ff6034, #ee0a24)\" @click=\"tobuy\">\r\n        </u-button>\r\n      </view>\r\n    </view>\r\n  </u-popup>\r\n</template>\r\n\r\n<script>\r\n  const TOOLS = require('@/common/tools')\r\n  export default {\r\n    name: 'goods-pop',\r\n    props: {\r\n      show: {\r\n        type: Boolean,\r\n        default: false\r\n      },\r\n      goodsDetail: {\r\n        type: Object,\r\n        default: null\r\n      },\r\n      skuList: {\r\n        type: Array,\r\n        default: null\r\n      },\r\n      kjid: undefined\r\n    },\r\n    data() {\r\n      return {\r\n        pic: undefined,\r\n        price: undefined,\r\n        score: undefined,\r\n        buyNumber: 1,\r\n        min: 1,\r\n        max: 0,\r\n        propertyChildIds: undefined, // 用户已经选的sku信息数组\r\n        propertyChildNames: undefined,\r\n        goodsAddition: undefined,\r\n        faved: false,\r\n        properties: undefined,\r\n        lock: false\r\n      }\r\n    },\r\n    watch: {\r\n      // word(newVal, oldVal) {\r\n      // \tconsole.log('最新值是：'+newVal,\"原来的值是：\"+ oldVal);\r\n      // },\r\n      goodsDetail: {\r\n        deep: true,\r\n        immediate: true,\r\n        handler(newVal, oldName) {\r\n          this._initData()\r\n        }\r\n      }\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    methods: {\r\n      _initData() {\r\n        if (this.lock || !this.goodsDetail) {\r\n          return\r\n        }\r\n        this.pic = this.goodsDetail.basicInfo.pic\r\n        this.price = this.goodsDetail.basicInfo.minPrice\r\n        this.score = this.goodsDetail.basicInfo.minScore\r\n        if (!this.goodsDetail.basicInfo.stores) {\r\n          this.min = 0\r\n        } else {\r\n          this.min = 1\r\n        }\r\n        this.max = this.goodsDetail.basicInfo.stores\r\n        this.goodsAddition = null\r\n        if (this.goodsDetail.basicInfo.hasAddition) {\r\n          this._goodsAddition()\r\n        }\r\n        this.properties = this.goodsDetail.properties\r\n        TOOLS.showTabBarBadge()\r\n        this.goodsFavCheck()\r\n      },\r\n      close() {\r\n        this.lock = false\r\n        this.$emit('close')\r\n      },\r\n      // sku 选择事件\r\n      async skuSelect(index, index2) {\r\n        this.lock = true\r\n        this.buyNumber = 1\r\n        const properties = this.goodsDetail.properties\r\n        const p = properties[index]\r\n        const c = p.childsCurGoods[index2]\r\n        // 当前sku往下的所有sku取消选中\r\n        for (let i = index; i < properties.length; i++) {\r\n          const _p = properties[i]\r\n          _p.childsCurGoods.forEach(ele => {\r\n            ele.selected = false\r\n          })\r\n          _p.selectedChild = null\r\n          properties.splice(i, 1, _p)\r\n        }\r\n        // 当前选中\r\n        c.selected = true\r\n        p.selectedChild = c\r\n        p.childsCurGoods.splice(index2, 1, c)\r\n        properties.splice(index, 1, p)\r\n        // 计算已经选中的sku信息\r\n        const propertyChildIds = []\r\n        const propertyChildNames = []\r\n        properties.forEach(ele => {\r\n          if (ele.selectedChild) {\r\n            propertyChildIds.push(ele.id + ':' + ele.selectedChild.id)\r\n            propertyChildNames.push(ele.name + ':' + ele.selectedChild.name)\r\n          }\r\n        })\r\n        this.propertyChildIds = propertyChildIds\r\n        this.propertyChildNames = propertyChildNames\r\n        // 目前条件下可继续匹配的sku\r\n        const skuList = this.goodsDetail.skuList.filter(ele => {\r\n          let ok = true\r\n          propertyChildIds.forEach(a => {\r\n            if (ele.propertyChildIds.indexOf(a) == -1) {\r\n              ok = false\r\n            }\r\n          })\r\n          return ok\r\n        })\r\n        // 设置下面的可选性\r\n        for (let i = index + 1; i < properties.length; i++) {\r\n          const _p = properties[i]\r\n          let a = skuList.findIndex(ele => {\r\n            return ele.propertyChildIds.indexOf(_p.id + ':') != -1\r\n          })\r\n          if (a == -1) {\r\n            _p.hidden = true\r\n          } else {\r\n            _p.hidden = false\r\n          }\r\n          _p.childsCurGoods.forEach(c => {\r\n            a = skuList.findIndex(ele => {\r\n              return ele.propertyChildIds.indexOf(_p.id + ':' + c.id) != -1\r\n            })\r\n            if (a == -1) {\r\n              c.hidden = true\r\n            } else {\r\n              c.hidden = false\r\n            }\r\n          })\r\n          properties.splice(i, 1, _p)\r\n        }\r\n        // 切换sku商品图片\r\n        if (this.goodsDetail.subPics && this.goodsDetail.subPics.length > 0) {\r\n          const _subPic = this.goodsDetail.subPics.find(ele => {\r\n            return ele.optionValueId == c.id\r\n          })\r\n          if (_subPic) {\r\n            this.pic = _subPic.pic\r\n          }\r\n        }\r\n        this.properties = properties\r\n        this.calculateGoodsPrice()\r\n      },\r\n      // 可选配件选择事件\r\n      async additionSelect(index, index2) {\r\n        const p = this.goodsAddition[index]\r\n        const c = p.items[index2]\r\n        if (c.selected) {\r\n          // 该操作为取消选择\r\n          c.selected = false\r\n          p.items.splice(index2, 1, c)\r\n          this.calculateGoodsPrice()\r\n          return\r\n        }\r\n        // 如果是单选，先取消现有选中的\r\n        if (p.type == 0) {\r\n          p.items.forEach(child => {\r\n            child.selected = false\r\n          })\r\n        }\r\n        c.selected = true\r\n        p.items.splice(index2, 1, c)\r\n        this.goodsAddition.splice(index, 1, p)\r\n        this.calculateGoodsPrice()\r\n      },\r\n      async _goodsAddition() {\r\n        // https://www.yuque.com/apifm/nu0f75/lveknt\r\n        const res = await this.$wxapi.goodsAddition(this.goodsDetail.basicInfo.id)\r\n        if (res.code == 0) {\r\n          this.goodsAddition = res.data\r\n        }\r\n      },\r\n      async calculateGoodsPrice() {\r\n        // 计算最终的商品价格\r\n        if (!this.propertyChildIds || !this.properties || this.propertyChildIds.length != this\r\n          .properties.length) {\r\n          this.price = this.goodsDetail.basicInfo.minPrice\r\n          this.score = this.goodsDetail.basicInfo.minScore\r\n          if (!this.goodsDetail.basicInfo.stores) {\r\n            this.min = 0\r\n          } else {\r\n            this.min = 1\r\n          }\r\n          this.max = this.goodsDetail.basicInfo.stores\r\n          return\r\n        }\r\n        // 获取价格\r\n        // https://www.yuque.com/apifm/nu0f75/dxvqq2\r\n        const res = await this.$wxapi.goodsPriceV2({\r\n          token: this.token,\r\n          goodsId: this.goodsDetail.basicInfo.id,\r\n          propertyChildIds: this.propertyChildIds.join()\r\n        })\r\n        if (res.code != 0) {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n          return\r\n        }\r\n        this.price = res.data.price\r\n        this.score = res.data.score\r\n        if (!res.data.stores) {\r\n          this.min = 0\r\n        } else {\r\n          this.min = 1\r\n        }\r\n        this.max = res.data.stores\r\n        if (this.goodsAddition) {\r\n          this.goodsAddition.forEach(big => {\r\n            big.items.forEach(small => {\r\n              if (small.selected) {\r\n                this.price = (this.price * 100 + small.price * 100) / 100\r\n              }\r\n            })\r\n          })\r\n        }\r\n      },\r\n      goCart() {\r\n        this.close()\r\n        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {\r\n          uni.setStorageSync('cart_tabIndex', 1)\r\n        }\r\n        uni.switchTab({\r\n          url: \"/pages/cart/index\"\r\n        })\r\n      },\r\n      async goodsFavCheck() {\r\n        const data = {\r\n          token: this.token,\r\n          type: 0,\r\n          goodsId: this.goodsDetail.basicInfo.id\r\n        }\r\n        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {\r\n          data.type = 1\r\n          data.goodsId = this.goodsDetail.basicInfo.yyId\r\n        }\r\n        // https://www.yuque.com/apifm/nu0f75/ugf7y9\r\n        const res = await this.$wxapi.goodsFavCheckV2(data)\r\n        if (res.code == 0) {\r\n          this.faved = true\r\n        } else {\r\n          this.faved = false\r\n        }\r\n      },\r\n      async addFav() {\r\n        if (!await getApp().checkHasLoginedH5()) {\r\n          uni.navigateTo({\r\n            url: \"/pages/login/login\"\r\n          })\r\n          return\r\n        }\r\n        const data = {\r\n          token: this.token,\r\n          type: 0,\r\n          goodsId: this.goodsDetail.basicInfo.id\r\n        }\r\n        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {\r\n          data.type = 1\r\n          data.goodsId = this.goodsDetail.basicInfo.yyId\r\n        }\r\n        if (this.faved) {\r\n          // 取消收藏 https://www.yuque.com/apifm/nu0f75/zy4sil\r\n          const res = await this.$wxapi.goodsFavDeleteV2(data)\r\n          if (res.code == 0) {\r\n            this.faved = false\r\n          } else {\r\n            uni.showToast({\r\n              title: res.msg,\r\n              icon: 'none'\r\n            })\r\n          }\r\n        } else {\r\n          const extJsonStr = {\r\n            pic: this.goodsDetail.basicInfo.pic,\r\n            goodsName: this.goodsDetail.basicInfo.name,\r\n            supplyType: this.goodsDetail.basicInfo.supplyType\r\n          }\r\n          data.extJsonStr = JSON.stringify(extJsonStr)\r\n          // 加入收藏 https://www.yuque.com/apifm/nu0f75/mr1471\r\n          const res = await this.$wxapi.goodsFavAdd(data)\r\n          if (res.code == 0) {\r\n            this.faved = true\r\n          } else {\r\n            uni.showToast({\r\n              title: res.msg,\r\n              icon: 'none'\r\n            })\r\n          }\r\n        }\r\n      },\r\n      checkOk() {\r\n        if (this.properties && (!this.propertyChildIds || this.propertyChildIds.length != this\r\n            .properties.length)) {\r\n          uni.showToast({\r\n            title: '请选择规格',\r\n            icon: 'none'\r\n          })\r\n          return false\r\n        }\r\n        let additionAllSelect = true\r\n        if (this.goodsAddition && this.goodsAddition.length > 0) {\r\n          this.goodsAddition.forEach(p => {\r\n            if (p.required) {\r\n              const find = p.items.find(c => {\r\n                return c.selected\r\n              })\r\n              if (!find) {\r\n                additionAllSelect = false\r\n              }\r\n            }\r\n          })\r\n        }\r\n        if (!additionAllSelect) {\r\n          uni.showToast({\r\n            title: '请选择规格',\r\n            icon: 'none'\r\n          })\r\n          return false\r\n        }\r\n        return true\r\n      },\r\n      async addCart() {\r\n        if (!await getApp().checkHasLoginedH5()) {\r\n          uni.navigateTo({\r\n            url: \"/pages/login/login\"\r\n          })\r\n          return\r\n        }\r\n        if (!this.checkOk()) {\r\n          return\r\n        }\r\n        if (this.buyNumber < 1) {\r\n          uni.showToast({\r\n            title: '请选择购买数量',\r\n            icon: 'none'\r\n          })\r\n          return\r\n        }\r\n        const sku = []\r\n        if (this.properties && this.properties.length > 0) {\r\n          this.properties.forEach(ele => {\r\n            sku.push({\r\n              optionId: ele.id,\r\n              optionValueId: ele.selectedChild.id\r\n            })\r\n          })\r\n        }\r\n        const goodsAddition = []\r\n        if (this.goodsAddition && this.goodsAddition.length > 0) {\r\n          this.goodsAddition.forEach(ele => {\r\n            ele.items.forEach(item => {\r\n              if (item.selected) {\r\n                goodsAddition.push({\r\n                  id: item.id,\r\n                  pid: item.pid\r\n                })\r\n              }\r\n            })\r\n          })\r\n        }\r\n        let res\r\n        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {\r\n          // https://www.yuque.com/apifm/nu0f75/yum741\r\n          res = await this.$wxapi.jdvopCartAdd({\r\n            token: this.token,\r\n            goodsId: this.goodsDetail.basicInfo.yyId,\r\n            number: this.buyNumber\r\n          })\r\n        } else {\r\n          // https://www.yuque.com/apifm/nu0f75/et6m6m\r\n          res = await this.$wxapi.shippingCarInfoAddItem(this.token, this.goodsDetail.basicInfo.id, this.buyNumber, sku, goodsAddition)\r\n        }\r\n        if (res.code != 0) {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n          return\r\n        }\r\n        this.close()\r\n        uni.showToast({\r\n          title: '加入购物车'\r\n        })\r\n        TOOLS.showTabBarBadge()\r\n      },\r\n      async tobuy() {\r\n        if (!await getApp().checkHasLoginedH5()) {\r\n          uni.navigateTo({\r\n            url: \"/pages/login/login\"\r\n          })\r\n          return\r\n        }\r\n        if (!this.checkOk()) {\r\n          return\r\n        }\r\n        if (this.buyNumber < 1) {\r\n          uni.showToast({\r\n            title: '请选择购买数量',\r\n            icon: 'none'\r\n          })\r\n          return\r\n        }\r\n        const sku = []\r\n        if (this.properties && this.properties.length > 0) {\r\n          this.properties.forEach(ele => {\r\n            sku.push({\r\n              optionId: ele.id,\r\n              optionValueId: ele.selectedChild.id,\r\n              optionName: ele.name,\r\n              optionValueName: ele.selectedChild.name,\r\n            })\r\n          })\r\n        }\r\n        const goodsAddition = []\r\n        if (this.goodsAddition && this.goodsAddition.length > 0) {\r\n          this.goodsAddition.forEach(ele => {\r\n            ele.items.forEach(item => {\r\n              if (item.selected) {\r\n                goodsAddition.push({\r\n                  id: item.id,\r\n                  pid: item.pid,\r\n                  name: ele.name,\r\n                  pname: item.name,\r\n                })\r\n              }\r\n            })\r\n          })\r\n        }\r\n        let goodsType = 0\r\n        let goodsId = this.goodsDetail.basicInfo.id\r\n        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {\r\n          goodsType = 1\r\n          goodsId = this.goodsDetail.basicInfo.yyId\r\n        }\r\n        if (this.goodsDetail.basicInfo.supplyType == 'jdJoycityPoints') {\r\n          goodsType = 2\r\n          goodsId = this.goodsDetail.basicInfo.yyIdStr\r\n        }\r\n        const goodsList = [{\r\n          goodsId,\r\n          goodsName: this.goodsDetail.basicInfo.name,\r\n          number: this.buyNumber,\r\n          pic: this.goodsDetail.basicInfo.pic,\r\n          price: this.goodsDetail.basicInfo.minPrice,\r\n          score: this.goodsDetail.basicInfo.minScore,\r\n          sku, // optionId optionName optionValueId optionValueName\r\n          additions: goodsAddition, // id name pid pname price\r\n          goodsType,\r\n          kjid: this.kjid ? this.kjid : '',\r\n          logisticsId: this.goodsDetail.basicInfo.logisticsId ? this.goodsDetail.basicInfo.logisticsId : '',\r\n        }]\r\n\r\n        uni.setStorageSync('goodsList', goodsList)\r\n        uni.navigateTo({\r\n          url: '../pay/order?mod=buy'\r\n        })\r\n      },\r\n    }\r\n\r\n  }\r\n</script>\r\n<style scoped lang=\"scss\">\r\n  .goodsList-pop {\r\n    margin-top: 32rpx;\r\n    padding: 0 8rpx;\r\n    display: flex;\r\n\r\n    .goods-info {\r\n      flex: 1;\r\n      margin-left: 24rpx;\r\n      position: relative;\r\n\r\n      .t {\r\n        font-weight: bold;\r\n        font-size: 28rpx;\r\n      }\r\n\r\n      .t2 {\r\n        color: #666;\r\n        font-size: 26rpx;\r\n        padding: 8rpx 0;\r\n      }\r\n\r\n      .price {\r\n        color: #e64340;\r\n        font-size: 40rpx;\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        text {\r\n          font-size: 24rpx;\r\n        }\r\n      }\r\n\r\n      .addCar {\r\n        position: absolute;\r\n        right: 24rpx;\r\n        bottom: 16rpx;\r\n      }\r\n    }\r\n  }\r\n\r\n  .skuList {\r\n    .t {\r\n      margin-left: 32rpx;\r\n      color: #333;\r\n      font-size: 28rpx;\r\n      // font-weight: bold;\r\n    }\r\n\r\n    .items {\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n\r\n      .item {\r\n        margin: 16rpx 0 0 32rpx;\r\n      }\r\n\r\n      padding-bottom: 32rpx;\r\n    }\r\n  }\r\n\r\n  .buy-number {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 0 32rpx;\r\n    color: #333;\r\n    font-size: 30rpx;\r\n  }\r\n\r\n  .btns {\r\n    display: flex;\r\n    padding: 32rpx;\r\n\r\n    .icon-btn {\r\n      position: relative;\r\n      display: flex;\r\n      flex-direction: column;\r\n      align-items: center;\r\n      font-size: 24rpx;\r\n      color: #333;\r\n      margin-right: 32rpx;\r\n\r\n      button {\r\n        position: absolute;\r\n        height: 100%;\r\n        width: 100%;\r\n        opacity: 0;\r\n        z-index: 99;\r\n      }\r\n    }\r\n\r\n    .btn {\r\n      flex: 1;\r\n\r\n      .half-l {\r\n        border-top-right-radius: 0;\r\n        border-bottom-right-radius: 0;\r\n      }\r\n\r\n      .half-r {\r\n        border-top-left-radius: 0;\r\n        border-bottom-left-radius: 0;\r\n      }\r\n    }\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goods-pop.vue?vue&type=style&index=0&id=56f7457a&scoped=true&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./goods-pop.vue?vue&type=style&index=0&id=56f7457a&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688737714\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}