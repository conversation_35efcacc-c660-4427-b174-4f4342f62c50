{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-grid-item/u-grid-item.vue?b0a0", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-grid-item/u-grid-item.vue?0a39", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-grid-item/u-grid-item.vue?bcef", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-grid-item/u-grid-item.vue?a0a2", "uni-app:///uni_modules/uview-ui/components/u-grid-item/u-grid-item.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-grid-item/u-grid-item.vue?26ef", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-grid-item/u-grid-item.vue?77ea"], "names": ["name", "mixins", "data", "parentData", "col", "border", "classes", "mounted", "computed", "width", "itemStyle", "background", "methods", "init", "uni", "updateParentData", "clickHandler", "getItemWidth", "parentWidth", "getParent<PERSON>idth", "gridItemClasses", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoI;AACpI;AAC+D;AACL;AACsC;;;AAGhG;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,kGAAM;AACR,EAAE,2GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAsqB,CAAgB,srBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC2B1rB;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,eAUA;EACAA;EACAC;EACAC;IACA;MACAC;QACAC;QAAA;QACAC;MACA;;MAIAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IAEA;IACAC;MACA;IACA;IAEAC;MACA;QACAC;QACAF;MACA;MACA;IACA;EACA;EACAG;IACAC;MAAA;MACA;MACA;MACAC;QACA;MACA;MACA;MACA;;MAOA;MACAA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;QAAA;MACA;MACA;MACA;MACA;QACAhB;UAAA;QAAA;MACA;MACA;MACA;MACA;IACA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAR;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAS;gBACAT;cAAA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAU,2CAWA;IACAC;MAAA;MACA;QACA;QACA;UACA;YACA;YACA;YACA;cACAd;YACA;YACA;YACA;YACA;YACA;YACA;cACAA;YACA;UACA;QACA;QACA;;QAIA;MACA;IACA;EACA;EACAe;IACA;IACAP;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChKA;AAAA;AAAA;AAAA;AAA6xC,CAAgB,ivCAAG,EAAC,C;;;;;;;;;;;ACAjzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-grid-item/u-grid-item.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-grid-item.vue?vue&type=template&id=5b3a01af&scoped=true&\"\nvar renderjs\nimport script from \"./u-grid-item.vue?vue&type=script&lang=js&\"\nexport * from \"./u-grid-item.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-grid-item.vue?vue&type=style&index=0&id=5b3a01af&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5b3a01af\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-grid-item/u-grid-item.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-grid-item.vue?vue&type=template&id=5b3a01af&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.itemStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-grid-item.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-grid-item.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<!-- #ifndef APP-NVUE -->\r\n\t<view\r\n\t    class=\"u-grid-item\"\r\n\t    hover-class=\"u-grid-item--hover-class\"\r\n\t    :hover-stay-time=\"200\"\r\n\t    @tap=\"clickHandler\"\r\n\t    :class=\"classes\"\r\n\t    :style=\"[itemStyle]\"\r\n\t>\r\n\t\t<slot />\r\n\t</view>\r\n\t<!-- #endif -->\r\n\t<!-- #ifdef APP-NVUE -->\r\n\t<view\r\n\t    class=\"u-grid-item\"\r\n\t    :hover-stay-time=\"200\"\r\n\t    @tap=\"clickHandler\"\r\n\t    :class=\"classes\"\r\n\t    :style=\"[itemStyle]\"\r\n\t>\r\n\t\t<slot />\r\n\t</view>\r\n\t<!-- #endif -->\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t/**\r\n\t * gridItem 提示\r\n\t * @description 宫格组件一般用于同时展示多个同类项目的场景，可以给宫格的项目设置徽标组件(badge)，或者图标等，也可以扩展为左右滑动的轮播形式。搭配u-grid使用\r\n\t * @tutorial https://www.uviewui.com/components/grid.html\r\n\t * @property {String | Number}\tname\t\t宫格的name ( 默认 null )\r\n\t * @property {String}\t\t\tbgColor\t\t宫格的背景颜色 （默认 'transparent' ）\r\n\t * @property {Object}\t\t\tcustomStyle\t自定义样式，对象形式\r\n\t * @event {Function} click 点击宫格触发\r\n\t * @example <u-grid-item></u-grid-item>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-grid-item\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tparentData: {\r\n\t\t\t\t\tcol: 3, // 父组件划分的宫格数\r\n\t\t\t\t\tborder: true, // 是否显示边框，根据父组件决定\r\n\t\t\t\t},\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\twidth: 0, // nvue下才这么计算，vue下放到computed中，否则会因为延时造成闪烁\r\n\t\t\t\t// #endif\r\n\t\t\t\tclasses: [], // 类名集合，用于判断是否显示右边和下边框\r\n\t\t\t};\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// #ifndef APP-NVUE\r\n\t\t\t// vue下放到computed中，否则会因为延时造成闪烁\r\n\t\t\twidth() {\r\n\t\t\t\treturn 100 / Number(this.parentData.col) + '%'\r\n\t\t\t},\r\n\t\t\t// #endif\r\n\t\t\titemStyle() {\r\n\t\t\t\tconst style = {\r\n\t\t\t\t\tbackground: this.bgColor,\r\n\t\t\t\t\twidth: this.width\r\n\t\t\t\t}\r\n\t\t\t\treturn uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit() {\r\n\t\t\t\t// 用于在父组件u-grid的children中被添加入子组件时，\r\n\t\t\t\t// 重新计算item的边框\r\n\t\t\t\tuni.$on('$uGridItem', () => {\r\n\t\t\t\t\tthis.gridItemClasses()\r\n\t\t\t\t})\r\n\t\t\t\t// 父组件的实例\r\n\t\t\t\tthis.updateParentData()\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 获取元素该有的长度，nvue下要延时才准确\r\n\t\t\t\tthis.$nextTick(function(){\r\n\t\t\t\t\tthis.getItemWidth()\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\t// 发出事件，通知所有的grid-item都重新计算自己的边框\r\n\t\t\t\tuni.$emit('$uGridItem')\r\n\t\t\t\tthis.gridItemClasses()\r\n\t\t\t},\r\n\t\t\t// 获取父组件的参数\r\n\t\t\tupdateParentData() {\r\n\t\t\t\t// 此方法写在mixin中\r\n\t\t\t\tthis.getParentData('u-grid');\r\n\t\t\t},\r\n\t\t\tclickHandler() {\r\n\t\t\t\tlet name = this.name\r\n\t\t\t\t// 如果没有设置name属性，历遍父组件的children数组，判断当前的元素是否和本实例this相等，找出当前组件的索引\r\n\t\t\t\tconst children = this.parent?.children\r\n\t\t\t\tif(children && this.name === null) {\r\n\t\t\t\t\tname = children.findIndex(child => child === this)\r\n\t\t\t\t}\r\n\t\t\t\t// 调用父组件方法，发出事件\r\n\t\t\t\tthis.parent && this.parent.childClick(name)\r\n\t\t\t\tthis.$emit('click', name)\r\n\t\t\t},\r\n\t\t\tasync getItemWidth() {\r\n\t\t\t\t// 如果是nvue，不能使用百分比，只能使用固定宽度\r\n\t\t\t\tlet width = 0\r\n\t\t\t\tif(this.parent) {\r\n\t\t\t\t\t// 获取父组件宽度后，除以栅格数，得出每个item的宽度\r\n\t\t\t\t\tconst parentWidth = await this.getParentWidth()\r\n\t\t\t\t\twidth = parentWidth / Number(this.parentData.col) + 'px'\r\n\t\t\t\t}\r\n\t\t\t\tthis.width = width\r\n\t\t\t},\r\n\t\t\t// 获取父元素的尺寸\r\n\t\t\tgetParentWidth() {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 返回一个promise，让调用者可以用await同步获取\r\n\t\t\t\tconst dom = uni.requireNativePlugin('dom')\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\t// 调用父组件的ref\r\n\t\t\t\t\tdom.getComponentRect(this.parent.$refs['u-grid'], res => {\r\n\t\t\t\t\t\tresolve(res.size.width)\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tgridItemClasses() {\r\n\t\t\t\tif(this.parentData.border) {\r\n\t\t\t\t\tconst classes = []\r\n\t\t\t\t\tthis.parent.children.map((child, index) =>{\r\n\t\t\t\t\t\tif(this === child) {\r\n\t\t\t\t\t\t\tconst len = this.parent.children.length\r\n\t\t\t\t\t\t\t// 贴近右边屏幕边沿的child，并且最后一个（比如只有横向2个的时候），无需右边框\r\n\t\t\t\t\t\t\tif((index + 1) % this.parentData.col !== 0 && index + 1 !== len) {\r\n\t\t\t\t\t\t\t\tclasses.push('u-border-right')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t\t// 总的宫格数量对列数取余的值\r\n\t\t\t\t\t\t\t// 如果取余后，值为0，则意味着要将最后一排的宫格，都不需要下边框\r\n\t\t\t\t\t\t\tconst lessNum = len % this.parentData.col === 0 ? this.parentData.col : len % this.parentData.col\r\n\t\t\t\t\t\t\t// 最下面的一排child，无需下边框\r\n\t\t\t\t\t\t\tif(index < len - lessNum) {\r\n\t\t\t\t\t\t\t\tclasses.push('u-border-bottom')\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// 支付宝，头条小程序无法动态绑定一个数组类名，否则解析出来的结果会带有\",\"，而导致失效\r\n\t\t\t\t\t// #ifdef MP-ALIPAY || MP-TOUTIAO\r\n\t\t\t\t\tclasses = classes.join(' ')\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\tthis.classes = classes\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tbeforeDestroy() {\r\n\t\t\t// 移除事件监听，释放性能\r\n\t\t\tuni.$off('$uGridItem')\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n      $u-grid-item-hover-class-opcatiy:.5 !default;\r\n      $u-grid-item-margin-top:1rpx !default;\r\n      $u-grid-item-border-right-width:0.5px !default;\r\n      $u-grid-item-border-bottom-width:0.5px !default;\r\n      $u-grid-item-border-right-color:$u-border-color !default;\r\n      $u-grid-item-border-bottom-color:$u-border-color !default;\r\n\t.u-grid-item {\r\n\t\talign-items: center;\r\n\t\tjustify-content: center;\r\n\t\tposition: relative;\r\n\t\tflex-direction: column;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing: border-box;\r\n\t\tdisplay: flex;\r\n\t\t/* #endif */\r\n\r\n\t\t/* #ifdef MP */\r\n\t\tposition: relative;\r\n\t\tfloat: left;\r\n\t\t/* #endif */\r\n\r\n\t\t/* #ifdef MP-WEIXIN */\r\n\t\tmargin-top:$u-grid-item-margin-top;\r\n\t\t/* #endif */\r\n\r\n\t\t&--hover-class {\r\n\t\t\topacity:$u-grid-item-hover-class-opcatiy;\r\n\t\t}\r\n\t}\r\n\r\n\t/* #ifdef APP-NVUE */\r\n\t// 由于nvue不支持组件内引入app.vue中再引入的样式，所以需要写在这里\r\n\t.u-border-right {\r\n\t\tborder-right-width:$u-grid-item-border-right-width;\r\n\t\tborder-color: $u-grid-item-border-right-color;\r\n\t}\r\n\r\n\t.u-border-bottom {\r\n\t\tborder-bottom-width:$u-grid-item-border-bottom-width;\r\n\t\tborder-color:$u-grid-item-border-bottom-color;\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-grid-item.vue?vue&type=style&index=0&id=5b3a01af&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-grid-item.vue?vue&type=style&index=0&id=5b3a01af&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688737681\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}