@charset "UTF-8";
/**
 * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量
 * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可
 * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 
 */
/* 变量 */
view.data-v-56090129, scroll-view.data-v-56090129, swiper-item.data-v-56090129 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-swiper-indicator__wrapper.data-v-56090129 {
  display: flex;
  flex-direction: row;
}
.u-swiper-indicator__wrapper--line.data-v-56090129 {
  border-radius: 100px;
  height: 4px;
}
.u-swiper-indicator__wrapper--line__bar.data-v-56090129 {
  width: 22px;
  height: 4px;
  border-radius: 100px;
  background-color: #FFFFFF;
  transition: -webkit-transform 0.3s;
  transition: transform 0.3s;
  transition: transform 0.3s, -webkit-transform 0.3s;
}
.u-swiper-indicator__wrapper__dot.data-v-56090129 {
  width: 5px;
  height: 5px;
  border-radius: 100px;
  margin: 0 4px;
}
.u-swiper-indicator__wrapper__dot--active.data-v-56090129 {
  width: 12px;
}

