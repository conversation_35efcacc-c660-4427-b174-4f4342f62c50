(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uview-ui/components/u--text/u--text"],{"0af9":function(n,t,e){"use strict";e.r(t);var u=e("7a35"),i=e.n(u);for(var o in u)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(o);t["default"]=i.a},"7a35":function(n,t,e){"use strict";(function(n){var u=e("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var i=u(e("c4f3")),o={name:"u--text",mixins:[n.$u.mpMixin,i.default,n.$u.mixin],components:{uvText:function(){Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-text/u-text")]).then(function(){return resolve(e("828c"))}.bind(null,e)).catch(e.oe)}}};t.default=o}).call(this,e("df3c")["default"])},b648:function(n,t,e){"use strict";e.r(t);var u=e("d7e3"),i=e("0af9");for(var o in i)["default"].indexOf(o)<0&&function(n){e.d(t,n,(function(){return i[n]}))}(o);var a=e("828b"),c=Object(a["a"])(i["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);t["default"]=c.exports},d7e3:function(n,t,e){"use strict";e.d(t,"b",(function(){return u})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){}));var u=function(){var n=this.$createElement;this._self._c},i=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uview-ui/components/u--text/u--text-create-component',
    {
        'uni_modules/uview-ui/components/u--text/u--text-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("b648"))
        })
    },
    [['uni_modules/uview-ui/components/u--text/u--text-create-component']]
]);
