{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/list.vue?ee3a", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/list.vue?508d", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/list.vue?ab92", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/list.vue?6fa0", "uni-app:///packageFx/coupons/list.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/list.vue?acc3", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/list.vue?023c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "type", "list", "food", "goods", "selectedIndex", "images", "src", "components", "shopcart", "cartcontrol", "onLoad", "pic", "query", "tag", "title", "methods", "cardDataGet", "that", "uni", "name", "foods", "price", "originalPrice", "description", "sellCount", "img", "id", "goto", "url", "cardImgSelect", "select", "addCart", "item", "good", "<PERSON><PERSON>", "decreaseCart", "inputCart", "delAll"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,+qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC8CnrB;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;MACA,GACA;QACAA;MACA,GACA;QACAA;MACA;IAEA;EACA;EACAC;IACAC;IACAC;EACA;EACAC;IACA,IACAC,MAIAC,MAJAD;MACAE,MAGAD,MAHAC;MACAC,QAEAF,MAFAE;MACAd,OACAY,MADAZ;IAEA;IAEA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA,GACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA,GACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA,GACA;UACA;UACA;UACA;UACA;UACA;UACA;QACA;MAEA;MAEA;IACA;IAEA;MACA;IACA;IAEA;MACA;IACA;EACA;EACAe;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA,OAEAC;kBACAlB;gBACA;kBACAiB;oBACAE;oBACAC;sBAAA;wBACAD;wBACAE;wBACAC;wBACAC;wBACAC;wBACAC;wBACAC;sBACA;oBAAA;kBACA;kBAEA;gBACA;kBACA;gBAAA,CACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;QACAT;UACAU;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UACA;QACA;MACA;MACA;IACA;IACAC;MACA;QACAC;QACA;UACAC;YACA,4BACA/B;UACA;QACA;MACA;QACA;UACA+B;YACA,4BACAC;UACA;QACA;MACA;IACA;IACAC;MACA;QACAH;QACA;UACAC;YACA,4BACA/B;UACA;QACA;MACA;IACA;IACAkC;MACA;QACAJ;QACA;UACAC;YACA,4BACA/B;UACA;QACA;MACA;QACA;UACA+B;YACA,4BACAC;UACA;QACA;MACA;IACA;IACAG;MACA;QACAJ;UACA;YACA/B;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC9OA;AAAA;AAAA;AAAA;AAA28B,CAAgB,87BAAG,EAAC,C;;;;;;;;;;;ACA/9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packageFx/coupons/list.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packageFx/coupons/list.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list.vue?vue&type=template&id=454dc0e5&\"\nvar renderjs\nimport script from \"./list.vue?vue&type=script&lang=js&\"\nexport * from \"./list.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packageFx/coupons/list.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=template&id=454dc0e5&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = Object.keys(_vm.list).length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"container-wrapper\">\r\n    <view class=\"main-wrapper\">\r\n      <view class=\"top-image-wrapper\">\r\n        <div class=\"top-image\">&nbsp;</div>\r\n      </view>\r\n      <view class=\"main\">\r\n        <view v-if=\"type === 'virtual'\">\r\n          <view class=\"title\">选择卡面</view>\r\n          <view class=\"card\">\r\n            <view v-for=\"(image, index) in images\" :key=\"index\" @tap=\"cardImgSelect(index)\">\r\n              <img :src=\"image.src\" :class=\"{ active: selectedIndex === index }\">\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"title\">选择礼品卡</view>\r\n        <view class=\"food-wrapper\" ref=\"foodsWrapper\">\r\n          <view v-if=\"Object.keys(list).length != 0\" class=\"food\" v-for=\"(food, index) in list.foods\" :key=\"index\" :style=\"{ 'border': food.count > 0 ? '1px #21A921 solid' : '1px #e4e4e4 solid' }\">\r\n            <image @click=\"goto(food)\" v-if=\"type === 'physical'\" class=\"food-img physical\" :src=\"food.img\" mode=\"\"></image>\r\n            <image @click=\"goto(food)\" v-if=\"type === 'virtual'\" class=\"food-img virtual\" :src=\"food.img\" mode=\"\"></image>\r\n            <image @click=\"goto(food)\" v-if=\"type === 'water'\" class=\"food-img water\" :src=\"food.img\" mode=\"\"></image>\r\n            <view class=\"food-info\">\r\n              <text class=\"food-title\" @click=\"goto(food)\">{{food.name}}</text>\r\n              <text class=\"food-dsc\" @click=\"goto(food)\">{{food.description}}</text>\r\n\r\n              <view v-if=\"food.price\" class=\"price-score\">\r\n                <view class=\"item\">\r\n                  <view><text>¥</text>{{food.price}}<span v-if=\"food.originalPrice\" class=\"original-pice\">原价￥{{ food.originalPrice }}</span></view>\r\n                </view>\r\n              </view>\r\n\r\n              <view class=\"food-btn\">\r\n                <cartcontrol :food=\"food\" @add=\"addCart\" @dec=\"decreaseCart\" @input=\"inputCart\"></cartcontrol>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <shopcart :goods=\"goods\" @add=\"addCart\" @dec=\"decreaseCart\" @input=\"inputCart\" @delAll=\"delAll\"></shopcart>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import shopcart from '@/components/shopcart.vue';\r\n  import cartcontrol from '@/components/cartcontrol.vue'\r\n  import Vue from 'vue'\r\n\r\n  export default {\r\n    data() {\r\n      return {\r\n        type: '',\r\n        list: [],\r\n        food: {},\r\n        goods: [],\r\n        selectedIndex: -1,\r\n        images: [{\r\n            src: 'https://ye.niutouren.vip/static/images/coupons/ln1.jpg'\r\n          },\r\n          {\r\n            src: 'https://ye.niutouren.vip/static/images/coupons/ln2.jpg'\r\n          },\r\n          {\r\n            src: 'https://ye.niutouren.vip/static/images/coupons/ln3.jpg'\r\n          },\r\n        ],\r\n      }\r\n    },\r\n    components: {\r\n      shopcart,\r\n      cartcontrol\r\n    },\r\n    onLoad(query) {\r\n      const {\r\n        pic,\r\n        tag,\r\n        title,\r\n        type\r\n      } = query\r\n      this.type = type\r\n\r\n      if (type === 'virtual') {\r\n        this.goods = [{\r\n          'name': 'newyear',\r\n          'foods': [{\r\n              'name': '100元',\r\n              'price': 100,\r\n              'originalPrice': '',\r\n              'description': '礼品卡100元',\r\n              'sellCount': 100,\r\n              'img': 'https://ye.niutouren.vip/static/images/coupons/100.png'\r\n            }, {\r\n              'name': '200元',\r\n              'price': 200,\r\n              'originalPrice': '',\r\n              'description': '礼品卡200元',\r\n              'sellCount': 100,\r\n              'img': 'https://ye.niutouren.vip/static/images/coupons/200.png'\r\n            },\r\n            {\r\n              'name': '500元',\r\n              'price': 500,\r\n              'originalPrice': '',\r\n              'description': '礼品卡500元',\r\n              'sellCount': 100,\r\n              'img': 'https://ye.niutouren.vip/static/images/coupons/500.png'\r\n            },\r\n            {\r\n              'name': '1000元',\r\n              'price': 1000,\r\n              'originalPrice': '',\r\n              'description': '礼品卡1000元',\r\n              'sellCount': 100,\r\n              'img': 'https://ye.niutouren.vip/static/images/coupons/1000.png'\r\n            },\r\n            {\r\n              'name': '2000元',\r\n              'price': 2000,\r\n              'originalPrice': '',\r\n              'description': '礼品卡2000元',\r\n              'sellCount': 100,\r\n              'img': 'https://ye.niutouren.vip/static/images/coupons/2000.png'\r\n            }\r\n          ]\r\n        }]\r\n\r\n        this.select(0, 'newyear')\r\n      }\r\n\r\n      if (type === 'physical') {\r\n        this.cardDataGet(type)\r\n      }\r\n\r\n      if (type === 'water') {\r\n        this.cardDataGet(type)\r\n      }\r\n    },\r\n    methods: {\r\n      async cardDataGet(type) {\r\n        let that = this\r\n\r\n        await uni.$u.http.post('https://ye.niutouren.vip/api/card', {\r\n          type: type,\r\n        }).then(res => {\r\n          that.goods = [{\r\n            name: 'newyear',\r\n            foods: res.goods.map(item => ({\r\n              name: item.name,\r\n              price: item.minPrice,\r\n              originalPrice: item.originalPrice,\r\n              description: '',\r\n              sellCount: item.numberSells,\r\n              img: item.pic,\r\n              id: item.id\r\n            }))\r\n          }]\r\n\r\n          this.select(0, 'newyear')\r\n        }).catch(err => {\r\n          //\r\n        })\r\n      },\r\n      goto(item) {\r\n        if (item.id) {\r\n          uni.navigateTo({\r\n            url: '/pages/goods/detail?card=1&id=' + item.id\r\n          })\r\n        }\r\n      },\r\n      cardImgSelect(index) {\r\n        this.selectedIndex = index\r\n      },\r\n      select(index, name) {\r\n        this.food = {}\r\n        this.goods.forEach(item => {\r\n          if (item.name === name) {\r\n            this.list = item\r\n          }\r\n        })\r\n        this.currentIndex = index;\r\n      },\r\n      addCart: function(item) {\r\n        if (item.count >= 0) {\r\n          item.count++\r\n          this.goods.forEach((good) => {\r\n            good.foods.forEach((food) => {\r\n              if (item.name == food.name)\r\n                food.count = item.count\r\n            })\r\n          })\r\n        } else {\r\n          this.goods.forEach((good) => {\r\n            good.foods.forEach((food) => {\r\n              if (item.name == food.name)\r\n                Vue.set(food, 'count', 1)\r\n            })\r\n          })\r\n        }\r\n      },\r\n      decreaseCart(item) {\r\n        if (item.count) {\r\n          item.count--\r\n          this.goods.forEach((good) => {\r\n            good.foods.forEach((food) => {\r\n              if (item.name == food.name)\r\n                food.count = item.count\r\n            })\r\n          })\r\n        }\r\n      },\r\n      inputCart: function(item) {\r\n        if (item.count >= 0) {\r\n          item.count++\r\n          this.goods.forEach((good) => {\r\n            good.foods.forEach((food) => {\r\n              if (item.name == food.name)\r\n                food.count = item.count + -1\r\n            })\r\n          })\r\n        } else {\r\n          this.goods.forEach((good) => {\r\n            good.foods.forEach((food) => {\r\n              if (item.name == food.name)\r\n                Vue.set(food, 'count', 1)\r\n            })\r\n          })\r\n        }\r\n      },\r\n      delAll() {\r\n        this.goods.forEach((good) => {\r\n          good.foods.forEach((food) => {\r\n            if (food.count) {\r\n              food.count = 0\r\n            }\r\n          })\r\n        })\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style>\r\n  .container-wrapper {\r\n    padding: 0;\r\n    margin: 0;\r\n    background: #ffffff;\r\n    height: 100vh;\r\n  }\r\n\r\n  .main-wrapper {\r\n    position: relative;\r\n  }\r\n\r\n  .top-image {\r\n    width: 100%;\r\n    height: 220px;\r\n    background-image: url('https://ye.niutouren.vip/static/images/coupons/bg.png');\r\n    background-size: cover;\r\n    background-position: bottom center;\r\n  }\r\n\r\n  .main {\r\n    background: #FFFFFF;\r\n    margin: 0;\r\n    margin-top: -120rpx;\r\n    padding: 30rpx;\r\n    min-height: 500rpx;\r\n    padding-bottom: 200rpx;\r\n  }\r\n\r\n  .main .title {\r\n    color: #2b2b2b;\r\n    font-size: 30rpx;\r\n    margin: 30rpx 0 20rpx 0;\r\n  }\r\n\r\n  .main .card {\r\n    display: flex;\r\n    flex-direction: row;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n  }\r\n\r\n  .main .card img {\r\n    width: 200rpx;\r\n    height: 257rpx;\r\n    border-radius: 10rpx;\r\n  }\r\n\r\n  .main .card img.active {\r\n    border: 6rpx solid #5EA537;\r\n  }\r\n\r\n  .menu-wrapper {\r\n    text-align: center;\r\n    width: 22%;\r\n    display: flex;\r\n    flex-direction: column;\r\n    background: #f3f5f7;\r\n  }\r\n\r\n  .menu-item {\r\n    width: 100%;\r\n    display: flex;\r\n    align-items: center;\r\n    justify-content: space-around;\r\n    height: 50px;\r\n  }\r\n\r\n  .food-wrapper {\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n  }\r\n\r\n  .food {\r\n    width: 48%;\r\n    border-radius: 20rpx;\r\n    margin-bottom: 20rpx;\r\n    text-align: center;\r\n  }\r\n\r\n  .food:nth-child(odd) {\r\n    margin-right: 2%;\r\n  }\r\n\r\n  .food-img {\r\n    width: 113px;\r\n    height: 75px;\r\n    margin-top: 6px;\r\n  }\r\n\r\n  .food-img.physical,\r\n  .food-img.water {\r\n    width: 130px;\r\n    height: 130px;\r\n    border-radius: 10px;\r\n    margin-top: 6px;\r\n  }\r\n\r\n  .food-info {\r\n    margin-left: 10px;\r\n    margin-right: 16px;\r\n    color: #858996;\r\n    font-size: 24rpx;\r\n    display: flex;\r\n    flex-direction: column;\r\n    flex: 2;\r\n  }\r\n\r\n  .food-title {\r\n    padding: 2px 0;\r\n    font-size: 30rpx;\r\n    margin: 10rpx 0 0 0;\r\n    display: -webkit-box;\r\n    -webkit-line-clamp: 2;\r\n    -webkit-box-orient: vertical;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n  }\r\n\r\n  .food-dsc {\r\n    color: #858996;\r\n    font-size: 24rpx;\r\n  }\r\n\r\n  .food-btn {\r\n    display: flex;\r\n    flex-direction: row;\r\n    margin: 20rpx 0 20rpx 60rpx;\r\n  }\r\n\r\n  .food-price {\r\n    color: #f01414;\r\n    font-size: 16px;\r\n  }\r\n\r\n  .original-pice {\r\n    font-size: 20rpx;\r\n    font-weight: normal;\r\n    margin-top: 10rpx;\r\n    margin-left: 6rpx;\r\n    text-decoration: line-through;\r\n    color: #858996;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692277259\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}