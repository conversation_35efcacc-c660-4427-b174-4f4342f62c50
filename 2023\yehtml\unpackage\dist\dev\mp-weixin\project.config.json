{"description": "项目配置文件。", "packOptions": {"ignore": []}, "setting": {"urlCheck": true, "es6": false, "postcss": true, "minified": true, "newFeature": true, "bigPackageSizeSupport": true, "minifyWXML": true}, "compileType": "miniprogram", "libVersion": "latest", "appid": "wx7f61f277224f688c", "projectname": "野贝农beta1.0.206", "condition": {"search": {"current": -1, "list": []}, "conversation": {"current": -1, "list": []}, "game": {"current": -1, "list": []}, "miniprogram": {"current": 1, "list": [{"name": "", "path": "", "query": "", "id": 0}, {"id": 1, "name": "pages/index/index", "pathName": "pages/index/index", "query": ""}]}}}