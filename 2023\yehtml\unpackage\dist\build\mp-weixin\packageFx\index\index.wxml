<view><view><image style="width:750rpx;height:486rpx;" mode="aspectFit" src="https://dcdn.it120.cc/static/yidianan/index-top-bg.png"></image></view><block wx:if="{{apiUserInfoMap.base&&apiUserInfoMap.base.isSeller}}"><view><view class="tabTop" style="margin-top:-420rpx;"><view class="header-box"><image class="avatar" src="{{apiUserInfoMap.base.avatarUrl}}" mode="aspectFill"></image><view class="r"><view class="uid">{{"用户编号: "+apiUserInfoMap.base.id}}</view><view style="display:flex;"><view class="nick">{{apiUserInfoMap.base.nick}}</view></view></view></view><view class="header-box2"></view><view class="line"></view><view class="asset"><view data-event-opts="{{[['tap',[['goAsset',['$event']]]]]}}" class="item" style="width:170rpx;" bindtap="__e"><view class="Count">{{fxCommisionPaying}}</view><view>未结算金额</view></view><view data-event-opts="{{[['tap',[['goAsset',['$event']]]]]}}" class="item" style="width:170rpx;" bindtap="__e"><view class="Count">{{freeze}}</view><view>冻结金额</view></view><view data-event-opts="{{[['tap',[['goAsset',['$event']]]]]}}" class="item right" style="width:170rpx;" bindtap="__e"><view class="Count" style="color:#FF444A;">{{balance}}</view><view>可用金额</view></view></view><view class="line"></view><view class="titleXS">我的业绩</view><view class="asset"><view class="item"><view class="Count">{{commisionData.todayXiaoshou}}</view><view>今日销售</view><view class="yjP">{{commisionData.today?commisionData.today:0}}</view><view class="yjT">（佣金）</view></view><view class="item right"><view class="Count">{{commisionData.yesdayXiaoshou}}</view><view>昨天销售</view><view class="yjP">{{commisionData.yesday?commisionData.yesday:0}}</view><view class="yjT">（佣金）</view></view><view class="item right"><view class="Count">{{commisionData.thisMonthXiaoshou}}</view><view>本月销售</view><view class="yjP">{{commisionData.thisMonth?commisionData.thisMonth:0}}</view><view class="yjT">（佣金）</view></view><view class="item right"><view class="Count">{{commisionData.lastMonthXiaoshou}}</view><view>上月销售</view><view class="yjP">{{commisionData.lastMonth?commisionData.lastMonth:0}}</view><view class="yjT">（佣金）</view></view></view></view><block wx:if="{{apiUserInfoMap.referrer}}"><view class="tuan" style="padding:40rpx 40rpx 20rpx 40rpx;"><view>我的邀请人</view><view class="line2"></view><view style="display:flex;"><image style="width:80rpx;height:80rpx;margin:10px 20px 0px 0;border-radius:100%;" src="{{apiUserInfoMap.referrer.avatarUrl}}"></image><view style="height:120rpx;line-height:120rpx;font-size:26rpx;">{{apiUserInfoMap.referrer.nick}}</view></view></view></block><block wx:if="{{fxIndexAdPos}}"><image class="adpos" src="{{fxIndexAdPos.val}}" mode="widthFix" data-event-opts="{{[['tap',[['goUrl',['$0'],['fxIndexAdPos.url']]]]]}}" bindtap="__e"></image></block><u-cell-group vue-id="7a9e4726-1" title="分销信息" customStyle="padding: 0 24rpx;" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('7a9e4726-2')+','+('7a9e4726-1')}}" label="我的邀请码" labelWidth="100" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('7a9e4726-3')+','+('7a9e4726-2')}}" disabled="{{true}}" clearable="{{true}}" inputAlign="center" value="{{apiUserInfoMap.base.id}}" data-event-opts="{{[['^input',[['__set_model',['$0','id','$event',[]],['apiUserInfoMap.base']]]]]}}" bind:__l="__l" vue-slots="{{['suffix']}}"><u-button vue-id="{{('7a9e4726-4')+','+('7a9e4726-3')}}" slot="suffix" text="复制" type="success" size="mini" data-event-opts="{{[['^tap',[['copyContent',['$0'],['apiUserInfoMap.base.id']]]]]}}" bind:tap="__e" bind:__l="__l"></u-button></u-input></u-form-item><u-cell vue-id="{{('7a9e4726-5')+','+('7a9e4726-1')}}" title="我的团队" value="查看" isLink="{{true}}" url="../myusers/index" bind:__l="__l"></u-cell><u-cell vue-id="{{('7a9e4726-6')+','+('7a9e4726-1')}}" title="推广订单" value="查看" isLink="{{true}}" url="../commisionLog/index" bind:__l="__l"></u-cell><u-cell vue-id="{{('7a9e4726-7')+','+('7a9e4726-1')}}" title="账单明细" value="查看" isLink="{{true}}" url="../../pages/asset/cashlog" bind:__l="__l"></u-cell></u-cell-group><block wx:if="{{apiUserInfoMap.saleDistributionTeam&&(apiUserInfoMap.saleDistributionTeam.leader==apiUserInfoMap.base.id||apiUserInfoMap.saleDistributionTeam.deputyLeader==apiUserInfoMap.base.id)}}"><u-cell-group vue-id="7a9e4726-8" title="我的团队" customStyle="padding: 0 24rpx;" bind:__l="__l" vue-slots="{{['default']}}"><u-cell vue-id="{{('7a9e4726-9')+','+('7a9e4726-8')}}" icon="setting-fill" title="{{apiUserInfoMap.saleDistributionTeam.name}}" bind:__l="__l"></u-cell><u-cell vue-id="{{('7a9e4726-10')+','+('7a9e4726-8')}}" icon="integral-fill" title="身份" value="{{apiUserInfoMap.saleDistributionTeam.leader==apiUserInfoMap.base.id?'队长':'副队长'}}" bind:__l="__l"></u-cell><u-cell vue-id="{{('7a9e4726-11')+','+('7a9e4726-8')}}" icon="integral-fill" title="销售目标" value="{{'¥'+apiUserInfoMap.saleDistributionTeam.standardSaleroom+'/月'}}" bind:__l="__l"></u-cell><u-cell vue-id="{{('7a9e4726-12')+','+('7a9e4726-8')}}" icon="integral-fill" title="本月销售" value="{{'¥'+apiUserInfoMap.saleDistributionTeam.curSaleroom}}" bind:__l="__l"></u-cell><u-cell vue-id="{{('7a9e4726-13')+','+('7a9e4726-8')}}" icon="integral-fill" title="月度报表" isLink="{{true}}" url="{{'../report/team?teamId='+apiUserInfoMap.base.teamId}}" bind:__l="__l"></u-cell></u-cell-group></block><block wx:for="{{fxCities}}" wx:for-item="item" wx:for-index="index"><u-cell-group vue-id="{{'7a9e4726-14-'+index}}" title="{{item.provinceName+item.cityName+'合伙人'}}" customStyle="padding: 0 24rpx;" bind:__l="__l" vue-slots="{{['default']}}"><u-cell vue-id="{{('7a9e4726-15-'+index)+','+('7a9e4726-14-'+index)}}" icon="setting-fill" title="销售目标" value="{{'¥'+item.standardSaleroom+'/月'}}" bind:__l="__l"></u-cell><u-cell vue-id="{{('7a9e4726-16-'+index)+','+('7a9e4726-14-'+index)}}" icon="integral-fill" title="本月销售" value="{{'¥'+item.curSaleroom}}" bind:__l="__l"></u-cell><u-cell vue-id="{{('7a9e4726-17-'+index)+','+('7a9e4726-14-'+index)}}" icon="integral-fill" title="月度报表" isLink="{{true}}" url="{{'../report/city?provinceId='+item.provinceId+'& cityId='+item.cityId}}" bind:__l="__l"></u-cell></u-cell-group></block><view class="noApply" style="padding-top:10px;padding-bottom:20px;"><view style="text-align:center;"><view class="canvas-box"><image class="canvas" style="width:200px;height:200px;" src="{{qrcode}}"></image></view><view data-event-opts="{{[['tap',[['saveToMobile',['$event']]]]]}}" class="tzBtn" style="margin-top:10px;background:#F5D795;padding:0 16rpx;" bindtap="__e">保存到相册</view></view></view></view></block><block wx:if="{{apiUserInfoMap.base&&!apiUserInfoMap.base.isSeller}}"><view class="tabTop" style="margin-top:-450rpx;"><view class="header-box"><image class="avatar" src="{{apiUserInfoMap.base.avatarUrl}}" mode="aspectFill"></image><view class="r"><view class="uid">{{"用户ID: "+apiUserInfoMap.base.id}}</view><view class="nick">{{apiUserInfoMap.base.nick}}</view></view></view><view class="header-box2">您当前还不是分销商</view><view class="line"></view><view data-event-opts="{{[['tap',[['goApply',['$event']]]]]}}" class="header-box2" bindtap="__e">立即前往申请成为分销商 ></view></view></block></view>