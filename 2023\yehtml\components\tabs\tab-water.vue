<template>
  <view class="tab-water">
    <list2 :list="goods"></list2>
  </view>
</template>

<script>
  import list2 from '@/components/list/list2'
  import empty from 'empty-value'

  export default {
    components: {
      list2,
    },
    data() {
      return {
        goods: [],
      }
    },
    mounted() {
      this._goods()
    },
    watch: {
      list: function(val) {
        //console.log('watch list is', val)
      }
    },
    methods: {
      changeTab(tab) {
        let that = this
        that.activeTab = tab
        that.goods = []
        that._goods()
      },
      async _goods() {
        let categoryId = '424167'
        // https://www.yuque.com/apifm/nu0f75/wg5t98
        const res = await this.$wxapi.goodsv2({
          categoryId: categoryId,
          showExtJson: true
        })
        if (res.code == 0) {
          let _goods = []
          let goods = []
          let goodsNew = []

          _goods = res.data.result

          if (!empty(_goods)) {
            _goods.forEach(async (good, index) => {
              good.image = good.pic
              good.title = good.name
              goods.push(good)
            })
          }

          this.goods = goods
        }
      },
    },
  }
</script>
<style>
  .tab-water {
    margin: 0 12px;
  }

  .tab.line {
    position: relative;
    width: 100%;
  }

  .tab.line::after {
    content: "";
    position: absolute;
    top: 20%;
    right: 0;
    bottom: 10%;
    width: 1px;
    background-color: #d3d3d3;
  }

  .tab .title {
    font-size: 34rpx;
    font-weight: bolder;
    margin-bottom: 10rpx;
  }

  .tab .dsc {
    font-size: 26rpx;
    height: 60rpx;
  }

  .tab.active {
    background-image: url("/static/images/tab-bg.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  .tab.active .dsc {
    background-image: url("/static/images/tab-dsc-bg.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    font-size: 28rpx;
    line-height: 56rpx;
    color: #FFFFFF;
  }
</style>