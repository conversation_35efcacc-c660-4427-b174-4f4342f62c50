<view class="u-album data-v-1055b7c9"><block wx:for="{{$root.l1}}" wx:for-item="arr" wx:for-index="index" wx:key="index"><view class="u-album__row data-v-1055b7c9 vue-ref-in-for" forComputedUse="{{albumWidth}}" data-ref="u-album__row"><block wx:for="{{arr.l0}}" wx:for-item="item" wx:for-index="index1" wx:key="index1"><view data-event-opts="{{[['tap',[['e0',['$event']]]]]}}" data-event-params="{{({item:item.$orig})}}" class="u-album__row__wrapper data-v-1055b7c9" style="{{item.s0}}" bindtap="__e"><image style="{{'width:'+(imageWidth)+';'+('height:'+(imageHeight)+';')}}" src="{{item.m0}}" mode="{{arr.g0===1?imageHeight>0?singleMode:'widthFix':multipleMode}}" class="data-v-1055b7c9"></image><block wx:if="{{item.g1}}"><view class="u-album__row__wrapper__text data-v-1055b7c9"><u--text vue-id="{{'4838ce8d-1-'+index+'-'+index1}}" text="{{'+'+(item.g2-maxCount)}}" color="#fff" size="{{multipleSize*0.3}}" align="center" customStyle="justify-content: center" class="data-v-1055b7c9" bind:__l="__l"></u--text></view></block></view></block></view></block></view>