(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/zb-tooltip/components/zb-tooltip/zb-tooltip"],{"339e":function(t,e,o){"use strict";(function(t){var n=o("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=n(o("7eb4")),r=n(o("ee10")),i={props:{visible:Boolean,color:{type:String,default:"#303133"},placement:{type:String,default:"top"},content:{type:String,default:""},show:{type:Boolean,default:!1}},data:function(){return{isShow:this.visible,title:"Hello",arrowLeft:0,query:null,style:{},arrowStyle:{},closeTimer:null}},onLoad:function(){},beforeDestroy:function(){clearTimeout(this.closeTimer)},watch:{isShow:{handler:function(t){this.$emit("update:visible",t)},immediate:!0},visible:{handler:function(t){var e=this;t&&this.$nextTick((function(){e.getPosition()})),this.isShow=t},immediate:!0}},mounted:function(){this.getPosition()},methods:{close:function(){this.isShow=!1},fixedWrap:function(){this.isShow=!1},handleClick:function(){var t=this;return(0,r.default)(a.default.mark((function e(){return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(clearTimeout(t.closeTimer),!t.isShow){e.next=3;break}return e.abrupt("return",t.isShow=!1);case 3:return e.next=5,t.getPosition();case 5:t.isShow=!0,t.closeTimer=setTimeout((function(){t.isShow=!1}),2e3);case 7:case"end":return e.stop()}}),e)})))()},getPosition:function(){var e=this;return new Promise((function(o){t.createSelectorQuery().in(e).selectAll(".zb_tooltip_content,.zb_tooltip__popper").boundingClientRect(function(){var t=(0,r.default)(a.default.mark((function t(n){var r,i,c,p,u,s;return a.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:r=n[0],r.left,r.bottom,r.right,r.top,i=r.width,c=r.height,p=n[1],u={},s={},t.t0=e.placement,t.next="top"===t.t0?7:"top-start"===t.t0?11:"top-end"===t.t0?14:"bottom"===t.t0?18:"bottom-start"===t.t0?22:"bottom-end"===t.t0?26:"right"===t.t0?30:"right-start"===t.t0?34:"right-end"===t.t0?38:"left"===t.t0?42:"left-start"===t.t0?46:"left-end"===t.t0?50:54;break;case 7:return p.width>i?u.left="-".concat((p.width-i)/2,"px"):u.left="".concat(Math.abs(p.width-i)/2,"px"),u.bottom="".concat(c+8,"px"),s.left=p.width/2-6+"px",t.abrupt("break",54);case 11:return u.left="0px",u.bottom="".concat(c+8,"px"),t.abrupt("break",54);case 14:return u.right="0px",u.bottom="".concat(c+8,"px"),s.right="8px",t.abrupt("break",54);case 18:return p.width>i?u.left="-".concat((p.width-i)/2,"px"):u.left="".concat(Math.abs(p.width-i)/2,"px"),u.top="".concat(c+8,"px"),s.left=p.width/2-6+"px",t.abrupt("break",54);case 22:return u.left="0px",u.top="".concat(c+8,"px"),s.left="8px",t.abrupt("break",54);case 26:return u.right="0px",u.top="".concat(c+8,"px"),s.right="8px",t.abrupt("break",54);case 30:return u.left="".concat(i+8,"px"),p.height>c?u.top="-".concat((p.height-c)/2,"px"):u.top="".concat(Math.abs((p.height-c)/2),"px"),s.top="".concat(p.height/2-6,"px"),t.abrupt("break",54);case 34:return u.left="".concat(i+8,"px"),u.top="0px",s.top="8px",t.abrupt("break",54);case 38:return u.left="".concat(i+8,"px"),u.bottom="0px",s.bottom="8px",t.abrupt("break",54);case 42:return u.right="".concat(i+8,"px"),p.height>c?u.top="-".concat((p.height-c)/2,"px"):u.top="".concat(Math.abs((p.height-c)/2),"px"),s.top="".concat(p.height/2-6,"px"),t.abrupt("break",54);case 46:return u.right="".concat(i+8,"px"),u.top="0px",s.top="8px",t.abrupt("break",54);case 50:return u.right="".concat(i+8,"px"),u.bottom="0px",s.bottom="8px",t.abrupt("break",54);case 54:e.style=u,e.arrowStyle=s,o();case 57:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}()).exec()}))}}};e.default=i}).call(this,o("df3c")["default"])},"41ac":function(t,e,o){},"50e3":function(t,e,o){"use strict";o.r(e);var n=o("339e"),a=o.n(n);for(var r in n)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return n[t]}))}(r);e["default"]=a.a},b5d1:function(t,e,o){"use strict";o.d(e,"b",(function(){return n})),o.d(e,"c",(function(){return a})),o.d(e,"a",(function(){}));var n=function(){var t=this,e=t.$createElement,o=(t._self._c,t.__get_style([t.style,{visibility:t.isShow?"visible":"hidden",color:"white"===t.color?"":"#fff",boxShadow:"white"===t.color?"0 3px 6px -4px #0000001f, 0 6px 16px #00000014, 0 9px 28px 8px #0000000d":""}])),n=t.__get_style([t.arrowStyle]),a=t.placement.indexOf("bottom"),r=t.placement.indexOf("top"),i=t.placement.indexOf("right"),c=t.placement.indexOf("left");t._isMounted||(t.e0=function(t){t.stopPropagation()}),t.$mp.data=Object.assign({},{$root:{s0:o,s1:n,g0:a,g1:r,g2:i,g3:c}})},a=[]},c51f:function(t,e,o){"use strict";var n=o("41ac"),a=o.n(n);a.a},dffd:function(t,e,o){"use strict";o.r(e);var n=o("b5d1"),a=o("50e3");for(var r in a)["default"].indexOf(r)<0&&function(t){o.d(e,t,(function(){return a[t]}))}(r);o("c51f");var i=o("828b"),c=Object(i["a"])(a["default"],n["b"],n["c"],!1,null,"7ac15f43",null,!1,n["a"],void 0);e["default"]=c.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/zb-tooltip/components/zb-tooltip/zb-tooltip-create-component',
    {
        'uni_modules/zb-tooltip/components/zb-tooltip/zb-tooltip-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("dffd"))
        })
    },
    [['uni_modules/zb-tooltip/components/zb-tooltip/zb-tooltip-create-component']]
]);
