{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/live/index.vue?00a6", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/live/index.vue?f389", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/live/index.vue?5bef", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/live/index.vue?6baa", "uni-app:///packageFx/live/index.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/live/index.vue?f852", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/live/index.vue?eb4a"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "Date", "format", "date", "data", "aliveRooms", "onReady", "onLoad", "mounted", "methods", "_wxaMpLiveRooms", "uni", "title", "res", "ele", "goLiveRoom", "console", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,grBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACmCprBC;EACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;IACAC;EACA;EACA;IACA;MACAA,2DACAC;IACA;EACA;EACA;AACA;AAAA,eAEA;EACAC;IACA;MACAC;IACA;EACA;EACAC;EACAC;IACA;EACA;EACAC;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAC;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACAF;gBACA;kBACAE;oBACA;sBACAC;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACAC;MACA;QACA;MACA;MAEApB;QACAqB;MACA;MAEAN;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnGA;AAAA;AAAA;AAAA;AAAuxC,CAAgB,2uCAAG,EAAC,C;;;;;;;;;;;ACA3yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packageFx/live/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packageFx/live/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=05877b98&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=05877b98&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"05877b98\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packageFx/live/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=05877b98&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<view class=\"container\">\r\n\t\t\t<view class=\"miaosha-container\">\r\n\t\t\t\t<block v-for=\"(item, index) in aliveRooms\" :key=\"index\">\r\n\t\t\t\t\t<view class=\"miaosha-goods-list\" @click=\"goLiveRoom(item)\">\r\n\t\t\t\t\t\t<image :src=\"item.share_img\" class=\"image\" mode=\"aspectFill\" lazy-load=\"true\" />\r\n\t\t\t\t\t\t<view class=\"r\">\r\n\t\t\t\t\t\t\t<view class=\"goods-title\">{{item.name}}</view>\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t<u-icon name=\"account\" size=\"30rpx\"></u-icon><text>{{item.anchor_name}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<view class=\"label\">\r\n\t\t\t\t\t\t\t\t<u-icon name=\"clock\" size=\"30rpx\"></u-icon><text>{{item.start_time_str}}</text>\r\n\t\t\t\t\t\t\t</view>\r\n\r\n\t\t\t\t\t\t\t<view class=\"miaosha-price-btn\">\r\n\t\t\t\t\t\t\t\t<u-button\r\n\t\t\t\t\t\t\t\t\tv-if=\"item.live_status == 107 || item.live_status == 106 || item.live_status == 104\"\r\n\t\t\t\t\t\t\t\t\ttype=\"primary\" size=\"small\" block round plain>已过期</u-button>\r\n\t\t\t\t\t\t\t\t<u-button v-else-if=\"item.live_status == 102\" type=\"error\" size=\"small\" block round>即将开播\r\n\t\t\t\t\t\t\t\t</u-button>\r\n\t\t\t\t\t\t\t\t<u-button v-else-if=\"item.live_status == 103\" type=\"warning\" size=\"small\" block round>\r\n\t\t\t\t\t\t\t\t\t直播结束</u-button>\r\n\t\t\t\t\t\t\t\t<u-button v-else type=\"success\" size=\"small\" block round>正在直播</u-button>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</block>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\tDate.prototype.format = function(format) {\r\n\t\tvar date = {\r\n\t\t\t\"M+\": this.getMonth() + 1,\r\n\t\t\t\"d+\": this.getDate(),\r\n\t\t\t\"h+\": this.getHours(),\r\n\t\t\t\"m+\": this.getMinutes(),\r\n\t\t\t\"s+\": this.getSeconds(),\r\n\t\t\t\"q+\": Math.floor((this.getMonth() + 3) / 3),\r\n\t\t\t\"S+\": this.getMilliseconds()\r\n\t\t};\r\n\t\tif (/(y+)/i.test(format)) {\r\n\t\t\tformat = format.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));\r\n\t\t}\r\n\t\tfor (var k in date) {\r\n\t\t\tif (new RegExp(\"(\" + k + \")\").test(format)) {\r\n\t\t\t\tformat = format.replace(RegExp.$1, RegExp.$1.length == 1 ?\r\n\t\t\t\t\tdate[k] : (\"00\" + date[k]).substr((\"\" + date[k]).length));\r\n\t\t\t}\r\n\t\t}\r\n\t\treturn format;\r\n\t}\r\n\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\taliveRooms: undefined\r\n\t\t\t};\r\n\t\t},\r\n\t\tonReady() {},\r\n\t\tonLoad(e) {\r\n\t\t\tthis._wxaMpLiveRooms()\r\n\t\t},\r\n\t\tmounted() {},\r\n\t\tmethods: {\r\n\t\t\tasync _wxaMpLiveRooms() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: ''\r\n\t\t\t\t})\r\n\t\t\t\tconst res = await this.$wxapi.wxaMpLiveRooms()\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tres.data.forEach(ele => {\r\n\t\t\t\t\t\tif (ele.start_time) {\r\n\t\t\t\t\t\t\tele.start_time_str = new Date(ele.start_time * 1000).format('yyyy-MM-dd h:m:s')\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis.aliveRooms = res.data\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoLiveRoom(item) {\r\n\t\t\t\tconsole.log(item);\r\n\t\t\t\tif (item.live_status == 107 || item.live_status == 106 || item.live_status == 104) {\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// #ifdef MP-WEIXIN\r\n\t\t\t\twx.navigateTo({\r\n\t\t\t\t\turl: `plugin-private://wx2b03c6e691cd7370/pages/live-player-plugin?room_id=${item.roomId}`\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '暂时无法跳转直播间'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.shops-container {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin: 16rpx;\r\n\t}\r\n\r\n\t.shops-container .l {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.shops-container .l text {\r\n\t\tcolor: #666;\r\n\t\tmargin-left: 16rpx;\r\n\t}\r\n\r\n\t.shops-container .l image {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\r\n\t.shops-container .r {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.shops-container .r text {\r\n\t\tcolor: #666;\r\n\t}\r\n\r\n\t.shops-container .r image {\r\n\t\twidth: 40rpx;\r\n\t\theight: 40rpx;\r\n\t}\r\n\r\n\t.search {\r\n\t\tposition: absolute;\r\n\t\ttop: 32rpx;\r\n\t\tleft: 25rpx;\r\n\t\twidth: 700rpx;\r\n\t\theight: 66rpx;\r\n\t\tdisplay: block;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\r\n\t.search input {\r\n\t\tdisplay: block;\r\n\t\tbox-sizing: border-box;\r\n\t\tbackground: rgba(255, 255, 255, 0.8);\r\n\t\tborder: 1rpx solid #e3e3e3;\r\n\t\twidth: 700rpx;\r\n\t\theight: 66rpx;\r\n\t\tborder-radius: 30rpx;\r\n\t\tpadding-left: 32rpx;\r\n\t}\r\n\r\n\t.search image {\r\n\t\twidth: 35rpx;\r\n\t\theight: 35rpx;\r\n\t\tposition: absolute;\r\n\t\ttop: 16rpx;\r\n\t\tright: 32rpx;\r\n\t\tz-index: 99;\r\n\t}\r\n\r\n\t.goodsDynamic {\r\n\t\tposition: absolute;\r\n\t\ttop: 110rpx;\r\n\t\tleft: 25rpx;\r\n\t\twidth: 650rpx;\r\n\t\theight: 66rpx;\r\n\t\tline-height: 66rpx;\r\n\t\tbackground: rgba(0, 0, 0, 0.6);\r\n\t\tcolor: #fff;\r\n\t\tpadding: 0 16rpx;\r\n\t\tborder-radius: 32rpx;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.goodsDynamic .swiper2 {\r\n\t\twidth: 100%;\r\n\t\theight: 100%;\r\n\t}\r\n\r\n\t.goodsDynamic-item {\r\n\t\twidth: 100%;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t}\r\n\r\n\t.goodsDynamic-item image {\r\n\t\twidth: 32rpx;\r\n\t\theight: 32rpx;\r\n\t\tborder-radius: 50%;\r\n\t\tflex-shrink: 0;\r\n\t}\r\n\r\n\t.goodsDynamic-item text {\r\n\t\tmargin-left: 8rpx;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.notice-box {\r\n\t\tdisplay: flex;\r\n\t\tbox-sizing: border-box;\r\n\t\twidth: 100vw;\r\n\t\theight: 88rpx;\r\n\t\tline-height: 88rpx;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tpadding-right: 32rpx;\r\n\t\tborder-bottom: 1rpx solid #efeff4;\r\n\t}\r\n\r\n\t.notice {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\twidth: 600rpx;\r\n\t}\r\n\r\n\t.notice_icon {\r\n\t\tmargin-left: 20rpx;\r\n\t\twidth: 30rpx;\r\n\t\theight: 30rpx;\r\n\t}\r\n\r\n\t.notice_swiper {\r\n\t\theight: 88rpx;\r\n\t\twidth: 600rpx;\r\n\t}\r\n\r\n\t.notice_itemr {\r\n\t\tpadding-left: 16rpx;\r\n\t\tfont-size: 24rpx;\r\n\t\toverflow: hidden;\r\n\t\tcolor: #e64340;\r\n\t}\r\n\r\n\t.notice-box .more {\r\n\t\tcolor: #666;\r\n\t\tfont-size: 24rpx;\r\n\t}\r\n\r\n\t.container {\r\n\t\tbackground-color: #fff;\r\n\t\tmin-height: 100%;\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.category-box {\r\n\t\tbackground-color: #fff;\r\n\t\tdisplay: flex;\r\n\t\tflex-wrap: wrap;\r\n\t}\r\n\r\n\t.category-list {\r\n\t\tflex: 1;\r\n\t\t/* width:150rpx; */\r\n\t\tmargin-top: 20rpx;\r\n\t\ttext-align: center;\r\n\t\tdisplay: inline-block;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.category-column {\r\n\t\twidth: 100%;\r\n\t\tmargin-top: 20rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.category-imgbox {\r\n\t\twidth: 90rpx;\r\n\t\theight: 90rpx;\r\n\t}\r\n\r\n\t.category-title {\r\n\t\tfont-size: 24rpx;\r\n\t\ttext-align: center;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\r\n\t.swiper-container {\r\n\t\twidth: 750rpx;\r\n\t\theight: 375rpx;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.swiper-container .swiper1 {\r\n\t\twidth: 750rpx;\r\n\t\theight: 375rpx;\r\n\t}\r\n\r\n\t.swiper-container .swiper1 image {\r\n\t\twidth: 750rpx;\r\n\t\theight: 375rpx;\r\n\t}\r\n\r\n\t::-webkit-scrollbar {\r\n\t\twidth: 0;\r\n\t\theight: 0;\r\n\t\tcolor: transparent;\r\n\t}\r\n\r\n\t.type-item-on {\r\n\t\tcolor: #e64340;\r\n\t\tborder-bottom: 1rpx solid #e64340;\r\n\t}\r\n\r\n\t.goods-container {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tflex-wrap: wrap;\r\n\t\tbox-sizing: content-box;\r\n\t\tpadding: 24rpx;\r\n\t\tbackground-color: #F2f2f2;\r\n\t}\r\n\r\n\t.goods-box {\r\n\t\twidth: 339rpx;\r\n\t\theight: 472rpx;\r\n\t\tbackground-color: #fff;\r\n\t\toverflow: hidden;\r\n\t\tmargin-bottom: 24rpx;\r\n\t\tborder-radius: 5px;\r\n\t}\r\n\r\n\t.goods-box .img-box {\r\n\t\twidth: 339rpx;\r\n\t\theight: 339rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.goods-box .img-box image {\r\n\t\twidth: 339rpx;\r\n\t\theight: 339rpx;\r\n\t}\r\n\r\n\t.goods-box .goods-title {\r\n\t\twidth: 280rpx;\r\n\t\ttext-overflow: ellipsis;\r\n\t\twhite-space: nowrap;\r\n\t\toverflow: hidden;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding: 24rpx 0 0rpx 0;\r\n\t\tcolor: #000;\r\n\t\tmargin-left: 24rpx;\r\n\t}\r\n\r\n\t.goods-box .goods-price {\r\n\t\twidth: 280rpx;\r\n\t\toverflow: hidden;\r\n\t\tfont-size: 24rpx;\r\n\t\tpadding: 24rpx 0;\r\n\t\tcolor: #e64340;\r\n\t\tmargin-left: 24rpx;\r\n\t}\r\n\r\n\t.pos-fiexd {\r\n\t\tposition: fixed;\r\n\t\ttop: 0;\r\n\t\tleft: 0;\r\n\t}\r\n\r\n\t.coupons {\r\n\t\tmargin-top: 10rpx;\r\n\t\twidth: 100%;\r\n\t\theight: 180rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.coupons-scroll {\r\n\t\twhite-space: nowrap;\r\n\t\theight: 180rpx;\r\n\t\twidth: 100%;\r\n\t\tbackground-color: #fff;\r\n\t}\r\n\r\n\t.coupons-item {\r\n\t\twidth: 300rpx;\r\n\t\theight: 180rpx;\r\n\t\tmargin: 10rpx;\r\n\t\tpadding-top: 20rpx;\r\n\t\tpadding-left: 15rpx;\r\n\t\tbackground-color: #f1a83b;\r\n\t\tbox-sizing: content-box;\r\n\t\tfont-size: 20rpx;\r\n\t\tline-height: 35rpx;\r\n\t\toverflow: hidden;\r\n\t\tcolor: #fff;\r\n\t\tdisplay: inline-block;\r\n\t}\r\n\r\n\t.coupons-float {\r\n\t\tposition: fixed;\r\n\t\tright: 15rpx;\r\n\t\tbottom: 80rpx;\r\n\t\t/* width:80rpx;\r\n  height:80rpx; */\r\n\t\t/* background-color: #fff; */\r\n\t\ttext-align: center;\r\n\t\t/* border-radius:50%;\r\n  border: 1rpx solid #ddd; */\r\n\t}\r\n\r\n\t.coupons-float image {\r\n\t\twidth: 110rpx;\r\n\t\theight: 110rpx;\r\n\t\tmargin-top: 10rpx;\r\n\t}\r\n\r\n\t.tuan {\r\n\t\twidth: 750rpx;\r\n\t\tbackground-color: #F2f2f2;\r\n\t\tpadding-top: 10rpx;\r\n\t}\r\n\r\n\t.tuan-item {\r\n\t\twidth: 720rpx;\r\n\t\tmargin: auto;\r\n\t\tmargin-top: 20rpx;\r\n\t\tbackground-color: #FFF;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.tuan-goods-pic {\r\n\t\twidth: 720rpx;\r\n\t\theight: 250rpx;\r\n\t\toverflow: hidden;\r\n\t}\r\n\r\n\t.tuan-goods-pic image {\r\n\t\twidth: 720rpx;\r\n\t}\r\n\r\n\t.tuan-title {\r\n\t\tmargin-top: 20rpx;\r\n\t\tmargin-left: 20rpx;\r\n\t\tfont-size: 30rpx;\r\n\t\tfont-weight: 400;\r\n\t}\r\n\r\n\t.tuan-profile {\r\n\t\tmargin-left: 20rpx;\r\n\t\tfont-size: 28rpx;\r\n\t\tcolor: #999;\r\n\t}\r\n\r\n\t.tuan-price {\r\n\t\tmargin-left: 20rpx;\r\n\t}\r\n\r\n\t.tuan-price .now {\r\n\t\tfont-size: 36rpx;\r\n\t\tcolor: #e64340;\r\n\t}\r\n\r\n\t.tuan-price .original {\r\n\t\tmargin-left: 20rpx;\r\n\t\tfont-size: 26rpx;\r\n\t\tcolor: #999;\r\n\t\ttext-decoration: line-through;\r\n\t}\r\n\r\n\t.tuan-btn {\r\n\t\tposition: absolute;\r\n\t\tright: 30rpx;\r\n\t\tbottom: 10rpx;\r\n\t}\r\n\r\n\t.category-goods-title {\r\n\t\twidth: 100vw;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\tmargin-bottom: 8rpx;\r\n\t\tborder-bottom: 1rpx solid #efeff4;\r\n\t}\r\n\r\n\t.category-goods-title .more {\r\n\t\tcolor: #666;\r\n\t\tfont-size: 24rpx;\r\n\t\tmargin-right: 32rpx;\r\n\t}\r\n\r\n\t.progress {\r\n\t\twidth: 686rpx;\r\n\t\tpadding: 0 32rpx;\r\n\t\tmargin-bottom: 16rpx;\r\n\t}\r\n\r\n\t/*秒杀*/\r\n\t.miaosha-container {\r\n\t\tpadding: 0;\r\n\t}\r\n\r\n\t.miaosha-goods-list {\r\n\t\tmargin: 20rpx;\r\n\t\tbackground: #f6f6f6;\r\n\t\tborder-radius: 16rpx;\r\n\t\tdisplay: flex;\r\n\t\tpadding: 20rpx;\r\n\t}\r\n\r\n\t.miaosha-goods-list .image {\r\n\t\twidth: 260rpx;\r\n\t\theight: 260rpx;\r\n\t\tflex-shrink: 0;\r\n\t\tborder-radius: 16rpx;\r\n\t}\r\n\r\n\t.miaosha-goods-list .r {\r\n\t\tmargin-left: 32rpx;\r\n\t}\r\n\r\n\t.miaosha-goods-list .r .goods-title {\r\n\t\tcolor: #333;\r\n\t\tfont-size: 28rpx;\r\n\t}\r\n\r\n\t.miaosha-goods-list .r .label {\r\n\t\tcolor: #e64340;\r\n\t\tfont-size: 24rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: flex-start;\r\n\t\tmargin-top: 8rpx;\r\n\t}\r\n\r\n\t.miaosha-goods-list .r .label text {\r\n\t\tmargin-left: 8rpx;\r\n\t}\r\n\r\n\t.miaosha-goods-list .count-down {\r\n\t\tbackground: #e64340;\r\n\t\tpadding: 8rpx 16rpx;\r\n\t\tborder-radius: 16rpx;\r\n\t\tmargin-top: 12rpx;\r\n\t}\r\n\r\n\t.van-count-down {\r\n\t\tcolor: #fff !important;\r\n\t\tfont-size: 26rpx !important;\r\n\t\ttext-align: center;\r\n\t}\r\n\r\n\t.miaosha-price-btn {\r\n\t\tpadding-top: 32rpx;\r\n\t}\r\n\r\n\t.miaosha-price-btn .price {\r\n\t\tcolor: #e64340;\r\n\t\tfont-size: 40rpx;\r\n\t\tmargin-top: 12rpx;\r\n\t}\r\n\r\n\t.miaosha-price-btn .price text {\r\n\t\tcolor: #666666;\r\n\t\tfont-size: 26rpx;\r\n\t\ttext-decoration: line-through;\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=05877b98&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=05877b98&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692284634\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}