<template>
  <view class="goods-box-wrapper">
    <view class="goods-box">
      <view @click="goDetail(item)">
        <view class="goods-title-box">
          <view class="goods-title">{{ item.name }}</view>
        </view>
        <view class="goods-dsc-box">
          <view class="goods-dsc">{{ item.characteristic }}</view>
        </view>
        <view class="goods-tags-box">
          <view class="goods-tags">
            <span v-for="(tag, index) in filteredTags" :key="index" :style="{ color: shouldHighlight(tag) ? '#e64340' : '', border: shouldHighlight(tag) ? '1px solid #e64340' : '', display: 'inline-block', 'margin-right': '5px' }">
              {{ tag }}
            </span>
          </view>
        </view>
        <view class="goods-quota">
          <progress :percent="100" :show-info="false" stroke-width="3" font-size="12" border-radius="6" :active="true" />
          <span class="num">剩余{{ item.stores }}</span>
        </view>
      </view>
      <view class="buy-wrapper" style="display: flex;">
        <view class="price-score">
          <view v-if="item.minPrice" class="item">
            <div class="original-price">
              <span>市场价：</span><br /><span class="price">¥{{item.originalPrice}}</span>
            </div><text>¥</text>{{item.minPrice}}
          </view>
          <view v-if="item.minScore" class="item"><text>
              <image class="score-icon" src="/static/images/score.png"></image>
            </text>{{item.minScore}}</view>
        </view>
        <view class="buy" @click="goDetail(item)">
          <u-icon name="shopping-cart" color="#FFFFFF" size="28"></u-icon>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      item: {
        type: Object,
        default: {},
      },
    },
    onReady() {
      console.log('item is', this.item)
    },
    data() {
      return {
        timeData: {}
      }
    },
    computed: {
      filteredTags() {
        if (this.item.tags) {
          const tags = this.item.tags.split(/[, ]+/);
          return tags.filter(tag => tag.trim() !== '');
        }
        return [];
      }
    },
    methods: {
      onChangeTimeData(e) {
        this.timeData = e
      },
      goDetail(item) {
        uni.navigateTo({
          url: '/pages/goods/detail?id=' + item.id
        })
      },
      shouldHighlight(tag) {
        return /[A-Za-z]\d+/.test(tag);
      }
    },
  }
</script>
<style>
  .goods-box-wrapper {
    margin-top: 24rpx;
    border-radius: 5px;
    padding-bottom: 10rpx;
    text-align: left;
    overflow: hidden;
  }

  .goods-box {
    padding: 0 10rpx;
  }

  .goods-box .goods-title-box {
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }

  .goods-box .goods-title {
    color: #2b2b2b;
    font-size: 30rpx;
  }

  .goods-box .goods-dsc-box {
    margin-top: 4rpx;
  }

  .goods-box .goods-dsc {
    color: #858996;
    font-size: 24rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .goods-box .goods-tags span {
    font-size: 22rpx;
    font-weight: normal;
    color: #858996;
    border: 1px #D9DBDF solid;
    margin-right: 10rpx;
    border-radius: 2px;
    padding: 0 4rpx;
    line-height: 32rpx;
  }

  .goods-box .goods-price-container {
    display: flex;
    align-items: baseline;
  }

  .goods-box .goods-price {
    overflow: hidden;
    font-size: 34rpx;
    color: #F20C32;
    margin-left: 24rpx;
  }

  .goods-box .goods-price2 {
    overflow: hidden;
    font-size: 26rpx;
    color: #aaa;
    text-decoration: line-through;
    margin-left: 20rpx;
  }

  .goods-box .buy-wrapper {
    margin-top: 10rpx;
    display: flex;
    justify-content: space-between;
  }

  .goods-box .buy {
    width: 52rpx;
    height: 52rpx;
    background-color: #34B764;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4rpx;
  }

  .goods-box .price-score text {
    margin-top: 6rpx;
  }

  .goods-box .original-price {
    color: #858996;
    font-size: 20rpx;
    font-weight: 100;
    line-height: 20rpx;
  }

  .goods-box .original-price .price {
    text-decoration: line-through;
  }

  .goods-quota {
    margin-top: 20rpx;
    width: 300rpx;
    position: relative;
    width: 200rpx;
  }

  .goods-quota .num {
    position: absolute;
    right: -80rpx;
    top: -12rpx;
    font-size: 22rpx;
  }
</style>
<style lang="scss">
  .time {
    &__custom {
      background-color: #e64340;
    }

    &__doc {
      color: #324A43;
      padding: 0px 2px;
      margin-top: 5px;
    }

    &__item {
      color: #606266;
      font-size: 10px;
      margin-right: 2px;
    }
  }
</style>