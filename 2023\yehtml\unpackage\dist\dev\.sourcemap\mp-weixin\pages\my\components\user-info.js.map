{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/components/user-info.vue?cbd4", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/components/user-info.vue?286a", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/components/user-info.vue?35ca", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/components/user-info.vue?552c", "uni-app:///pages/my/components/user-info.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/components/user-info.vue?fce5", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/components/user-info.vue?40df"], "names": ["name", "props", "data", "type", "default", "needLogin", "methods", "onChooseAvatar", "that", "avatarUrl", "res", "d", "token", "_res", "uni", "url", "goLogin", "USER", "editNick"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AAC6D;AACL;AACsC;;;AAG9F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,+EAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAoqB,CAAgB,orBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsBxrB;AAAA,gBAEA;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;MACA;IACA;IACAC;MACAF;MACAC;IACA;EACA;EACAF;IACA;EACA;EACAI;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAC;gBAAA,KAEAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAH;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAI;gBACAC;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAC;MAEAC;IAiBA;IACAC;MACAJ;QACAC;MACA;IACA;EACA;AACA;AAAA,4B;;;;;;;;;;;;;ACvFA;AAAA;AAAA;AAAA;AAA2xC,CAAgB,+uCAAG,EAAC,C;;;;;;;;;;;ACA/yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/components/user-info.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./user-info.vue?vue&type=template&id=0764bad1&scoped=true&\"\nvar renderjs\nimport script from \"./user-info.vue?vue&type=script&lang=js&\"\nexport * from \"./user-info.vue?vue&type=script&lang=js&\"\nimport style0 from \"./user-info.vue?vue&type=style&index=0&id=0764bad1&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0764bad1\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/components/user-info.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-info.vue?vue&type=template&id=0764bad1&scoped=true&\"", "var components\ntry {\n  components = {\n    uAvatar: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-avatar/u-avatar\" */ \"@/uni_modules/uview-ui/components/u-avatar/u-avatar.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-info.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-info.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"slot\">\r\n    <view class=\"left\" v-if=\"data\">\r\n      <button class=\"avatar-wrapper\" open-type=\"chooseAvatar\" @chooseavatar=\"onChooseAvatar\" plain=\"true\">\r\n        <u-avatar size=\"60\" :src=\"data.base.avatarUrl\"></u-avatar>\r\n      </button>\r\n      <view class=\"info\" @click=\"editNick\">\r\n        <view class=\"username\">{{ data.base.nick || '请设置昵称' }}</view>\r\n        <view class=\"desc\">{{ data.base.id }}</view>\r\n      </view>\r\n    </view>\r\n    <view class=\"left\" v-if=\"needLogin\">\r\n      <u-avatar size=\"60\"></u-avatar>\r\n      <view class=\"info\" @click=\"goLogin\">\r\n        <view class=\"username\">登录</view>\r\n        <view class=\"desc\">点击登录跳转至登录页面</view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  const USER = require('@/common/user.js')\r\n\r\n  export default {\r\n    name: 'user-info',\r\n    props: {\r\n      data: {\r\n        type: Object,\r\n        default: () => {\r\n          return {};\r\n        }\r\n      },\r\n      needLogin: {\r\n        type: Boolean,\r\n        default: false\r\n      }\r\n    },\r\n    data() {\r\n      return {}\r\n    },\r\n    methods: {\r\n      async onChooseAvatar(e) {\r\n        let that = this\r\n        const avatarUrl = e.detail.avatarUrl\r\n\r\n        if (avatarUrl) {\r\n          const res = await this.$wxapi.uploadFile(this.token, avatarUrl)\r\n\r\n          if (res.code == 0) {\r\n            const d = {\r\n              token: this.token,\r\n              avatarUrl: res.data.url\r\n            }\r\n            const _res = await this.$wxapi.modifyUserInfo(d)\r\n            uni.navigateTo({\r\n              url: '/pages/my/info'\r\n            })\r\n          }\r\n        }\r\n      },\r\n      goLogin() {\r\n        // #ifdef MP\r\n        USER.autoLogin()\r\n        // #endif\r\n        // #ifdef H5\r\n        const ua = window.navigator.userAgent.toLowerCase();\r\n        if (ua.match(/MicroMessenger/i) == 'micromessenger') {\r\n          USER.autoLogin()\r\n        } else {\r\n          uni.navigateTo({\r\n            url: '/pages/login/login'\r\n          })\r\n        }\r\n        // #endif\r\n        // #ifndef MP || H5\r\n        uni.navigateTo({\r\n          url: '/pages/login/login'\r\n        })\r\n        // #endif\r\n      },\r\n      editNick() {\r\n        uni.navigateTo({\r\n          url: '/pages/my/info'\r\n        })\r\n      }\r\n    }\r\n  };\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .slot {\r\n    color: #FFFFFF;\r\n    display: flex;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    padding: 48rpx;\r\n    position: relative;\r\n    z-index: 0;\r\n\r\n    .left {\r\n      display: flex;\r\n      align-items: center;\r\n\r\n      .avatar-wrapper {\r\n        border: none;\r\n        background: none;\r\n        outline: none;\r\n        padding: 0;\r\n      }\r\n\r\n      .info {\r\n        margin-left: 24rpx;\r\n        display: flex;\r\n        align-content: space-between;\r\n        flex-wrap: wrap;\r\n\r\n        .username {\r\n          font-size: 32rpx;\r\n          width: 100%;\r\n          color: #FFF;\r\n          margin-bottom: 16rpx;\r\n        }\r\n\r\n        .desc {\r\n          font-size: 24rpx;\r\n          width: 100%;\r\n          color: #FFF;\r\n          display: -webkit-box;\r\n          overflow: hidden;\r\n          -webkit-line-clamp: 1;\r\n          -webkit-box-orient: vertical;\r\n        }\r\n      }\r\n    }\r\n\r\n    .right {\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-info.vue?vue&type=style&index=0&id=0764bad1&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./user-info.vue?vue&type=style&index=0&id=0764bad1&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688737790\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}