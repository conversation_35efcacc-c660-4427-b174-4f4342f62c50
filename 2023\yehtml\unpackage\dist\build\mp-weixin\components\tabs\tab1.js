(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/tabs/tab1"],{2940:function(n,t,e){"use strict";e.r(t);var u=e("41a5"),o=e("6bbb");for(var a in o)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(a);var r=e("828b"),i=Object(r["a"])(o["default"],u["b"],u["c"],!1,null,null,null,!1,u["a"],void 0);t["default"]=i.exports},4144:function(n,t,e){"use strict";var u=e("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;u(e("bc37"));var o={components:{},props:{type:{type:String,default:""}},onReady:function(){},data:function(){return{activeTab:"tab1"}},watch:{},methods:{changeTab:function(n){this.activeTab=n}}};t.default=o},"41a5":function(n,t,e){"use strict";e.d(t,"b",(function(){return o})),e.d(t,"c",(function(){return a})),e.d(t,"a",(function(){return u}));var u={uRow:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-row/u-row")]).then(e.bind(null,"f632"))},uCol:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-col/u-col")]).then(e.bind(null,"44b6"))}},o=function(){var n=this.$createElement;this._self._c},a=[]},"6bbb":function(n,t,e){"use strict";e.r(t);var u=e("4144"),o=e.n(u);for(var a in u)["default"].indexOf(a)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(a);t["default"]=o.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/tabs/tab1-create-component',
    {
        'components/tabs/tab1-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("2940"))
        })
    },
    [['components/tabs/tab1-create-component']]
]);
