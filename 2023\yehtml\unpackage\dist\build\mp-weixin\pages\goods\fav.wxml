<view class="data-v-6081778a"><block wx:if="{{$root.g0}}"><page-box-empty vue-id="dc4eff26-1" title="{{title}}" sub-title="可以去看看有那些想买的～" show-btn="{{true}}" class="data-v-6081778a" bind:__l="__l"></page-box-empty></block><view class="goods-container data-v-6081778a"><block wx:for="{{goods}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="goods-box data-v-6081778a"><view class="img-box data-v-6081778a"><image class="image data-v-6081778a" src="{{item.pic}}" mode="aspectFill" lazy-load="true" data-event-opts="{{[['tap',[['goDetail',['$0'],[[['goods','',index]]]]]]]}}" bindtap="__e"></image></view><view class="name data-v-6081778a"><u-text class="goods-title data-v-6081778a" vue-id="{{'dc4eff26-2-'+index}}" text="{{item.goodsName}}" lines="{{3}}" size="28rpx" color="#333" data-event-opts="{{[['^click',[['goDetail',['$0'],[[['goods','',index]]]]]]]}}" bind:click="__e" bind:__l="__l"></u-text></view><view class="delete data-v-6081778a"><u-icon vue-id="{{'dc4eff26-3-'+index}}" name="trash" color="#F20C32" size="48rpx" data-event-opts="{{[['^click',[['deleteFav',[index,'$0'],[[['goods','',index]]]]]]]}}" bind:click="__e" class="data-v-6081778a" bind:__l="__l"></u-icon></view></view></block></view></view>