<view class="data-v-ed6681a0"><u-empty vue-id="35a7246c-1" mode="permission" text="请先登陆" marginTop="88rpx" class="data-v-ed6681a0" bind:__l="__l"></u-empty><view class="form-box data-v-ed6681a0"><u-form vue-id="35a7246c-2" label-width="130rpx" model="{{form}}" data-ref="uForm" class="data-v-ed6681a0 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('35a7246c-3')+','+('35a7246c-2')}}" label="手机号码" prop="mobile" required="{{true}}" class="data-v-ed6681a0" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('35a7246c-4')+','+('35a7246c-3')}}" type="number" clearable="{{true}}" maxlength="11" focus="{{true}}" placeholder="请输入手机号码" value="{{form.mobile}}" data-event-opts="{{[['^input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" class="data-v-ed6681a0" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('35a7246c-5')+','+('35a7246c-2')}}" label="密码" prop="pwd" required="{{true}}" class="data-v-ed6681a0" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('35a7246c-6')+','+('35a7246c-5')}}" type="password" clearable="{{true}}" placeholder="请输入密码" value="{{form.pwd}}" data-event-opts="{{[['^input',[['__set_model',['$0','pwd','$event',[]],['form']]]]]}}" class="data-v-ed6681a0" bind:__l="__l"></u-input></u-form-item></u-form></view><view class="submit data-v-ed6681a0"><u-button vue-id="35a7246c-7" type="success" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" class="data-v-ed6681a0" bind:__l="__l" vue-slots="{{['default']}}">登陆</u-button><view class="text-btns data-v-ed6681a0"><view data-event-opts="{{[['tap',[['goReg',['$event']]]]]}}" bindtap="__e" class="data-v-ed6681a0">注册新账户</view><view data-event-opts="{{[['tap',[['goResetpwd',['$event']]]]]}}" bindtap="__e" class="data-v-ed6681a0">忘记登陆密码？</view></view></view></view>