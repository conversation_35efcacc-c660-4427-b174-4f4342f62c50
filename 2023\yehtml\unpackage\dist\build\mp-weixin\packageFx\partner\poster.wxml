<view class="page data-v-3f8406d9"><block wx:if="{{!posterShow}}"><view class="poster data-v-3f8406d9"><l-painter vue-id="601e8c6d-1" board="{{posterObj}}" data-ref="painter" class="data-v-3f8406d9 vue-ref" bind:__l="__l"></l-painter><view class="footer-btn data-v-3f8406d9"><view class="data-v-3f8406d9"><u-button vue-id="601e8c6d-2" plain="{{true}}" type="info" text="返回" data-event-opts="{{[['^click',[['goBack']]]]}}" bind:click="__e" class="data-v-3f8406d9" bind:__l="__l"></u-button></view><view class="data-v-3f8406d9"><u-button vue-id="601e8c6d-3" type="warning" text="保存" color="#2F4C42" data-event-opts="{{[['^click',[['toSave']]]]}}" bind:click="__e" class="data-v-3f8406d9" bind:__l="__l"></u-button></view></view></view></block><uni-popup vue-id="601e8c6d-4" type="center" maskClick="{{false}}" data-ref="posterImg" class="data-v-3f8406d9 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><view class="poster-img data-v-3f8406d9"><text data-event-opts="{{[['tap',[['closePopup',['$event']]]]]}}" bindtap="__e" class="data-v-3f8406d9"></text><image src="{{path}}" mode="heightFix" data-event-opts="{{[['tap',[['previewImg',['$event']]]]]}}" bindtap="__e" class="data-v-3f8406d9"></image><view style="text-align:left;margin-left:1em;" class="data-v-3f8406d9">点击长按图片保存到手机</view></view></uni-popup></view>