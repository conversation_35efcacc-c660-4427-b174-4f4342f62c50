@charset "UTF-8";
/**
 * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量
 * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可
 * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 
 */
/* 变量 */
view.data-v-0d9729bf, scroll-view.data-v-0d9729bf, swiper-item.data-v-0d9729bf {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-tabbar-item.data-v-0d9729bf {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
}
.u-tabbar-item__icon.data-v-0d9729bf {
  display: flex;
  flex-direction: row;
  position: relative;
  width: 150rpx;
  justify-content: center;
}
.u-tabbar-item__text.data-v-0d9729bf {
  margin-top: 2px;
  font-size: 12px;
  color: #606266;
}
.data-v-0d9729bf:host {
  flex: 1;
}

