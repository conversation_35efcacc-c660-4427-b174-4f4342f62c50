{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-swipe-action/u-swipe-action.vue?6947", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-swipe-action/u-swipe-action.vue?36e0", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-swipe-action/u-swipe-action.vue?1d1e", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-swipe-action/u-swipe-action.vue?d64f", "uni-app:///uni_modules/uview-ui/components/u-swipe-action/u-swipe-action.vue"], "names": ["name", "mixins", "data", "provide", "swipeAction", "computed", "parentData", "watch", "created", "methods", "closeOther", "item"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;;;AAG7D;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACtBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyqB,CAAgB,yrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACO7rB;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,eAQA;EACAA;EACAC;EACAC;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAD;MACA;QACA;UACA;UACA;QACA;MACA;IACA;EACA;EACAE;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;UACA;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B", "file": "uni_modules/uview-ui/components/u-swipe-action/u-swipe-action.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-swipe-action.vue?vue&type=template&id=9005746e&scoped=true&\"\nvar renderjs\nimport script from \"./u-swipe-action.vue?vue&type=script&lang=js&\"\nexport * from \"./u-swipe-action.vue?vue&type=script&lang=js&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9005746e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-swipe-action/u-swipe-action.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-swipe-action.vue?vue&type=template&id=9005746e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-swipe-action.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-swipe-action.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-swipe-action\">\r\n\t\t<slot></slot>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t/**\r\n\t * SwipeAction 滑动单元格 \r\n\t * @description 该组件一般用于左滑唤出操作菜单的场景，用的最多的是左滑删除操作\r\n\t * @tutorial https://www.uviewui.com/components/swipeAction.html\r\n\t * @property {Boolean}\tautoClose\t是否自动关闭其他swipe按钮组\r\n\t * @event {Function(index)}\tclick\t点击组件时触发\r\n\t * @example\t<u-swipe-action><u-swipe-action-item :rightOptions=\"options1\" ></u-swipe-action-item></u-swipe-action>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-swipe-action',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {}\r\n\t\t},\r\n\t\tprovide() {\r\n\t\t\treturn {\r\n\t\t\t\tswipeAction: this\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 这里computed的变量，都是子组件u-swipe-action-item需要用到的，由于头条小程序的兼容性差异，子组件无法实时监听父组件参数的变化\r\n\t\t\t// 所以需要手动通知子组件，这里返回一个parentData变量，供watch监听，在其中去通知每一个子组件重新从父组件(u-swipe-action-item)\r\n\t\t\t// 拉取父组件新的变化后的参数\r\n\t\t\tparentData() {\r\n\t\t\t\treturn [this.autoClose]\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 当父组件需要子组件需要共享的参数发生了变化，手动通知子组件\r\n\t\t\tparentData() {\r\n\t\t\t\tif (this.children.length) {\r\n\t\t\t\t\tthis.children.map(child => {\r\n\t\t\t\t\t\t// 判断子组件(u-swipe-action-item)如果有updateParentData方法的话，就就执行(执行的结果是子组件重新从父组件拉取了最新的值)\r\n\t\t\t\t\t\ttypeof(child.updateParentData) === 'function' && child.updateParentData()\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\tthis.children = []\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tcloseOther(child) {\r\n\t\t\t\tif (this.autoClose) {\r\n\t\t\t\t\t// 历遍所有的单元格，找出非当前操作中的单元格，进行关闭\r\n\t\t\t\t\tthis.children.map((item, index) => {\r\n\t\t\t\t\t\tif (child !== item) {\r\n\t\t\t\t\t\t\titem.closeHandler()\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\r\n</style>\r\n"], "sourceRoot": ""}