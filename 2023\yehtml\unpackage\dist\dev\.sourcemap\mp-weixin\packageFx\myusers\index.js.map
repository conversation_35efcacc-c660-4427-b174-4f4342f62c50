{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/myusers/index.vue?c60e", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/myusers/index.vue?bfe1", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/myusers/index.vue?5815", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/myusers/index.vue?6728", "uni-app:///packageFx/myusers/index.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/myusers/index.vue?b41b", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/myusers/index.vue?869c"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "members", "list", "name", "number1", "number2", "activeIndex", "page", "methods", "onLoad", "onShow", "fxMembersStatistics", "res", "fxMembers", "token", "level", "uni", "title", "icon", "statisticsCommisionMap", "userCashMap", "ele", "tabChange", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8SAEN;AACP,KAAK;AACL;AACA,aAAa,sUAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACzDA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,grBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkBprB;EACAC;IACA;MACAC;MACAC,OACA;QACAC;MACA,GACA;QACAA;MACA,EACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;MACA;IACA;IACAC,2BAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;kBACAP;kBACAQ;gBACA;cAAA;gBAJAH;gBAKA;kBACA;oBACA;kBACA;oBACAI;sBACAC;sBACAC;oBACA;kBACA;gBACA;gBACA;kBACAC;kBACAC;kBACAR;oBACA;sBACAS;oBACA;oBACA;sBACAA;oBACA;oBACA;oBACA;sBACAA;sBACAA;oBACA;oBACA;sBACA;sBACA;wBACAA;wBACAA;wBACAA;sBACA;oBACA;kBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACAR;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClHA;AAAA;AAAA;AAAA;AAA48B,CAAgB,+7BAAG,EAAC,C;;;;;;;;;;;ACAh+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packageFx/myusers/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packageFx/myusers/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=2d1438ac&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packageFx/myusers/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=2d1438ac&\"", "var components\ntry {\n  components = {\n    uSticky: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-sticky/u-sticky\" */ \"@/uni_modules/uview-ui/components/u-sticky/u-sticky.vue\"\n      )\n    },\n    uSubsection: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-subsection/u-subsection\" */ \"@/uni_modules/uview-ui/components/u-subsection/u-subsection.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-empty/u-empty\" */ \"@/uni_modules/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.members || _vm.members.length == 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<u-sticky>\r\n\t\t\t<u-subsection :list=\"list\" :current=\"activeIndex\" @change=\"tabChange\"></u-subsection>\r\n\t\t</u-sticky>\r\n\t\t<u-empty v-if=\"!members || members.length == 0\" text=\"暂无团队\" mode=\"car\" icon=\"http://cdn.uviewui.com/uview/empty/car.png\"></u-empty>\r\n\t\t<view v-for=\"(item,index) in members\" class=\"list\">\r\n\t\t\t<image :src=\"item.avatarUrls\" mode=\"aspectFill\" class=\"l\"></image>\r\n\t\t\t<view class=\"r\">\r\n\t\t\t\t<u-cell :title=\"item.nicks\" :label=\"item.mobileMasks\" isLink :url=\"'myusers-detail?id=' + item.uids\"></u-cell>\r\n\t\t\t\t<u-cell title=\"成交额\" :value=\"'¥' + item.totalPayAmount ? item.totalPayAmount : 0\" titleStyle=\"flex:2\"></u-cell>\r\n\t\t\t\t<u-cell title=\"订单数\" :value=\"item.totalPayNumber ? item.totalPayNumber : 0 + '笔'\" :label=\"'最近下单:' + item.lastOrderDate ? item.lastOrderDate : '-'\" titleStyle=\"flex:2\"></u-cell>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tmembers: [],\r\n\t\t\t\tlist: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '直推'\r\n\t\t\t\t\t}, \r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '间推'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tnumber1: 0, // 直推用户数\r\n\t\t\t\tnumber2: 0, // 间推用户数\r\n\t\t\t\tactiveIndex: 0, // tab点亮索引\r\n\t\t\t\tpage: 1 // 读取第几页\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonLoad: function () {\r\n\t\t\t\tthis.fxMembersStatistics()\r\n\t\t\t\tthis.fxMembers()\r\n\t\t\t},\r\n\t\t\tonShow: function () {\r\n\r\n\t\t\t},\r\n\t\t\tasync fxMembersStatistics() {\r\n\t\t\t\tconst res = await this.$wxapi.fxMembersStatistics(this.token)\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.number1 = res.data.totleFansLevel1\r\n\t\t\t\t\tthis.number2 = res.data.totleFansLevel2\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync fxMembers() {\r\n\t\t\t\tconst res = await this.$wxapi.fxMembers({\r\n\t\t\t\t\ttoken: this.token,\r\n\t\t\t\t\tpage: this.page,\r\n\t\t\t\t\tlevel: this.activeIndex == 0 ? 1 : 2\r\n\t\t\t\t})\r\n\t\t\t\tif (res.code == 700) {\r\n\t\t\t\t\tif (this.page == 1) {\r\n\t\t\t\t\t\tthis.members = []\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '没有更多了',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tconst statisticsCommisionMap = res.data.statisticsCommisionMap\r\n\t\t\t\t\tconst userCashMap = res.data.userCashMap\r\n\t\t\t\t\tres.data.result.forEach(ele => {\r\n\t\t\t\t\t\tif (!ele.avatarUrls) {\r\n\t\t\t\t\t\t\tele.avatarUrls = '/images/nav/my-off.png'\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (!ele.nicks) {\r\n\t\t\t\t\t\t\tele.nicks = '用户' + ele.uids\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tconst _statisticsCommisionMap = statisticsCommisionMap[ele.uids]\r\n\t\t\t\t\t\tif (_statisticsCommisionMap) {\r\n\t\t\t\t\t\t\tele.saleroom = _statisticsCommisionMap.saleroom\r\n\t\t\t\t\t\t\tele.numberOrder = _statisticsCommisionMap.numberOrder\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (userCashMap) {\r\n\t\t\t\t\t\t\tconst _userCashMap = userCashMap[ele.uids]\r\n\t\t\t\t\t\t\tif (_userCashMap) {\r\n\t\t\t\t\t\t\t\tele.totleConsumed = _userCashMap.totleConsumed\r\n\t\t\t\t\t\t\t\tele.totalPayNumber = _userCashMap.totalPayNumber\r\n\t\t\t\t\t\t\t\tele.totalPayAmount = _userCashMap.totalPayAmount\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (this.page == 1) {\r\n\t\t\t\t\t\tthis.members = res.data.result\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.members = this.members.concat(res.data.result)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\ttabChange(index) {\r\n\t\t\t\tthis.activeIndex = index\r\n\t\t\t\tthis.page = 1\r\n\t\t\t\tthis.fxMembers()\r\n\t\t\t},\r\n\t\t\tonReachBottom: function() {\r\n\t\t\t\tthis.page += 1\r\n\t\t\t\tthis.fxMembers()\r\n\t\t\t},\r\n\t\t\tonPullDownRefresh: function() {\r\n\t\t\t\tthis.page = 1\r\n\t\t\t\tthis.fxMembersStatistics()\r\n\t\t\t\tthis.fxMembers()\r\n\t\t\t\tuni.stopPullDownRefresh()\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.list {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 100vw;\r\n\t\tpadding: 8rpx 32rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.list .l {\r\n\t\twidth: 100rpx;\r\n\t\theight: 100rpx;\r\n\t\tborder-radius: 50%;\r\n\t}\r\n\t.list .r {\r\n\t\tflex: 1;\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692277244\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}