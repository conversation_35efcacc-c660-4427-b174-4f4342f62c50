{
    "name" : "野贝农beta1.0.206",
    "appid" : "__UNI__0FF43D0",
    "description" : "野贝农",
    "versionName" : "1.0.0",
    "versionCode" : 1,
    "transformPx" : false,
    "sassImplementationName" : "node-sass",
    "app-plus" : {
        "optimization" : {
            "subPackages" : true
        },
        "safearea" : {
            "bottom" : {
                "offset" : "none"
            }
        },
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "modules" : {
            "Webview-x5" : {},
            "Maps" : {},
            "Payment" : {}
        },
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.REQUEST_INSTALL_PACKAGES\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_CONTACTS\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a" ]
            },
            "ios" : {
                "idfa" : false,
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:static-80ab3332-a88f-4a44-8486-06fa7fc6392f.bspapp.com" ]
                    }
                }
            },
            "sdkConfigs" : {
                "ad" : {},
                "push" : {
                    "unipush" : {}
                },
                "maps" : {
                    "amap" : {
                        "appkey_ios" : "f9548782d26ef6412d9db1dd37220394",
                        "appkey_android" : "0550ad69eb6ffc604b53a5e79fc7b9ff"
                    }
                },
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wx9b04553fd8c7b9c3",
                        "UniversalLinks" : "https://static-80ab3332-a88f-4a44-8486-06fa7fc6392f.bspapp.com/uni-universallinks/__UNI__151F11E/"
                    },
                    "alipay" : {
                        "__platform__" : [ "ios", "android" ]
                    }
                }
            },
            "icons" : {
                "android" : {
                    "hdpi" : "unpackage/res/icons/72x72.png",
                    "xhdpi" : "unpackage/res/icons/96x96.png",
                    "xxhdpi" : "unpackage/res/icons/144x144.png",
                    "xxxhdpi" : "unpackage/res/icons/192x192.png"
                },
                "ios" : {
                    "appstore" : "unpackage/res/icons/1024x1024.png",
                    "ipad" : {
                        "app" : "unpackage/res/icons/76x76.png",
                        "app@2x" : "unpackage/res/icons/152x152.png",
                        "notification" : "unpackage/res/icons/20x20.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "proapp@2x" : "unpackage/res/icons/167x167.png",
                        "settings" : "unpackage/res/icons/29x29.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "spotlight" : "unpackage/res/icons/40x40.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png"
                    },
                    "iphone" : {
                        "app@2x" : "unpackage/res/icons/120x120.png",
                        "app@3x" : "unpackage/res/icons/180x180.png",
                        "notification@2x" : "unpackage/res/icons/40x40.png",
                        "notification@3x" : "unpackage/res/icons/60x60.png",
                        "settings@2x" : "unpackage/res/icons/58x58.png",
                        "settings@3x" : "unpackage/res/icons/87x87.png",
                        "spotlight@2x" : "unpackage/res/icons/80x80.png",
                        "spotlight@3x" : "unpackage/res/icons/120x120.png"
                    }
                }
            }
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "wx7f61f277224f688c",
        "setting" : {
            "urlCheck" : true,
            "es6" : false,
            "minified" : true,
            "postcss" : true
        },
        "optimization" : {
            "subPackages" : true
        },
        "usingComponents" : true,
        "permission" : {},
        /* "scope.userLocation" : {
                "desc" : "为你推荐附近的门店"
            } */
        "libVersion" : "latest"
    },
    "mp-alipay" : {
        "usingComponents" : true,
        "component2" : true
    },
    "mp-qq" : {
        "optimization" : {
            "subPackages" : true
        },
        "appid" : "1110971857"
    },
    "mp-baidu" : {
        "usingComponents" : true,
        "appid" : ""
    },
    "mp-toutiao" : {
        "usingComponents" : true,
        "appid" : ""
    },
    "h5" : {
        "template" : "template.h5.html",
        "router" : {
            "mode" : "history",
            "base" : "/"
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : false
            }
        },
        "title" : "野贝农土鲜商城",
        "sdkConfigs" : {
            "maps" : {
                "qqmap" : {
                    "key" : "T37BZ-S3DRW-LFVRR-OJ6ZL-2ZZHF-7MBX4"
                }
            }
        },
        "domain" : "",
        "devServer" : {
            "https" : false
        }
    },
    "_spaceID" : "80ab3332-a88f-4a44-8486-06fa7fc6392f"
}
