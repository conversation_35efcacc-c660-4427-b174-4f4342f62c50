<view class="container-wrapper"><view class="main-wrapper"><view class="top-image-wrapper"><view class="top-image _div"></view></view><view class="main"><block wx:if="{{type==='virtual'}}"><view><view class="title">选择卡面</view><view class="card"><block wx:for="{{images}}" wx:for-item="image" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['cardImgSelect',[index]]]]]}}" bindtap="__e"><image class="{{['_img',(selectedIndex===index)?'active':'']}}" src="{{image.src}}"></image></view></block></view></view></block><view class="title">选择礼品卡</view><view data-ref="foodsWrapper" class="food-wrapper vue-ref"><block wx:for="{{list.foods}}" wx:for-item="food" wx:for-index="index" wx:key="index"><block wx:if="{{$root.g0!=0}}"><view class="food" style="{{'border:'+(food.count>0?'1px #21A921 solid':'1px #e4e4e4 solid')+';'}}"><block wx:if="{{type==='physical'}}"><image class="food-img physical" src="{{food.img}}" mode data-event-opts="{{[['tap',[['goto',['$0'],[[['list.foods','',index]]]]]]]}}" bindtap="__e"></image></block><block wx:if="{{type==='virtual'}}"><image class="food-img virtual" src="{{food.img}}" mode data-event-opts="{{[['tap',[['goto',['$0'],[[['list.foods','',index]]]]]]]}}" bindtap="__e"></image></block><block wx:if="{{type==='water'}}"><image class="food-img water" src="{{food.img}}" mode data-event-opts="{{[['tap',[['goto',['$0'],[[['list.foods','',index]]]]]]]}}" bindtap="__e"></image></block><view class="food-info"><text data-event-opts="{{[['tap',[['goto',['$0'],[[['list.foods','',index]]]]]]]}}" class="food-title" bindtap="__e">{{food.name}}</text><text data-event-opts="{{[['tap',[['goto',['$0'],[[['list.foods','',index]]]]]]]}}" class="food-dsc" bindtap="__e">{{food.description}}</text><block wx:if="{{food.price}}"><view class="price-score"><view class="item"><view><text>¥</text>{{food.price}}<block wx:if="{{food.originalPrice}}"><label class="original-pice _span">{{"原价￥"+food.originalPrice}}</label></block></view></view></view></block><view class="food-btn"><cartcontrol vue-id="{{'646adcc1-1-'+index}}" food="{{food}}" data-event-opts="{{[['^add',[['addCart']]],['^dec',[['decreaseCart']]],['^input',[['inputCart']]]]}}" bind:add="__e" bind:dec="__e" bind:input="__e" bind:__l="__l"></cartcontrol></view></view></view></block></block></view><shopcart vue-id="646adcc1-2" goods="{{goods}}" data-event-opts="{{[['^add',[['addCart']]],['^dec',[['decreaseCart']]],['^input',[['inputCart']]],['^delAll',[['delAll']]]]}}" bind:add="__e" bind:dec="__e" bind:input="__e" bind:delAll="__e" bind:__l="__l"></shopcart></view></view></view>