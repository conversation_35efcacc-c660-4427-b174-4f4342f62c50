<template>
  <view class="index container-wrapper">
    <view class="main-wrapper">
      <view class="top-image-wrapper">
        <div class="top-image">&nbsp;</div>
      </view>
      <view class="main">
        <list-coupons-index :list="list"></list-coupons-index>
      </view>
    </view>
  </view>
</template>

<script>
  import empty from 'empty-value'
  import listCouponsIndex from '@/components/list/list-coupons-index'
  import listCouponsInfo from '@/components/list/list-coupons-info'

  export default {
    components: {
      listCouponsIndex,
      listCouponsInfo
    },
    data() {
      return {
        list: [{
            'title': '实物礼品卡',
            'pic': 'https://ye.niutouren.vip/static/images/coupons/card2.jpg',
            'type': 'physical',
            'tag': '指定抵扣,可自用,可赠送',
          },
          {
            'title': '消费礼品卡',
            'pic': 'https://ye.niutouren.vip/static/images/coupons/card1.jpg',
            'type': 'virtual',
            'tag': '全品通用,可自用,可赠送',
          },
          {
            'title': '水卡',
            'pic': 'https://dcdn.it120.cc/2024/05/05/77302a3c-066e-426d-a36e-da545df8fccf.png',
            'type': 'water',
            'tag': '购水抵扣,可自用,可赠送',
          },
        ],
      }
    }
  }
</script>
<style scoped lang="scss">
  .index.container-wrapper {
    padding: 0;
    margin: 0;
    background: #f72526;
    height: 100vh;
  }

  .index {
    .main-wrapper {
      position: relative;
    }

    .top-image {
      width: 100%;
      height: 220px;
      background-image: url('https://ye.niutouren.vip/static/images/coupons/bg.png');
      background-size: cover;
      background-position: bottom center;
    }
  }

  .main {
    background: #FFFFFF;
    margin: 0 20rpx;
    padding: 10rpx 30rpx;
    border-radius: 20rpx;
  }
</style>