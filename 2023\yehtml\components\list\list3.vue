<template>
  <view class="list3">
    <view v-for="(item, index) in list" :key="index" slot="slot{{index}}">
      <view class="item-wrapper">
        <u-row justify="space-between" gutter="10">
          <u-col span="4" @click="imageClick(item)">
            <div class="list-image-wrapper" :style="{ 'background-image': `url(${item.pic})` }"></div>
          </u-col>
          <u-col span="8">
            <listGoodsItem3 :item="item"></listGoodsItem3>
          </u-col>
        </u-row>
      </view>
    </view>
  </view>
</template>

<script>
  import empty from 'empty-value'
  import listGoodsItem3 from '@/components/list-item/list-goods-item3.vue'

  const TOOLS = require('@/common/tools')
  export default {
    components: {
      listGoodsItem3
    },
    props: {
      list: {
        type: Array,
        default: [],
      },
      type: {
        type: String,
        default: '',
      },
    },
    onReady() {},
    data() {
      return {}
    },
    watch: {
      list: function(val) {
        console.log('watch list is', val)
      }
    },
    methods: {
      imageClick(item) {
        const categoryIds = [383898, 383899, 383900, 383901, 383902, 383903, 383904, 383905]
        if (categoryIds.includes(item.categoryId)) {
          TOOLS.softOpeningTip()
        } else {
          uni.navigateTo({
            url: '/pages/goods/detail?id=' + item.id
          })
        }
      }
    },
  }
</script>
<style>
  .list2 {
    padding: 20rpx 0;
  }

  .item-wrapper {
    border-radius: 10rpx;
    background: #FFFFFF;
    color: #2C3E50;
    padding: 10rpx;
    margin-bottom: 20rpx;
  }
</style>