{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/category/category.vue?e77f", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/category/category.vue?0a85", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/category/category.vue?30d9", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/category/category.vue?f469", "uni-app:///pages/category/category.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/category/category.vue?2e4e", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/category/category.vue?16b8", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/category/category.vue?5ed8", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/category/category.vue?d8d1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "list3", "data", "tabIndex", "kw", "categorySelected", "activeCategory", "categories", "firstCategories", "adPosition", "current", "scrolltop", "goodsList", "skuCurGoods", "page", "showGoodsPop", "goodsDetail", "activeTab", "created", "mounted", "onReady", "onLoad", "uni", "withShareTicket", "TOOLS", "onShow", "onShareAppMessage", "title", "path", "methods", "_categories", "key", "res", "changeTab", "swichMenu", "index", "getGoodsList", "category", "categoryId", "pageSize", "icon", "_showGoodsPop", "getApp", "url", "addCart", "console", "item", "token", "goodsId", "number", "search", "searchscan", "scanType", "success"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,iBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;AACxB;;;AAGrE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,8LAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsDvrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MAEAC;IACA;EACA;EACAC,6BAEA;EACAC,6BAEA;EACAC,6BAEA;EACAC;IAEAC;MACAC;IACA;;IAGA;IACA;IACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;MACA;IACA;IAEAC;EACA;EACAC,2BAEA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBACAT;kBACAK;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAK;gBACAV;gBACAjB;gBACA;kBACAE;oBACA;kBACA;kBAEAC;oBACA;kBACA;kBACAH;kBAEA;kBAEA;oBACA;oBACA;oBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA4B;MACA;MAEA;QACA;MACA;MACA;QACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA,MACAC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBACA;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAd;kBACAK;gBACA;gBACAU,kDACA;gBAAA;gBAAA,OACA;kBACAC;kBACAxB;kBACAyB;gBACA;cAAA;gBAJAP;gBAKAV;gBAAA,MACAU;kBAAA;kBAAA;gBAAA;gBACA;kBACA;gBACA;kBACAV;oBACAK;oBACAa;kBACA;gBACA;gBAAA;cAAA;gBAAA,MAGAR;kBAAA;kBAAA;gBAAA;gBACAV;kBACAK;kBACAa;gBACA;gBAAA;cAAA;gBAGA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACApB;kBACAqB;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAX;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAV;kBACAK;kBACAa;gBACA;gBAAA;cAAA;gBAGA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA;gBAAA,OACAH;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACApB;kBACAqB;gBACA;gBAAA;cAAA;gBAAA,MAIAG;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAJAjB;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAOA;cAAA;gBAAAA;cAAA;gBAAA,MAEAA;kBAAA;kBAAA;gBAAA;gBACAV;kBACAK;kBACAa;gBACA;gBAAA;cAAA;gBAGAhB;gBACAF;kBACAK;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAuB;MACA5B;QACAqB;MACA;IACA;IACAQ;MAAA;MACA7B;QACA8B;QACAC;UACA;UACA/B;YACAqB;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1RA;AAAA;AAAA;AAAA;AAA0xC,CAAgB,8uCAAG,EAAC,C;;;;;;;;;;;ACA9yC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAkwC,CAAgB,stCAAG,EAAC,C;;;;;;;;;;;ACAtxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/category/category.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/category/category.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./category.vue?vue&type=template&id=682a9774&scoped=true&\"\nvar renderjs\nimport script from \"./category.vue?vue&type=script&lang=js&\"\nexport * from \"./category.vue?vue&type=script&lang=js&\"\nimport style0 from \"./category.vue?vue&type=style&index=0&id=682a9774&scoped=true&lang=scss&\"\nimport style1 from \"./category.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"682a9774\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/category/category.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./category.vue?vue&type=template&id=682a9774&scoped=true&\"", "var components\ntry {\n  components = {\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-search/u-search\" */ \"@/uni_modules/uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uRow: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-row/u-row\" */ \"@/uni_modules/uview-ui/components/u-row/u-row.vue\"\n      )\n    },\n    uCol: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-col/u-col\" */ \"@/uni_modules/uview-ui/components/u-col/u-col.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-empty/u-empty\" */ \"@/uni_modules/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    goodsPop: function () {\n      return import(\n        /* webpackChunkName: \"components/goods-pop/goods-pop\" */ \"@/components/goods-pop/goods-pop.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showGoodsPop = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./category.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./category.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"index container-wrapper\">\r\n    <view class=\"category-page-box\">\r\n      <view class=\"category-page\">\r\n        <view class=\"search\">\r\n          <u-search placeholder=\"输入关键词搜索\" searchIconSize=\"28.6\" height=\"41.6\" v-model=\"kw\" :showAction=\"false\"\r\n            @search=\"search\"></u-search>\r\n        </view>\r\n        <view class=\"_main\">\r\n          <view class=\"tab1\">\r\n            <u-row customStyle=\"margin-bottom: 10px\">\r\n              <u-col span=\"4\" justify=\"center\" textAlign=\"center\">\r\n                <div class=\"tab line\" :class=\"{ active: activeTab === 'tab1' }\" @click=\"changeTab('tab1')\">\r\n                  <div style=\"height: 30rpx;\">&nbsp;</div>\r\n                  <div class=\"title\">土鲜多</div>\r\n                  <!-- <div class=\"dsc\">深山好货</div> -->\r\n                </div>\r\n              </u-col>\r\n              <u-col span=\"4\" justify=\"center\" textAlign=\"center\">\r\n                <div class=\"tab line\" :class=\"{ active: activeTab === 'tab2' }\" @click=\"changeTab('tab2')\">\r\n                  <div style=\"height: 30rpx;\">&nbsp;</div>\r\n                  <div class=\"title\">山泉多</div>\r\n                  <!-- <div class=\"dsc\">甘甜健康</div> -->\r\n                </div>\r\n              </u-col>\r\n              <u-col span=\"4\" justify=\"center\" textAlign=\"center\">\r\n                <div class=\"tab\" :class=\"{ active: activeTab === 'tab3' }\" @click=\"changeTab('tab3')\">\r\n                  <div style=\"height: 30rpx;\">&nbsp;</div>\r\n                  <div class=\"title\">好礼多</div>\r\n                  <!-- <div class=\"dsc\">礼品多多</div> -->\r\n                </div>\r\n              </u-col>\r\n            </u-row>\r\n          </view>\r\n          <view class=\"main\">\r\n            <scroll-view class=\"u-tab-view menu-scroll-view\" scroll-y=\"true\" scroll-with-animation=\"true\">\r\n              <view v-for=\"(item,index) in firstCategories\" :key=\"index\" class=\"u-tab-item\"\r\n                :class=\"[current==index ? 'u-tab-item-active' : '']\" :data-current=\"index\" @tap.stop=\"swichMenu(index)\">\r\n                <text class=\"u-line-1\">{{item.name}}</text>\r\n              </view>\r\n            </scroll-view>\r\n            <scroll-view class=\"goods-container\" scroll-y=\"true\" :scroll-top=\"scrolltop\">\r\n              <u-empty v-if=\"!goodsList\" mode=\"list\" text=\"暂无商品\" marginTop=\"200rpx\" />\r\n              <list3 :list=\"goodsList\"></list3>\r\n            </scroll-view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <goods-pop :show=\"showGoodsPop\" :goodsDetail=\"goodsDetail\" @close=\"showGoodsPop = false\"></goods-pop>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import empty from 'empty-value'\r\n  import list3 from '@/components/list/list3'\r\n\r\n  const TOOLS = require('@/common/tools')\r\n  export default {\r\n    components: {\r\n      list3\r\n    },\r\n    data() {\r\n      return {\r\n        tabIndex: 1,\r\n        kw: '',\r\n        categorySelected: {},\r\n        activeCategory: 0,\r\n        categories: undefined,\r\n        firstCategories: undefined,\r\n        adPosition: undefined,\r\n        current: 0, // 预设当前项的值\r\n        scrolltop: 0,\r\n        goodsList: undefined,\r\n        skuCurGoods: undefined,\r\n        page: 1,\r\n        // 下面为弹出商品详情\r\n        showGoodsPop: false,\r\n        goodsDetail: undefined,\r\n\r\n        activeTab: 'tab1',\r\n      }\r\n    },\r\n    created() {\r\n\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    onReady() {\r\n\r\n    },\r\n    onLoad(e) {\r\n      // #ifdef  MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ\r\n      uni.showShareMenu({\r\n        withShareTicket: true,\r\n      })\r\n      // #endif\r\n\r\n      // 切到“山泉水”\r\n      // TODO: 未做其他分类判断\r\n      if (e && e.tid) {\r\n        if (e.tid === '1') {\r\n          this._categories('YBN2')\r\n        }\r\n        if (e.tid === '2') {\r\n          this._categories('YBN3')\r\n        }\r\n      } else {\r\n        this._categories()\r\n      }\r\n\r\n      TOOLS.showTabBarBadge()\r\n    },\r\n    onShow() {\r\n\r\n    },\r\n    onShareAppMessage(e) {\r\n      return {\r\n        title: '\"' + this.sysconfigMap.mallName + '\" ' + this.sysconfigMap.share_profile,\r\n        path: '/pages/index/index?inviter_id=' + this.uid\r\n      }\r\n    },\r\n    methods: {\r\n      async _categories(key = 'YBN1') {\r\n        uni.showLoading({\r\n          title: '',\r\n        })\r\n        // https://www.yuque.com/apifm/nu0f75/racmle\r\n        const res = await this.$wxapi.goodsCategory()\r\n        uni.hideLoading()\r\n        let categorySelected = this.categorySelected\r\n        if (res.code == 0) {\r\n          const categories = res.data.filter(ele => {\r\n            return !ele.vopCid1 && !ele.vopCid2 && ele.key === key;\r\n          })\r\n\r\n          const firstCategories = categories.filter(ele => {\r\n            return ele.level == 1\r\n          })\r\n          categorySelected = firstCategories[0]\r\n\r\n          this.current = 0\r\n\r\n          if (!empty(categories)) {\r\n            this.categories = categories\r\n            this.firstCategories = firstCategories\r\n            this.categorySelected = categorySelected\r\n            this.getGoodsList(0)\r\n          }\r\n        }\r\n      },\r\n      changeTab(tab) {\r\n        this.activeTab = tab\r\n\r\n        if (tab === 'tab1') {\r\n          this._categories('YBN1')\r\n        }\r\n        if (tab === 'tab2') {\r\n          this._categories('YBN2')\r\n        }\r\n        if (tab === 'tab3') {\r\n          this._categories('YBN3')\r\n        }\r\n      },\r\n      // 点击左边的栏目切换\r\n      async swichMenu(index) {\r\n        if (index == this.current) return;\r\n        this.current = index;\r\n        // 如果为0，意味着尚未初始化\r\n        if (this.menuHeight == 0 || this.menuItemHeight == 0) {\r\n          await this.getElRect('menu-scroll-view', 'menuHeight');\r\n          await this.getElRect('u-tab-item', 'menuItemHeight');\r\n        }\r\n        this.scrollTop = 0\r\n        this.getGoodsList(index)\r\n      },\r\n      async getGoodsList(categoryIndex) {\r\n        uni.showLoading({\r\n          title: '',\r\n        })\r\n        const category = this.firstCategories[categoryIndex]\r\n        // https://www.yuque.com/apifm/nu0f75/wg5t98\r\n        const res = await this.$wxapi.goodsv2({\r\n          categoryId: category.id,\r\n          page: this.page,\r\n          pageSize: 20\r\n        })\r\n        uni.hideLoading()\r\n        if (res.code == 700) {\r\n          if (this.page == 1) {\r\n            this.goodsList = null\r\n          } else {\r\n            uni.showToast({\r\n              title: '没有更多了',\r\n              icon: 'none'\r\n            })\r\n          }\r\n          return\r\n        }\r\n        if (res.code != 0) {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n          return\r\n        }\r\n        if (this.page == 1) {\r\n          this.goodsList = res.data.result\r\n        } else {\r\n          this.goodsList = this.goodsList.concat(res.data.result)\r\n        }\r\n      },\r\n      // 弹出商品购买弹窗\r\n      async _showGoodsPop(item) {\r\n        if (!await getApp().checkHasLoginedH5()) {\r\n          uni.navigateTo({\r\n            url: \"/pages/login/login\"\r\n          })\r\n          return\r\n        }\r\n        // https://www.yuque.com/apifm/nu0f75/vuml8a\r\n        const res = await this.$wxapi.goodsDetail(item.id, this.token)\r\n        if (res.code != 0) {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n          return\r\n        }\r\n        this.goodsDetail = res.data\r\n        this.showGoodsPop = true\r\n      },\r\n      async addCart(item) {\r\n        console.log('token', this.token);\r\n        if (!await getApp().checkHasLoginedH5()) {\r\n          uni.navigateTo({\r\n            url: \"/pages/login/login\"\r\n          })\r\n          return\r\n        }\r\n        let res\r\n        if (item.supplyType == 'vop_jd') {\r\n          // https://www.yuque.com/apifm/nu0f75/yum741\r\n          res = await this.$wxapi.jdvopCartAdd({\r\n            token: this.token,\r\n            goodsId: item.yyId,\r\n            number: 1\r\n          })\r\n        } else {\r\n          // https://www.yuque.com/apifm/nu0f75/et6m6m\r\n          res = await this.$wxapi.shippingCarInfoAddItem(this.token, item.id, 1, [], [])\r\n        }\r\n        if (res.code != 0) {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n          return\r\n        }\r\n        TOOLS.showTabBarBadge()\r\n        uni.showToast({\r\n          title: '加入购物车'\r\n        })\r\n      },\r\n      search(v) {\r\n        uni.navigateTo({\r\n          url: '/pages/goods/list?kw=' + v,\r\n        })\r\n      },\r\n      searchscan() {\r\n        uni.scanCode({\r\n          scanType: ['barCode', 'qrCode', 'datamatrix', 'pdf417'],\r\n          success: res => {\r\n            this.kw = res.result\r\n            uni.navigateTo({\r\n              url: '/pages/goods/list?kw=' + res.result,\r\n            })\r\n          }\r\n        })\r\n      }\r\n    }\r\n  }\r\n</script>\r\n<style scoped lang=\"scss\">\r\n  .index.container-wrapper {\r\n    padding: 0;\r\n    margin: 0;\r\n    background: linear-gradient(90deg, #35641e, #80a933) fixed;\r\n    height: 100vh;\r\n  }\r\n\r\n  .category-page {\r\n    display: flex;\r\n    flex-direction: column;\r\n    width: 100vw;\r\n    height: 100vh;\r\n\r\n    .search {\r\n      margin: 100rpx 0 100rpx 60rpx;\r\n      width: 400rpx;\r\n      padding: 16rpx;\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    ._main {\r\n      background: #fff;\r\n      border-radius: 10px 10px 0 0;\r\n    }\r\n\r\n    .main {\r\n      flex: 1;\r\n      overflow: hidden;\r\n      display: flex;\r\n\r\n      .u-tab-view {\r\n        width: 180rpx;\r\n        height: 100%;\r\n        background-color: #f6f6f6;\r\n      }\r\n\r\n      .u-tab-item {\r\n        height: 110rpx;\r\n        background: #FFFFFF;\r\n        box-sizing: border-box;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: center;\r\n        font-size: 26rpx;\r\n        color: #444;\r\n        font-weight: 400;\r\n        line-height: 1;\r\n      }\r\n\r\n      .u-tab-item-active {\r\n        position: relative;\r\n        color: #000;\r\n        font-size: 30rpx;\r\n        font-weight: 600;\r\n        background: #F6F6F6;\r\n      }\r\n\r\n      .u-tab-item-active::before {\r\n        content: \"\";\r\n        position: absolute;\r\n        border-left: 4px solid #e64340;\r\n        height: 60rpx;\r\n        left: 0;\r\n        top: 25rpx;\r\n      }\r\n\r\n      .goods-container {\r\n        flex: 1;\r\n        height: 100%;\r\n        background: #F6F6F6;\r\n        padding: 30rpx 16rpx;\r\n\r\n        .goodsList {\r\n          margin-bottom: 32rpx;\r\n          padding: 0 8rpx;\r\n          display: flex;\r\n\r\n          .goods-info {\r\n            flex: 1;\r\n            margin-left: 24rpx;\r\n            position: relative;\r\n\r\n            .t {\r\n              font-weight: bold;\r\n              color: #333;\r\n              font-size: 28rpx;\r\n            }\r\n\r\n            .t2 {\r\n              color: #666;\r\n              font-size: 26rpx;\r\n            }\r\n\r\n            .price {\r\n              color: #e64340;\r\n              font-size: 40rpx;\r\n              display: flex;\r\n              align-items: center;\r\n\r\n              font {\r\n                font-size: 22rpx;\r\n              }\r\n            }\r\n\r\n            .addCar {\r\n              position: absolute;\r\n              right: 24rpx;\r\n              bottom: 16rpx;\r\n            }\r\n          }\r\n        }\r\n      }\r\n    }\r\n\r\n  }\r\n</style>\r\n<style lang=\"scss\">\r\n  .tab1 {\r\n    margin: 0 12px;\r\n  }\r\n\r\n  .tab1 .tab.line {\r\n    position: relative;\r\n    width: 100%;\r\n    padding-bottom: 2rpx;\r\n  }\r\n\r\n  .tab1 .tab.line::after {\r\n    content: \"\";\r\n    position: absolute;\r\n    top: 20%;\r\n    right: 0;\r\n    bottom: 10%;\r\n    width: 1px;\r\n    background-color: #d3d3d3;\r\n  }\r\n\r\n  .tab1 .tab .title {\r\n    font-size: 40rpx;\r\n    font-weight: bolder;\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  .tab1 .tab .dsc {\r\n    font-size: 30rpx;\r\n    height: 60rpx;\r\n  }\r\n\r\n  .tab1 .tab.active {\r\n    //background-image: url(\"/static/images/tab-bg.png\");\r\n    background-size: contain;\r\n    background-repeat: no-repeat;\r\n    background-position: center;\r\n  }\r\n\r\n  .tab1 .tab.active .dsc {\r\n    //background-image: url(\"/static/images/tab-dsc-bg.png\");\r\n    background-size: contain;\r\n    background-repeat: no-repeat;\r\n    background-position: center;\r\n    font-size: 32rpx;\r\n    line-height: 50rpx;\r\n    color: #FFFFFF;\r\n  }\r\n\r\n  .swiper {\r\n    height: 420rpx;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./category.vue?vue&type=style&index=0&id=682a9774&scoped=true&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./category.vue?vue&type=style&index=0&id=682a9774&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692294089\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./category.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./category.vue?vue&type=style&index=1&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692294096\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}