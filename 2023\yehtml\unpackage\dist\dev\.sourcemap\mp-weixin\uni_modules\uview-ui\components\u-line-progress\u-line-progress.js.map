{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-line-progress/u-line-progress.vue?53df", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-line-progress/u-line-progress.vue?13ab", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-line-progress/u-line-progress.vue?2830", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-line-progress/u-line-progress.vue?7952", "uni-app:///uni_modules/uview-ui/components/u-line-progress/u-line-progress.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-line-progress/u-line-progress.vue?6a3e", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-line-progress/u-line-progress.vue?8be1"], "names": ["name", "mixins", "data", "lineWidth", "watch", "percentage", "computed", "progressStyle", "style", "innserPercentage", "mounted", "methods", "init", "uni", "getProgressWidth", "resizeProgressWidth", "width", "size"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0B9rB;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,eAYA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACAC;MACAA;MACAA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MACAC;QACA;MACA;IACA;IACAC;MAEA;IAWA;IACAC;MAAA;MACA;QACA,IACAC,QACAC,KADAD;QAEA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACrGA;AAAA;AAAA;AAAA;AAAiyC,CAAgB,qvCAAG,EAAC,C;;;;;;;;;;;ACArzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-line-progress/u-line-progress.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-line-progress.vue?vue&type=template&id=26adb0f2&scoped=true&\"\nvar renderjs\nimport script from \"./u-line-progress.vue?vue&type=script&lang=js&\"\nexport * from \"./u-line-progress.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-line-progress.vue?vue&type=style&index=0&id=26adb0f2&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"26adb0f2\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-line-progress/u-line-progress.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-line-progress.vue?vue&type=template&id=26adb0f2&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.$u.addStyle(_vm.customStyle)])\n  var g0 = _vm.$u.addUnit(_vm.height)\n  var s1 = _vm.__get_style([_vm.progressStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-line-progress.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-line-progress.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t    class=\"u-line-progress\"\r\n\t    :style=\"[$u.addStyle(customStyle)]\"\r\n\t>\r\n\t\t<view\r\n\t\t    class=\"u-line-progress__background\"\r\n\t\t    ref=\"u-line-progress__background\"\r\n\t\t    :style=\"[{\r\n\t\t\t\tbackgroundColor: inactiveColor,\r\n\t\t\t\theight: $u.addUnit(height),\r\n\t\t\t}]\"\r\n\t\t>\r\n\t\t</view>\r\n\t\t<view\r\n\t\t    class=\"u-line-progress__line\"\r\n\t\t    :style=\"[progressStyle]\"\r\n\t\t> \r\n\t\t\t<slot>\r\n\t\t\t\t<text v-if=\"showText && percentage >= 10\" class=\"u-line-progress__text\">{{innserPercentage + '%'}}</text>\r\n\t\t\t</slot> \r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t// #ifdef APP-NVUE\r\n\tconst dom = uni.requireNativePlugin('dom')\r\n\t// #endif\r\n\t/**\r\n\t * lineProgress 线型进度条\r\n\t * @description 展示操作或任务的当前进度，比如上传文件，是一个线形的进度条。\r\n\t * @tutorial https://www.uviewui.com/components/lineProgress.html\r\n\t * @property {String}\t\t\tactiveColor\t\t激活部分的颜色 ( 默认 '#19be6b' )\r\n\t * @property {String}\t\t\tinactiveColor\t背景色 ( 默认 '#ececec' )\r\n\t * @property {String | Number}\tpercentage\t\t进度百分比，数值 ( 默认 0 )\r\n\t * @property {Boolean}\t\t\tshowText\t\t是否在进度条内部显示百分比的值 ( 默认 true )\r\n\t * @property {String | Number}\theight\t\t\t进度条的高度，单位px ( 默认 12 )\r\n\t * \r\n\t * @example <u-line-progress :percent=\"70\" :show-percent=\"true\"></u-line-progress>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-line-progress\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tlineWidth: 0,\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tpercentage(n) {\r\n\t\t\t\tthis.resizeProgressWidth()\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tprogressStyle() { \r\n\t\t\t\tlet style = {}\r\n\t\t\t\tstyle.width = this.lineWidth\r\n\t\t\t\tstyle.backgroundColor = this.activeColor\r\n\t\t\t\tstyle.height = uni.$u.addUnit(this.height)\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\tinnserPercentage() {\r\n\t\t\t\t// 控制范围在0-100之间\r\n\t\t\t\treturn uni.$u.range(0, 100, this.percentage)\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit() {\r\n\t\t\t\tuni.$u.sleep(20).then(() => {\r\n\t\t\t\t\tthis.resizeProgressWidth()\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tgetProgressWidth() {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\treturn this.$uGetRect('.u-line-progress__background')\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 返回一个promise\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tdom.getComponentRect(this.$refs['u-line-progress__background'], (res) => {\r\n\t\t\t\t\t\tresolve(res.size)\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tresizeProgressWidth() {\r\n\t\t\t\tthis.getProgressWidth().then(size => {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\twidth\r\n\t\t\t\t\t} = size\r\n\t\t\t\t\t// 通过设置的percentage值，计算其所占总长度的百分比\r\n\t\t\t\t\tthis.lineWidth = width * this.innserPercentage / 100 + 'px'\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-line-progress {\r\n\t\talign-items: stretch;\r\n\t\tposition: relative;\r\n\t\t@include flex(row);\r\n\t\tflex: 1;\r\n\t\toverflow: hidden;\r\n\t\tborder-radius: 100px;\r\n\r\n\t\t&__background {\r\n\t\t\tbackground-color: #ececec;\r\n\t\t\tborder-radius: 100px;\r\n\t\t\tflex: 1;\r\n\t\t}\r\n\r\n\t\t&__line {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 0;\r\n\t\t\tleft: 0;\r\n\t\t\tbottom: 0;\r\n\t\t\talign-items: center;\r\n\t\t\t@include flex(row);\r\n\t\t\tcolor: #ffffff;\r\n\t\t\tborder-radius: 100px;\r\n\t\t\ttransition: width 0.5s ease;\r\n\t\t\tjustify-content: flex-end;\r\n\t\t}\r\n\r\n\t\t&__text {\r\n\t\t\tfont-size: 10px;\r\n\t\t\talign-items: center;\r\n\t\t\ttext-align: right;\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t\tmargin-right: 5px;\r\n\t\t\ttransform: scale(0.9);\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-line-progress.vue?vue&type=style&index=0&id=26adb0f2&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-line-progress.vue?vue&type=style&index=0&id=26adb0f2&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688737758\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}