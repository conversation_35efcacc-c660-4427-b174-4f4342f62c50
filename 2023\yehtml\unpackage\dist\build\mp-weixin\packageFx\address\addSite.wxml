<view class="wrap data-v-e2bf6686"><view class="top data-v-e2bf6686"><u-form vue-id="136fb08e-1" label-width="130rpx" model="{{form}}" data-ref="uForm" class="data-v-e2bf6686 vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('136fb08e-2')+','+('136fb08e-1')}}" label="收货人" prop="linkMan" required="{{true}}" class="data-v-e2bf6686" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('136fb08e-3')+','+('136fb08e-2')}}" clearable="{{true}}" placeholder="请输入收货人" value="{{form.linkMan}}" data-event-opts="{{[['^input',[['__set_model',['$0','linkMan','$event',[]],['form']]]]]}}" class="data-v-e2bf6686" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('136fb08e-4')+','+('136fb08e-1')}}" label="手机号码" prop="mobile" required="{{true}}" class="data-v-e2bf6686" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('136fb08e-5')+','+('136fb08e-4')}}" type="number" clearable="{{true}}" placeholder="请输入手机号码" value="{{form.mobile}}" data-event-opts="{{[['^input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" class="data-v-e2bf6686" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('136fb08e-6')+','+('136fb08e-1')}}" label="所在地区" prop="areaDisplay" required="{{true}}" class="data-v-e2bf6686" bind:__l="__l" vue-slots="{{['default']}}"><u-cell style="width:100%;" vue-id="{{('136fb08e-7')+','+('136fb08e-6')}}" title="{{form.areaDisplay?form.areaDisplay:'省市区县、乡镇信息'}}" required="{{true}}" clickable="{{true}}" isLink="{{true}}" border="{{false}}" data-event-opts="{{[['^click',[['e0']]]]}}" bind:click="__e" class="data-v-e2bf6686" bind:__l="__l"></u-cell><city-select vue-id="{{('136fb08e-8')+','+('136fb08e-6')}}" area-code="{{areaCode}}" level="{{addressLevel}}" value="{{showRegion}}" data-event-opts="{{[['^cityChange',[['cityChange']]],['^input',[['__set_model',['','showRegion','$event',[]]]]]]}}" bind:cityChange="__e" bind:input="__e" class="data-v-e2bf6686" bind:__l="__l"></city-select></u-form-item><u-form-item vue-id="{{('136fb08e-9')+','+('136fb08e-1')}}" label="详细地址" prop="address" required="{{true}}" class="data-v-e2bf6686" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('136fb08e-10')+','+('136fb08e-9')}}" type="textarea" auto-height="{{true}}" clearable="{{true}}" placeholder="请输入详细地址" value="{{form.address}}" data-event-opts="{{[['^input',[['__set_model',['$0','address','$event',[]],['form']]]]]}}" class="data-v-e2bf6686" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('136fb08e-11')+','+('136fb08e-1')}}" label="选择小区" prop="xiaoqu" class="data-v-e2bf6686" bind:__l="__l" vue-slots="{{['default']}}"><view class="autocomplete data-v-e2bf6686"><str-autocomplete vue-id="{{('136fb08e-12')+','+('136fb08e-11')}}" importvalue="{{importString}}" list="{{list}}" label="{{labelName}}" highlightColor="#FF0000" data-event-opts="{{[['^select',[['selectOne']]],['^change',[['textChange']]]]}}" bind:select="__e" bind:change="__e" class="data-v-e2bf6686" bind:__l="__l"></str-autocomplete></view></u-form-item><u-form-item vue-id="{{('136fb08e-13')+','+('136fb08e-1')}}" label="默认地址" class="data-v-e2bf6686" bind:__l="__l" vue-slots="{{['right']}}"><u-switch bind:input="__e" vue-id="{{('136fb08e-14')+','+('136fb08e-13')}}" slot="right" active-color="#19be6b" value="{{form.isDefault}}" data-event-opts="{{[['^input',[['__set_model',['$0','isDefault','$event',[]],['form']]]]]}}" class="data-v-e2bf6686" bind:__l="__l"></u-switch></u-form-item></u-form></view><view class="submit-btn data-v-e2bf6686"><u-button vue-id="136fb08e-15" type="success" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" class="data-v-e2bf6686" bind:__l="__l" vue-slots="{{['default']}}">保存</u-button></view></view>