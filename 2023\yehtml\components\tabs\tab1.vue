<template>
  <view class="tab1">
    <u-row customStyle="margin-bottom: 10px">
      <u-col span="4" justify="center" textAlign="center">
        <div class="tab line" :class="{ active: activeTab === 'tab1' }" @click="changeTab('tab1')">
          <div style="height: 30rpx;">&nbsp;</div>
          <div class="title">土鲜多</div>
          <div class="dsc">深山好货</div>
        </div>
      </u-col>
      <u-col span="4" justify="center" textAlign="center">
        <div class="tab line" :class="{ active: activeTab === 'tab2' }" @click="changeTab('tab2')">
          <div style="height: 30rpx;">&nbsp;</div>
          <div class="title">泉水多</div>
          <div class="dsc">健康安全</div>
        </div>
      </u-col>
      <u-col span="4" justify="center" textAlign="center">
        <div class="tab" :class="{ active: activeTab === 'tab3' }" @click="changeTab('tab3')">
          <div style="height: 30rpx;">&nbsp;</div>
          <div class="title">好礼多</div>
          <div class="dsc">礼品多多</div>
        </div>
      </u-col>
    </u-row>
    <view v-show="activeTab === 'tab1'">Content for Tab 1</view>
    <view v-show="activeTab === 'tab2'">Content for Tab 2</view>
    <view v-show="activeTab === 'tab3'">Content for Tab 3</view>
  </view>
</template>

<script>
  import empty from 'empty-value'

  export default {
    components: {},
    props: {
      type: {
        type: String,
        default: '',
      },
    },
    onReady() {},
    data() {
      return {
        activeTab: 'tab1'
      }
    },
    watch: {},
    methods: {
      changeTab(tab) {
        this.activeTab = tab
      }
    },
  }
</script>