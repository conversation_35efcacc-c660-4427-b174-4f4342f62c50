<u-popup vue-id="297e2191-1" mode="center" zoom="{{zoom}}" show="{{show}}" customStyle="{{$root.a0}}" closeOnClickOverlay="{{closeOnClickOverlay}}" safeAreaInsetBottom="{{false}}" duration="{{400}}" data-event-opts="{{[['^click',[['clickHandler']]]]}}" bind:click="__e" class="data-v-77f573cf" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-modal data-v-77f573cf" style="{{'width:'+($root.g0)+';'}}"><block wx:if="{{title}}"><text class="u-modal__title data-v-77f573cf">{{title}}</text></block><view class="u-modal__content data-v-77f573cf" style="{{'padding-top:'+((title?12:25)+'px')+';'}}"><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><text class="u-modal__content__text data-v-77f573cf">{{content}}</text></block></view><block wx:if="{{$slots.confirmButton}}"><view class="u-modal__button-group--confirm-button data-v-77f573cf"><slot name="confirmButton"></slot></view></block><block wx:else><u-line vue-id="{{('297e2191-2')+','+('297e2191-1')}}" class="data-v-77f573cf" bind:__l="__l"></u-line><view class="u-modal__button-group data-v-77f573cf" style="{{'flex-direction:'+(buttonReverse?'row-reverse':'row')+';'}}"><block wx:if="{{showCancelButton}}"><view class="{{['u-modal__button-group__wrapper','u-modal__button-group__wrapper--cancel','data-v-77f573cf',showCancelButton&&!showConfirmButton&&'u-modal__button-group__wrapper--only-cancel']}}" hover-stay-time="{{150}}" hover-class="u-modal__button-group__wrapper--hover" data-event-opts="{{[['tap',[['cancelHandler',['$event']]]]]}}" bindtap="__e"><text class="u-modal__button-group__wrapper__text data-v-77f573cf" style="{{'color:'+(cancelColor)+';'}}">{{cancelText}}</text></view></block><block wx:if="{{showConfirmButton&&showCancelButton}}"><u-line vue-id="{{('297e2191-3')+','+('297e2191-1')}}" direction="column" class="data-v-77f573cf" bind:__l="__l"></u-line></block><block wx:if="{{showConfirmButton}}"><view class="{{['u-modal__button-group__wrapper','u-modal__button-group__wrapper--confirm','data-v-77f573cf',!showCancelButton&&showConfirmButton&&'u-modal__button-group__wrapper--only-confirm']}}" hover-stay-time="{{150}}" hover-class="u-modal__button-group__wrapper--hover" data-event-opts="{{[['tap',[['confirmHandler',['$event']]]]]}}" bindtap="__e"><block wx:if="{{loading}}"><u-loading-icon vue-id="{{('297e2191-4')+','+('297e2191-1')}}" class="data-v-77f573cf" bind:__l="__l"></u-loading-icon></block><block wx:else><text class="u-modal__button-group__wrapper__text data-v-77f573cf" style="{{'color:'+(confirmColor)+';'}}">{{confirmText}}</text></block></view></block></view></block></view></u-popup>