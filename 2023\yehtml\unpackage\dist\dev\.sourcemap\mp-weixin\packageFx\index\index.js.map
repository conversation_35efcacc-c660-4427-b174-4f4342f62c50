{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/index/index.vue?d902", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/index/index.vue?acd9", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/index/index.vue?012e", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/index/index.vue?da95", "uni-app:///packageFx/index/index.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/index/index.vue?74d2", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/index/index.vue?0f88"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "Date", "format", "date", "data", "qrcode", "apiUserInfoMap", "balance", "freeze", "fxCommisionPaying", "score", "commisionData", "today", "yesday", "thisMonth", "lastM<PERSON>h", "todayXiaoshou", "yesdayXiaoshou", "thisMonthXiaoshou", "lastMonthXiaosh<PERSON>", "methods", "onLoad", "onShow", "commision", "uid", "nowDate", "console", "yestoday", "year", "month", "dateBegin", "dateEnd", "res", "toFixed", "getUserApiInfo", "that", "doneShow", "uni", "title", "icon", "_this", "copyContent", "doneShow2", "userDetail", "applyStatus", "fetchQrcode", "mask", "scene", "page", "is_hyaline", "autoColor", "expireHours", "saveToMobile", "filePath", "success", "content", "showCancel", "confirmText", "confirmColor", "fail", "fxCities", "adPosition", "goUrl", "url", "onShareAppMessage", "path", "imageUrl", "goApply"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,aAAa,gUAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,grBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACiJprBC;EACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EACA;EACA;IACAC;EACA;EACA;IACA;MACAA,2DACAC;IACA;EACA;EACA;AACA;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACA;QACA;UACA;UACA;UACA;QACA;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBACAb;gBACAc;gBACAC;gBACAA;gBACAC;gBACAD;gBACA;gBACAE;gBACAC;gBACA;kBACAA;kBACAD;gBACA;kBACAC;gBACA;gBACAd;gBACAW;gBAAA;gBAAA,OACA;kBACAI;kBACAC;kBACAP;gBACA;cAAA;gBAJAQ;gBAKA;kBACArB;kBACAA;kBACA;oBACAA,yGACA;oBACAA;kBACA;gBACA;gBAAA;gBAAA,OACA;kBACAmB;kBACAC;kBACAP;gBACA;cAAA;gBAJAQ;gBAKA;kBACArB;kBACAA;kBACA;oBACAA,0GACA;oBACAA;kBACA;gBACA;gBAAA;gBAAA,OACA;kBACAmB;kBACAC;kBACAP;gBACA;cAAA;gBAJAQ;gBAKA;kBACArB;kBACAA;kBACA;oBACAA,qGACAsB;oBACAtB,mGACAsB;kBACA;gBACA;gBAAA;gBAAA,OACA;kBACAH;kBACAC;kBACAP;gBACA;cAAA;gBAJAQ;gBAKA;kBACArB;kBACAA;kBACA;oBACAA,qGACAsB;oBACAtB,mGACAsB;kBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;QACA;UACAC;UACAA;UACA;YACA;YACAA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;MACA;MACA;QACA;UACAC;YACAC;YACAC;UACA;UACA;QACA;QACA;UACA;QACA;QACA;UACAC;UACAA;UACAA;UACAA;UACAA;QACA;MACA;IACA;IACAC;MACAJ;QACAjC;MACA;IACA;IACAsC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAF;gBAAA;gBAAA,OACA;cAAA;gBAAAG;gBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACAH;kBACA;kBACA;oBACA;sBACAI;oBACA;sBACAA;oBACA;oBACAJ;oBACAA;kBACA;kBACA;oBACAA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAK;MAAA;MACA;MACAR;QACAC;QACAQ;MACA;MACA;QACAC;QACAC;QACAC;QACAC;QACAC;MACA;QACAd;QACA;UACAA;YACAC;YACAC;UACA;UACA;QACA;QACA;UACA;UACA;QACA;MACA;IACA;IAEAa;MACA;MACAf;QACAgB;QACAC;UACAjB;YACAkB;YACAC;YACAC;YACAC;UACA;QACA;QACAC;UACAtB;YACAC;YACAC;UACA;QACA;MACA;IACA;IACA;IACAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA5B;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACA6B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA7B;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA8B;MACA;MACA;QACAzB;UACA0B;QACA;MACA;IACA;IACAC;MACA;QACA1B;QACA2B;QACAC;QACAZ;UACA;QAAA,CACA;QACAK;UACA;QAAA;MAEA;IACA;IACAQ;MACA9B;QACA0B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvbA;AAAA;AAAA;AAAA;AAA48B,CAAgB,+7BAAG,EAAC,C;;;;;;;;;;;ACAh+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packageFx/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packageFx/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=3580f5e0&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packageFx/index/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=3580f5e0&\"", "var components\ntry {\n  components = {\n    uCellGroup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell-group/u-cell-group\" */ \"@/uni_modules/uview-ui/components/u-cell-group/u-cell-group.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <view>\r\n      <image style=\"width:750rpx;height:486rpx\" mode=\"aspectFit\"\r\n        src=\"https://dcdn.it120.cc/static/yidianan/index-top-bg.png\"></image>\r\n    </view>\r\n\r\n    <!-- 如果当前用户是分销商 -->\r\n    <view v-if=\"apiUserInfoMap.base && apiUserInfoMap.base.isSeller\">\r\n      <view class=\"tabTop\" style=\"margin-top:-420rpx\">\r\n        <view class=\"header-box\">\r\n          <image class=\"avatar\" :src=\"apiUserInfoMap.base.avatarUrl\" mode=\"aspectFill\"></image>\r\n          <view class=\"r\">\r\n            <view class=\"uid\">用户编号: {{ apiUserInfoMap.base.id }}</view>\r\n            <view style=\"display:flex\">\r\n              <view class=\"nick\">{{ apiUserInfoMap.base.nick }}</view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n        <view class=\"header-box2\"> </view>\r\n        <view class=\"line\"></view>\r\n        <view class=\"asset\">\r\n          <view class='item' @click='goAsset' style=\"width: 170rpx\">\r\n            <view class=\"Count\">{{fxCommisionPaying}}</view>\r\n            <view>未结算金额</view>\r\n          </view>\r\n          <view class='item' @click='goAsset' style=\"width: 170rpx\">\r\n            <view class=\"Count\">{{freeze}}</view>\r\n            <view>冻结金额</view>\r\n          </view>\r\n          <view class='item right' @click='goAsset' style=\"width: 170rpx\">\r\n            <view class=\"Count\" style=\"color:#FF444A\">{{balance}}</view>\r\n            <view>可用金额</view>\r\n          </view>\r\n        </view>\r\n        <view class=\"line\"></view>\r\n        <view class=\"titleXS\">我的业绩</view>\r\n        <view class=\"asset\">\r\n          <view class='item'>\r\n            <view class=\"Count\">{{commisionData.todayXiaoshou}}</view>\r\n            <view>今日销售</view>\r\n            <view class=\"yjP\">{{commisionData.today ? commisionData.today : 0}}</view>\r\n            <view class=\"yjT\">（佣金）</view>\r\n          </view>\r\n          <view class='item right'>\r\n            <view class=\"Count\">{{commisionData.yesdayXiaoshou}}</view>\r\n            <view>昨天销售</view>\r\n            <view class=\"yjP\">{{commisionData.yesday ? commisionData.yesday : 0}}</view>\r\n            <view class=\"yjT\">（佣金）</view>\r\n          </view>\r\n          <view class='item right'>\r\n            <view class=\"Count\">{{commisionData.thisMonthXiaoshou}}</view>\r\n            <view>本月销售</view>\r\n            <view class=\"yjP\">{{commisionData.thisMonth ? commisionData.thisMonth : 0}}</view>\r\n            <view class=\"yjT\">（佣金）</view>\r\n          </view>\r\n          <view class='item right'>\r\n            <view class=\"Count\">{{commisionData.lastMonthXiaoshou}}</view>\r\n            <view>上月销售</view>\r\n            <view class=\"yjP\">{{commisionData.lastMonth ? commisionData.lastMonth : 0}}</view>\r\n            <view class=\"yjT\">（佣金）</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view v-if=\"apiUserInfoMap.referrer\" class=\"tuan\" style=\"padding: 40rpx 40rpx 20rpx 40rpx;\">\r\n        <view>我的邀请人</view>\r\n        <view class=\"line2\"></view>\r\n        <view style=\"display:flex\">\r\n          <image style=\"width:80rpx;height:80rpx;margin:10px 20px 0px 0;border-radius:100%\"\r\n            :src=\"apiUserInfoMap.referrer.avatarUrl\"></image>\r\n          <view style=\"height:120rpx;line-height:120rpx;font-size:26rpx;\">{{apiUserInfoMap.referrer.nick}}</view>\r\n        </view>\r\n      </view>\r\n      <image v-if=\"fxIndexAdPos\" :src=\"fxIndexAdPos.val\" mode=\"widthFix\" class=\"adpos\" @click=\"goUrl(fxIndexAdPos.url)\">\r\n      </image>\r\n\r\n      <u-cell-group title=\"分销信息\" customStyle=\"padding: 0 24rpx;\">\r\n        <u-form-item label=\"我的邀请码\" labelWidth=\"100\">\r\n          <u-input v-model=\"apiUserInfoMap.base.id\" disabled clearable inputAlign=\"center\">\r\n            <template>\r\n              <u-button slot=\"suffix\" @tap=\"copyContent(apiUserInfoMap.base.id)\" text=\"复制\" type=\"success\"\r\n                size=\"mini\"></u-button>\r\n            </template>\r\n          </u-input>\r\n        </u-form-item>\r\n\r\n        <u-cell title=\"我的团队\" value=\"查看\" isLink url=\"../myusers/index\"></u-cell>\r\n        <u-cell title=\"推广订单\" value=\"查看\" isLink url=\"../commisionLog/index\"></u-cell>\r\n        <u-cell title=\"账单明细\" value=\"查看\" isLink url=\"../../pages/asset/cashlog\"></u-cell>\r\n      </u-cell-group>\r\n      <!-- 团队长、副队长 -->\r\n      <u-cell-group\r\n        v-if=\"apiUserInfoMap.saleDistributionTeam && (apiUserInfoMap.saleDistributionTeam.leader == apiUserInfoMap.base.id || apiUserInfoMap.saleDistributionTeam.deputyLeader == apiUserInfoMap.base.id )\"\r\n        title=\"我的团队\" customStyle=\"padding: 0 24rpx;\">\r\n        <u-cell icon=\"setting-fill\" :title=\"apiUserInfoMap.saleDistributionTeam.name\"></u-cell>\r\n        <u-cell icon=\"integral-fill\" title=\"身份\"\r\n          :value=\"apiUserInfoMap.saleDistributionTeam.leader == apiUserInfoMap.base.id ? '队长' : '副队长'\"></u-cell>\r\n        <u-cell icon=\"integral-fill\" title=\"销售目标\"\r\n          :value=\"'¥' + apiUserInfoMap.saleDistributionTeam.standardSaleroom + '/月'\"></u-cell>\r\n        <u-cell icon=\"integral-fill\" title=\"本月销售\"\r\n          :value=\"'¥' + apiUserInfoMap.saleDistributionTeam.curSaleroom\"></u-cell>\r\n        <u-cell icon=\"integral-fill\" title=\"月度报表\" isLink\r\n          :url=\"'../report/team?teamId=' + apiUserInfoMap.base.teamId\"></u-cell>\r\n      </u-cell-group>\r\n\r\n      <!-- 城市合伙人 -->\r\n      <u-cell-group v-for=\"(item,index) in fxCities\" :title=\"item.provinceName + item.cityName + '合伙人'\"\r\n        customStyle=\"padding: 0 24rpx;\">\r\n        <u-cell icon=\"setting-fill\" title=\"销售目标\" :value=\"'¥' + item.standardSaleroom + '/月'\"></u-cell>\r\n        <u-cell icon=\"integral-fill\" title=\"本月销售\" :value=\"'¥' + item.curSaleroom\"></u-cell>\r\n        <u-cell icon=\"integral-fill\" title=\"月度报表\" isLink\r\n          :url=\"'../report/city?provinceId=' + item.provinceId + '& cityId=' + item.cityId\"></u-cell>\r\n      </u-cell-group>\r\n\r\n\r\n      <view class='noApply' style=\"padding-top:10px;padding-bottom:20px\">\r\n        <view style=\"text-align:center;\">\r\n          <view class=\"canvas-box\">\r\n            <image class=\"canvas\" style=\"width:200px;height:200px\" :src=\"qrcode\"></image>\r\n          </view>\r\n          <view class=\"tzBtn\" @click=\"saveToMobile\" style=\"margin-top:10px;background: #F5D795;padding: 0 16rpx;\">保存到相册\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n    </view>\r\n\r\n    <!-- 还不是分销商 -->\r\n    <view v-if=\"apiUserInfoMap.base && !apiUserInfoMap.base.isSeller\" class=\"tabTop\" style=\"margin-top:-450rpx\">\r\n      <view class=\"header-box\">\r\n        <image class=\"avatar\" :src=\"apiUserInfoMap.base.avatarUrl\" mode=\"aspectFill\"></image>\r\n        <view class=\"r\">\r\n          <view class=\"uid\">用户ID: {{ apiUserInfoMap.base.id }}</view>\r\n          <view class=\"nick\">{{ apiUserInfoMap.base.nick }}</view>\r\n        </view>\r\n      </view>\r\n      <view class=\"header-box2\">您当前还不是分销商</view>\r\n      <view class=\"line\"></view>\r\n      <view class=\"header-box2\" @click=\"goApply\">立即前往申请成为分销商 ></view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  Date.prototype.format = function(format) {\r\n    var date = {\r\n      \"M+\": this.getMonth() + 1,\r\n      \"d+\": this.getDate(),\r\n      \"h+\": this.getHours(),\r\n      \"m+\": this.getMinutes(),\r\n      \"s+\": this.getSeconds(),\r\n      \"q+\": Math.floor((this.getMonth() + 3) / 3),\r\n      \"S+\": this.getMilliseconds()\r\n    };\r\n    if (/(y+)/i.test(format)) {\r\n      format = format.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length));\r\n    }\r\n    for (var k in date) {\r\n      if (new RegExp(\"(\" + k + \")\").test(format)) {\r\n        format = format.replace(RegExp.$1, RegExp.$1.length == 1 ?\r\n          date[k] : (\"00\" + date[k]).substr((\"\" + date[k]).length));\r\n      }\r\n    }\r\n    return format;\r\n  }\r\n  export default {\r\n    data() {\r\n      return {\r\n        qrcode: '',\r\n        apiUserInfoMap: [],\r\n        balance: 0,\r\n        freeze: 0,\r\n        fxCommisionPaying: 0,\r\n        score: 0,\r\n        commisionData: {\r\n          today: 0,\r\n          yesday: 0,\r\n          thisMonth: 0,\r\n          lastMonth: 0,\r\n          todayXiaoshou: 0,\r\n          yesdayXiaoshou: 0,\r\n          thisMonthXiaoshou: 0,\r\n          lastMonthXiaoshou: 0,\r\n        },\r\n      }\r\n    },\r\n    methods: {\r\n      onLoad: function(options) {\r\n        // uni.setStorageSync('token', '4f02de6e-914f-4439-a128-a62a6bbdc3e4')\r\n        this.adPosition()\r\n      },\r\n      onShow: function() {\r\n        this.$wxapi.checkToken(this.token).then(res => {\r\n          if (res.code === 0) {\r\n            this.doneShow();\r\n            this.doneShow2();\r\n            this.getUserApiInfo();\r\n          }\r\n        })\r\n      },\r\n\r\n      async commision() {\r\n        const uid = this.apiUserInfoMap.base.id\r\n        var commisionData = this.commisionData\r\n        const nowDate = new Date()\r\n        console.log('今天', nowDate.format('yyyyMMdd'))\r\n        console.log('本月', nowDate.format('yyyyMM'))\r\n        const yestoday = new Date(nowDate.getTime() - 24 * 60 * 60 * 1000)\r\n        console.log('昨天', yestoday.format('yyyyMMdd'))\r\n        // 上个月\r\n        let year = nowDate.getFullYear()\r\n        let month = nowDate.getMonth() + 1\r\n        if (month == 1) {\r\n          month = 12\r\n          year--\r\n        } else {\r\n          month--\r\n        }\r\n        const lastMonth = year + \"\" + (month < 10 ? ('0' + month) : month)\r\n        console.log('上个月', lastMonth)\r\n        let res = await this.$wxapi.siteStatisticsSaleroom({\r\n          dateBegin: nowDate.format('yyyyMMdd'),\r\n          dateEnd: nowDate.format('yyyyMMdd'),\r\n          uid: uid\r\n        })\r\n        if (res.code === 0) {\r\n          commisionData.today = res.data[0].estimateCommission\r\n          commisionData.todayXiaoshou = res.data[0].saleroom\r\n          if (uni.getStorageSync('exchangerate')) {\r\n            commisionData.aaaaatoday = (res.data[0].estimateCommission * uni.getStorageSync('exchangerate')).toFixed(\r\n              2)\r\n            commisionData.aaaaatodayXiaoshou = (res.data[0].saleroom * uni.getStorageSync('exchangerate')).toFixed(2)\r\n          }\r\n        }\r\n        res = await this.$wxapi.siteStatisticsSaleroom({\r\n          dateBegin: yestoday.format('yyyyMMdd'),\r\n          dateEnd: yestoday.format('yyyyMMdd'),\r\n          uid: uid\r\n        })\r\n        if (res.code === 0) {\r\n          commisionData.yesday = res.data[0].estimateCommission\r\n          commisionData.yesdayXiaoshou = res.data[0].saleroom\r\n          if (uni.getStorageSync('exchangerate')) {\r\n            commisionData.aaaaayesday = (res.data[0].estimateCommission * uni.getStorageSync('exchangerate')).toFixed(\r\n              2)\r\n            commisionData.aaaaayesdayXiaoshou = (res.data[0].saleroom * uni.getStorageSync('exchangerate')).toFixed(2)\r\n          }\r\n        }\r\n        res = await this.$wxapi.siteStatisticsSaleroom({\r\n          dateBegin: nowDate.format('yyyyMM'),\r\n          dateEnd: nowDate.format('yyyyMM'),\r\n          uid: uid\r\n        })\r\n        if (res.code === 0) {\r\n          commisionData.thisMonth = res.data[0].estimateCommission\r\n          commisionData.thisMonthXiaoshou = res.data[0].saleroom\r\n          if (uni.getStorageSync('exchangerate')) {\r\n            commisionData.aaaaathisMonth = (res.data[0].estimateCommission * uni.getStorageSync('exchangerate'))\r\n              .toFixed(2)\r\n            commisionData.aaaaathisMonthXiaoshou = (res.data[0].saleroom * uni.getStorageSync('exchangerate'))\r\n              .toFixed(2)\r\n          }\r\n        }\r\n        res = await this.$wxapi.siteStatisticsSaleroom({\r\n          dateBegin: lastMonth,\r\n          dateEnd: lastMonth,\r\n          uid: uid\r\n        })\r\n        if (res.code === 0) {\r\n          commisionData.lastMonth = res.data[0].estimateCommission\r\n          commisionData.lastMonthXiaoshou = res.data[0].saleroom\r\n          if (uni.getStorageSync('exchangerate')) {\r\n            commisionData.aaaaalastMonth = (res.data[0].estimateCommission * uni.getStorageSync('exchangerate'))\r\n              .toFixed(2)\r\n            commisionData.aaaaalastMonthXiaoshou = (res.data[0].saleroom * uni.getStorageSync('exchangerate'))\r\n              .toFixed(2)\r\n          }\r\n        }\r\n        this.commisionData = commisionData\r\n      },\r\n      getUserApiInfo: function() {\r\n        var that = this;\r\n        this.$wxapi.userDetail(this.token).then(function(res) {\r\n          if (res.code == 0) {\r\n            that.apiUserInfoMap = res.data\r\n            that.commision();\r\n            if (res.data.base.isSeller) {\r\n              // 判断是否是市区合伙人\r\n              that.fxCities()\r\n            }\r\n          }\r\n        })\r\n      },\r\n      doneShow: function() {\r\n        const _this = this\r\n        const token = this.token\r\n        if (!token) {\r\n          return\r\n        }\r\n        this.$wxapi.userAmount(token).then(function(res) {\r\n          if (res.code == 700) {\r\n            uni.showToast({\r\n              title: '当前账户存在异常',\r\n              icon: 'none'\r\n            })\r\n            return\r\n          }\r\n          if (res.code == 2000) {\r\n            return\r\n          }\r\n          if (res.code == 0) {\r\n            _this.balance = res.data.balance.toFixed(2)\r\n            _this.freeze = res.data.freeze.toFixed(2)\r\n            _this.fxCommisionPaying = res.data.fxCommisionPaying.toFixed(2)\r\n            _this.totleConsumed = res.data.totleConsumed.toFixed(2)\r\n            _this.score = res.data.score\r\n          }\r\n        })\r\n      },\r\n      copyContent(e) {\r\n        uni.setClipboardData({\r\n          data: e\r\n        })\r\n      },\r\n      async doneShow2() {\r\n        const _this = this\r\n        const userDetail = await this.$wxapi.userDetail(this.token)\r\n        this.$wxapi.fxApplyProgress(this.token).then(res => {\r\n          let applyStatus = userDetail.data.base.isSeller ? 2 : -1\r\n          if (res.code == 2000) {\r\n            return\r\n          }\r\n          if (res.code === 700) {\r\n            _this.applyStatus = applyStatus\r\n          }\r\n          if (res.code === 0) {\r\n            if (userDetail.data.base.isSeller) {\r\n              applyStatus = 2\r\n            } else {\r\n              applyStatus = res.data.status\r\n            }\r\n            _this.applyStatus = applyStatus\r\n            _this.applyInfo = res.data\r\n          }\r\n          if (applyStatus == 2) {\r\n            _this.fetchQrcode()\r\n          }\r\n        })\r\n      },\r\n      fetchQrcode() {\r\n        const _this = this\r\n        uni.showLoading({\r\n          title: '加载中',\r\n          mask: true\r\n        })\r\n        this.$wxapi.wxaQrcode({\r\n          scene: 'inviter_id=' + uni.getStorageSync('uid'),\r\n          page: 'pages/index/index',\r\n          is_hyaline: true,\r\n          autoColor: false,\r\n          expireHours: 1\r\n        }).then(res => {\r\n          uni.hideLoading()\r\n          if (res.code == 41030) {\r\n            uni.showToast({\r\n              title: '上线以后才可以获取二维码',\r\n              icon: 'none'\r\n            })\r\n            return\r\n          }\r\n          if (res.code == 0) {\r\n            this.qrcode = res.data\r\n            //_this.showCanvas(res.data)\r\n          }\r\n        })\r\n      },\r\n\r\n      saveToMobile() {\r\n        var that = this\r\n        uni.saveImageToPhotosAlbum({\r\n          filePath: this.qrcode,\r\n          success: (res) => {\r\n            uni.showModal({\r\n              content: that.i18n['packagefx.index.erweima'],\r\n              showCancel: false,\r\n              confirmText: that.i18n['packagefx.index.okle'],\r\n              confirmColor: '#333'\r\n            })\r\n          },\r\n          fail: (res) => {\r\n            uni.showToast({\r\n              title: res.errMsg,\r\n              icon: 'none'\r\n            })\r\n          }\r\n        })\r\n      },\r\n      // 读取市区合伙人\r\n      async fxCities() {\r\n        const res = await this.$wxapi.fxCities(this.token)\r\n        if (res.code == 0) {\r\n          this.fxCities = res.data\r\n        }\r\n      },\r\n      // 读取广告位\r\n      async adPosition() {\r\n        const res = await this.$wxapi.adPosition('fx_index')\r\n        if (res.code == 0) {\r\n          this.fxIndexAdPos = res.data\r\n        }\r\n      },\r\n      goUrl(e) {\r\n        const url = e.currentTarget.dataset.url\r\n        if (url) {\r\n          uni.navigateTo({\r\n            url: url,\r\n          })\r\n        }\r\n      },\r\n      onShareAppMessage() {\r\n        return {\r\n          title: '\"' + uni.getStorageSync('mallName') + '\" ' + uni.getStorageSync('share_profile'),\r\n          path: '/pages/index/index?inviter_id=' + uni.getStorageSync('uid'),\r\n          imageUrl: uni.getStorageSync('share_pic'),\r\n          success: function(res) {\r\n            // 转发成功\r\n          },\r\n          fail: function(res) {\r\n            // 转发失败\r\n          }\r\n        }\r\n      },\r\n      goApply() {\r\n        uni.redirectTo({\r\n          url: '/packageFx/apply/index',\r\n        })\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style>\r\n  page {\r\n    background: #FDF3E7\r\n  }\r\n\r\n  .asset {\r\n    display: flex;\r\n    padding: 20rpx 32rpx;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .asset .item {\r\n    display: flex;\r\n    flex-direction: column;\r\n    text-align: center;\r\n    font-size: 26rpx;\r\n    color: #3F240A;\r\n  }\r\n\r\n  .btn-view {\r\n    height: 88rpx;\r\n    /* background-color: #e85654; */\r\n    padding-right: 40rpx;\r\n    padding-bottom: 30rpx;\r\n    width: 100vw;\r\n    display: flex;\r\n    flex-direction: row-reverse;\r\n  }\r\n\r\n  .btn-view .btn {\r\n    border-color: #fff !important;\r\n    color: #fff !important;\r\n    margin-right: 20rpx;\r\n  }\r\n\r\n  .btn-hover {\r\n    border-color: #fff;\r\n    color: #fff;\r\n  }\r\n\r\n  .no-data {\r\n    margin-top: 100rpx;\r\n    text-align: center;\r\n    font-size: 13px;\r\n    color: #ccc;\r\n  }\r\n\r\n  .cashlogs {\r\n    display: flex;\r\n    font-size: 12px;\r\n    margin-top: 20rpx;\r\n    padding-bottom: 20rpx;\r\n    border-bottom: 1px solid #eee;\r\n    line-height: 20px;\r\n  }\r\n\r\n  .cashlogs .profile {\r\n    width: 600rpx;\r\n    padding-left: 30rpx;\r\n  }\r\n\r\n  .cashlogs .amount {\r\n    width: 150rpx;\r\n  }\r\n\r\n  .tabTop {\r\n    width: 710rpx;\r\n    /* height: 500rpx; */\r\n    background: linear-gradient(270deg, #F6C173 0%, #FFECC0 100%);\r\n    border-radius: 20rpx;\r\n    margin-left: 20rpx;\r\n    /* margin-top: -380rpx; */\r\n    position: relative;\r\n    padding-bottom: 20rpx;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .tabTop2 {\r\n    margin-top: 10px;\r\n    width: 710rpx;\r\n    /* height: 500rpx; */\r\n    background: linear-gradient(270deg, #F6C173 0%, #FFECC0 100%);\r\n    border-radius: 20rpx;\r\n    margin-left: 20rpx;\r\n    /* margin-top: -380rpx; */\r\n    position: relative;\r\n    padding-bottom: 20rpx;\r\n    margin-bottom: 10px;\r\n  }\r\n\r\n  .ava {\r\n    width: 100rpx;\r\n    height: 100rpx;\r\n  }\r\n\r\n  .header-box {\r\n    padding: 32rpx 32rpx 0 32rpx;\r\n    display: flex;\r\n    align-items: center;\r\n  }\r\n\r\n  .header-box2 {\r\n    padding: 16rpx 32rpx;\r\n    display: flex;\r\n    align-items: center;\r\n    margin-left: 10rpx;\r\n    font-size: 26rpx;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    font-weight: 400;\r\n    color: #3E240D;\r\n  }\r\n\r\n  .header-box .avatar {\r\n    width: 128rpx;\r\n    height: 128rpx;\r\n    border-radius: 50%;\r\n    border: 2px solid white;\r\n    flex-shrink: 0;\r\n  }\r\n\r\n  .header-box .avatar2 {\r\n    width: 130rpx;\r\n    height: 130rpx;\r\n    position: absolute;\r\n  }\r\n\r\n  .header-box .btn {\r\n    margin-left: 32rpx;\r\n  }\r\n\r\n  .header-box .r {\r\n    margin-left: 32rpx;\r\n    color: #333;\r\n    font-size: 28rpx;\r\n  }\r\n\r\n  .line {\r\n    width: 558rpx;\r\n    height: 2px;\r\n    background: linear-gradient(90deg, #FFFFFF 0%, rgba(255, 255, 255, 0) 100%);\r\n    border-radius: 1px;\r\n    margin-left: 20px;\r\n  }\r\n\r\n  .txBtn {\r\n    width: 160rpx;\r\n    height: 56rpx;\r\n\r\n    border-radius: 28px;\r\n    border: 2rpx solid #3F240A;\r\n    line-height: 56rpx;\r\n    position: absolute;\r\n    right: 16px;\r\n    text-align: center;\r\n    font-size: 14px;\r\n    padding-top: 2px;\r\n  }\r\n\r\n  .tzBtn {\r\n    width: 160rpx;\r\n    height: 56rpx;\r\n    margin: auto;\r\n    border-radius: 28px;\r\n    border: 2rpx solid #3F240A;\r\n    line-height: 56rpx;\r\n    right: 16px;\r\n    text-align: center;\r\n    font-size: 14px;\r\n    padding-top: 2px;\r\n  }\r\n\r\n  .Count {\r\n    height: 38px;\r\n    font-size: 40rpx;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #3F240A;\r\n    line-height: 38px;\r\n\r\n  }\r\n\r\n  .tuan {\r\n    margin-left: 20rpx;\r\n    width: 630rpx;\r\n    background: #FFFFFF;\r\n    border-radius: 8rpx;\r\n    padding: 40rpx;\r\n  }\r\n\r\n  .tuan2 {\r\n    margin-left: 20rpx;\r\n    width: 630rpx;\r\n    background: #FFFFFF;\r\n    border-radius: 8rpx;\r\n    padding: 26rpx 40rpx;\r\n  }\r\n\r\n  .line2 {\r\n    width: 630rpx;\r\n    height: 1px;\r\n    background: #E6E6E6;\r\n    border-radius: 1px;\r\n    margin-top: 20rpx;\r\n  }\r\n\r\n  .tuanItem {\r\n    width: 315rpx;\r\n    text-align: center;\r\n    padding-top: 42rpx\r\n  }\r\n\r\n  .tI1 {\r\n    font-size: 38rpx;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #333333;\r\n    margin: 0 0 8rpx 0;\r\n  }\r\n\r\n  .tI2 {\r\n    font-size: 28rpx;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    font-weight: 400;\r\n    color: #666666;\r\n  }\r\n\r\n  .yqCode {\r\n    background: #F2F5FF;\r\n    height: 37rpx;\r\n    font-size: 26rpx;\r\n    font-family: PingFangSC-Medium, PingFang SC;\r\n    font-weight: 500;\r\n    color: #182442;\r\n    line-height: 37rpx;\r\n    padding: 2rpx 20rpx;\r\n    margin-left: 20rpx;\r\n  }\r\n\r\n  .cybtn {\r\n    width: 140rpx;\r\n    height: 50rpx;\r\n    background: #FFD43E;\r\n    border-radius: 28px;\r\n    line-height: 50rpx;\r\n    text-align: center;\r\n    font-size: 14px;\r\n  }\r\n\r\n  .yjP {\r\n    height: 37rpx;\r\n    font-size: 26rpx;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    font-weight: 400;\r\n    color: #FF444A;\r\n    line-height: 37rpx;\r\n  }\r\n\r\n  .yjT {\r\n    height: 30rpx;\r\n    font-size: 22rpx;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    font-weight: 400;\r\n    color: #666666;\r\n    line-height: 30rpx;\r\n  }\r\n\r\n  .titleXS {\r\n    /* width: 140px; */\r\n    height: 40rpx;\r\n    font-size: 28rpx;\r\n    /* font-family: PingFangSC-Medium, PingFang SC; */\r\n    /* font-weight: 400; */\r\n    color: #3F240A;\r\n    line-height: 40rpx;\r\n    margin-left: 22px;\r\n    margin-top: 10px\r\n  }\r\n\r\n  .textsjtz {\r\n    height: 40rpx;\r\n    font-size: 28rpx;\r\n    font-family: PingFangSC-Regular, PingFang SC;\r\n    font-weight: 400;\r\n    color: #999999;\r\n    line-height: 40rpx;\r\n    margin-top: 10px;\r\n  }\r\n\r\n  .next {\r\n    width: 40rpx;\r\n    height: 40rpx;\r\n    position: absolute;\r\n    right: 20px;\r\n  }\r\n\r\n  .goods-container {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    flex-wrap: wrap;\r\n    box-sizing: content-box;\r\n    padding: 22rpx;\r\n    margin-bottom: 100rpx;\r\n  }\r\n\r\n  .goods-box {\r\n    width: 339rpx;\r\n    height: 472rpx;\r\n    background-color: #fff;\r\n    overflow: hidden;\r\n    margin-bottom: 24rpx;\r\n    border-radius: 5px;\r\n  }\r\n\r\n  .goods-box .img-box {\r\n    width: 339rpx;\r\n    height: 339rpx;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .goods-box .img-box image {\r\n    width: 339rpx;\r\n    height: 339rpx;\r\n  }\r\n\r\n  .goods-box .goods-title {\r\n    width: 280rpx;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    font-size: 26rpx;\r\n    padding: 20rpx 0 0rpx 0;\r\n    color: #000;\r\n    margin-left: 24rpx;\r\n  }\r\n\r\n  .goods-box .goods-price {\r\n    /* width: 280rpx; */\r\n    overflow: hidden;\r\n    font-size: 36rpx;\r\n    padding: 8rpx 0;\r\n    color: #e64340;\r\n    margin-left: 24rpx;\r\n  }\r\n\r\n  .cell-class {\r\n    padding: 0 24rpx;\r\n  }\r\n\r\n  .canvas-box {\r\n    width: 100vw;\r\n    display: flex;\r\n    justify-content: center;\r\n  }\r\n\r\n  .adpos {\r\n    padding: 0 24rpx;\r\n    width: 100vw;\r\n    box-sizing: border-box;\r\n    border-radius: 16rpx;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692277263\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}