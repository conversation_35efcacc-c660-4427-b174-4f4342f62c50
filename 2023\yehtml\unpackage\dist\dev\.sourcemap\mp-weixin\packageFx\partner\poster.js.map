{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/partner/poster.vue?5a82", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/partner/poster.vue?c760", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/partner/poster.vue?e4b8", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/partner/poster.vue?ae99", "uni-app:///packageFx/partner/poster.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/partner/poster.vue?06d0", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/partner/poster.vue?8143", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/partner/poster.vue?540b", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/partner/poster.vue?ddb1"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "l<PERSON><PERSON><PERSON>", "uniPopup", "data", "userInfo", "qrcode", "minPriceOriginal", "tags", "goodsId", "name", "unit", "posterShow", "path", "posterObj", "check_idx", "onShow", "onLoad", "methods", "_userDetail", "res", "scene", "page", "is_hyaline", "autoColor", "expireHours", "check_path", "qrcodeRes", "width", "height", "background", "borderRadius", "views", "type", "src", "css", "left", "top", "text", "fontSize", "fontWeight", "color", "lineHeight", "fontStyle", "previewImg", "uni", "current", "urls", "closePopup", "toSave", "title", "painter", "goBack", "delta"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACa;AACyB;;;AAG3F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,qPAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtCA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC2BrrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAGA;EACAC;IACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;MACA;MACA;MACA;MACA;MACA;IACA;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAf;gBACA;gBAAA;gBAAA,OAEA;kBACAgB;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAPAC;gBASA;kBACA;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;sBACAC;sBACAC;sBACAC;wBACAC;wBACAC;wBACAT;wBACAC;sBACA;oBACA,GACA;sBACAI;sBACAC;sBACAC;wBACAC;wBACAC;wBACAN;wBACAH;wBACAC;sBACA;oBACA,GACA;sBACAI;sBACAK;sBACAH;wBACAI;wBACAC;wBACAC;wBACAC;wBACAN;wBACAC;sBACA;oBACA,GACA;sBACAJ;sBACAK;sBACAH;wBACAI;wBACAE;wBACAC;wBACAN;wBACAC;sBACA;oBACA,GACA;sBACAJ;sBACAC;sBACAC;wBACAC;wBACAC;wBACAT;wBACAC;wBACAE;sBACA;oBACA,GACA;sBACAE;sBACAC;sBACAC;wBACAC;wBACAC;wBACAT;wBACAC;sBACA;oBACA;sBACAI;sBACAK;sBACAH;wBACAI;wBACAE;wBACAC;wBACAN;wBACAC;wBACAT;sBACA;oBACA;sBACAK;sBACAK;sBACAH;wBACAI;wBACAE;wBACAC;wBACAF;wBACAJ;wBACAC;wBACAT;sBACA;oBACA;sBACAK;sBACAK;sBACAH;wBACAI;wBACAE;wBACAC;wBACAN;wBACAC;wBACAT;sBACA;oBACA;sBACAK;sBACAK;sBACAH;wBACAI;wBACAE;wBACAC;wBACAF;wBACAG;wBACAP;wBACAC;wBACAT;sBACA;oBACA;sBACAK;sBACAK;sBACAH;wBACAI;wBACAE;wBACAC;wBACAC;wBACAP;wBACAC;wBACAT;sBACA;oBACA;kBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAgB;MAIAC;QACAC;QACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MACAJ;QACAK;MACA;MACA;MACAC;QACA;QACA;QACA;QACAN;MACA;IACA;IACAO;MACAP;QACAQ;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3PA;AAAA;AAAA;AAAA;AAA68B,CAAgB,g8BAAG,EAAC,C;;;;;;;;;;;ACAj+B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAwxC,CAAgB,4uCAAG,EAAC,C;;;;;;;;;;;ACA5yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packageFx/partner/poster.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packageFx/partner/poster.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./poster.vue?vue&type=template&id=4c61288e&scoped=true&\"\nvar renderjs\nimport script from \"./poster.vue?vue&type=script&lang=js&\"\nexport * from \"./poster.vue?vue&type=script&lang=js&\"\nimport style0 from \"./poster.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./poster.vue?vue&type=style&index=1&id=4c61288e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4c61288e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packageFx/partner/poster.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./poster.vue?vue&type=template&id=4c61288e&scoped=true&\"", "var components\ntry {\n  components = {\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uniPopup: function () {\n      return import(\n        /* webpackChunkName: \"components/uni-popup/uni-popup\" */ \"@/components/uni-popup/uni-popup.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./poster.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./poster.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"page\">\r\n    <view class=\"poster\" v-if=\"!posterShow\">\r\n      <lPainter :board=\"posterObj\" ref=\"painter\"></lPainter>\r\n      <view class=\"footer-btn\">\r\n        <view class=\"\">\r\n          <u-button @click=\"goBack\" :plain=\"true\" type=\"info\" text=\"返回\"></u-button>\r\n        </view>\r\n        <view>\r\n          <u-button @click=\"toSave\" type=\"warning\" text=\"保存\" color=\"#2F4C42\"></u-button>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <!-- 生成的图片 -->\r\n    <uni-popup type=\"center\" ref=\"posterImg\" :maskClick=\"false\">\r\n      <view class=\"poster-img\">\r\n        <text @click=\"closePopup\"></text>\r\n        <image :src=\"path\" mode=\"heightFix\" @click=\"previewImg\"></image>\r\n        <view style=\"text-align: left;margin-left: 1em;\">\r\n          点击长按图片保存到手机\r\n        </view>\r\n      </view>\r\n    </uni-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import empty from 'empty-value'\r\n  import uniPopup from \"../../components/uni-popup/uni-popup.vue\";\r\n  import lPainter from '../../components/lime-painter/index.vue'\r\n  export default {\r\n    components: {\r\n      lPainter,\r\n      uniPopup\r\n    },\r\n    data() {\r\n      return {\r\n        userInfo: [],\r\n        qrcode: '',\r\n        minPriceOriginal: '',\r\n        tags: '',\r\n        goodsId: '',\r\n        name: '',\r\n        unit: '',\r\n        posterShow: false, //显示海报\r\n        path: '', //生成的图片地址\r\n        posterObj: {}, //画板数据\r\n        check_idx: 0, //底部选中的下标\r\n      }\r\n    },\r\n    onShow() {\r\n      this._userDetail()\r\n    },\r\n    onLoad(query) {\r\n      if (!empty(query.id)) {\r\n        this.goodsId = decodeURIComponent(query.id)\r\n        this.pic = decodeURIComponent(query.pic)\r\n        this.minPriceOriginal = decodeURIComponent(query.minPriceOriginal) + '/' + decodeURIComponent(query.unit)\r\n        this.tags = decodeURIComponent(query.tags)\r\n        this.name = decodeURIComponent(query.name)\r\n      } else {\r\n        this.goodsId = 1780169\r\n        this.pic = 'https://dcdn.it120.cc/2024/05/17/bee3152e-d59c-4192-a54f-426ca2cf421b.jpeg'\r\n        this.minPriceOriginal = '125/只'\r\n        this.tags = '地标产品、清水养殖、五谷喂养'\r\n        this.name = '御江青头鸭'\r\n      }\r\n    },\r\n    methods: {\r\n      async _userDetail() {\r\n        // https://www.yuque.com/apifm/nu0f75/zgf8pu\r\n        const res = await this.$wxapi.userDetail(this.token)\r\n        if (res.code == 0) {\r\n          let userInfo = res.data\r\n          this.userInfo = userInfo\r\n\r\n          const qrcodeRes = await this.$wxapi.wxaQrcode({\r\n            scene: this.goodsId + ',' + userInfo.base.id,\r\n            page: 'pages/goods/detail',\r\n            is_hyaline: true,\r\n            autoColor: true,\r\n            expireHours: 1,\r\n            check_path: __wxConfig.envVersion == 'release' ? true : false\r\n          })\r\n\r\n          if (qrcodeRes.code === 0) {\r\n            this.posterObj = {\r\n              width: '670rpx',\r\n              height: '1165rpx',\r\n              background: '#fff',\r\n              borderRadius: '16rpx',\r\n              views: [{\r\n                  type: 'image',\r\n                  src: 'https://ye.niutouren.vip/static/images/poster/bg.png',\r\n                  css: {\r\n                    left: '0',\r\n                    top: '0',\r\n                    width: '100%',\r\n                    height: '100%'\r\n                  }\r\n                },\r\n                {\r\n                  type: 'image',\r\n                  src: userInfo.base.avatarUrl,\r\n                  css: {\r\n                    left: '60rpx',\r\n                    top: '120rpx',\r\n                    borderRadius: '50%',\r\n                    width: '80rpx',\r\n                    height: '80rpx'\r\n                  }\r\n                },\r\n                {\r\n                  type: 'text',\r\n                  text: userInfo.base.nick,\r\n                  css: {\r\n                    fontSize: '28rpx',\r\n                    fontWeight: 'bold',\r\n                    color: '#1A2033',\r\n                    lineHeight: '28rpx',\r\n                    left: '170rpx',\r\n                    top: '130rpx'\r\n                  }\r\n                },\r\n                {\r\n                  type: 'text',\r\n                  text: '邀请您品味品质生活',\r\n                  css: {\r\n                    fontSize: '24rpx',\r\n                    color: '#1A2033',\r\n                    lineHeight: '24rpx',\r\n                    left: '170rpx',\r\n                    top: '170rpx'\r\n                  }\r\n                },\r\n                {\r\n                  type: 'image',\r\n                  src: this.pic,\r\n                  css: {\r\n                    left: '60rpx',\r\n                    top: '230rpx',\r\n                    width: '546rpx',\r\n                    height: '364rpx',\r\n                    borderRadius: '16rpx'\r\n                  }\r\n                },\r\n                {\r\n                  type: 'image',\r\n                  src: qrcodeRes.data,\r\n                  css: {\r\n                    left: '400rpx',\r\n                    top: '631rpx',\r\n                    width: '200rpx',\r\n                    height: '200rpx',\r\n                  },\r\n                }, {\r\n                  type: 'text',\r\n                  text: '长按/扫描打开小程序',\r\n                  css: {\r\n                    fontSize: '24rpx',\r\n                    color: '#1A2033',\r\n                    lineHeight: '45rpx',\r\n                    left: '380rpx',\r\n                    top: '850rpx',\r\n                    width: '606rpx'\r\n                  }\r\n                }, {\r\n                  type: 'text',\r\n                  text: this.name,\r\n                  css: {\r\n                    fontSize: '48rpx',\r\n                    color: '#1A2033',\r\n                    lineHeight: '45rpx',\r\n                    fontWeight: 'bold',\r\n                    left: '60rpx',\r\n                    top: '690rpx',\r\n                    width: '606rpx'\r\n                  }\r\n                }, {\r\n                  type: 'text',\r\n                  text: this.tags,\r\n                  css: {\r\n                    fontSize: '24rpx',\r\n                    color: '#1A2033',\r\n                    lineHeight: '45rpx',\r\n                    left: '60rpx',\r\n                    top: '750rpx',\r\n                    width: '606rpx'\r\n                  }\r\n                }, {\r\n                  type: 'text',\r\n                  text: this.minPriceOriginal,\r\n                  css: {\r\n                    fontSize: '60rpx',\r\n                    color: '#e64340',\r\n                    lineHeight: '45rpx',\r\n                    fontWeight: 'bold',\r\n                    fontStyle: 'italic',\r\n                    left: '80rpx',\r\n                    top: '820rpx',\r\n                    width: '606rpx'\r\n                  }\r\n                }, {\r\n                  type: 'text',\r\n                  text: '¥',\r\n                  css: {\r\n                    fontSize: '24rpx',\r\n                    color: '#e64340',\r\n                    lineHeight: '45rpx',\r\n                    fontStyle: 'italic',\r\n                    left: '60rpx',\r\n                    top: '830rpx',\r\n                    width: '100rpx'\r\n                  }\r\n                },\r\n              ]\r\n            }\r\n          }\r\n        }\r\n      },\r\n      previewImg() {\r\n        // #ifdef H5\r\n        return;\r\n        // #endif\r\n        uni.previewImage({\r\n          current: this.path,\r\n          urls: [this.path]\r\n        });\r\n      },\r\n      closePopup() {\r\n        this.$refs.posterImg.close();\r\n        this.posterShow = false;\r\n      },\r\n      toSave() {\r\n        uni.showLoading({\r\n          title: '海报生成中',\r\n        })\r\n        const painter = this.$refs.painter;\r\n        painter.canvasToTempFilePath().then(res => {\r\n          this.path = res.tempFilePath;\r\n          this.$refs.posterImg.open();\r\n          this.posterShow = true;\r\n          uni.hideLoading()\r\n        });\r\n      },\r\n      goBack() {\r\n        uni.navigateBack({\r\n          delta: 1\r\n        });\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style>\r\n  page {\r\n    background: #999;\r\n  }\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n  .page {\r\n    /deep/ .uni-transition {\r\n      background-color: rgba(0, 0, 0, 0.6)\r\n    }\r\n\r\n    .poster-img {\r\n      width: 670rpx;\r\n      height: 928rpx;\r\n      position: relative;\r\n\r\n      text {\r\n        background: url('https://s.yun-live.com/images/20210201/5c4ef9d86bc5eec90f2f915683d9db08.png') no-repeat;\r\n        background-size: 100% 100%;\r\n        display: inline-block;\r\n        width: 50rpx;\r\n        height: 50rpx;\r\n        position: absolute;\r\n        top: -60rpx;\r\n        right: 0;\r\n      }\r\n\r\n      image {\r\n        width: 90%;\r\n        height: 90%;\r\n      }\r\n\r\n      view {\r\n        font-size: 32rpx;\r\n        font-family: PingFang-SC-Bold, PingFang-SC;\r\n        font-weight: bold;\r\n        color: #FFFFFF;\r\n        line-height: 32rpx;\r\n        text-align: center;\r\n        margin-top: 28rpx;\r\n      }\r\n    }\r\n\r\n    .poster {\r\n      padding: 24rpx 40rpx;\r\n\r\n      .footer-btn {\r\n        margin-top: 24rpx;\r\n        display: flex;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n\r\n        view {\r\n          width: 320rpx;\r\n        }\r\n      }\r\n    }\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./poster.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./poster.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692285271\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./poster.vue?vue&type=style&index=1&id=4c61288e&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./poster.vue?vue&type=style&index=1&id=4c61288e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692293841\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}