<view class="list3"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view slot="slot{{index}}"><view class="item-wrapper"><u-row vue-id="{{'51a2f4c7-1-'+index}}" justify="space-between" gutter="10" bind:__l="__l" vue-slots="{{['default']}}"><u-col vue-id="{{('51a2f4c7-2-'+index)+','+('51a2f4c7-1-'+index)}}" span="4" data-event-opts="{{[['^click',[['imageClick',['$0'],[[['list','',index]]]]]]]}}" bind:click="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="list-image-wrapper _div" style="{{'background-image:'+('url('+item.pic+')')+';'}}"></view></u-col><u-col vue-id="{{('51a2f4c7-3-'+index)+','+('51a2f4c7-1-'+index)}}" span="8" bind:__l="__l" vue-slots="{{['default']}}"><list-goods-item3 vue-id="{{('51a2f4c7-4-'+index)+','+('51a2f4c7-3-'+index)}}" item="{{item}}" bind:__l="__l"></list-goods-item3></u-col></u-row></view></view></block></view>