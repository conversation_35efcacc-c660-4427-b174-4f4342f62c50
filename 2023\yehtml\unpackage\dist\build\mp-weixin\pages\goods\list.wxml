<view class="data-v-0a0fad54"><u-sticky vue-id="36971996-1" bgColor="#FFFFFF" class="data-v-0a0fad54" bind:__l="__l" vue-slots="{{['default']}}"><view class="search data-v-0a0fad54"><u-search vue-id="{{('36971996-2')+','+('36971996-1')}}" placeholder="输入关键词搜索" showAction="{{false}}" value="{{kw}}" data-event-opts="{{[['^search',[['search']]],['^input',[['__set_model',['','kw','$event',[]]]]]]}}" bind:search="__e" bind:input="__e" class="data-v-0a0fad54" bind:__l="__l"></u-search><view data-event-opts="{{[['tap',[['searchscan',['$event']]]]]}}" class="scan data-v-0a0fad54" bindtap="__e"><u-icon vue-id="{{('36971996-3')+','+('36971996-1')}}" name="scan" size="48rpx" class="data-v-0a0fad54" bind:__l="__l"></u-icon></view></view><u-subsection vue-id="{{('36971996-4')+','+('36971996-1')}}" activeColor="#e64340" list="{{tabs}}" current="{{activetab}}" data-event-opts="{{[['^change',[['paixu']]]]}}" bind:change="__e" class="data-v-0a0fad54" bind:__l="__l"></u-subsection></u-sticky><block wx:if="{{$root.g0}}"><page-box-empty vue-id="36971996-5" title="暂无商品" sub-title="当前类目下无法帮你找到合适的商品" show-btn="{{true}}" class="data-v-0a0fad54" bind:__l="__l"></page-box-empty></block><view class="goodslist data-v-0a0fad54"><view class="goods-container data-v-0a0fad54"><list1 vue-id="36971996-6" list="{{goods}}" type="goods" class="data-v-0a0fad54" bind:__l="__l"></list1></view></view></view>