{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/report/city.vue?4ff6", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/report/city.vue?68d4", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/report/city.vue?4dd7", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/report/city.vue?eb8e", "uni-app:///packageFx/report/city.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/report/city.vue?0655", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/report/city.vue?9023"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "page", "methods", "onLoad", "onShow", "fxCityReport", "token", "provinceId", "cityId", "res", "uni", "title", "icon", "onReachBottom", "onPullDownRefresh"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiH;AACjH;AACwD;AACL;AACa;;;AAGhE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,+EAAM;AACR,EAAE,wFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/CA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,+qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCQnrB;EACAC;IACA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;MACA;MACA;IACA;IACAC,2BAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;kBACAC;kBACAC;kBACAP;gBACA;cAAA;gBALAQ;gBAMA;kBACA;oBACA;kBACA;oBACAC;sBACAC;sBACAC;oBACA;kBACA;gBACA;gBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;IACA;IACAC;MACA;MACA;MACAJ;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1DA;AAAA;AAAA;AAAA;AAA28B,CAAgB,87BAAG,EAAC,C;;;;;;;;;;;ACA/9B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packageFx/report/city.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packageFx/report/city.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./city.vue?vue&type=template&id=56e78971&\"\nvar renderjs\nimport script from \"./city.vue?vue&type=script&lang=js&\"\nexport * from \"./city.vue?vue&type=script&lang=js&\"\nimport style0 from \"./city.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packageFx/report/city.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city.vue?vue&type=template&id=56e78971&\"", "var components\ntry {\n  components = {\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-empty/u-empty\" */ \"@/uni_modules/uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.members || _vm.members.length == 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<u-empty v-if=\"!members || members.length == 0\" mode=\"car\" text=\"暂无记录\" icon=\"http://cdn.uviewui.com/uview/empty/car.png\"></u-empty>\r\n\t\t<u-cell v-for=\"(item,index) in members\" :title=\"'月份' + item.month\" :value=\"'¥' + item.curSaleroom\" :label=\"'目标¥' + item.standardSaleroom\"></u-cell>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tpage: 1 // 读取第几页\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tonLoad(e) {\r\n\t\t\t\tthis.provinceId = e.provinceId\r\n\t\t\t\tthis.cityId = e.cityId\r\n\t\t\t\tthis.fxCityReport()\r\n\t\t\t},\r\n\t\t\tonShow: function () {\r\n\r\n\t\t\t},\r\n\t\t\tasync fxCityReport() {\r\n\t\t\t\tconst res = await this.$wxapi.fxCityReport({\r\n\t\t\t\t\ttoken: this.token,\r\n\t\t\t\t\tprovinceId: this.provinceId,\r\n\t\t\t\t\tcityId: this.cityId,\r\n\t\t\t\t\tpage: this.page\r\n\t\t\t\t})\r\n\t\t\t\tif (res.code == 700) {\r\n\t\t\t\t\tif (this.page == 1) {\r\n\t\t\t\t\t\tthis.members = []\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '没有更多了',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tif (this.page == 1) {\r\n\t\t\t\t\t\tthis.members = res.data.result\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.members = this.members.concat(res.data.result)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tonReachBottom: function() {\r\n\t\t\t\tthis.page += 1\r\n\t\t\t\tthis.fxCityReport()\r\n\t\t\t},\r\n\t\t\tonPullDownRefresh: function() {\r\n\t\t\t\tthis.page = 1\r\n\t\t\t\tthis.fxCityReport()\r\n\t\t\t\tuni.stopPullDownRefresh()\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style>\r\n\t.list {\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\twidth: 100vw;\r\n\t\tpadding: 8rpx 32rpx;\r\n\t\tbox-sizing: border-box;\r\n\t}\r\n\t.list .l {\r\n\t\twidth: 88rpx;\r\n\t\theight: 88rpx;\r\n\t}\r\n\t.list .r {\r\n\t\tflex: 1;\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688705380\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}