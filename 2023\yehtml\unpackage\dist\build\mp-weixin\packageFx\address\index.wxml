<view class="address-list data-v-13fc3230"><block wx:if="{{!siteList}}"><page-box-empty vue-id="0a792384-1" title="暂无收货记录" sub-title="赶快添加一个收货地址吧～" show-btn="{{true}}" btn-name="立即添加" url="/packageFx/address/addSite" class="data-v-13fc3230" bind:__l="__l"></page-box-empty></block><u-swipe-action vue-id="0a792384-2" class="data-v-13fc3230" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{siteList}}" wx:for-item="res" wx:for-index="index" wx:key="id"><u-swipe-action-item vue-id="{{('0a792384-3-'+index)+','+('0a792384-2')}}" options="{{options}}" show="{{res.show}}" index="{{index}}" name="{{index}}" data-event-opts="{{[['^click',[['click']]]]}}" bind:click="__e" class="data-v-13fc3230" bind:__l="__l" vue-slots="{{['default']}}"><view class="item data-v-13fc3230"><view class="top data-v-13fc3230"><view class="name data-v-13fc3230">{{res.linkMan}}</view><view class="phone data-v-13fc3230">{{res.mobile}}</view><block wx:if="{{res.isDefault}}"><view class="tag data-v-13fc3230"><text class="red data-v-13fc3230">默认</text></view></block></view><view class="bottom data-v-13fc3230">{{''+res.provinceStr+" "+res.cityStr+" "+res.areaStr+" "+res.xiaoqu+" "+res.address+''}}<block wx:if="{{select===1}}"><view style="flex-direction:row !important;" class="data-v-13fc3230"><u-button vue-id="{{('0a792384-4-'+index)+','+('0a792384-3-'+index)}}" type="warning" size="small" text="选择" data-event-opts="{{[['^click',[['sel',[index]]]]]}}" bind:click="__e" class="data-v-13fc3230" bind:__l="__l"></u-button></view></block></view></view></u-swipe-action-item></block></u-swipe-action><view data-event-opts="{{[['tap',[['toAddSite',[-1]]]]]}}" class="addSite data-v-13fc3230" bindtap="__e"><view class="add data-v-13fc3230"><u-icon class="icon data-v-13fc3230" vue-id="0a792384-5" name="plus" color="#ffffff" size="32rpx" bind:__l="__l"></u-icon>新建收货地址</view></view><view class="tip data-v-13fc3230">* 每条地址向左滑动可以编辑/删除地址</view></view>