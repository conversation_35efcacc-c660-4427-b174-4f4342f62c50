{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/list-vop.vue?e079", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/list-vop.vue?8819", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/list-vop.vue?9610", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/list-vop.vue?60a7", "uni-app:///pages/goods/list-vop.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/list-vop.vue?c460", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/goods/list-vop.vue?801e"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "cid1", "cid2", "kw", "orderBy", "categoryId", "page", "tabs", "code", "name", "imageDomain", "goods", "showmod", "created", "mounted", "onReady", "onLoad", "onShow", "onShareAppMessage", "title", "path", "onReachBottom", "methods", "search", "searchscan", "uni", "scanType", "success", "paixu", "_goods", "keyword", "sortType", "res", "ele", "icon", "goDetail", "url"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,gBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8SAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,aAAa,4RAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCqDvrB;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC,OACA;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC,6BAEA;EACAC,6BAEA;EACAC,6BAEA;EACAC;IACA;IACA;IACA;IACA;EACA;EACAC,2BAEA;EACAC;IACA;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MACAC;QACAC;QACAC;UACA;UACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAJ;kBACAN;gBACA;gBACA;gBAAA;gBAAA,OACA;kBACAb;kBACAwB;kBACAC;kBACA9B;kBACAC;gBACA;cAAA;gBANA8B;gBAOAP;gBACA;kBACA;kBACAO;oBACAC;oBACAA;oBACAA;kBACA;kBACA;oBACA;kBACA;oBACA;kBACA;gBACA;kBACA;oBACA;kBACA;oBACAR;sBACAN;sBACAe;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACAV;QACAW;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChLA;AAAA;AAAA;AAAA;AAA0xC,CAAgB,8uCAAG,EAAC,C;;;;;;;;;;;ACA9yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/goods/list-vop.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/goods/list-vop.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./list-vop.vue?vue&type=template&id=0d9751fa&scoped=true&\"\nvar renderjs\nimport script from \"./list-vop.vue?vue&type=script&lang=js&\"\nexport * from \"./list-vop.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list-vop.vue?vue&type=style&index=0&id=0d9751fa&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0d9751fa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/goods/list-vop.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-vop.vue?vue&type=template&id=0d9751fa&scoped=true&\"", "var components\ntry {\n  components = {\n    uSticky: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-sticky/u-sticky\" */ \"@/uni_modules/uview-ui/components/u-sticky/u-sticky.vue\"\n      )\n    },\n    uSearch: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-search/u-search\" */ \"@/uni_modules/uview-ui/components/u-search/u-search.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tabs/u-tabs\" */ \"@/uni_modules/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    pageBoxEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/page-box-empty/page-box-empty\" */ \"@/components/page-box-empty/page-box-empty.vue\"\n      )\n    },\n    uTag: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tag/u-tag\" */ \"@/uni_modules/uview-ui/components/u-tag/u-tag.vue\"\n      )\n    },\n    uText: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-text/u-text\" */ \"@/uni_modules/uview-ui/components/u-text/u-text.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.goods || _vm.goods.length == 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-vop.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-vop.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view>\r\n\t\t<u-sticky bgColor=\"#FFFFFF\">\r\n\t\t\t<view class=\"search\">\r\n\t\t\t\t<u-search placeholder=\"输入关键词搜索\" v-model=\"kw\" :showAction=\"false\" @search=\"search\">\r\n\t\t\t\t</u-search>\r\n\t\t\t\t<!--  #ifndef  H5 || MP-KUAISHOU -->\r\n\t\t\t\t<view class=\"scan\" @click=\"searchscan\">\r\n\t\t\t\t\t<u-icon name=\"scan\" size=\"48rpx\"></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t\t<!--  #endif -->\r\n\t\t\t</view>\r\n\t\t\t<u-tabs :list=\"tabs\" lineColor=\"#e64340\" @click=\"paixu\"></u-tabs>\r\n\t\t</u-sticky>\r\n\t\t<page-box-empty v-if=\"!goods || goods.length == 0\" title=\"暂无商品\" sub-title=\"当前类目下无法帮你找到合适的商品\" :show-btn=\"true\" />\r\n\t\t<view v-if=\"showmod == 0\" class=\"goodslist\">\r\n\t\t\t<view v-for=\"(item, index) in goods\" :key=\"index\" class=\"list\" @click=\"toDetailsTap\">\r\n\t\t\t\t<image :src=\"item.pic\" class=\"image\" mode=\"aspectFill\" lazy-load=\"true\" @click=\"goDetail(item)\" />\r\n\t\t\t\t<view class=\"r\">\r\n\t\t\t\t\t<view class=\"goods-title u-line-3 pt16\" @click=\"goDetail(item)\">\r\n\t\t\t\t\t\t<u-tag v-if=\"item.supplyType == 'vop_jd' || item.supplyType == 'jdJoycityPoints'\" text=\"京东自营\" bgColor=\"#e64340\" borderColor=\"#e64340\" size=\"mini\" class=\"goods-title-tag\"></u-tag>\r\n\t\t\t\t\t\t<text class=\"goods-title\">{{ item.name }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<u-text v-if=\"item.characteristic\" class=\"goods-title\" :text=\"item.characteristic\" size=\"28rpx\"\r\n\t\t\t\t\t\tcolor=\"#c95060\"></u-text>\r\n\t\t\t\t\t<view class=\"price-score\">\r\n\t\t\t\t\t\t<view v-if=\"item.minPrice\" class=\"item\"><text>¥</text>{{item.minPrice}}</view>\r\n\t\t\t\t\t\t<view v-if=\"item.minScore\" class=\"item\"><text><image class=\"score-icon\" src=\"/static/images/score.png\"></image></text>{{item.minScore}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view v-else>\r\n\t\t\t<view class=\"goods-container\">\r\n\t\t\t\t<view v-for=\"(item, index) in goods\" :key=\"index\" class=\"goods-box\" bindtap=\"toDetailsTap\">\r\n\t\t\t\t\t<view class=\"img-box\">\r\n\t\t\t\t\t\t<image :src=\"item.pic\" class=\"image\" mode=\"aspectFill\" lazy-load=\"true\" @click=\"goDetail(item)\" />\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<u-text class=\"goods-title\" :text=\"item.name\" :lines=\"3\" size=\"28rpx\" color=\"#333\" @click=\"goDetail(item)\"></u-text>\r\n\t\t\t\t\t<u-text v-if=\"item.characteristic\" class=\"goods-title\" :text=\"item.characteristic\" size=\"28rpx\"\r\n\t\t\t\t\t\tcolor=\"#c95060\"></u-text>\r\n\t\t\t\t\t<view class=\"price-score\">\r\n\t\t\t\t\t\t<view v-if=\"item.minPrice\" class=\"item\"><text>¥</text>{{item.minPrice}}</view>\r\n\t\t\t\t\t\t<view v-if=\"item.minScore\" class=\"item\"><text><image class=\"score-icon\" src=\"/static/images/score.png\"></image></text>{{item.minScore}}</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcid1: '',\r\n\t\t\t\tcid2: '',\r\n\t\t\t\tkw: '',\r\n\t\t\t\torderBy: '',\r\n\t\t\t\tcategoryId: '',\r\n\t\t\t\tpage: 1,\r\n\t\t\t\ttabs: [\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tcode: '',\r\n\t\t\t\t\t\tname: '综合',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tcode: 'winsdate_desc',\r\n\t\t\t\t\t\tname: '新品',\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tcode: 'sort_totalsales15_desc',\r\n\t\t\t\t\t\tname: '销售'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tcode: 'price_asc',\r\n\t\t\t\t\t\tname: '价格'\r\n\t\t\t\t\t},\r\n\t\t\t\t],\r\n\t\t\t\timageDomain: undefined,\r\n\t\t\t\tgoods: undefined,\r\n\t\t\t\tshowmod: 1\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\r\n\t\t},\r\n\t\tmounted() {\r\n\r\n\t\t},\r\n\t\tonReady() {\r\n\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis.kw = e.kw ? e.kw : ''\r\n\t\t\tthis.cid1 = e.cid1 ? e.cid1 : ''\r\n\t\t\tthis.cid2 = e.cid2 ? e.cid2 : ''\r\n\t\t\tthis._goods()\r\n\t\t},\r\n\t\tonShow() {\r\n\r\n\t\t},\r\n\t\tonShareAppMessage(e) {\r\n\t\t\treturn {\r\n\t\t\t\ttitle: '\"' + this.sysconfigMap.mallName + '\" ' + this.sysconfigMap.share_profile,\r\n\t\t\t\tpath: '/pages/index/index?inviter_id=' + this.uid\r\n\t\t\t}\r\n\t\t},\r\n\t\tonReachBottom() {\r\n\t\t\tthis.page += 1\r\n\t\t\tthis._goods()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tsearch(v) {\r\n\t\t\t\tthis.kw = v\r\n\t\t\t\tthis.page = 1\r\n\t\t\t\tthis._goods()\r\n\t\t\t},\r\n\t\t\tsearchscan() {\r\n\t\t\t\tuni.scanCode({\r\n\t\t\t\t\tscanType: ['barCode', 'qrCode', 'datamatrix', 'pdf417'],\r\n\t\t\t\t\tsuccess: res => {\r\n\t\t\t\t\t\tthis.kw = res.result\r\n\t\t\t\t\t\tthis.page = 1\r\n\t\t\t\t\t\tthis._goods()\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tpaixu(item) {\r\n\t\t\t\tthis.orderBy = this.tabs[item.index].code\r\n\t\t\t\tthis.page = 1\r\n\t\t\t\tthis._goods()\r\n\t\t\t},\r\n\t\t\tasync _goods() {\r\n\t\t\t\tuni.showLoading({\r\n\t\t\t\t\ttitle: ''\r\n\t\t\t\t})\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/gqmtgw\r\n\t\t\t\tconst res = await this.$wxapi.jdvopGoodsList({\r\n\t\t\t\t\tpage: this.page,\r\n\t\t\t\t\tkeyword: this.kw,\r\n\t\t\t\t\tsortType: this.orderBy ? this.orderBy : '',\r\n\t\t\t\t\tcid1: this.cid1 ? this.cid1 : '',\r\n\t\t\t\t\tcid2: this.cid2 ? this.cid2 : '',\r\n\t\t\t\t})\r\n\t\t\t\tuni.hideLoading()\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.imageDomain = res.data.imageDomain\r\n\t\t\t\t\tres.data.result.forEach(ele => {\r\n\t\t\t\t\t\tele.pic = this.imageDomain + ele.pic\r\n\t\t\t\t\t\tele.name = ele.skuName\r\n\t\t\t\t\t\tele.minPrice = ele.priceSale\r\n\t\t\t\t\t})\r\n\t\t\t\t\tif (this.page == 1) {\r\n\t\t\t\t\t\tthis.goods = res.data.result\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tthis.goods = this.goods.concat(res.data.result)\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (this.page == 1) {\r\n\t\t\t\t\t\tthis.goods = null\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\t\ttitle: '没有更多了～',\r\n\t\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoDetail(item) {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/pages/goods/detail?supplyType=vop_jd&yyId=' + item.skuId\r\n\t\t\t\t})\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n\t.search {\r\n\t\tpadding: 8rpx;\r\n\t\tdisplay: flex;\r\n\t\talign-items: center;\r\n\t\t.scan {\r\n\t\t\tpadding: 0 16rpx;\r\n\t\t}\r\n\t}\r\n\t.goodslist {\r\n\t\t.list {\r\n\t\t\tmargin: 20rpx;\r\n\t\t\tborder-radius: 16rpx;\r\n\t\t\tdisplay: flex;\r\n\t\t\tpadding: 20rpx;\r\n\t\t\t.image {\r\n\t\t\t\twidth: 260rpx;\r\n\t\t\t\theight: 260rpx;\r\n\t\t\t\tflex-shrink: 0;\r\n\t\t\t\tborder-radius: 16rpx;\r\n\t\t\t}\r\n\t\t\t.r {\r\n\t\t\t\tmargin-left: 32rpx;\r\n\t\t\t\t.goods-title {\r\n\t\t\t\t\tcolor: #333;\r\n\t\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\t}\r\n\t\t\t\t.label {\r\n\t\t\t\t\tcolor: #e64340;\r\n\t\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\t\tdisplay: flex;\r\n\t\t\t\t\talign-items: flex-start;\r\n\t\t\t\t\tmargin-top: 8rpx;\r\n\t\t\t\t\ttext {\r\n\t\t\t\t\t\tmargin-left: 8rpx;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.count-down {\r\n\t\t\t\tbackground: rgba(250, 195, 198, 0.3);\r\n\t\t\t\tborder-radius: 5rpx;\r\n\t\t\t\tfont-size: 14rpx;\r\n\t\t\t\tcolor: red;\r\n\t\t\t\tfont-weight: 400;\r\n\t\t\t\tpadding: 6rpx 16rpx;\r\n\t\t\t\tmargin-top: 6rpx;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.price-box {\r\n\t\t\tdisplay: flex;\r\n\t\t\tjustify-content: space-between;\r\n\t\t\talign-items: center;\r\n\t\t\t.msbtn {\r\n\t\t\t\twidth: 170rpx;\r\n\t\t\t\theight: 60rpx;\r\n\t\t\t\tbackground: linear-gradient(156deg, #FF7863 0%, #FF211A 100%);\r\n\t\t\t\tborder-radius: 34rpx;\r\n\t\t\t\tborder: none !important;\r\n\t\t\t\tline-height: 60rpx !important;\r\n\t\t\t\tfont-size: 13px !important;\r\n\t\t\t}\r\n\t\t\t.price {\r\n\t\t\t\tcolor: #e64340;\r\n\t\t\t\tfont-size: 40rpx;\r\n\t\t\t\tmargin-top: 12rpx;\r\n\t\t\t\tpadding-right: 32rpx;\r\n\t\t\t\ttext {\r\n\t\t\t\t\tmargin-left: 16rpx;\r\n\t\t\t\t\tcolor: #666666;\r\n\t\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\t\ttext-decoration: line-through;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.goods-container {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\tflex-wrap: wrap;\r\n\t\tbox-sizing: content-box;\r\n\t\tpadding: 0 24rpx;\r\n\t\t.goods-box {\r\n\t\t\twidth: 339rpx;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\toverflow: hidden;\r\n\t\t\tmargin-top: 24rpx;\r\n\t\t\tborder-radius: 5px;\r\n\t\t\tborder: 1px solid #D1D1D1;\r\n\t\t\tpadding-bottom: 10rpx;\r\n\t\t\t.img-box {\r\n\t\t\t\twidth: 339rpx;\r\n\t\t\t\theight: 339rpx;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\timage {\r\n\t\t\t\t\twidth: 339rpx;\r\n\t\t\t\t\theight: 339rpx;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t.goods-title {\r\n\t\t\t\tpadding: 0 4rpx;\r\n\t\t\t}\r\n\t\t\t.goods-price-container {\r\n\t\t\t\tdisplay: flex;\r\n\t\t\t\talign-items: baseline;\r\n\t\t\t}\r\n\t\t\t.goods-price {\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tfont-size: 34rpx;\r\n\t\t\t\tcolor: #F20C32;\r\n\t\t\t\tmargin-left: 24rpx;\r\n\t\t\t}\r\n\t\t\t.goods-price2 {\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\tfont-size: 26rpx;\r\n\t\t\t\tcolor: #aaa;\r\n\t\t\t\ttext-decoration: line-through;\r\n\t\t\t\tmargin-left: 20rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-vop.vue?vue&type=style&index=0&id=0d9751fa&scoped=true&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-vop.vue?vue&type=style&index=0&id=0d9751fa&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692293986\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}