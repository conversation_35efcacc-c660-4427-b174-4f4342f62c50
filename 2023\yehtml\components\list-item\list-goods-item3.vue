<template>
  <view class="goods-box-wrapper">
    <view class="goods-box">
      <view @click="goDetail(item)">
        <view class="goods-title-box">
          <view class="goods-title">{{ item.name }}</view>
        </view>
        <view v-if="item.characteristic" class="goods-dsc-box">
          <view class="goods-dsc">{{ item.characteristic }}</view>
        </view>
        <view class="goods-tags-box">
          <view class="goods-tags">
            <span v-for="(tag, index) in filteredTags" :key="index" :style="{ color: shouldHighlight(tag) ? '#e64340' : '', border: shouldHighlight(tag) ? '1px solid #e64340' : '', display: 'inline-block', 'margin-right': '5px' }">
              {{ tag }}
            </span>
          </view>
        </view>
      </view>
      <view class="buy-wrapper" style="display: flex;">
        <view class="price-score">
          <view v-if="item.minPrice" class="item"><text>¥</text>{{item.minPrice}}</view>
          <view v-if="item.minScore" class="item"><text>
              <image class="score-icon" src="/static/images/score.png"></image>
            </text>{{item.minScore}}</view>
        </view>
        <view class="buy" @click="goDetail(item)">
          <u-icon name="shopping-cart" color="#F20C32" size="28"></u-icon>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  const TOOLS = require('@/common/tools')

  export default {
    props: {
      item: {
        type: Object,
        default: {},
      },
    },
    onReady() {},
    data() {
      return {}
    },
    computed: {
      filteredTags() {
        if (this.item.tags) {
          const tags = this.item.tags.split(/[, ]+/);
          return tags.filter(tag => tag.trim() !== '');
        }
        return [];
      }
    },
    methods: {
      goDetail(item) {
        const categoryIds = [383898, 383899, 383900, 383901, 383902, 383903, 383904, 383905]
        if (categoryIds.includes(item.categoryId)) {
          TOOLS.softOpeningTip()
        } else {
          uni.navigateTo({
            url: '/pages/goods/detail?id=' + item.id
          })
        }
      },
      shouldHighlight(tag) {
        return /[A-Za-z]\d+/.test(tag);
      }
    },
  }
</script>
<style>
  .goods-box-wrapper {
    margin-top: 24rpx;
    border-radius: 5px;
    padding-bottom: 10rpx;
    text-align: left;
    overflow: hidden;
  }

  .goods-box {
    padding: 0 10rpx;
  }

  .goods-box .goods-title-box {
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }

  .goods-box .goods-title {
    color: #2b2b2b;
    font-size: 30rpx;
  }

  .goods-box .goods-dsc-box {
    margin-top: 4rpx;
  }

  .goods-box .goods-dsc {
    color: #858996;
    font-size: 24rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .goods-box .goods-price-container {
    display: flex;
    align-items: baseline;
  }

  .goods-box .goods-price {
    overflow: hidden;
    font-size: 34rpx;
    color: #F20C32;
    margin-left: 24rpx;
  }

  .goods-box .goods-price2 {
    overflow: hidden;
    font-size: 26rpx;
    color: #aaa;
    text-decoration: line-through;
    margin-left: 20rpx;
  }

  .goods-box .buy-wrapper {
    margin-top: 10rpx;
    display: flex;
    justify-content: space-between;
  }

  .goods-box .price-score .item {
    padding: 0 8rpx 0 0;
  }

  .goods-box .price-score text {
    margin-top: 6rpx;
  }
</style>