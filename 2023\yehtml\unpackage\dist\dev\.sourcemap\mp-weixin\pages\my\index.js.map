{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/index.vue?7f1b", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/index.vue?a954", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/index.vue?cffb", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/index.vue?3f93", "uni-app:///pages/my/index.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/index.vue?f393", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/index.vue?9d1d"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "TitleOperate", "UserInfo", "LabelCount", "<PERSON><PERSON><PERSON><PERSON>", "tabbar", "data", "tabIndex", "isTab1Visible", "isTab2Visible", "needLogin", "apiUserInfoMap", "balance", "freeze", "score", "growth", "pic", "show", "version", "undefined", "label", "count", "url", "name", "img", "onLoad", "onShow", "methods", "_userDetail", "res", "itemPartner", "_myCoupons", "token", "status", "consumAmount", "_getUserAmount", "handleOrderCount", "_orderStatistics", "count_id_no_confirm", "count_id_no_pay", "count_id_no_reputation", "count_id_no_transfer", "orderGridClick", "uni", "go", "goGrade", "clearStorage", "title", "loginout", "_cardMyList", "changeTab", "computed", "showEnterpriseTabs", "tabsList"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7CA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,grBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACqEprB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA;AAAA,eAEA;EACAC;IACAC;IACAC;IACAC;IACAC;IACAC;EACA;EACAC;IAAA;IACA;MACAC;MAEAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;IAAA,kDACAC,8DACAA,uEAEA,2DACA,kEACA,gEACA,wDAEA;MACAC;MACAC;MACAC;IACA,GACA;MACAF;MACAC;MACAC;IACA,GACA;MACAF;MACAC;MACAC;IACA,EACA,sDACA;MACAC;MACAC;MACAF;IACA,GACA;MACAC;MACAC;MACAF;IACA,GACA;MACAC;MACAC;MACAF;IACA,GACA;MACAC;MACAC;MACAF;IACA,GACA;MACAC;MACAC;MACAF;IACA,EACA,sDACA;MACAC;MACAC;MACAF;IACA;IACA;AACA;AACA;AACA;AACA;IACA;MACAC;MACAC;MACAF;IACA,GACA;MACAC;MACAC;MACAF;IACA,GACA;MACAC;MACAC;MACAF;IACA,EACA,oDACA;MACAC;MACAC;MACAF;IACA,GACA;MACAC;MACAC;MACAF;IACA,GACA;MACAC;MACAC;MACAF;IACA,EACA;EAEA;EACAG;IACA;EACA;EACAC;IACA;IACA;IACA;IACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;gBACA;gBACA;kBACA;;kBAEA;kBACA;oBACA;oBACAC;sBACAP;sBACAC;sBACAF;oBACA;oBACA;kBACA;;kBAEA;kBACA;oBACA;oBACAQ;sBACAP;sBACAC;sBACAF;oBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;kBACAC;kBACAC;gBACA;cAAA;gBAJAL;gBAKA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAM;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAN;gBACA;kBACA;gBACA;gBACA;kBACA,8CACA,4CACA,+BACA;kBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAO;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAR;gBACA;kBAAA,QAMAA,gBAJAS,iDACAC,yCACAC,uDACAC;kBAGA;kBACA;kBACA;kBACA;kBAEA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;oBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;QACAC;UACArB;QACA;MACA;QACAqB;UACArB;QACA;MACA;IACA;IACAsB;MACA;QACAD;UACArB;QACA;MACA;QACAqB;UACArB;QACA;MACA;IACA;IACAuB;MACAF;QACArB;MACA;IACA;IACAwB;MACAH;MACAA;QACAI;MACA;MACAJ;QACArB;MACA;IACA;IACA0B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBACA;gBACA;gBACAL;kBACArB;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAApB;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAqB;MACA;QACA;QACA;MACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;IACA;IACAC;MACA;QACA;UACA9B;QACA;UACAA;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACnXA;AAAA;AAAA;AAAA;AAAuxC,CAAgB,2uCAAG,EAAC,C;;;;;;;;;;;ACA3yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=4dcceeb0&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=4dcceeb0&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"4dcceeb0\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=4dcceeb0&scoped=true&\"", "var components\ntry {\n  components = {\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tabs/u-tabs\" */ \"@/uni_modules/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      return _vm.$u.route({\n        url: \"/pages/order/index\",\n      })\n    }\n    _vm.e1 = function ($event) {\n      return _vm.$u.route({\n        url: \"/pages/order/index\",\n      })\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"my container-wrapper\">\r\n\t\t<view class=\"userinfo\">\r\n\t\t\t<UserInfo :data=\"apiUserInfoMap\" :needLogin=\"needLogin\"></UserInfo>\r\n\t\t</view>\r\n\r\n\t\t<view class=\"vip-ad\" @click=\"goGrade\">\r\n\t\t\t<span class=\"text1\">开通会员 专属优惠专享</span>\r\n\t\t\t<span class=\"text2\">开通会员</span>\r\n\t\t\t<img src=\"https://ye.niutouren.vip/static/images/my/vipad.png\" />\r\n\t\t</view>\r\n\t\t<view class=\"main-wrapper\">\r\n\t\t\t<view class=\"tabs\" v-if=\"showEnterpriseTabs\">\r\n\t\t\t\t<u-tabs :list=\"tabsList\" @click=\"changeTab\" lineWidth=\"30\" lineColor=\"#223F36\" :activeStyle=\"{\r\n                    color: '#303133',\r\n                    fontWeight: 'bold',\r\n                    transform: 'scale(1.05)'\r\n                }\" :inactiveStyle=\"{\r\n                    color: '#606266',\r\n                    transform: 'scale(1)'\r\n                }\" itemStyle=\"padding-left: 15px; padding-right: 15px; height: 34px;\">\r\n\t\t\t\t</u-tabs>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view v-if=\"!showEnterpriseTabs || isTab1Visible\">\r\n\t\t\t\t<view class=\"user-count\">\r\n\t\t\t\t\t<LabelCount :ops=\"mineCountOps\"></LabelCount>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"order-nav\">\r\n\t\t\t\t\t<DialNav :mode=\"10\" shadow :list=\"orderNavOps\" nameSize=\"24rpx\" imgSize=\"50rpx\">\r\n\t\t\t\t\t\t<TitleOperate padding=\"30rpx 30rpx 0 30rpx\" showMore title=\"我的订单\" moreLabel=\"全部订单\"\r\n\t\t\t\t\t\t\t@clickMore=\"$u.route({ url: '/pages/order/index' })\"></TitleOperate>\r\n\t\t\t\t\t</DialNav>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"other-nav\">\r\n\t\t\t\t\t<DialNav marginTopLine=\"28rpx\" :mode=\"8\" shadow :list=\"favNavOps\" nameSize=\"24rpx\" imgSize=\"40rpx\">\r\n\t\t\t\t\t\t<TitleOperate padding=\"30rpx 30rpx 0 30rpx\" title=\"收藏夹\"></TitleOperate>\r\n\t\t\t\t\t</DialNav>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"other-nav\">\r\n\t\t\t\t\t<DialNav marginTopLine=\"28rpx\" :mode=\"8\" shadow :list=\"otherNavOps\" nameSize=\"24rpx\" imgSize=\"40rpx\">\r\n\t\t\t\t\t\t<TitleOperate padding=\"30rpx 30rpx 0 30rpx\" title=\"常用功能\"></TitleOperate>\r\n\t\t\t\t\t</DialNav>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view v-if=\"showEnterpriseTabs && isTab2Visible\">\r\n\t\t\t\t<view class=\"user-count\">\r\n\t\t\t\t\t<LabelCount :ops=\"mineCountOps\"></LabelCount>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view class=\"order-nav\">\r\n\t\t\t\t\t<DialNav :mode=\"10\" shadow :list=\"orderNavOps\" nameSize=\"24rpx\" imgSize=\"50rpx\">\r\n\t\t\t\t\t\t<TitleOperate padding=\"30rpx 30rpx 0 30rpx\" showMore title=\"企业订单\" moreLabel=\"全部订单\"\r\n\t\t\t\t\t\t\t@clickMore=\"$u.route({ url: '/pages/order/index' })\"></TitleOperate>\r\n\t\t\t\t\t</DialNav>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\r\n\t\t\t<view class=\"copyright\">桂ICP备2024044061号</view>\r\n\t\t</view>\r\n\r\n\t\t<tabbar :tabIndex=\"tabIndex\"></tabbar>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport empty from 'empty-value'\r\n\timport UserInfo from '@/pages/my/components/user-info'\r\n\timport LabelCount from '@/components/nav/label-count'\r\n\timport DialNav from '@/components/nav/dial-nav'\r\n\timport tabbar from '@/components/tabbar'\r\n\timport TitleOperate from '@/components/title-operate'\r\n\r\n\tconst USER = require('@/common/user.js')\r\n\r\n\texport default {\r\n\t\tcomponents: {\r\n\t\t\tTitleOperate,\r\n\t\t\tUserInfo,\r\n\t\t\tLabelCount,\r\n\t\t\tDialNav,\r\n\t\t\ttabbar,\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\ttabIndex: 4,\r\n\r\n\t\t\t\tisTab1Visible: true,\r\n\t\t\t\tisTab2Visible: false,\r\n\r\n\t\t\t\tneedLogin: false,\r\n\t\t\t\tapiUserInfoMap: undefined,\r\n\t\t\t\tbalance: 0,\r\n\t\t\t\tfreeze: 0,\r\n\t\t\t\tscore: 0,\r\n\t\t\t\tgrowth: 0,\r\n\t\t\t\tpic: 'https://uviewui.com/common/logo.png',\r\n\t\t\t\tshow: true,\r\n\t\t\t\tversion: getApp().globalData.version,\r\n\t\t\t\tversion: undefined,\r\n\t\t\t\tcardMyList: undefined,\r\n\r\n\t\t\t\tcount_id_no_confirm: 0,\r\n\t\t\t\tcount_id_no_pay: 0,\r\n\t\t\t\tcount_id_no_reputation: 0,\r\n\t\t\t\tcount_id_no_transfer: 0,\r\n\r\n\t\t\t\tmineCountOps: [{\r\n\t\t\t\t\t\tlabel: '余额',\r\n\t\t\t\t\t\tcount: 0,\r\n\t\t\t\t\t\turl: '/pages/asset/balance'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '礼品卡',\r\n\t\t\t\t\t\tcount: 0,\r\n\t\t\t\t\t\turl: '/packageFx/coupons/my'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tlabel: '积分',\r\n\t\t\t\t\t\tcount: 0,\r\n\t\t\t\t\t\turl: '/pages/score/index'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\torderNavOps: [{\r\n\t\t\t\t\t\tname: '待付款',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-order-1.png'),\r\n\t\t\t\t\t\turl: '/pages/order/index?status=0'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '待发货',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-order-2.png'),\r\n\t\t\t\t\t\turl: '/pages/order/index?status=1'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '待收货',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-order-3.png'),\r\n\t\t\t\t\t\turl: '/pages/order/index?status=2'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '待评论',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-order-4.png'),\r\n\t\t\t\t\t\turl: '/pages/order/index?status=3'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '退款售后',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-order-5.png'),\r\n\t\t\t\t\t\turl: '/pages/order/index?status=99'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\totherNavOps: [{\r\n\t\t\t\t\t\tname: '使用帮助',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-setting-1.png'),\r\n\t\t\t\t\t\turl: '/packageFx/help/list'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t/* {\r\n\t\t\t\t\t  name: '客服电话',\r\n\t\t\t\t\t  img: require('../../static/images/my/nav/mine-setting-2.png'),\r\n\t\t\t\t\t  url: '/packageFx/about/about?key=service'\r\n\t\t\t\t\t}, */\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '关于我们',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-setting-3.png'),\r\n\t\t\t\t\t\turl: '/packageFx/about/about?key=aboutus'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '收货地址',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-setting-4.png'),\r\n\t\t\t\t\t\turl: '/packageFx/address/index'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '个人设置',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-setting-7.png'),\r\n\t\t\t\t\t\turl: '/pages/my/info-menu'\r\n\t\t\t\t\t}\r\n\t\t\t\t],\r\n\t\t\t\tfavNavOps: [{\r\n\t\t\t\t\t\tname: '我喜欢的',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-setting-6-1.png'),\r\n\t\t\t\t\t\turl: '/pages/goods/fav?type=1'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '朋友赠送',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-setting-6-2.png'),\r\n\t\t\t\t\t\turl: '/pages/goods/fav?type=2'\r\n\t\t\t\t\t},\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\tname: '朋友推荐',\r\n\t\t\t\t\t\timg: require('../../static/images/my/nav/mine-setting-6-3.png'),\r\n\t\t\t\t\t\turl: '/pages/goods/fav?type=3'\r\n\t\t\t\t\t}\r\n\t\t\t\t]\r\n\t\t\t}\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis.version = getApp().globalData.version\r\n\t\t},\r\n\t\tonShow() {\r\n\t\t\tthis._myCoupons()\r\n\t\t\tthis._userDetail()\r\n\t\t\tthis._getUserAmount()\r\n\t\t\tthis._cardMyList()\r\n\t\t\tthis._orderStatistics()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync _userDetail() {\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/zgf8pu\r\n\t\t\t\tconst res = await this.$wxapi.userDetail(this.token)\r\n\t\t\t\tif (res.code == 2000) {\r\n\t\t\t\t\tthis.needLogin = true\r\n\t\t\t\t}\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.apiUserInfoMap = res.data\r\n\r\n\t\t\t\t\t// 个人合伙人入口\r\n\t\t\t\t\tif (!empty(res.data.userLevel) && res.data.userLevel.level > 4) {\r\n\t\t\t\t\t\tthis.otherNavOps.shift()\r\n\t\t\t\t\t\tconst itemPartner = {\r\n\t\t\t\t\t\t\tname: '个人合伙人',\r\n\t\t\t\t\t\t\timg: require('../../static/images/my/nav/partner.png'),\r\n\t\t\t\t\t\t\turl: '/packageFx/partner/share'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tthis.otherNavOps.unshift(itemPartner);\r\n\t\t\t\t\t}\r\n\r\n\t\t\t\t\t// 平台合伙人入口\r\n\t\t\t\t\tif (!empty(res.data.userLevel) && res.data.userLevel.level > 5) {\r\n\t\t\t\t\t\tthis.otherNavOps.shift()\r\n\t\t\t\t\t\tconst itemPartner = {\r\n\t\t\t\t\t\t\tname: '平台合伙人',\r\n\t\t\t\t\t\t\timg: require('../../static/images/my/nav/partner.png'),\r\n\t\t\t\t\t\t\turl: '/packageFx/partner/share'\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t\tthis.otherNavOps.unshift(itemPartner);\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync _myCoupons() {\r\n\t\t\t\tconst res = await this.$wxapi.myCoupons({\r\n\t\t\t\t\ttoken: this.token,\r\n\t\t\t\t\tstatus: 0,\r\n\t\t\t\t\tconsumAmount: ''\r\n\t\t\t\t})\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.mineCountOps[1].count = res.data.length\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync _getUserAmount() {\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/wrqkcb\r\n\t\t\t\tconst res = await this.$wxapi.userAmount(this.token)\r\n\t\t\t\tif (res.code == 2000) {\r\n\t\t\t\t\tthis.needLogin = true\r\n\t\t\t\t}\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.balance = res.data.balance.toFixed(2),\r\n\t\t\t\t\t\tthis.freeze = res.data.freeze.toFixed(2),\r\n\t\t\t\t\t\tthis.score = res.data.score,\r\n\t\t\t\t\t\tthis.growth = res.data.growth\r\n\r\n\t\t\t\t\tthis.mineCountOps[0].count = this.balance\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\thandleOrderCount(count) {\r\n\t\t\t\treturn count > 99 ? '99+' : count;\r\n\t\t\t},\r\n\t\t\tasync _orderStatistics() {\r\n\t\t\t\tconst res = await this.$wxapi.orderStatistics(this.token)\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tconst {\r\n\t\t\t\t\t\tcount_id_no_confirm,\r\n\t\t\t\t\t\tcount_id_no_pay,\r\n\t\t\t\t\t\tcount_id_no_reputation,\r\n\t\t\t\t\t\tcount_id_no_transfer,\r\n\t\t\t\t\t} = res.data || {}\r\n\r\n\t\t\t\t\tthis.count_id_no_confirm = this.handleOrderCount(count_id_no_confirm)\r\n\t\t\t\t\tthis.count_id_no_pay = this.handleOrderCount(count_id_no_pay)\r\n\t\t\t\t\tthis.count_id_no_reputation = this.handleOrderCount(count_id_no_reputation)\r\n\t\t\t\t\tthis.count_id_no_transfer = this.handleOrderCount(count_id_no_transfer)\r\n\r\n\t\t\t\t\tthis.orderNavOps.forEach(ele => {\r\n\t\t\t\t\t\tif (ele.name === '待付款') {\r\n\t\t\t\t\t\t\tthis.orderNavOps[0].badgeNum = count_id_no_pay\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (ele.name === '待发货') {\r\n\t\t\t\t\t\t\tthis.orderNavOps[1].badgeNum = count_id_no_transfer\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t\tif (ele.name === '待收货') {\r\n\t\t\t\t\t\t\tthis.orderNavOps[2].badgeNum = count_id_no_confirm\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\torderGridClick(status) {\r\n\t\t\t\tif (status == 99) {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/order/index?status=' + status\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: '/pages/order/index?status=' + status\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgo(url) {\r\n\t\t\t\tif (url.indexOf('switchTab:') != -1) {\r\n\t\t\t\t\tuni.switchTab({\r\n\t\t\t\t\t\turl: url.substring(10)\r\n\t\t\t\t\t})\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\t\turl: url\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgoGrade() {\r\n\t\t\t\tuni.navigateTo({\r\n\t\t\t\t\turl: '/packageFx/my/grade'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tclearStorage() {\r\n\t\t\t\tuni.clearStorageSync()\r\n\t\t\t\tuni.showToast({\r\n\t\t\t\t\ttitle: '已清除'\r\n\t\t\t\t})\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: '../index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync loginout() {\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/mg77aq\r\n\t\t\t\tawait this.$wxapi.loginout(this.token)\r\n\t\t\t\tthis.$u.vuex('token', '')\r\n\t\t\t\tthis.$u.vuex('uid', '')\r\n\t\t\t\tuni.reLaunch({\r\n\t\t\t\t\turl: '../index/index'\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync _cardMyList() {\r\n\t\t\t\tconst res = await this.$wxapi.cardMyList(this.token)\r\n\t\t\t\tif (res.code == 0) {\r\n\t\t\t\t\tthis.cardMyList = res.data\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tchangeTab(item) {\r\n\t\t\t\tif (item.index === 0) {\r\n\t\t\t\t\tthis.isTab1Visible = true;\r\n\t\t\t\t\tthis.isTab2Visible = false;\r\n\t\t\t\t} else if (item.index === 1) {\r\n\t\t\t\t\tthis.isTab1Visible = false;\r\n\t\t\t\t\tthis.isTab2Visible = true;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tshowEnterpriseTabs() {\r\n\t\t\t\treturn this.apiUserInfoMap?.userLevel?.id === 33080\r\n\t\t\t},\r\n\t\t\ttabsList() {\r\n\t\t\t\tif (this.showEnterpriseTabs) {\r\n\t\t\t\t\treturn [{\r\n\t\t\t\t\t\tname: '个人',\r\n\t\t\t\t\t}, {\r\n\t\t\t\t\t\tname: '企业',\r\n\t\t\t\t\t}]\r\n\t\t\t\t}\r\n\t\t\t\treturn []\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.my.container-wrapper {\r\n\t\tbackground: linear-gradient(90deg, #80a933, #446B30) fixed;\r\n\t\tbackground-position: top center, center center, bottom center;\r\n\t\tbackground-size: 100% 33.33%;\r\n\t\tbackground-repeat: no-repeat;\r\n\t}\r\n\r\n\t.tabs {\r\n\t\tmargin-bottom: 20rpx;\r\n\t}\r\n\r\n\t.tab1 {\r\n\t\tdisplay: block;\r\n\t}\r\n\r\n\t.tab2 {\r\n\t\tdisplay: none;\r\n\t}\r\n\r\n\t.userinfo {\r\n\t\tmargin-top: 80rpx;\r\n\t}\r\n\r\n\t.main-wrapper {\r\n\t\tposition: relative;\r\n\t\tpadding: 30rpx;\r\n\t\tborder-top-left-radius: 10px;\r\n\t\tborder-top-right-radius: 10px;\r\n\t\tposition: relative;\r\n\t\tbackground: #F7F8FA;\r\n\t}\r\n\r\n\t.vip-ad {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tposition: relative;\r\n\t}\r\n\r\n\t.vip-ad img {\r\n\t\twidth: 690rpx;\r\n\t\theight: 128rpx;\r\n\t}\r\n\r\n\t.vip-ad .text1 {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tposition: absolute;\r\n\t\tleft: 70rpx;\r\n\t\ttop: 30rpx;\r\n\t\tcolor: #55301c;\r\n\t}\r\n\r\n\t.vip-ad .text2 {\r\n\t\tfont-size: 28rpx;\r\n\t\tfont-weight: bold;\r\n\t\tposition: absolute;\r\n\t\tright: 110rpx;\r\n\t\ttop: 46rpx;\r\n\t\tcolor: #55301c;\r\n\t}\r\n\r\n\t.user-count,\r\n\t.order-nav,\r\n\t.community-nav,\r\n\t.other-nav {\r\n\t\tposition: relative;\r\n\t\tz-index: 0;\r\n\t\tpadding: 0 0 30rpx 0;\r\n\t}\r\n\r\n\t.copyright {\r\n\t\tfont-size: 11px;\r\n\t\tcolor: #999;\r\n\t\ttext-align: center;\r\n\t\tmargin-bottom: 20px;\r\n\t}\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4dcceeb0&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=4dcceeb0&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688737337\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}