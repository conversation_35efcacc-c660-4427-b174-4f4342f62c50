<view class="data-v-48e3a299"><block wx:if="{{userMobile}}"><u-cell vue-id="9c85b4dc-1" title="手机号码" value="{{userMobile}}" class="data-v-48e3a299" bind:__l="__l"></u-cell></block><u-cell vue-id="9c85b4dc-2" wx:else title="绑定手机" class="data-v-48e3a299" bind:__l="__l" vue-slots="{{['value']}}"><view class="mobile-btn data-v-48e3a299" slot="value"><u-button vue-id="{{('9c85b4dc-3')+','+('9c85b4dc-2')}}" type="success" size="mini" open-type="getPhoneNumber" data-event-opts="{{[['^getphonenumber',[['getPhoneNumber']]]]}}" bind:getphonenumber="__e" class="data-v-48e3a299" bind:__l="__l" vue-slots="{{['default']}}">立即绑定</u-button></view></u-cell><u-cell vue-id="9c85b4dc-4" title="收货地址" is-link="{{true}}" url="/packageFx/address/index" class="data-v-48e3a299" bind:__l="__l"></u-cell><u-cell vue-id="9c85b4dc-5" title="我的收藏" is-link="{{true}}" url="/pages/goods/fav" class="data-v-48e3a299" bind:__l="__l"></u-cell><u-cell vue-id="9c85b4dc-6" title="修改资料" is-link="{{true}}" url="/pages/my/info" class="data-v-48e3a299" bind:__l="__l"></u-cell></view>