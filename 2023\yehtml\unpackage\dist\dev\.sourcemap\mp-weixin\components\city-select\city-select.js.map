{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/components/city-select/city-select.vue?80a1", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/city-select/city-select.vue?32c1", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/city-select/city-select.vue?090b", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/city-select/city-select.vue?42df", "uni-app:///components/city-select/city-select.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/city-select/city-select.vue?2f26", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/city-select/city-select.vue?f181"], "names": ["name", "props", "value", "type", "default", "defaultRegion", "areaCode", "maskCloseAble", "zIndex", "level", "data", "cityValue", "isChooseP", "province", "provinces", "isChooseC", "city", "citys", "isChooseA", "area", "areas", "isChooseS", "street", "streets", "tabsIndex", "watch", "deep", "immediate", "handler", "mounted", "computed", "isChange", "genTabsList", "tabsList", "uZIndex", "methods", "init", "res", "label", "setProvince", "k", "console", "v", "setCity", "<PERSON><PERSON><PERSON>", "setStreet", "close", "tabsChange", "provinceChange", "index", "cityChange", "result", "areaChange", "streetChange"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwH;AACxH;AAC+D;AACL;AACc;;;AAGxE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,iFAAM;AACR,EAAE,sFAAM;AACR,EAAE,+FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrDA;AAAA;AAAA;AAAA;AAAsqB,CAAgB,srBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACsE1rB;AACA;AACA;AACA;AACA;AACA;AACA;AANA,gBAOA;EACAA;EACAC;IACA;IACAC;MACAC;MACAC;IACA;IACA;IACAC;MACAF;MACAC;QACA;MACA;IACA;IACA;IACAE;MACAH;MACAC;QACA;MACA;IACA;IACA;IACAG;MACAJ;MACAC;IACA;IACA;IACAI;MACAL;MACAC;IACA;IACAK;MACAN;MACAC;IACA;EACA;EACAM;IACA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;IACA;EACA;EACAC;IACAnB;MACAoB;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;QACAhC;MACA;MACA;QACAiC;QACAA;UACAjC;QACA;MACA;MACA;QACAiC;QACA;UACAA;YACAjC;UACA;QACA;MACA;MACA;QACAiC;QACA;UACAA;YACAjC;UACA;QACA;MACA;MACA;QACAiC;MACA;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAC;gBACA;gBACA;kBACAA;oBACA;sBACAC;sBACApC;oBACA;kBACA;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA;cAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACAqC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAD;gBAAApC;gBACAsC;kBACA;gBACA;gBACAC;gBAAA,MACAD;kBAAA;kBAAA;gBAAA;gBACAE;gBAAA;gBAAA,OACA;kBAAA1C;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA2C;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAL;gBAAApC;gBACAsC;kBACA;gBACA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAE;gBAAA;gBAAA,OACA;kBAAA1C;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA4C;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAN;gBAAApC;gBACAsC;kBACA;gBACA;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAE;gBACA;kBACA;kBACA;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;kBAAA1C;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAGA;IACA6C;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAP;gBAAApC;gBACAsC;kBACA;gBACA;gBACA;kBACAE;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAI;MACA;IACA;IACAC;MACA;MACA;QACA;UAAA/C;QAAA;MACA;MACA;QACA;UAAAA;QAAA;MACA;MACA;QACA;UAAAA;QAAA;MACA;MACA;QACA;UAAAA;QAAA;MACA;IACA;IACAgD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAP;gBACAQ;gBACA;gBACA;gBACA;gBACA;gBACA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAZ;gBACA;gBACA;kBACAA;oBACA;sBACAC;sBACApC;oBACA;kBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAgD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAD;gBACA;gBACA;gBACA;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;kBACAE;kBACAA;kBACAA;kBACA;kBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAd;gBACA;gBACA;kBACAA;oBACA;sBACAC;sBACApC;oBACA;kBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAkD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAH;gBACA;gBACA;gBACA;gBACA;kBACAE;kBACAA;kBACAA;kBACAA;kBACA;kBACA;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAd;gBACA;gBACA;kBACAA;oBACA;sBACAC;sBACApC;oBACA;kBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAmD;MACA;MACA;MACA;MACA;MACAF;MACAA;MACAA;MACAA;MACA;MACA;IACA;EACA;AAEA;AAAA,4B;;;;;;;;;;;;ACrYA;AAAA;AAAA;AAAA;AAAqwC,CAAgB,ytCAAG,EAAC,C;;;;;;;;;;;ACAzxC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/city-select/city-select.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./city-select.vue?vue&type=template&id=c758b27c&\"\nvar renderjs\nimport script from \"./city-select.vue?vue&type=script&lang=js&\"\nexport * from \"./city-select.vue?vue&type=script&lang=js&\"\nimport style0 from \"./city-select.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/city-select/city-select.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city-select.vue?vue&type=template&id=c758b27c&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uTabs: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-tabs/u-tabs\" */ \"@/uni_modules/uview-ui/components/u-tabs/u-tabs.vue\"\n      )\n    },\n    uCellGroup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell-group/u-cell-group\" */ \"@/uni_modules/uview-ui/components/u-cell-group/u-cell-group.vue\"\n      )\n    },\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city-select.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city-select.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<u-popup :show=\"value\" mode=\"bottom\" round=\"32rpx\" :closeable=\"true\"\r\n\t :z-index=\"uZIndex\" :closeOnClickOverlay=\"maskCloseAble\" @close=\"close\">\r\n\t\t<u-tabs v-if=\"value\" :list=\"genTabsList\" :current=\"tabsIndex\" @change=\"tabsChange\"></u-tabs>\r\n\t\t<view class=\"area-box\">\r\n\t\t\t<view class=\"u-flex\" :class=\"{ 'change':isChange }\">\r\n\t\t\t\t<view class=\"area-item\">\r\n\t\t\t\t\t<view class=\"u-padding-10 u-bg-gray\" style=\"height: 100%;\">\r\n\t\t\t\t\t\t<scroll-view :scroll-y=\"true\" style=\"height: 100%\">\r\n\t\t\t\t\t\t\t<u-cell-group>\r\n\t\t\t\t\t\t\t\t<u-cell v-for=\"(item,index) in provinces\" :key=\"index\" :title=\"item.label\" :name=\"index\"\r\n\t\t\t\t\t\t\t\t @click=\"provinceChange\">\r\n\t\t\t\t\t\t\t\t\t<view slot=\"right-icon\">\r\n\t\t\t\t\t\t\t\t\t\t<u-icon v-if=\"isChooseP && province == index\" size=\"48rpx\" name=\"checkbox-mark\" color=\"green\"></u-icon>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</u-cell>\r\n\t\t\t\t\t\t\t</u-cell-group>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view v-if=\"isChooseP\" class=\"area-item\">\r\n\t\t\t\t\t<view class=\"u-padding-10 u-bg-gray\" style=\"height: 100%;\">\r\n\t\t\t\t\t\t<scroll-view :scroll-y=\"true\" style=\"height: 100%\">\r\n\t\t\t\t\t\t\t<u-cell-group>\r\n\t\t\t\t\t\t\t\t<u-cell v-for=\"(item,index) in citys\" :key=\"index\" :title=\"item.label\" :name=\"index\"\r\n\t\t\t\t\t\t\t\t @click=\"cityChange\">\r\n\t\t\t\t\t\t\t\t\t<view slot=\"right-icon\">\r\n\t\t\t\t\t\t\t\t\t\t<u-icon v-if=\"isChooseC && city == index\" size=\"48rpx\" name=\"checkbox-mark\" color=\"green\"></u-icon>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</u-cell>\r\n\t\t\t\t\t\t\t</u-cell-group>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\r\n\t\t\t\t<view v-if=\"isChooseC && level >= 3\" class=\"area-item\">\r\n\t\t\t\t\t<view class=\"u-padding-10 u-bg-gray\" style=\"height: 100%;\">\r\n\t\t\t\t\t\t<scroll-view :scroll-y=\"true\" style=\"height: 100%\">\r\n\t\t\t\t\t\t\t<u-cell-group>\r\n\t\t\t\t\t\t\t\t<u-cell v-for=\"(item,index) in areas\" :key=\"index\" :title=\"item.label\" :name=\"index\"\r\n\t\t\t\t\t\t\t\t @click=\"areaChange\">\r\n\t\t\t\t\t\t\t\t\t<view slot=\"right-icon\">\r\n\t\t\t\t\t\t\t\t\t\t<u-icon v-if=\"isChooseA&&area == index\" size=\"48rpx\" name=\"checkbox-mark\" color=\"green\"></u-icon>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</u-cell>\r\n\t\t\t\t\t\t\t</u-cell-group>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t\t<view v-if=\"isChooseA && level >= 4\" class=\"area-item\">\r\n\t\t\t\t\t<view class=\"u-padding-10 u-bg-gray\" style=\"height: 100%;\">\r\n\t\t\t\t\t\t<scroll-view :scroll-y=\"true\" style=\"height: 100%\">\r\n\t\t\t\t\t\t\t<u-cell-group>\r\n\t\t\t\t\t\t\t\t<u-cell v-for=\"(item,index) in streets\" :key=\"index\" :title=\"item.label\" :name=\"index\"\r\n\t\t\t\t\t\t\t\t @click=\"streetChange\">\r\n\t\t\t\t\t\t\t\t\t<view slot=\"right-icon\">\r\n\t\t\t\t\t\t\t\t\t\t<u-icon v-if=\"isChooseS&&street == index\" size=\"48rpx\" name=\"checkbox-mark\" color=\"green\"></u-icon>\r\n\t\t\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t\t</u-cell>\r\n\t\t\t\t\t\t\t</u-cell-group>\r\n\t\t\t\t\t\t</scroll-view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</u-popup>\r\n</template>\r\n\r\n<script>\r\n\t/**\r\n\t * city-select 省市区级联选择器\r\n\t * @property {String Number} z-index 弹出时的z-index值（默认1075）\r\n\t * @property {Boolean} mask-close-able 是否允许通过点击遮罩关闭Picker（默认true）\r\n\t * @property {String} default-region 默认选中的地区，中文形式\r\n\t * @property {String} default-code 默认选中的地区，编号形式\r\n\t */\r\n\texport default {\r\n\t\tname: 'city-select',\r\n\t\tprops: {\r\n\t\t\t// 通过双向绑定控制组件的弹出与收起\r\n\t\t\tvalue: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: false\r\n\t\t\t},\r\n\t\t\t// 默认显示的地区，可传类似[\"河北省\", \"秦皇岛市\", \"北戴河区\"]\r\n\t\t\tdefaultRegion: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 默认显示地区的编码，defaultRegion和areaCode同时存在，areaCode优先，可传类似[\"13\", \"1303\", \"130304\"]\r\n\t\t\tareaCode: {\r\n\t\t\t\ttype: Array,\r\n\t\t\t\tdefault () {\r\n\t\t\t\t\treturn [];\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 是否允许通过点击遮罩关闭Picker\r\n\t\t\tmaskCloseAble: {\r\n\t\t\t\ttype: Boolean,\r\n\t\t\t\tdefault: true\r\n\t\t\t},\r\n\t\t\t// 弹出的z-index值\r\n\t\t\tzIndex: {\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 0\r\n\t\t\t},\r\n\t\t\tlevel: {\r\n\t\t\t\ttype: Number,\r\n\t\t\t\tdefault: 3\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcityValue: \"\",\r\n\t\t\t\tisChooseP: false, //是否已经选择了省\r\n\t\t\t\tprovince: 0, //省级下标\r\n\t\t\t\tprovinces: [],\r\n\t\t\t\tisChooseC: false, //是否已经选择了市\r\n\t\t\t\tcity: 0, //市级下标\r\n\t\t\t\tcitys: [],\r\n\t\t\t\tisChooseA: false, //是否已经选择了区\r\n\t\t\t\tarea: 0, //区级下标\r\n\t\t\t\tareas: [],\r\n\t\t\t\tisChooseS: false, //是否已经选择了街道\r\n\t\t\t\tstreet: 0, //街道下标\r\n\t\t\t\tstreets: [],\r\n\t\t\t\ttabsIndex: 0,\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tareaCode: {\r\n\t\t\t\tdeep: true,\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(newVal, oldName) {\r\n\t\t\t\t\tthis.init();\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// this.init();\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tisChange() {\r\n\t\t\t\treturn this.tabsIndex > 1;\r\n\t\t\t},\r\n\t\t\tgenTabsList() {\r\n\t\t\t\tlet tabsList = [{\r\n\t\t\t\t\tname: \"请选择\"\r\n\t\t\t\t}];\r\n\t\t\t\tif (this.isChooseP) {\r\n\t\t\t\t\ttabsList[0]['name'] = this.provinces[this.province]['label'];\r\n\t\t\t\t\ttabsList[1] = {\r\n\t\t\t\t\t\tname: \"请选择\"\r\n\t\t\t\t\t};\r\n\t\t\t\t}\r\n\t\t\t\tif (this.isChooseC) {\r\n\t\t\t\t\ttabsList[1]['name'] = this.citys[this.city]['label'];\r\n\t\t\t\t\tif(this.level >= 3) {\r\n\t\t\t\t\t\ttabsList[2] = {\r\n\t\t\t\t\t\t\tname: \"请选择\"\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.isChooseA && this.level >= 3) {\r\n\t\t\t\t\ttabsList[2]['name'] = this.areas[this.area]['label'];\r\n\t\t\t\t\tif(this.level >= 4) {\r\n\t\t\t\t\t\ttabsList[3] = {\r\n\t\t\t\t\t\t\tname: \"请选择\"\r\n\t\t\t\t\t\t};\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif (this.isChooseS && this.level >= 4) {\r\n\t\t\t\t\ttabsList[3]['name'] = this.streets[this.street]['label'];\r\n\t\t\t\t}\r\n\t\t\t\treturn tabsList;\r\n\t\t\t},\r\n\t\t\tuZIndex() {\r\n\t\t\t\t// 如果用户有传递z-index值，优先使用\r\n\t\t\t\treturn this.zIndex ? this.zIndex : this.$u.zIndex.popup;\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync init() {\r\n\t\t\t\t// 获取所有的省份\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/anab2a\r\n\t\t\t\tconst res = await this.$wxapi.province()\r\n\t\t\t\tthis.provinces = []\r\n\t\t\t\tif(res.code == 0) {\r\n\t\t\t\t\tres.data.forEach(ele => {\r\n\t\t\t\t\t\tthis.provinces.push({\r\n\t\t\t\t\t\t\tlabel: ele.name,\r\n\t\t\t\t\t\t\tvalue: ele.id\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\tif(this.level == 2) {\r\n\t\t\t\t\tif (this.areaCode.length >= 2) {\r\n\t\t\t\t\t\tawait this.setProvince(\"\", this.areaCode[0]);\r\n\t\t\t\t\t\tawait this.setCity(\"\", this.areaCode[1]);\r\n\t\t\t\t\t} else if (this.defaultRegion.length >= 2) {\r\n\t\t\t\t\t\tawait this.setProvince(this.defaultRegion[0], \"\");\r\n\t\t\t\t\t\tawait this.setCity(this.defaultRegion[1], \"\");\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(this.level == 3) {\r\n\t\t\t\t\tif (this.areaCode.length >= 3) {\r\n\t\t\t\t\t\tawait this.setProvince(\"\", this.areaCode[0]);\r\n\t\t\t\t\t\tawait this.setCity(\"\", this.areaCode[1]);\r\n\t\t\t\t\t\tawait this.setArea(\"\", this.areaCode[2]);\r\n\t\t\t\t\t} else if (this.defaultRegion.length >= 3) {\r\n\t\t\t\t\t\tawait this.setProvince(this.defaultRegion[0], \"\");\r\n\t\t\t\t\t\tawait this.setCity(this.defaultRegion[1], \"\");\r\n\t\t\t\t\t\tawait this.setArea(this.defaultRegion[2], \"\");\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tif(this.level == 4) {\r\n\t\t\t\t\tif (this.areaCode.length >= 4) {\r\n\t\t\t\t\t\tawait this.setProvince(\"\", this.areaCode[0]);\r\n\t\t\t\t\t\tawait this.setCity(\"\", this.areaCode[1]);\r\n\t\t\t\t\t\tawait this.setArea(\"\", this.areaCode[2]);\r\n\t\t\t\t\t\tawait this.setStreet(\"\", this.areaCode[3]);\r\n\t\t\t\t\t} else if (this.defaultRegion.length >= 4) {\r\n\t\t\t\t\t\tawait this.setProvince(this.defaultRegion[0], \"\");\r\n\t\t\t\t\t\tawait this.setCity(this.defaultRegion[1], \"\");\r\n\t\t\t\t\t\tawait this.setArea(this.defaultRegion[2], \"\");\r\n\t\t\t\t\t\tawait this.setStreet(this.defaultRegion[3], \"\");\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync setProvince(label = \"\", value = \"\") {\r\n\t\t\t\tconst k = this.provinces.findIndex(v => {\r\n\t\t\t\t\treturn value ? v.value == value : v.label == label\r\n\t\t\t\t})\r\n\t\t\t\tconsole.log(k);\r\n\t\t\t\tif(k != -1) {\r\n\t\t\t\t\tconst v = this.provinces[k]\r\n\t\t\t\t\tawait this.provinceChange({ name: k })\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync setCity(label = \"\", value = \"\") {\r\n\t\t\t\tconst k = this.citys.findIndex(v => {\r\n\t\t\t\t\treturn value ? v.value == value : v.label == label\r\n\t\t\t\t})\r\n\t\t\t\tif(k != -1) {\r\n\t\t\t\t\tconst v = this.citys[k]\r\n\t\t\t\t\tawait this.cityChange({ name: k }, true)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync setArea(label = \"\", value = \"\") {\r\n\t\t\t\tconst k = this.areas.findIndex(v => {\r\n\t\t\t\t\treturn value ? v.value == value : v.label == label\r\n\t\t\t\t})\r\n\t\t\t\tif(k != -1) {\r\n\t\t\t\t\tconst v = this.areas[k]\r\n\t\t\t\t\tif(this.level == 3) {\r\n\t\t\t\t\t\tthis.isChooseA = true;\r\n\t\t\t\t\t\tthis.area = k;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tif(this.level == 4) {\r\n\t\t\t\t\t\tawait this.areaChange({ name: k })\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync setStreet(label = \"\", value = \"\") {\r\n\t\t\t\tconst k = this.streets.findIndex(v => {\r\n\t\t\t\t\treturn value ? v.value == value : v.label == label\r\n\t\t\t\t})\r\n\t\t\t\tif(k != -1) {\r\n\t\t\t\t\tconst v = this.streets[k]\r\n\t\t\t\t\tthis.isChooseS = true;\r\n\t\t\t\t\tthis.street = k;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tclose() {\r\n\t\t\t\tthis.$emit('input', false);\r\n\t\t\t},\r\n\t\t\ttabsChange(e) {\r\n\t\t\t\tthis.tabsIndex = e.index;\r\n\t\t\t\tif(e.index == 0) {\r\n\t\t\t\t\tthis.provinceChange({ name: this.province })\r\n\t\t\t\t}\r\n\t\t\t\tif(e.index == 1) {\r\n\t\t\t\t\tthis.cityChange({ name: this.city }, true)\r\n\t\t\t\t}\r\n\t\t\t\tif(e.index == 2) {\r\n\t\t\t\t\tthis.areaChange({ name: this.area })\r\n\t\t\t\t}\r\n\t\t\t\tif(e.index == 3) {\r\n\t\t\t\t\tthis.streetChange({ name: this.street })\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync provinceChange(e) {\r\n\t\t\t\tconsole.log(e);\r\n\t\t\t\tconst index = e.name\r\n\t\t\t\tthis.isChooseP = true;\r\n\t\t\t\tthis.isChooseC = false;\r\n\t\t\t\tthis.isChooseA = false;\r\n\t\t\t\tthis.isChooseS = false;\r\n\t\t\t\tthis.province = index;\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/kfukig\r\n\t\t\t\tconst res = await this.$wxapi.nextRegion(this.provinces[index].value)\r\n\t\t\t\tthis.citys = []\r\n\t\t\t\tif(res.code == 0) {\r\n\t\t\t\t\tres.data.forEach(ele => {\r\n\t\t\t\t\t\tthis.citys.push({\r\n\t\t\t\t\t\t\tlabel: ele.name,\r\n\t\t\t\t\t\t\tvalue: ele.id\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// 接口读取结束\r\n\t\t\t\tthis.tabsIndex = 1;\r\n\t\t\t},\r\n\t\t\tasync cityChange(e, initAction) {\r\n\t\t\t\tconst index = e.name\r\n\t\t\t\tthis.isChooseC = true;\r\n\t\t\t\tthis.isChooseA = false;\r\n\t\t\t\tthis.isChooseS = false;\r\n\t\t\t\tthis.city = index;\r\n\t\t\t\tif(this.level == 2) {\r\n\t\t\t\t\tif (!initAction) {\r\n\t\t\t\t\t\tlet result = {};\r\n\t\t\t\t\t\tresult.province = this.provinces[this.province];\r\n\t\t\t\t\t\tresult.city = this.citys[this.city];\r\n\t\t\t\t\t\tthis.$emit('city-change', result);\r\n\t\t\t\t\t\tthis.close();\r\n\t\t\t\t\t}\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/kfukig\r\n\t\t\t\tconst res = await this.$wxapi.nextRegion(this.citys[index].value)\r\n\t\t\t\tthis.areas = []\r\n\t\t\t\tif(res.code == 0) {\r\n\t\t\t\t\tres.data.forEach(ele => {\r\n\t\t\t\t\t\tthis.areas.push({\r\n\t\t\t\t\t\t\tlabel: ele.name,\r\n\t\t\t\t\t\t\tvalue: ele.id\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t\t// 接口读取结束\r\n\t\t\t\tthis.tabsIndex = 2;\r\n\t\t\t},\r\n\t\t\tasync areaChange(e) {\r\n\t\t\t\tconst index = e.name\r\n\t\t\t\tthis.isChooseA = true;\r\n\t\t\t\tthis.isChooseS = false;\r\n\t\t\t\tthis.area = index;\r\n\t\t\t\tif(this.level == 3) {\r\n\t\t\t\t\tlet result = {};\r\n\t\t\t\t\tresult.province = this.provinces[this.province];\r\n\t\t\t\t\tresult.city = this.citys[this.city];\r\n\t\t\t\t\tresult.area = this.areas[this.area];\r\n\t\t\t\t\tthis.$emit('city-change', result);\r\n\t\t\t\t\tthis.close();\r\n\t\t\t\t}\r\n\t\t\t\tif(this.level == 4) {\r\n\t\t\t\t\t// https://www.yuque.com/apifm/nu0f75/kfukig\r\n\t\t\t\t\tconst res = await this.$wxapi.nextRegion(this.areas[index].value)\r\n\t\t\t\t\tthis.streets = []\r\n\t\t\t\t\tif(res.code == 0) {\r\n\t\t\t\t\t\tres.data.forEach(ele => {\r\n\t\t\t\t\t\t\tthis.streets.push({\r\n\t\t\t\t\t\t\t\tlabel: ele.name,\r\n\t\t\t\t\t\t\t\tvalue: ele.id\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 接口读取结束\r\n\t\t\t\t\tthis.tabsIndex = 3;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tstreetChange(e) {\r\n\t\t\t\tconst index = e.name\r\n\t\t\t\tthis.isChooseS = true;\r\n\t\t\t\tthis.street = index;\r\n\t\t\t\tlet result = {};\r\n\t\t\t\tresult.province = this.provinces[this.province];\r\n\t\t\t\tresult.city = this.citys[this.city];\r\n\t\t\t\tresult.area = this.areas[this.area];\r\n\t\t\t\tresult.street = this.streets[this.street];\r\n\t\t\t\tthis.$emit('city-change', result);\r\n\t\t\t\tthis.close();\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n</script>\r\n<style lang=\"scss\">\r\n\t.area-box {\r\n\t\twidth: 100%;\r\n\t\toverflow: hidden;\r\n\t\theight: 800rpx;\r\n\r\n\t\t>view {\r\n\t\t\twidth: 150%;\r\n\t\t\ttransition: transform 0.3s ease-in-out 0s;\r\n\t\t\ttransform: translateX(0);\r\n\r\n\t\t\t&.change {\r\n\t\t\t\ttransform: translateX(-0%);\r\n\t\t\t}\r\n\t\t}\r\n\t\t.u-flex {\r\n\t\t\twidth: 100vw;\r\n\t\t\tdisplay: flex;\r\n\t\t\t.area-item {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\theight: 800rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city-select.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./city-select.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688736907\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}