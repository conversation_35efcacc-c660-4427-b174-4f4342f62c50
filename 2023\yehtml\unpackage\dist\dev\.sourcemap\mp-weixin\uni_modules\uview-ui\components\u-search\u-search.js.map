{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-search/u-search.vue?0839", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-search/u-search.vue?98f6", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-search/u-search.vue?11f1", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-search/u-search.vue?4e20", "uni-app:///uni_modules/uview-ui/components/u-search/u-search.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-search/u-search.vue?db29", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-search/u-search.vue?7841"], "names": ["name", "mixins", "data", "keyword", "showClear", "show", "focused", "watch", "value", "immediate", "handler", "computed", "showActionBtn", "methods", "inputChange", "clear", "search", "uni", "custom", "getFocus", "blur", "setTimeout", "clickHandler", "clickIcon"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC3DA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC0EvrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlCA,eAmCA;EACAA;EACAC;EACAC;IACA;MACAC;MACAC;MAAA;MACAC;MACA;MACAC;MACA;MACA;IACA;EACA;;EACAC;IACAJ;MACA;MACA;MACA;MACA;IACA;IACAK;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACAC;MACA;IACA;IACA;IACAC;MACA;MACA;QACA;QACAD;MACA;IACA;IACA;IACAE;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;MACAC;QACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxMA;AAAA;AAAA;AAAA;AAA0xC,CAAgB,8uCAAG,EAAC,C;;;;;;;;;;;ACA9yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-search/u-search.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-search.vue?vue&type=template&id=0a306a29&scoped=true&\"\nvar renderjs\nimport script from \"./u-search.vue?vue&type=script&lang=js&\"\nexport * from \"./u-search.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-search.vue?vue&type=style&index=0&id=0a306a29&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"0a306a29\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-search/u-search.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-search.vue?vue&type=template&id=0a306a29&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([\n    {\n      margin: _vm.margin,\n    },\n    _vm.$u.addStyle(_vm.customStyle),\n  ])\n  var s1 = _vm.__get_style([\n    {\n      textAlign: _vm.inputAlign,\n      color: _vm.color,\n      backgroundColor: _vm.bgColor,\n      height: _vm.$u.addUnit(_vm.height),\n    },\n    _vm.inputStyle,\n  ])\n  var s2 = _vm.__get_style([_vm.actionStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-search.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-search.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t    class=\"u-search\"\r\n\t    @tap=\"clickHandler\"\r\n\t    :style=\"[{\r\n\t\t\tmargin: margin,\r\n\t\t}, $u.addStyle(customStyle)]\"\r\n\t>\r\n\t\t<view\r\n\t\t    class=\"u-search__content\"\r\n\t\t    :style=\"{\r\n\t\t\t\tbackgroundColor: bgColor,\r\n\t\t\t\tborderRadius: shape == 'round' ? '100px' : '4px',\r\n\t\t\t\tborderColor: borderColor,\r\n\t\t\t}\"\r\n\t\t>\r\n\t\t\t<template v-if=\"$slots.label || label !== null\">\r\n\t\t\t\t<slot name=\"label\">\r\n\t\t\t\t\t<text class=\"u-search__content__label\">{{ label }}</text>\r\n\t\t\t\t</slot>\r\n\t\t\t</template>\r\n\t\t\t<view class=\"u-search__content__icon\">\r\n\t\t\t\t<u-icon\r\n\t\t\t\t\t@tap=\"clickIcon\"\r\n\t\t\t\t    :size=\"searchIconSize\"\r\n\t\t\t\t    :name=\"searchIcon\"\r\n\t\t\t\t    :color=\"searchIconColor ? searchIconColor : color\"\r\n\t\t\t\t></u-icon>\r\n\t\t\t</view>\r\n\t\t\t<input\r\n\t\t\t    confirm-type=\"search\"\r\n\t\t\t    @blur=\"blur\"\r\n\t\t\t    :value=\"value\"\r\n\t\t\t    @confirm=\"search\"\r\n\t\t\t    @input=\"inputChange\"\r\n\t\t\t    :disabled=\"disabled\"\r\n\t\t\t    @focus=\"getFocus\"\r\n\t\t\t    :focus=\"focus\"\r\n\t\t\t    :maxlength=\"maxlength\"\r\n\t\t\t    placeholder-class=\"u-search__content__input--placeholder\"\r\n\t\t\t    :placeholder=\"placeholder\"\r\n\t\t\t    :placeholder-style=\"`color: ${placeholderColor}`\"\r\n\t\t\t    class=\"u-search__content__input\"\r\n\t\t\t    type=\"text\"\r\n\t\t\t    :style=\"[{\r\n\t\t\t\t\ttextAlign: inputAlign,\r\n\t\t\t\t\tcolor: color,\r\n\t\t\t\t\tbackgroundColor: bgColor,\r\n\t\t\t\t\theight: $u.addUnit(height)\r\n\t\t\t\t}, inputStyle]\"\r\n\t\t\t/>\r\n\t\t\t<view\r\n\t\t\t    class=\"u-search__content__icon u-search__content__close\"\r\n\t\t\t    v-if=\"keyword && clearabled && focused\"\r\n\t\t\t    @tap=\"clear\"\r\n\t\t\t>\r\n\t\t\t\t<u-icon\r\n\t\t\t\t    name=\"close\"\r\n\t\t\t\t    size=\"11\"\r\n\t\t\t\t    color=\"#ffffff\"\r\n\t\t\t\t\tcustomStyle=\"line-height: 12px\"\r\n\t\t\t\t></u-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<text\r\n\t\t    :style=\"[actionStyle]\"\r\n\t\t    class=\"u-search__action\"\r\n\t\t    :class=\"[(showActionBtn || show) && 'u-search__action--active']\"\r\n\t\t    @tap.stop.prevent=\"custom\"\r\n\t\t>{{ actionText }}</text>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\r\n\t/**\r\n\t * search 搜索框\r\n\t * @description 搜索组件，集成了常见搜索框所需功能，用户可以一键引入，开箱即用。\r\n\t * @tutorial https://www.uviewui.com/components/search.html\r\n\t * @property {String}\t\t\tshape\t\t\t\t搜索框形状，round-圆形，square-方形（默认 'round' ）\r\n\t * @property {String}\t\t\tbgColor\t\t\t\t搜索框背景颜色（默认 '#f2f2f2' ）\r\n\t * @property {String}\t\t\tplaceholder\t\t\t占位文字内容（默认 '请输入关键字' ）\r\n\t * @property {Boolean}\t\t\tclearabled\t\t\t是否启用清除控件（默认 true ）\r\n\t * @property {Boolean}\t\t\tfocus\t\t\t\t是否自动获得焦点（默认 false ）\r\n\t * @property {Boolean}\t\t\tshowAction\t\t\t是否显示右侧控件（默认 true ）\r\n\t * @property {Object}\t\t\tactionStyle\t\t\t右侧控件的样式，对象形式\r\n\t * @property {String}\t\t\tactionText\t\t\t右侧控件文字（默认 '搜索' ）\r\n\t * @property {String}\t\t\tinputAlign\t\t\t输入框内容水平对齐方式 （默认 'left' ）\r\n\t * @property {Object}\t\t\tinputStyle\t\t\t自定义输入框样式，对象形式\r\n\t * @property {Boolean}\t\t\tdisabled\t\t\t是否启用输入框（默认 false ）\r\n\t * @property {String}\t\t\tborderColor\t\t\t边框颜色，配置了颜色，才会有边框 (默认 'transparent' )\r\n\t * @property {String}\t\t\tsearchIconColor\t\t搜索图标的颜色，默认同输入框字体颜色 (默认 '#909399' )\r\n\t * @property {Number | String}\tsearchIconSize 搜索图标的字体，默认22\r\n\t * @property {String}\t\t\tcolor\t\t\t\t输入框字体颜色（默认 '#606266' ）\r\n\t * @property {String}\t\t\tplaceholderColor\tplaceholder的颜色（默认 '#909399' ）\r\n\t * @property {String}\t\t\tsearchIcon\t\t\t输入框左边的图标，可以为uView图标名称或图片路径  (默认 'search' )\r\n\t * @property {String}\t\t\tmargin\t\t\t\t组件与其他上下左右元素之间的距离，带单位的字符串形式，如\"30px\"   (默认 '0' )\r\n\t * @property {Boolean} \t\t\tanimation\t\t\t是否开启动画，见上方说明（默认 false ）\r\n\t * @property {String}\t\t\tvalue\t\t\t\t输入框初始值\r\n\t * @property {String | Number}\tmaxlength\t\t\t输入框最大能输入的长度，-1为不限制长度  (默认 '-1' )\r\n\t * @property {String | Number}\theight\t\t\t\t输入框高度，单位px（默认 64 ）\r\n\t * @property {String | Number}\tlabel\t\t\t\t搜索框左边显示内容\r\n\t * @property {Object}\t\t\tcustomStyle\t\t\t定义需要用到的外部样式\r\n\t *\r\n\t * @event {Function} change 输入框内容发生变化时触发\r\n\t * @event {Function} search 用户确定搜索时触发，用户按回车键，或者手机键盘右下角的\"搜索\"键时触发\r\n\t * @event {Function} custom 用户点击右侧控件时触发\r\n\t * @event {Function} clear 用户点击清除按钮时触发\r\n\t * @example <u-search placeholder=\"日照香炉生紫烟\" v-model=\"keyword\"></u-search>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-search\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tkeyword: '',\r\n\t\t\t\tshowClear: false, // 是否显示右边的清除图标\r\n\t\t\t\tshow: false,\r\n\t\t\t\t// 标记input当前状态是否处于聚焦中，如果是，才会显示右侧的清除控件\r\n\t\t\t\tfocused: this.focus\r\n\t\t\t\t// 绑定输入框的值\r\n\t\t\t\t// inputValue: this.value\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tkeyword(nVal) {\r\n\t\t\t\t// 双向绑定值，让v-model绑定的值双向变化\r\n\t\t\t\tthis.$emit('input', nVal);\r\n\t\t\t\t// 触发change事件，事件效果和v-model双向绑定的效果一样，让用户多一个选择\r\n\t\t\t\tthis.$emit('change', nVal);\r\n\t\t\t},\r\n\t\t\tvalue: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(nVal) {\r\n\t\t\t\t\tthis.keyword = nVal;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tshowActionBtn() {\r\n\t\t\t\treturn !this.animation && this.showAction\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 目前HX2.6.9 v-model双向绑定无效，故监听input事件获取输入框内容的变化\r\n\t\t\tinputChange(e) {\r\n\t\t\t\tthis.keyword = e.detail.value;\r\n\t\t\t},\r\n\t\t\t// 清空输入\r\n\t\t\t// 也可以作为用户通过this.$refs形式调用清空输入框内容\r\n\t\t\tclear() {\r\n\t\t\t\tthis.keyword = '';\r\n\t\t\t\t// 延后发出事件，避免在父组件监听clear事件时，value为更新前的值(不为空)\r\n\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\tthis.$emit('clear');\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\t// 确定搜索\r\n\t\t\tsearch(e) {\r\n\t\t\t\tthis.$emit('search', e.detail.value);\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 收起键盘\r\n\t\t\t\t\tuni.hideKeyboard();\r\n\t\t\t\t} catch (e) {}\r\n\t\t\t},\r\n\t\t\t// 点击右边自定义按钮的事件\r\n\t\t\tcustom() {\r\n\t\t\t\tthis.$emit('custom', this.keyword);\r\n\t\t\t\ttry {\r\n\t\t\t\t\t// 收起键盘\r\n\t\t\t\t\tuni.hideKeyboard();\r\n\t\t\t\t} catch (e) {}\r\n\t\t\t},\r\n\t\t\t// 获取焦点\r\n\t\t\tgetFocus() {\r\n\t\t\t\tthis.focused = true;\r\n\t\t\t\t// 开启右侧搜索按钮展开的动画效果\r\n\t\t\t\tif (this.animation && this.showAction) this.show = true;\r\n\t\t\t\tthis.$emit('focus', this.keyword);\r\n\t\t\t},\r\n\t\t\t// 失去焦点\r\n\t\t\tblur() {\r\n\t\t\t\t// 最开始使用的是监听图标@touchstart事件，自从hx2.8.4后，此方法在微信小程序出错\r\n\t\t\t\t// 这里改为监听点击事件，手点击清除图标时，同时也发生了@blur事件，导致图标消失而无法点击，这里做一个延时\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.focused = false;\r\n\t\t\t\t}, 100)\r\n\t\t\t\tthis.show = false;\r\n\t\t\t\tthis.$emit('blur', this.keyword);\r\n\t\t\t},\r\n\t\t\t// 点击搜索框，只有disabled=true时才发出事件，因为禁止了输入，意味着是想跳转真正的搜索页\r\n\t\t\tclickHandler() {\r\n\t\t\t\tif (this.disabled) this.$emit('click');\r\n\t\t\t},\r\n\t\t\t// 点击左边图标\r\n\t\t\tclickIcon() {\r\n\t\t\t\tthis.$emit('clickIcon');\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"../../libs/css/components.scss\";\r\n$u-search-content-padding: 0 10px !default;\r\n$u-search-label-color: $u-main-color !default;\r\n$u-search-label-font-size: 14px !default;\r\n$u-search-label-margin: 0 4px !default;\r\n$u-search-close-size: 20px !default;\r\n$u-search-close-radius: 100px !default;\r\n$u-search-close-bgColor: #C6C7CB !default;\r\n$u-search-close-transform: scale(0.82) !default;\r\n$u-search-input-font-size: 14px !default;\r\n$u-search-input-margin: 0 5px !default;\r\n$u-search-input-color: $u-main-color !default;\r\n$u-search-input-placeholder-color: $u-tips-color !default;\r\n$u-search-action-font-size: 14px !default;\r\n$u-search-action-color: $u-main-color !default;\r\n$u-search-action-width: 0 !default;\r\n$u-search-action-active-width: 40px !default;\r\n$u-search-action-margin-left: 5px !default;\r\n\r\n/* #ifdef H5 */\r\n// iOS15在H5下，hx的某些版本，input type=search时，会多了一个搜索图标，进行移除\r\n[type=\"search\"]::-webkit-search-decoration {\r\n    display: none;\r\n}\r\n/* #endif */\r\n\r\n.u-search {\r\n\t@include flex(row);\r\n\talign-items: center;\r\n\tflex: 1;\r\n\r\n\t&__content {\r\n\t\t@include flex;\r\n\t\talign-items: center;\r\n\t\tpadding: $u-search-content-padding;\r\n\t\tflex: 1;\r\n\t\tjustify-content: space-between;\r\n\t\tborder-width: 1px;\r\n\t\tborder-color: transparent;\r\n\t\tborder-style: solid;\r\n\t\toverflow: hidden;\r\n\r\n\t\t&__icon {\r\n\t\t\t@include flex;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t&__label {\r\n\t\t\tcolor: $u-search-label-color;\r\n\t\t\tfont-size: $u-search-label-font-size;\r\n\t\t\tmargin: $u-search-label-margin;\r\n\t\t}\r\n\r\n\t\t&__close {\r\n\t\t\twidth: $u-search-close-size;\r\n\t\t\theight: $u-search-close-size;\r\n\t\t\tborder-top-left-radius: $u-search-close-radius;\r\n\t\t\tborder-top-right-radius: $u-search-close-radius;\r\n\t\t\tborder-bottom-left-radius: $u-search-close-radius;\r\n\t\t\tborder-bottom-right-radius: $u-search-close-radius;\r\n\t\t\tbackground-color: $u-search-close-bgColor;\r\n\t\t\t@include flex(row);\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\ttransform: $u-search-close-transform;\r\n\t\t}\r\n\r\n\t\t&__input {\r\n\t\t\tflex: 1;\r\n\t\t\tfont-size: $u-search-input-font-size;\r\n\t\t\tline-height: 1;\r\n\t\t\tmargin: $u-search-input-margin;\r\n\t\t\tcolor: $u-search-input-color;\r\n\r\n\t\t\t&--placeholder {\r\n\t\t\t\tcolor: $u-search-input-placeholder-color;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t&__action {\r\n\t\tfont-size: $u-search-action-font-size;\r\n\t\tcolor: $u-search-action-color;\r\n\t\twidth: $u-search-action-width;\r\n\t\toverflow: hidden;\r\n\t\ttransition-property: width;\r\n\t\ttransition-duration: 0.3s;\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\twhite-space: nowrap;\r\n\t\t/* #endif */\r\n\t\ttext-align: center;\r\n\r\n\t\t&--active {\r\n\t\t\twidth: $u-search-action-active-width;\r\n\t\t\tmargin-left: $u-search-action-margin-left;\r\n\t\t}\r\n\t}\r\n}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-search.vue?vue&type=style&index=0&id=0a306a29&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-search.vue?vue&type=style&index=0&id=0a306a29&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692289607\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}