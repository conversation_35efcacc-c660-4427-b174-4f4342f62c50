(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/page-box-empty/page-box-empty"],{"536a":function(t,n,e){"use strict";e.d(n,"b",(function(){return a})),e.d(n,"c",(function(){return u})),e.d(n,"a",(function(){}));var a=function(){var t=this.$createElement;this._self._c},u=[]},"584a":function(t,n,e){"use strict";e.r(n);var a=e("fad5"),u=e.n(a);for(var i in a)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return a[t]}))}(i);n["default"]=u.a},"6fb7":function(t,n,e){},ad30:function(t,n,e){"use strict";var a=e("6fb7"),u=e.n(a);u.a},bc43:function(t,n,e){"use strict";e.r(n);var a=e("536a"),u=e("584a");for(var i in u)["default"].indexOf(i)<0&&function(t){e.d(n,t,(function(){return u[t]}))}(i);e("ad30");var f=e("828b"),o=Object(f["a"])(u["default"],a["b"],a["c"],!1,null,"53f2f502",null,!1,a["a"],void 0);n["default"]=o.exports},fad5:function(t,n,e){"use strict";(function(t){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var e={name:"page-box-empty",props:{title:{type:String,default:"暂无数据"},subTitle:{type:String,default:""},btnName:{type:String,default:"随便逛逛"},url:{type:String,default:""},showBtn:{type:Boolean,default:!1}},data:function(){return{}},created:function(){},mounted:function(){},onReady:function(){},methods:{goIndex:function(){this.url?t.navigateTo({url:this.url}):t.switchTab({url:"../../pages/index/index"})}}};n.default=e}).call(this,e("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/page-box-empty/page-box-empty-create-component',
    {
        'components/page-box-empty/page-box-empty-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("bc43"))
        })
    },
    [['components/page-box-empty/page-box-empty-create-component']]
]);
