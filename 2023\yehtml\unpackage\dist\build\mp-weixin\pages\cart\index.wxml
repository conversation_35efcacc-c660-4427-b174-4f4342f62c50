<view class="cart data-v-3ef9fc5c"><view class="tip data-v-3ef9fc5c">* 向左滑动可以删除项目</view><block wx:if="{{!shippingCarInfo||shippingCarInfo.number==0}}"><page-box-empty vue-id="6a21c8ac-1" title="您还没有挑选任何商品" sub-title="可以去看看有那些想买的～" show-btn="{{true}}" class="data-v-3ef9fc5c" bind:__l="__l"></page-box-empty></block><block wx:if="{{shippingCarInfo}}"><view class="order data-v-3ef9fc5c"><u-swipe-action vue-id="6a21c8ac-2" class="data-v-3ef9fc5c" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{shippingCarInfo.items}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-swipe-action-item vue-id="{{('6a21c8ac-3-'+index)+','+('6a21c8ac-2')}}" show="{{item.show}}" index="{{index}}" options="{{options}}" data-event-opts="{{[['^click',[['deleterecord0']]]]}}" bind:click="__e" class="data-v-3ef9fc5c" bind:__l="__l" vue-slots="{{['default']}}"><view class="item data-v-3ef9fc5c"><view class="left data-v-3ef9fc5c"><image src="{{item.pic}}" mode="aspectFill" class="data-v-3ef9fc5c"></image></view><view class="content data-v-3ef9fc5c"><view class="title data-v-3ef9fc5c">{{item.name}}</view><view class="type data-v-3ef9fc5c"><block wx:for="{{item.sku}}" wx:for-item="item2" wx:for-index="index2"><text class="data-v-3ef9fc5c">{{item2.optionName+":"+item2.optionValueName+"/"}}</text></block><block wx:for="{{item.additions}}" wx:for-item="item3" wx:for-index="index3"><text class="data-v-3ef9fc5c">{{item3.pname+":"+item3.name+"/"}}</text></block></view><view class="delivery-time data-v-3ef9fc5c"><u-number-box vue-id="{{('6a21c8ac-4-'+index)+','+('6a21c8ac-3-'+index)}}" name="{{index}}" min="{{item.minBuyNumber}}" max="{{item.stores}}" value="{{item.number}}" data-event-opts="{{[['^change',[['numberChange0']]],['^input',[['__set_model',['$0','number','$event',[]],[[['shippingCarInfo.items','',index]]]]]]]}}" bind:change="__e" bind:input="__e" class="data-v-3ef9fc5c" bind:__l="__l"></u-number-box></view></view><view class="right data-v-3ef9fc5c"><view class="price data-v-3ef9fc5c"><text class="data-v-3ef9fc5c">￥</text>{{item.price+''}}</view></view></view></u-swipe-action-item></block></u-swipe-action><view class="settlement-box data-v-3ef9fc5c"><view class="left-price data-v-3ef9fc5c"><view class="total data-v-3ef9fc5c">共<text class="number data-v-3ef9fc5c">{{shippingCarInfo.number}}</text>件商品 合计<text class="total-price data-v-3ef9fc5c"><text class="data-v-3ef9fc5c">￥</text>{{shippingCarInfo.price+''}}</text></view></view><block wx:if="{{shippingCarInfo&&shippingCarInfo.number>0}}"><view data-event-opts="{{[['tap',[['submit0',['$event']]]]]}}" class="to-pay-btn data-v-3ef9fc5c" bindtap="__e">结算</view></block></view></view></block><view class="recommend data-v-3ef9fc5c"><view class="goods-container data-v-3ef9fc5c"><view class="recommend-title data-v-3ef9fc5c"><image style="height:36px;" src="/static/images/cart/recommend-title.jpg" class="_img data-v-3ef9fc5c"></image></view><block wx:if="{{goodsRecommend}}"><view class="goodsRecommend data-v-3ef9fc5c"><view class="goods-container data-v-3ef9fc5c"><list1 vue-id="6a21c8ac-5" list="{{goodsRecommend}}" type="goods" class="data-v-3ef9fc5c" bind:__l="__l"></list1></view></view></block></view></view></view>