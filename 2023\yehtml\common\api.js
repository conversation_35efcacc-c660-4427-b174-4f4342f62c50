import moment from 'moment'
let currentTimestamp = moment().unix()

const http = uni.$u.http

export const oauthToken = (params, config = {}) => http.post('/oauth/token', params, config)
export const userGet = (params, config = {}) => http.get('/user/' + params.uid + '?_format=json&time=' + currentTimestamp, params)

//export const openIdGet = (params, config = {}) => http.get('/getJiShuangOpenId?code=' + params.code, params, config)