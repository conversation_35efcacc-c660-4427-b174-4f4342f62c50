<view class="three1"><view><block wx:if="{{list[1].name}}"><u-row vue-id="42e7afcc-1" customStyle="padding: 12px;margin-bottom: 10px;" gutter="12" bind:__l="__l" vue-slots="{{['default']}}"><u-col vue-id="{{('42e7afcc-2')+','+('42e7afcc-1')}}" span="6" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['goDetail',['$0'],['list.__$n1']]]]]}}" class="right" bindtap="__e"><view class="right-top" style="{{'background-image:'+('url('+list[1].pic+')')+';'}}"></view><view class="right-bottom"><view class="title">{{list[1].name}}</view><view class="price"><view class="price-right"><label class="prefix _span">￥</label><label class="_span">38</label></view><view class="price-left"><view class="original-price"><label class="_span">{{"原价￥"+list[1].originalPrice}}</label>{{'/ '+list[1].unit}}</view><view class="only">仅剩<label class="_span">{{list[1].stores}}</label>只</view></view></view></view></view><view data-event-opts="{{[['tap',[['goDetail',['$0'],['list.__$n2']]]]]}}" class="right" style="margin-top:20rpx;" bindtap="__e"><view class="right-top" style="{{'background-image:'+('url('+list[2].pic+')')+';'}}"></view><view class="right-bottom"><view class="title">{{list[2].name}}</view><view class="price"><view class="price-right"><label class="prefix _span">￥</label><label class="_span">38</label></view><view class="price-left"><view class="original-price"><label class="_span">{{"原价￥"+list[2].originalPrice}}</label>{{'/ '+list[2].minPrice+''}}</view><view class="only">仅剩<label class="_span">{{list[2].stores}}</label>只</view></view></view></view></view></u-col><u-col vue-id="{{('42e7afcc-3')+','+('42e7afcc-1')}}" span="6" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['goDetail',['$0'],['list.__$n0']]]]]}}" class="left" bindtap="__e"><view class="left-top" style="{{'background-image:'+('url('+list[0].pic+')')+';'}}"></view><view class="left-bottom"><view class="title">{{list[0].name}}</view><view class="dsc">{{$root.m0}}</view><view class="price"><view class="price-left"><view class="price-tip">抢购价</view><view class="only">仅剩<label class="_span">{{list[0].stores}}</label>只</view></view><view class="price-right"><label class="prefix _span">￥</label><label class="_span">{{list[0].minPrice}}</label></view></view></view></view></u-col></u-row></block><block wx:if="{{!list[0].name}}"><view style="padding:12px;margin-bottom:10px;"><u-skeleton vue-id="42e7afcc-4" rows="3" bind:__l="__l"></u-skeleton></view></block></view></view>