<view class="data-v-cd6afd1e"><u-empty vue-id="35520879-1" mode="permission" text="重置登陆密码" marginTop="88rpx" class="data-v-cd6afd1e" bind:__l="__l"></u-empty><view class="form-box data-v-cd6afd1e"><u-form vue-id="35520879-2" label-width="150rpx" model="{{form}}" data-ref="uForm" class="data-v-cd6afd1e vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('35520879-3')+','+('35520879-2')}}" label="手机号码" prop="mobile" required="{{true}}" class="data-v-cd6afd1e" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('35520879-4')+','+('35520879-3')}}" type="number" clearable="{{true}}" maxlength="11" focus="{{true}}" placeholder="请输入手机号码" value="{{form.mobile}}" data-event-opts="{{[['^input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" class="data-v-cd6afd1e" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('35520879-5')+','+('35520879-2')}}" label="图片验证码" prop="imgcode" required="{{true}}" class="data-v-cd6afd1e" bind:__l="__l" vue-slots="{{['default','right']}}"><u-input bind:input="__e" vue-id="{{('35520879-6')+','+('35520879-5')}}" type="number" clearable="{{true}}" maxlength="4" focus="{{true}}" placeholder="请输入图片验证码" value="{{form.imgcode}}" data-event-opts="{{[['^input',[['__set_model',['$0','imgcode','$event',[]],['form']]]]]}}" class="data-v-cd6afd1e" bind:__l="__l"></u-input><view slot="right" class="data-v-cd6afd1e"><u-image vue-id="{{('35520879-7')+','+('35520879-5')}}" showLoading="{{true}}" src="{{imgsrc}}" width="200rpx" height="80rpx" data-event-opts="{{[['^click',[['changeImgCode']]]]}}" bind:click="__e" class="data-v-cd6afd1e" bind:__l="__l"></u-image></view></u-form-item><u-form-item vue-id="{{('35520879-8')+','+('35520879-2')}}" label="短信验证码" prop="code" required="{{true}}" class="data-v-cd6afd1e" bind:__l="__l" vue-slots="{{['default','right']}}"><u-input bind:input="__e" vue-id="{{('35520879-9')+','+('35520879-8')}}" type="number" clearable="{{true}}" maxlength="4" focus="{{true}}" placeholder="请输入短信验证码" value="{{form.code}}" data-event-opts="{{[['^input',[['__set_model',['$0','code','$event',[]],['form']]]]]}}" class="data-v-cd6afd1e" bind:__l="__l"></u-input><view style="padding-left:24rpx;" slot="right" class="data-v-cd6afd1e"><u-toast vue-id="{{('35520879-10')+','+('35520879-8')}}" data-ref="uToast" class="data-v-cd6afd1e vue-ref" bind:__l="__l"></u-toast><u-code vue-id="{{('35520879-11')+','+('35520879-8')}}" seconds="{{seconds}}" keepRunning="{{true}}" data-ref="uCode" data-event-opts="{{[['^end',[['end']]],['^start',[['start']]],['^change',[['codeChange']]]]}}" bind:end="__e" bind:start="__e" bind:change="__e" class="data-v-cd6afd1e vue-ref" bind:__l="__l"></u-code><u-button vue-id="{{('35520879-12')+','+('35520879-8')}}" type="success" size="small" data-event-opts="{{[['^tap',[['getCode']]]]}}" bind:tap="__e" class="data-v-cd6afd1e" bind:__l="__l" vue-slots="{{['default']}}">{{tips}}</u-button></view></u-form-item><u-form-item vue-id="{{('35520879-13')+','+('35520879-2')}}" label="新密码" prop="pwd" required="{{true}}" class="data-v-cd6afd1e" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('35520879-14')+','+('35520879-13')}}" type="password" clearable="{{true}}" placeholder="请输入新的登陆密码" value="{{form.pwd}}" data-event-opts="{{[['^input',[['__set_model',['$0','pwd','$event',[]],['form']]]]]}}" class="data-v-cd6afd1e" bind:__l="__l"></u-input></u-form-item></u-form></view><view class="submit data-v-cd6afd1e"><u-button vue-id="35520879-15" type="success" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" class="data-v-cd6afd1e" bind:__l="__l" vue-slots="{{['default']}}">立即重置密码</u-button><view class="text-btns data-v-cd6afd1e"><view data-event-opts="{{[['tap',[['goLogin',['$event']]]]]}}" bindtap="__e" class="data-v-cd6afd1e">已有账号？直接登陆</view></view></view></view>