<view class="shopcart data-v-78435283"><view class="cartBottom data-v-78435283"><view data-event-opts="{{[['tap',[['toggleList',['$event']]]]]}}" class="carIcon data-v-78435283" bindtap="__e"><view class="{{['iconBox','data-v-78435283',getAllCount?'active':'']}}"><block wx:if="{{getAllCount}}"><text class="allcount data-v-78435283">{{getAllCount}}</text></block><image class="img data-v-78435283" src="/static/cart.png" mode></image></view></view><view data-event-opts="{{[['tap',[['toggleList',['$event']]]]]}}" class="middle data-v-78435283" bindtap="__e"><text class="{{['price','data-v-78435283',getAllCount?'active':'']}}">{{"￥"+getAllPrice}}</text></view><view class="BtnRight data-v-78435283"><button style="border-radius:20rpx;" type="{{buyType}}" disabled="{{buyDis}}" data-event-opts="{{[['tap',[['buyList',['$event']]]]]}}" bindtap="__e" class="data-v-78435283">去结算</button></view></view><view hidden="{{!($root.g0)}}" class="cartList data-v-78435283"><scroll-view style="max-height:200px;" scroll-y="{{true}}" class="data-v-78435283"><view class="title data-v-78435283"><text class="data-v-78435283">购物车</text><view data-event-opts="{{[['tap',[['delShopcart',['$event']]]]]}}" class="clear data-v-78435283" bindtap="__e">清空</view></view><view class="list data-v-78435283"><block wx:for="{{getList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="list-text data-v-78435283"><text style="flex:1;" class="data-v-78435283">{{item.name}}</text><text style="flex:1;" class="data-v-78435283">{{"￥"+item.price}}</text><cartcontrol vue-id="{{'2279a6d5-1-'+index}}" food="{{item}}" data-event-opts="{{[['^add',[['addCart']]],['^dec',[['decreaseCart']]],['^input',[['inputCart']]]]}}" bind:add="__e" bind:dec="__e" bind:input="__e" class="data-v-78435283" bind:__l="__l"></cartcontrol></view></block></view></scroll-view></view><view data-event-opts="{{[['tap',[['toggleList',['$event']]]]]}}" hidden="{{!($root.g1)}}" class="listMask data-v-78435283" bindtap="__e"></view></view>