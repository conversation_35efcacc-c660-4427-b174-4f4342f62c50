(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/city-select/city-select"],{"15ea":function(e,t,n){"use strict";n.d(t,"b",(function(){return r})),n.d(t,"c",(function(){return i})),n.d(t,"a",(function(){return a}));var a={uPopup:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-popup/u-popup")]).then(n.bind(null,"d8d0"))},uTabs:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-tabs/u-tabs")]).then(n.bind(null,"3ebd"))},uCellGroup:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-cell-group/u-cell-group")]).then(n.bind(null,"4446"))},uCell:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-cell/u-cell")]).then(n.bind(null,"819c"))},uIcon:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-icon/u-icon")]).then(n.bind(null,"5f3a"))}},r=function(){var e=this.$createElement;this._self._c},i=[]},2428:function(e,t,n){"use strict";var a=n("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var r=a(n("7eb4")),i=a(n("ee10")),s={name:"city-select",props:{value:{type:Boolean,default:!1},defaultRegion:{type:Array,default:function(){return[]}},areaCode:{type:Array,default:function(){return[]}},maskCloseAble:{type:Boolean,default:!0},zIndex:{type:[String,Number],default:0},level:{type:Number,default:3}},data:function(){return{cityValue:"",isChooseP:!1,province:0,provinces:[],isChooseC:!1,city:0,citys:[],isChooseA:!1,area:0,areas:[],isChooseS:!1,street:0,streets:[],tabsIndex:0}},watch:{areaCode:{deep:!0,immediate:!0,handler:function(e,t){this.init()}}},mounted:function(){},computed:{isChange:function(){return this.tabsIndex>1},genTabsList:function(){var e=[{name:"请选择"}];return this.isChooseP&&(e[0]["name"]=this.provinces[this.province]["label"],e[1]={name:"请选择"}),this.isChooseC&&(e[1]["name"]=this.citys[this.city]["label"],this.level>=3&&(e[2]={name:"请选择"})),this.isChooseA&&this.level>=3&&(e[2]["name"]=this.areas[this.area]["label"],this.level>=4&&(e[3]={name:"请选择"})),this.isChooseS&&this.level>=4&&(e[3]["name"]=this.streets[this.street]["label"]),e},uZIndex:function(){return this.zIndex?this.zIndex:this.$u.zIndex.popup}},methods:{init:function(){var e=this;return(0,i.default)(r.default.mark((function t(){var n;return r.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,e.$wxapi.province();case 2:if(n=t.sent,e.provinces=[],0==n.code&&n.data.forEach((function(t){e.provinces.push({label:t.name,value:t.id})})),2!=e.level){t.next=18;break}if(!(e.areaCode.length>=2)){t.next=13;break}return t.next=9,e.setProvince("",e.areaCode[0]);case 9:return t.next=11,e.setCity("",e.areaCode[1]);case 11:t.next=18;break;case 13:if(!(e.defaultRegion.length>=2)){t.next=18;break}return t.next=16,e.setProvince(e.defaultRegion[0],"");case 16:return t.next=18,e.setCity(e.defaultRegion[1],"");case 18:if(3!=e.level){t.next=35;break}if(!(e.areaCode.length>=3)){t.next=28;break}return t.next=22,e.setProvince("",e.areaCode[0]);case 22:return t.next=24,e.setCity("",e.areaCode[1]);case 24:return t.next=26,e.setArea("",e.areaCode[2]);case 26:t.next=35;break;case 28:if(!(e.defaultRegion.length>=3)){t.next=35;break}return t.next=31,e.setProvince(e.defaultRegion[0],"");case 31:return t.next=33,e.setCity(e.defaultRegion[1],"");case 33:return t.next=35,e.setArea(e.defaultRegion[2],"");case 35:if(4!=e.level){t.next=56;break}if(!(e.areaCode.length>=4)){t.next=47;break}return t.next=39,e.setProvince("",e.areaCode[0]);case 39:return t.next=41,e.setCity("",e.areaCode[1]);case 41:return t.next=43,e.setArea("",e.areaCode[2]);case 43:return t.next=45,e.setStreet("",e.areaCode[3]);case 45:t.next=56;break;case 47:if(!(e.defaultRegion.length>=4)){t.next=56;break}return t.next=50,e.setProvince(e.defaultRegion[0],"");case 50:return t.next=52,e.setCity(e.defaultRegion[1],"");case 52:return t.next=54,e.setArea(e.defaultRegion[2],"");case 54:return t.next=56,e.setStreet(e.defaultRegion[3],"");case 56:case"end":return t.stop()}}),t)})))()},setProvince:function(){var e=arguments,t=this;return(0,i.default)(r.default.mark((function n(){var a,i,s;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=e.length>0&&void 0!==e[0]?e[0]:"",i=e.length>1&&void 0!==e[1]?e[1]:"",s=t.provinces.findIndex((function(e){return i?e.value==i:e.label==a})),console.log(s),-1==s){n.next=8;break}return t.provinces[s],n.next=8,t.provinceChange({name:s});case 8:case"end":return n.stop()}}),n)})))()},setCity:function(){var e=arguments,t=this;return(0,i.default)(r.default.mark((function n(){var a,i,s;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=e.length>0&&void 0!==e[0]?e[0]:"",i=e.length>1&&void 0!==e[1]?e[1]:"",s=t.citys.findIndex((function(e){return i?e.value==i:e.label==a})),-1==s){n.next=7;break}return t.citys[s],n.next=7,t.cityChange({name:s},!0);case 7:case"end":return n.stop()}}),n)})))()},setArea:function(){var e=arguments,t=this;return(0,i.default)(r.default.mark((function n(){var a,i,s;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=e.length>0&&void 0!==e[0]?e[0]:"",i=e.length>1&&void 0!==e[1]?e[1]:"",s=t.areas.findIndex((function(e){return i?e.value==i:e.label==a})),-1==s){n.next=9;break}if(t.areas[s],3==t.level&&(t.isChooseA=!0,t.area=s),4!=t.level){n.next=9;break}return n.next=9,t.areaChange({name:s});case 9:case"end":return n.stop()}}),n)})))()},setStreet:function(){var e=arguments,t=this;return(0,i.default)(r.default.mark((function n(){var a,i,s;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:a=e.length>0&&void 0!==e[0]?e[0]:"",i=e.length>1&&void 0!==e[1]?e[1]:"",s=t.streets.findIndex((function(e){return i?e.value==i:e.label==a})),-1!=s&&(t.streets[s],t.isChooseS=!0,t.street=s);case 4:case"end":return n.stop()}}),n)})))()},close:function(){this.$emit("input",!1)},tabsChange:function(e){this.tabsIndex=e.index,0==e.index&&this.provinceChange({name:this.province}),1==e.index&&this.cityChange({name:this.city},!0),2==e.index&&this.areaChange({name:this.area}),3==e.index&&this.streetChange({name:this.street})},provinceChange:function(e){var t=this;return(0,i.default)(r.default.mark((function n(){var a,i;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return console.log(e),a=e.name,t.isChooseP=!0,t.isChooseC=!1,t.isChooseA=!1,t.isChooseS=!1,t.province=a,n.next=9,t.$wxapi.nextRegion(t.provinces[a].value);case 9:i=n.sent,t.citys=[],0==i.code&&i.data.forEach((function(e){t.citys.push({label:e.name,value:e.id})})),t.tabsIndex=1;case 13:case"end":return n.stop()}}),n)})))()},cityChange:function(e,t){var n=this;return(0,i.default)(r.default.mark((function a(){var i,s,u;return r.default.wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(i=e.name,n.isChooseC=!0,n.isChooseA=!1,n.isChooseS=!1,n.city=i,2!=n.level){a.next=8;break}return t||(s={},s.province=n.provinces[n.province],s.city=n.citys[n.city],n.$emit("city-change",s),n.close()),a.abrupt("return");case 8:return a.next=10,n.$wxapi.nextRegion(n.citys[i].value);case 10:u=a.sent,n.areas=[],0==u.code&&u.data.forEach((function(e){n.areas.push({label:e.name,value:e.id})})),n.tabsIndex=2;case 14:case"end":return a.stop()}}),a)})))()},areaChange:function(e){var t=this;return(0,i.default)(r.default.mark((function n(){var a,i,s;return r.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(a=e.name,t.isChooseA=!0,t.isChooseS=!1,t.area=a,3==t.level&&(i={},i.province=t.provinces[t.province],i.city=t.citys[t.city],i.area=t.areas[t.area],t.$emit("city-change",i),t.close()),4!=t.level){n.next=12;break}return n.next=8,t.$wxapi.nextRegion(t.areas[a].value);case 8:s=n.sent,t.streets=[],0==s.code&&s.data.forEach((function(e){t.streets.push({label:e.name,value:e.id})})),t.tabsIndex=3;case 12:case"end":return n.stop()}}),n)})))()},streetChange:function(e){var t=e.name;this.isChooseS=!0,this.street=t;var n={};n.province=this.provinces[this.province],n.city=this.citys[this.city],n.area=this.areas[this.area],n.street=this.streets[this.street],this.$emit("city-change",n),this.close()}}};t.default=s},"3a30":function(e,t,n){"use strict";n.r(t);var a=n("2428"),r=n.n(a);for(var i in a)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return a[e]}))}(i);t["default"]=r.a},"409e":function(e,t,n){},"4f97":function(e,t,n){"use strict";var a=n("409e"),r=n.n(a);r.a},7021:function(e,t,n){"use strict";n.r(t);var a=n("15ea"),r=n("3a30");for(var i in r)["default"].indexOf(i)<0&&function(e){n.d(t,e,(function(){return r[e]}))}(i);n("4f97");var s=n("828b"),u=Object(s["a"])(r["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);t["default"]=u.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/city-select/city-select-create-component',
    {
        'components/city-select/city-select-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7021"))
        })
    },
    [['components/city-select/city-select-create-component']]
]);
