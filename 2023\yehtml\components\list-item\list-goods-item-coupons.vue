<template>
  <view class="goods-box-wrapper">
    <view class="goods-box">
      <view @click="getCounpon(item)">
        <view class="goods-title-box">
          <view class="goods-title">{{ item.name }}</view>
        </view>
        <view class="goods-dsc-box">
          <view class="goods-dsc">年货送礼，自由消费</view>
        </view>
        <view class="goods-tags-box">
          <view class="goods-tags">
            <span>全品通用</span><span>可赠送</span>
          </view>
        </view>
      </view>
      <view class="buy-wrapper" style="display: flex;">
        <view class="price-score">
          <view v-if="item.moneyMin" class="item">
            <view><text>¥</text>{{item.moneyMin}}</view>
          </view>
        </view>
        <view class="buy" @click="getCounpon(item)">
          <u-icon name="shopping-cart" color="#FFFFFF" size="28"></u-icon>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      item: {
        type: Object,
        default: {},
      },
    },
    onReady() {},
    data() {
      return {
        timeData: {}
      }
    },
    methods: {
      async getCounpon(item, pwd) {
        if (!await getApp().checkHasLoginedH5()) {
          uni.navigateTo({
            url: "/pages/login/login"
          })
          return
        }
        this.curItem = item
        this.couponPwdShow = false
        if (item.pwd && !pwd) {
          this.couponPwdShow = true
          this.couponPwd = ''
          return
        }
        // https://www.yuque.com/apifm/nu0f75/dhxcpu
        const res = await this.$wxapi.fetchCoupons({
          id: item.id,
          token: this.token,
          pwd: this.couponPwd ? this.couponPwd : ''
        })
        if (res.code == 700) {
          if (pwd) {
            uni.showToast({
              title: '口令输入有误',
              icon: 'none'
            })
          } else {
            uni.showToast({
              title: '礼品卡不存在',
              icon: 'none'
            })
          }
          return;
        }
        if (res.code == 20001 || res.code == 20002) {
          uni.showModal({
            title: '错误',
            content: '来晚了',
            showCancel: false
          })
          return;
        }
        if (res.code == 20003) {
          uni.showModal({
            title: '错误',
            content: '你领过了，别贪心哦~',
            showCancel: false
          })
          return;
        }
        if (res.code == 30001) {
          uni.showModal({
            title: '错误',
            content: '您的积分不足',
            showCancel: false
          })
          return;
        }
        if (res.code == 20004) {
          uni.showModal({
            title: '错误',
            content: '已过期~',
            showCancel: false
          })
          return;
        }
        if (res.code == 0) {
          uni.showToast({
            title: '购买成功',
            icon: 'success'
          })
        } else {
          uni.showModal({
            title: '错误',
            content: res.msg,
            showCancel: false
          })
        }
      },
    },
  }
</script>
<style>
  .goods-box-wrapper {
    margin-top: 24rpx;
    border-radius: 5px;
    padding-bottom: 10rpx;
    text-align: left;
    overflow: hidden;
  }

  .goods-box {
    padding: 0 10rpx;
  }

  .goods-box .goods-title-box {
    line-height: 18px;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }

  .goods-box .goods-title {
    color: #2b2b2b;
    font-size: 30rpx;
  }

  .goods-box .goods-dsc-box {
    margin-top: 4rpx;
  }

  .goods-box .goods-dsc {
    color: #858996;
    font-size: 24rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .goods-box .goods-tags span {
    font-size: 22rpx;
    font-weight: normal;
    color: #858996;
    border: 1px #D9DBDF solid;
    margin-right: 10rpx;
    border-radius: 2px;
    padding: 0 4rpx;
    line-height: 32rpx;
  }

  .goods-box .goods-price-container {
    display: flex;
    align-items: baseline;
  }

  .goods-box .goods-price {
    overflow: hidden;
    font-size: 34rpx;
    color: #F20C32;
    margin-left: 24rpx;
  }

  .goods-box .goods-price2 {
    overflow: hidden;
    font-size: 26rpx;
    color: #aaa;
    text-decoration: line-through;
    margin-left: 20rpx;
  }

  .goods-box .buy-wrapper {
    margin-top: 10rpx;
    display: flex;
    justify-content: space-between;
  }

  .goods-box .buy {
    width: 52rpx;
    height: 52rpx;
    background-color: #34B764;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4rpx;
  }

  .goods-box .price-score text {
    margin-top: 6rpx;
  }

  .goods-box .original-price {
    color: #858996;
    font-size: 20rpx;
    font-weight: 100;
    line-height: 20rpx;
  }

  .goods-box .original-price .price {
    text-decoration: line-through;
  }

  .goods-quota {
    margin-top: 20rpx;
    width: 300rpx;
  }
</style>
<style lang="scss">
  .time {
    &__custom {
      background-color: #e64340;
    }

    &__doc {
      color: #324A43;
      padding: 0px 2px;
      margin-top: 5px;
    }

    &__item {
      color: #606266;
      font-size: 10px;
      margin-right: 2px;
    }
  }
</style>