{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/pay/order.vue?d8f9", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/pay/order.vue?8b27", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/pay/order.vue?0995", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/pay/order.vue?9bd0", "uni-app:///pages/pay/order.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/pay/order.vue?2238", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/pay/order.vue?f0cc"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "mod", "cartType", "couponId", "defaultAddress", "goodsList", "goodsNumber", "goodsPrice", "goodsScore", "remark", "canSubmit", "orderInfo", "userAmount", "goodsType", "kjid", "logisticsId", "card", "isSelfPickup", "onLoad", "onShow", "methods", "_defaultAddress", "res", "couponChange", "onSelfPickupChange", "priceChange", "uni", "amountLogistics", "type", "title", "_userAmount", "readCartApifm", "supplyType", "supplyTypeHasEmpty", "supplyTypeCanBuy", "key", "goodsId", "goodsName", "number", "pic", "price", "score", "sku", "additions", "icon", "readCartJdVop", "buildOrderParams", "g", "propertyChildIds", "optionValueId", "goodsAdditionList", "pid", "id", "goodsJsonStr", "token", "calculate", "calculatePrice", "orderParams", "moneyUnit", "ele", "moneyHreshold", "address", "console", "submit", "keys", "pay", "needPay", "url", "PAY", "appid"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,4UAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,8SAEN;AACP,KAAK;AACL;AACA,aAAa,0TAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,grBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACqJprB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eAEA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MAAA;MACAC;IACA;EACA;EACAC;IAAA;IACA;MACA;IACA;IACA;IACA;IACA;MACA;QACA;MACA;MACA;QACA;QACA;MACA;IACA;IACA;MACA;MACA;QACA;QACA;QACA;QACA;QACA;UACA;QACA;QACA;UACA;QACA;MACA;MACA;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;kBACAC;kBACAC;kBACAjB;gBACA;kBACAe;oBACAG;kBACA;;kBAEA;kBACAlB;kBAEA;gBACA;kBACA;gBAAA,CACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAR;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAT;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAU;gBACAC;gBACAC;gBACA;gBACA;gBACAZ;kBACA;oBACAW;kBACA;kBACA;oBACAD;kBACA;kBACA;oBACAE;kBACA;kBACA;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBACAC;oBAAA;oBACAC;kBACA;gBACA;;gBACA;kBACAT;gBACA;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBACAR;kBACAG;kBACAe;gBACA;gBAAA;cAAA;gBAGA;kBACA;gBACA;kBACA;gBACA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAvB;gBACA;kBACA;kBACA;kBACAA;oBACA;sBACAa;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBACAC;sBAAA;sBACAC;oBACA;kBACA;;kBACA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAG;MACA;MACA;QACA;QACA;UACAC;YACAC,2EACAC;UACA;QACA;QACA;QACA;UACAF;YACAG;cACAC;cACAC;YACA;UACA;QACA;QACAC;UACAjB;UACAE;UACAU;UACAE;QACA;MACA;MACA;QACAI;QACAD;QACA5C;QAAA;QACA8C;QACA1C;QACAV;QACAW;QACAG;MACA;;MAEA;QACA;QACAjB;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;QACA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;QACAA;MACA;MACA;IACA;IACAwD;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAGA;gBACAC;gBAAA;gBAAA,OACA;cAAA;gBAAAnC;gBACA;kBACA;kBACA;oBACAA;sBACA;sBACA;wBACA;0BACA;0BACAA;wBACA;sBACA;sBAEA;sBACA;wBACAoC;sBACA;sBACA;wBACAC,qEACAC;sBACA;wBACAD;sBACA;oBACA;kBACA;kBAEA;;kBAEA;AACA;;kBAEA;kBACA;oBACA;oBACA;oBACA;oBACA;kBACA;oBACA;oBACA;sBACA;wBACAjC;0BACA+B;0BACA9C;0BACAkD;0BACAjC;wBACA;0BACA;0BACAkC;0BACA;0BACA;0BACA;wBACA;0BACA;wBAAA,CACA;sBACA;oBACA;kBACA;gBACA;kBACApC;oBACAG;oBACAe;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAmB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACArC;kBACAG;kBACAe;gBACA;gBACA;gBAAA;cAAA;gBAAA,MAIA;kBAAA;kBAAA;gBAAA;gBACAlB;kBACAG;kBACAe;gBACA;gBACA;gBAAA;cAAA;gBAAA,MAGA;kBAAA;kBAAA;gBAAA;gBACAlB;kBACAG;kBACAe;gBACA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAtB;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACA0C;gBACA;kBACAA;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA,MAEA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAIA;gBACA;gBAAA;gBAAA;cAAA;gBAEAtC;kBACAG;kBACAe;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA5C;gBACA;kBACAI;oBACAG;oBACAe;kBACA;kBACAlB;oBACAyC;kBACA;gBACA;kBACAzC;oBACAG;kBACA;kBACAH;oBACAyC;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAEA;gBACAC;kBACAC;gBACA;kBACAzC;kBACAwB;gBACA;kBACA1B;oBACAyC;kBACA;gBACA;kBACAzC;oBACAyC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7iBA;AAAA;AAAA;AAAA;AAAuxC,CAAgB,2uCAAG,EAAC,C;;;;;;;;;;;ACA3yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/pay/order.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/pay/order.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./order.vue?vue&type=template&id=015d7104&scoped=true&\"\nvar renderjs\nimport script from \"./order.vue?vue&type=script&lang=js&\"\nexport * from \"./order.vue?vue&type=script&lang=js&\"\nimport style0 from \"./order.vue?vue&type=style&index=0&id=015d7104&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"015d7104\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/pay/order.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=template&id=015d7104&scoped=true&\"", "var components\ntry {\n  components = {\n    uGap: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-gap/u-gap\" */ \"@/uni_modules/uview-ui/components/u-gap/u-gap.vue\"\n      )\n    },\n    uRadioGroup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-radio-group/u-radio-group\" */ \"@/uni_modules/uview-ui/components/u-radio-group/u-radio-group.vue\"\n      )\n    },\n    uRadio: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-radio/u-radio\" */ \"@/uni_modules/uview-ui/components/u-radio/u-radio.vue\"\n      )\n    },\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n    uSwitch: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-switch/u-switch\" */ \"@/uni_modules/uview-ui/components/u-switch/u-switch.vue\"\n      )\n    },\n    uTextarea: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-textarea/u-textarea\" */ \"@/uni_modules/uview-ui/components/u-textarea/u-textarea.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"to-pay-order\">\r\n    <view class=\"order\">\r\n      <view class=\"item u-border-bottom\" v-for=\"(item, index) in goodsList\" :key=\"'a' + index\">\r\n        <view class=\"left\">\r\n          <image :src=\"item.pic\" mode=\"aspectFill\"></image>\r\n        </view>\r\n        <view class=\"right\">\r\n          <view class=\"r1\">\r\n            <view class=\"title\">{{ item.goodsName }}</view>\r\n            <view class=\"sku\">\r\n              <text v-for=\"(item2, index2) in item.sku\" :key=\"'b' + index2\">{{ item2.optionName }}:{{ item2.optionValueName }}/</text>\r\n              <text v-for=\"(item3, index3) in item.additions\" :key=\"'c' + index3\">{{ item3.pname }}:{{ item3.name }}/</text>\r\n            </view>\r\n          </view>\r\n          <view class=\"r2\">\r\n            <view class=\"price-score\">\r\n              <view v-if=\"item.price\" class=\"item\"><text>¥</text>{{item.price}}</view>\r\n              <view v-if=\"item.score\" class=\"item\">\r\n                <image class=\"score-icon\" src=\"/static/images/score.png\"></image>{{item.score}}\r\n              </view>\r\n            </view>\r\n            <view class=\"number\"><text>x</text>{{ item.number }}</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <view class=\"total\">\r\n        <text>共 {{ goodsNumber }} 件商品 合计:</text>\r\n        <view class=\"price-score\" style=\"display: inline-flex;\">\r\n          <view v-if=\"goodsPrice\" class=\"item\"><text>¥</text>{{ goodsPrice }}</view>\r\n          <view v-if=\"goodsScore\" class=\"item\"><text>\r\n              <image class=\"score-icon\" src=\"/static/images/score.png\"></image>\r\n            </text>{{ goodsScore }}</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <block v-if=\"orderInfo && orderInfo.couponUserList\">\r\n      <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n      <view class=\"label-title\">\r\n        <view class=\"icon\"></view>使用礼品卡免费提货\r\n      </view>\r\n      <view class=\"coupon-box\">\r\n        <u-radio-group placement=\"column\" v-model=\"couponId\" iconPlacement=\"right\" @change=\"couponChange\">\r\n          <u-radio v-if=\"card === 0\" name=\"\" label=\"不使用礼品卡\" activeColor=\"red\"></u-radio>\r\n          <u-radio v-for=\"(item,index) in orderInfo.couponUserList\" :key=\"index\" :name=\"item.id\" :label=\"item.nameExt\" activeColor=\"red\"></u-radio>\r\n        </u-radio-group>\r\n        <view style=\"height: 30rpx;\"></view>\r\n      </view>\r\n    </block>\r\n\r\n    <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n    <view class=\"label-title\">\r\n      <view class=\"icon\"></view>配送方式\r\n    </view>\r\n    <view class=\"delivery-section\">\r\n      <u-cell title=\"自提\" size=\"large\">\r\n        <view slot=\"value\">\r\n          <u-switch v-model=\"isSelfPickup\" @change=\"onSelfPickupChange\" activeColor=\"#e64340\"></u-switch>\r\n        </view>\r\n      </u-cell>\r\n    </view>\r\n\r\n    <view v-if=\"!isSelfPickup\">\r\n      <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n      <view class=\"label-title\">\r\n        <view class=\"icon\"></view>收货地址\r\n      </view>\r\n      <u-cell v-if=\"!defaultAddress\" title=\"新增收货地址\" size=\"large\" isLink url=\"/packageFx/address/addSite\"></u-cell>\r\n      <u-cell v-else :title=\"defaultAddress.info.linkMan + ' ' + defaultAddress.info.mobile\" :label=\"defaultAddress.info.provinceStr + defaultAddress.info.cityStr + defaultAddress.info.areaStr + defaultAddress.info.address\" isLink size=\"large\" url=\"/packageFx/address/index?select=1\"></u-cell>\r\n    </view>\r\n\r\n    <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n    <view class=\"label-title\">\r\n      <view class=\"icon\"></view>备注\r\n    </view>\r\n    <view class=\"remark\">\r\n      <u-textarea v-model=\"remark\" class=\"order-remark\" placeholder=\"如果需备注请输入\"></u-textarea>\r\n    </view>\r\n    <view v-if=\"orderInfo\">\r\n      <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n      <view class=\"label-title\">\r\n        <view class=\"icon\"></view>合计\r\n      </view>\r\n      <u-cell v-if=\"orderInfo.amountTotle && card === 0\" title=\"商品金额\">\r\n        <view slot=\"value\" class=\"price-score\">\r\n          <view class=\"item\"><text>¥</text>{{ orderInfo.amountTotle }}</view>\r\n        </view>\r\n      </u-cell>\r\n      <u-cell v-if=\"orderInfo.score\" title=\"需要支付积分\">\r\n        <view slot=\"value\" class=\"price-score\">\r\n          <view class=\"item\">\r\n            <image class=\"score-icon\" src=\"/static/images/score.png\"></image>{{ orderInfo.score }}\r\n          </view>\r\n        </view>\r\n      </u-cell>\r\n      <u-cell v-if=\"orderInfo.amountLogistics\" title=\"运费\">\r\n        <view slot=\"value\" class=\"price-score\">\r\n          <view class=\"item\">\r\n            <view v-if=\"logisticsId === 90581\" style=\"font-size: 20rpx;font-weight: normal;color: #222222;margin-top: 12rpx;\">(运费原价¥{{ orderInfo.amountLogistics + 12 }}，平台承担¥12)</view>\r\n            <view><text>¥</text>{{ orderInfo.amountLogistics }}</view>\r\n          </view>\r\n        </view>\r\n      </u-cell>\r\n      <u-cell v-if=\"orderInfo.freightScore\" title=\"运费积分\">\r\n        <view slot=\"value\" class=\"price-score\">\r\n          <view class=\"item\">\r\n            <image class=\"score-icon\" src=\"/static/images/score.png\"></image>{{ orderInfo.freightScore }}\r\n          </view>\r\n        </view>\r\n      </u-cell>\r\n      <u-cell v-if=\"orderInfo.amountReal\" title=\"总计\">\r\n        <view slot=\"value\" class=\"price-score\">\r\n          <view class=\"item\"><text>¥</text>{{ orderInfo.amountReal }}</view>\r\n        </view>\r\n      </u-cell>\r\n      <u-cell v-if=\"orderInfo.couponAmount\" title=\"礼品卡抵扣\">\r\n        <view slot=\"value\" class=\"price-score\">\r\n          <view class=\"item\"><text>¥</text>{{ orderInfo.couponAmount }}</view>\r\n        </view>\r\n      </u-cell>\r\n    </view>\r\n\r\n    <view v-if=\"userAmount && card === 0\">\r\n      <u-gap height=\"20rpx\" bgColor=\"#eee\"></u-gap>\r\n      <view class=\"label-title\">\r\n        <view class=\"icon\"></view>账户余额\r\n      </view>\r\n      <u-cell title=\"可用余额\">\r\n        <view slot=\"value\" class=\"price-score\">\r\n          <view class=\"item\"><text>¥</text>{{ userAmount.balance }}</view>\r\n        </view>\r\n      </u-cell>\r\n      <u-cell title=\"可用积分\">\r\n        <view slot=\"value\" class=\"price-score\">\r\n          <view class=\"item\">\r\n            <image class=\"score-icon\" src=\"/static/images/score.png\"></image>{{ userAmount.score }}\r\n          </view>\r\n        </view>\r\n      </u-cell>\r\n    </view>\r\n\r\n    <u-gap height=\"48rpx\" bgColor=\"#eee\"></u-gap>\r\n    <view class=\"submit safe-area-inset-bottom\">\r\n      <u-button type=\"error\" @click=\"submit\" shape=\"circle\" :disabled=\"!canSubmit\">提交订单</u-button>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import empty from 'empty-value'\r\n  const PAY = require('@/common/pay.js')\r\n\r\n  export default {\r\n    data() {\r\n      return {\r\n        mod: undefined,\r\n        cartType: undefined,\r\n        couponId: '',\r\n        defaultAddress: undefined,\r\n        goodsList: undefined,\r\n        goodsNumber: 0,\r\n        goodsPrice: 0,\r\n        goodsScore: 0,\r\n        remark: '',\r\n        canSubmit: false,\r\n        orderInfo: undefined,\r\n        userAmount: undefined,\r\n        goodsType: 0, // 0 自营商品； 1 京东vop商品\r\n        kjid: undefined,\r\n        logisticsId: 0,\r\n        card: 0, // 礼品卡\r\n        isSelfPickup: false, // 是否自提\r\n      };\r\n    },\r\n    onLoad(e) {\r\n      if (e.card) {\r\n        this.card = e.card\r\n      }\r\n      this.mod = e.mod\r\n      this.cartType = e.cartType\r\n      if (e.mod == 'cart') {\r\n        if (e.cartType == 'apifm') {\r\n          this.readCartApifm()\r\n        }\r\n        if (e.cartType == 'jdvop') {\r\n          this.goodsType = 1\r\n          this.readCartJdVop()\r\n        }\r\n      }\r\n      if (e.mod == 'buy') {\r\n        this.goodsList = uni.getStorageSync('goodsList')\r\n        this.goodsList.forEach(ele => {\r\n          this.goodsNumber += ele.number\r\n          this.goodsPrice += ele.price * ele.number\r\n          this.goodsScore += ele.score * ele.number\r\n          this.goodsType = ele.goodsType\r\n          if (ele.kjid) {\r\n            this.kjid = ele.kjid\r\n          }\r\n          if (ele.logisticsId) {\r\n            this.logisticsId = ele.logisticsId\r\n          }\r\n        })\r\n        this.calculatePrice()\r\n      }\r\n      this._userAmount()\r\n    },\r\n    onShow() {\r\n      this._defaultAddress()\r\n    },\r\n    methods: {\r\n      async _defaultAddress() {\r\n        // https://www.yuque.com/apifm/nu0f75/udrcag\r\n        const res = await this.$wxapi.defaultAddress(this.token)\r\n        if (res.code == 0) {\r\n          this.defaultAddress = res.data\r\n          this.calculatePrice()\r\n        }\r\n      },\r\n      couponChange() {\r\n        this.calculatePrice()\r\n      },\r\n      onSelfPickupChange(value) {\r\n        this.isSelfPickup = value\r\n        this.calculatePrice()\r\n      },\r\n      async priceChange(orderInfo) {\r\n        await uni.$u.http.post('https://ye.niutouren.vip/shansong/order', {\r\n          amountLogistics: this.orderInfo.amountLogistics,\r\n          type: 'order_price_change',\r\n          orderInfo: orderInfo\r\n        }).then(res => {\r\n          uni.showToast({\r\n            title: '下单成功'\r\n          })\r\n\r\n          // 价格变化重新赋值\r\n          orderInfo.amountReal = res.money\r\n\r\n          this.pay(orderInfo)\r\n        }).catch(err => {\r\n          //\r\n        })\r\n      },\r\n      async _userAmount() {\r\n        // https://www.yuque.com/apifm/nu0f75/wrqkcb\r\n        const res = await this.$wxapi.userAmount(this.token)\r\n        if (res.code == 0) {\r\n          this.userAmount = res.data\r\n        }\r\n      },\r\n      async readCartApifm() {\r\n        // https://www.yuque.com/apifm/nu0f75/awql14\r\n        const res = await this.$wxapi.shippingCarInfo(this.token)\r\n        if (res.code == 0) {\r\n          let supplyType = ''\r\n          let supplyTypeHasEmpty = false // 是否有空类型\r\n          let supplyTypeCanBuy = true // 是否允许下单\r\n          this.goodsList = []\r\n          // .filter(ele => { return ele.selected })\r\n          res.data.items.forEach(ele => {\r\n            if (!ele.supplyType) {\r\n              supplyTypeHasEmpty = true\r\n            }\r\n            if (!supplyType && ele.supplyType) {\r\n              supplyType = ele.supplyType\r\n            }\r\n            if (supplyType && supplyType != ele.supplyType) {\r\n              supplyTypeCanBuy = false\r\n            }\r\n            this.goodsList.push({\r\n              key: ele.key,\r\n              goodsId: ele.supplyType == 'jdJoycityPoints' ? ele.yyIdStr : ele.goodsId,\r\n              goodsName: ele.name,\r\n              number: ele.number,\r\n              pic: ele.pic,\r\n              price: ele.price,\r\n              score: ele.score,\r\n              sku: ele.sku, // optionId optionName optionValueId optionValueName\r\n              additions: ele.additions, // id name pid pname price\r\n            })\r\n          })\r\n          if (supplyTypeHasEmpty && supplyType) {\r\n            supplyTypeCanBuy = false\r\n          }\r\n          if (!supplyTypeCanBuy) {\r\n            uni.showToast({\r\n              title: supplyType + '商品不能和其他商品一起下单',\r\n              icon: 'none'\r\n            })\r\n            return\r\n          }\r\n          if (supplyType == 'jdJoycityPoints') {\r\n            this.goodsType = 2\r\n          } else {\r\n            this.goodsType = 0\r\n          }\r\n          this.goodsNumber = res.data.number\r\n          this.goodsPrice = res.data.price\r\n          this.goodsScore = res.data.score\r\n          this.calculatePrice()\r\n        }\r\n      },\r\n      async readCartJdVop() {\r\n        // https://www.yuque.com/apifm/nu0f75/gwat37\r\n        const res = await this.$wxapi.jdvopCartInfo(this.token)\r\n        if (res.code == 0) {\r\n          this.goodsList = []\r\n          // .filter(ele => { return ele.selected })\r\n          res.data.items.forEach(ele => {\r\n            this.goodsList.push({\r\n              key: ele.key,\r\n              goodsId: ele.goodsId,\r\n              goodsName: ele.name,\r\n              number: ele.number,\r\n              pic: ele.pic,\r\n              price: ele.price,\r\n              score: 0,\r\n              sku: ele.sku, // optionId optionName optionValueId optionValueName\r\n              additions: ele.additions, // id name pid pname price\r\n            })\r\n          })\r\n          this.goodsNumber = res.data.number\r\n          this.goodsPrice = res.data.price\r\n          this.goodsScore = 0\r\n          this.calculatePrice()\r\n        }\r\n      },\r\n      buildOrderParams(calculate) {\r\n        const goodsJsonStr = []\r\n        this.goodsList.forEach(g => {\r\n          let propertyChildIds = ''\r\n          if (g.sku && g.sku.length > 0) {\r\n            g.sku.forEach(option => {\r\n              propertyChildIds = propertyChildIds + ',' + option.optionId + ':' + option\r\n                .optionValueId\r\n            })\r\n          }\r\n          let goodsAdditionList = []\r\n          if (g.additions && g.additions.length > 0) {\r\n            g.additions.forEach(option => {\r\n              goodsAdditionList.push({\r\n                pid: option.pid,\r\n                id: option.id\r\n              })\r\n            })\r\n          }\r\n          goodsJsonStr.push({\r\n            goodsId: g.goodsId,\r\n            number: g.number,\r\n            propertyChildIds,\r\n            goodsAdditionList\r\n          })\r\n        })\r\n        const data = {\r\n          token: this.token,\r\n          goodsJsonStr: JSON.stringify(goodsJsonStr),\r\n          remark: this.isSelfPickup ? '[自提订单] ' + this.remark : this.remark, // 自提时在备注前加标识\r\n          calculate,\r\n          goodsType: this.goodsType,\r\n          couponId: this.couponId,\r\n          kjid: this.kjid ? this.kjid : '',\r\n          isSelfPickup: this.isSelfPickup // 添加自提标识\r\n        }\r\n\r\n        if (this.isSelfPickup) {\r\n          // 自提时提供默认地址信息，避免后端验证失败\r\n          data.provinceId = 1 // 默认省份ID\r\n          data.cityId = 1 // 默认城市ID\r\n          data.districtId = 1 // 默认区域ID\r\n          data.streetId = 0 // 默认街道ID\r\n          data.address = '自提'\r\n          data.linkMan = '自提'\r\n          data.mobile = '13800138000'\r\n        } else if (this.defaultAddress) {\r\n          // 配送时使用真实地址信息\r\n          data.provinceId = this.defaultAddress.info.provinceId\r\n          data.cityId = this.defaultAddress.info.cityId\r\n          data.districtId = this.defaultAddress.info.districtId\r\n          data.streetId = this.defaultAddress.info.streetId\r\n          data.address = this.defaultAddress.info.address\r\n          data.linkMan = this.defaultAddress.info.linkMan\r\n          data.mobile = this.defaultAddress.info.mobile\r\n        }\r\n        return data\r\n      },\r\n      async calculatePrice() {\r\n        this.canSubmit = false\r\n        if (!this.goodsList || this.goodsList.length == 0) {\r\n          return\r\n        }\r\n        // https://www.yuque.com/apifm/nu0f75/qx4w98\r\n        const orderParams = this.buildOrderParams(true);\r\n        const res = await this.$wxapi.orderCreate(orderParams)\r\n        if (res.code == 0) {\r\n          this.canSubmit = true\r\n          if (res.data.couponUserList) {\r\n            res.data.couponUserList.forEach(ele => {\r\n              // 默认使用礼品卡\r\n              if (!empty(this.card)) {\r\n                if (ele.id === parseInt(this.card)) {\r\n                  this.couponId = ele.id\r\n                  res.data.couponAmount = ele.money\r\n                }\r\n              }\r\n\r\n              let moneyUnit = '元'\r\n              if (ele.moneyType == 1) {\r\n                moneyUnit = '%'\r\n              }\r\n              if (ele.moneyHreshold) {\r\n                ele.nameExt = ele.name + ' （面值' + ele.money + moneyUnit + '，满' + ele\r\n                  .moneyHreshold + '元可用）'\r\n              } else {\r\n                ele.nameExt = ele.name\r\n              }\r\n            })\r\n          }\r\n\r\n          this.orderInfo = res.data\r\n\r\n          /* console.log('orderParams is', orderParams)\r\n          console.log('orderInfo is', res.data) */\r\n\r\n          // 如果选择自提，则清零运费\r\n          if (this.isSelfPickup) {\r\n            this.orderInfo.amountLogistics = 0\r\n            this.orderInfo.freightScore = 0\r\n            // 重新计算总价\r\n            this.orderInfo.amountReal = this.orderInfo.amountTotleOriginal - (this.orderInfo.couponAmount || 0)\r\n          } else {\r\n            // 闪送模版，按“距离”的 要改价\r\n            if (this.logisticsId === 90581 && res.data.amountLogistics > 0) {\r\n              if (!empty(orderParams.address)) {\r\n                uni.$u.http.post('https://ye.niutouren.vip/shansong/order', {\r\n                  orderParams: orderParams,\r\n                  orderInfo: res.data,\r\n                  address: this.defaultAddress.info.provinceStr + this.defaultAddress.info.cityStr + this.defaultAddress.info.areaStr + this.defaultAddress.info.address,\r\n                  type: 'express_fee_get'\r\n                }).then(res => {\r\n                  // 闪送运费重构\r\n                  console.log('express_fee_get is', res)\r\n                  this.orderInfo.amountLogistics = Math.floor((res.data.totalAmount / 100 - 12) * 10) / 10;\r\n                  // 免12邮费\r\n                  this.orderInfo.amountReal = Math.floor((this.orderInfo.amountTotleOriginal + this.orderInfo.amountLogistics) * 10) / 10;\r\n                }).catch(err => {\r\n                  //\r\n                })\r\n              }\r\n            }\r\n          }\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n        }\r\n      },\r\n      async submit() {\r\n        this.canSubmit = false\r\n        if (!this.goodsList || this.goodsList.length == 0) {\r\n          this.canSubmit = true\r\n          return\r\n        }\r\n        // 如果不是自提，需要验证收货地址\r\n        if (!this.isSelfPickup && !this.defaultAddress) {\r\n          uni.showToast({\r\n            title: '请选择收货地址',\r\n            icon: 'none'\r\n          })\r\n          this.canSubmit = true\r\n          return\r\n        }\r\n        // 判断余额和积分是否足够\r\n        if (!this.orderInfo || !this.userAmount) {\r\n          uni.showToast({\r\n            title: '请稍后',\r\n            icon: 'none'\r\n          })\r\n          this.canSubmit = true\r\n          return\r\n        }\r\n        if (this.userAmount.score < this.orderInfo.score) {\r\n          uni.showToast({\r\n            title: '可用积分不足',\r\n            icon: 'none'\r\n          })\r\n          this.canSubmit = true\r\n          return\r\n        }\r\n        // https://www.yuque.com/apifm/nu0f75/qx4w98\r\n        const res = await this.$wxapi.orderCreate(this.buildOrderParams(false))\r\n        if (res.code == 0) {\r\n          if (this.mod == 'cart') {\r\n            const keys = []\r\n            this.goodsList.forEach(ele => {\r\n              keys.push(ele.key)\r\n            })\r\n            if (this.cartType == 'apifm') {\r\n              // https://www.yuque.com/apifm/nu0f75/pndgyc\r\n              await this.$wxapi.shippingCarInfoRemoveItem(this.token, keys.join())\r\n            }\r\n            if (this.cartType == 'jdvop') {\r\n              // https://www.yuque.com/apifm/nu0f75/syqlot\r\n              await this.$wxapi.jdvopCartRemove(this.token, keys.join())\r\n            }\r\n          }\r\n\r\n          // 下单成功，修改it120后端运费\r\n          this.priceChange(res.data)\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n        }\r\n      },\r\n      async pay(orderInfo) {\r\n        const needPay = (orderInfo.amountReal - this.userAmount.balance).toFixed(2)\r\n        if (needPay <= 0) {\r\n          // 直接调用支付接口\r\n          const res = await this.$wxapi.orderPay(this.token, orderInfo.id)\r\n          if (res.code != 0) {\r\n            uni.showToast({\r\n              title: res.msg,\r\n              icon: 'none'\r\n            })\r\n            uni.redirectTo({\r\n              url: \"../order/index?status=0\"\r\n            })\r\n          } else {\r\n            uni.showToast({\r\n              title: '支付成功'\r\n            })\r\n            uni.redirectTo({\r\n              url: \"../order/index?status=1\"\r\n            })\r\n          }\r\n        } else {\r\n          // 发起在线支付\r\n          PAY.pay('wxpay', {\r\n            appid: getApp().globalData.wxpayOpenAppId\r\n          }, needPay, '支付订单 ：' + orderInfo.id, '支付订单 ：' + orderInfo.id, {\r\n            type: 0,\r\n            id: orderInfo.id\r\n          }, () => {\r\n            uni.redirectTo({\r\n              url: \"../order/index?status=0\"\r\n            })\r\n          }, () => {\r\n            uni.redirectTo({\r\n              url: \"../order/index?status=0\"\r\n            })\r\n          })\r\n        }\r\n      },\r\n    }\r\n  };\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n  .to-pay-order {\r\n    padding-bottom: 100rpx;\r\n\r\n    .shop-section {\r\n      margin-top: 32rpx;\r\n    }\r\n  }\r\n\r\n  .order {\r\n    width: 710rpx;\r\n    background-color: #ffffff;\r\n    margin: auto;\r\n    border-radius: 20rpx;\r\n    box-sizing: border-box;\r\n    padding: 0 20rpx;\r\n    font-size: 28rpx;\r\n\r\n    .top {\r\n      display: flex;\r\n      justify-content: space-between;\r\n\r\n      .left {\r\n        display: flex;\r\n        align-items: center;\r\n\r\n        .store {\r\n          margin: 0 10rpx;\r\n          font-size: 32rpx;\r\n          font-weight: bold;\r\n        }\r\n      }\r\n\r\n      .right {\r\n        color: $u-warning-dark;\r\n      }\r\n    }\r\n\r\n    .item {\r\n      display: flex;\r\n      margin: 20rpx 0 0;\r\n\r\n      .left {\r\n        margin-right: 20rpx;\r\n\r\n        image {\r\n          width: 200rpx;\r\n          height: 200rpx;\r\n          border-radius: 10rpx;\r\n        }\r\n      }\r\n\r\n      .right {\r\n        flex: 1;\r\n        display: flex;\r\n        flex-direction: column;\r\n        justify-content: space-between;\r\n\r\n        .r1 {\r\n          padding-left: 16rpx;\r\n\r\n          .title {\r\n            font-size: 28rpx;\r\n            line-height: 50rpx;\r\n            font-weight: bold;\r\n          }\r\n\r\n          .sku {\r\n            margin: 10rpx 0;\r\n            font-size: 24rpx;\r\n            line-height: 42rpx;\r\n            color: $u-tips-color;\r\n          }\r\n        }\r\n\r\n        .r2 {\r\n          margin-left: 10rpx;\r\n          display: flex;\r\n          justify-content: space-between;\r\n          align-items: baseline;\r\n          padding-bottom: 24rpx;\r\n\r\n          .number {\r\n            text {\r\n              font-size: 24rpx;\r\n              padding: 0 4rpx;\r\n            }\r\n\r\n            color: $u-tips-color;\r\n            font-size: 34rpx;\r\n            color: #e64340;\r\n          }\r\n        }\r\n      }\r\n\r\n    }\r\n\r\n    .total {\r\n      padding: 20rpx 0;\r\n      text-align: right;\r\n      font-size: 24rpx;\r\n    }\r\n\r\n    .bottom {\r\n      display: flex;\r\n      margin-top: 40rpx;\r\n      padding: 0 10rpx;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n\r\n      .btn {\r\n        line-height: 52rpx;\r\n        width: 160rpx;\r\n        border-radius: 26rpx;\r\n        border: 2rpx solid $u-border-color;\r\n        font-size: 26rpx;\r\n        text-align: center;\r\n        color: $u-info-dark;\r\n      }\r\n\r\n      .evaluate {\r\n        color: $u-warning-dark;\r\n        border-color: $u-warning-dark;\r\n      }\r\n    }\r\n  }\r\n\r\n  .remark {\r\n    padding: 32rpx;\r\n  }\r\n\r\n  .submit {\r\n    box-sizing: border-box;\r\n    position: fixed;\r\n    padding: 32rpx;\r\n    width: 100vw;\r\n    background-color: #ffffff;\r\n    left: 0;\r\n    bottom: 0;\r\n    z-index: 9;\r\n  }\r\n\r\n  .coupon-box {\r\n    padding: 0 32rpx;\r\n  }\r\n\r\n  .order-remark {\r\n    background: #FFEBE0;\r\n  }\r\n\r\n  .delivery-section {\r\n    background-color: #ffffff;\r\n    border-radius: 20rpx;\r\n    margin: 0 20rpx;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=style&index=0&id=015d7104&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./order.vue?vue&type=style&index=0&id=015d7104&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688737516\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}