(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/list/list-quota"],{"4db8":function(n,t,e){"use strict";var o=e("8360"),u=e.n(o);u.a},8360:function(n,t,e){},9872:function(n,t,e){"use strict";e.d(t,"b",(function(){return u})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){return o}));var o={uRow:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-row/u-row")]).then(e.bind(null,"f632"))},uCol:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-col/u-col")]).then(e.bind(null,"44b6"))},uImage:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-image/u-image")]).then(e.bind(null,"73f7"))}},u=function(){var n=this.$createElement;this._self._c},i=[]},a817:function(n,t,e){"use strict";e.r(t);var o=e("b621"),u=e.n(o);for(var i in o)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(i);t["default"]=u.a},b621:function(n,t,e){"use strict";(function(n){var o=e("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;o(e("bc37"));var u={components:{listGoodsItemQuota:function(){e.e("components/list-item/list-goods-item-quota").then(function(){return resolve(e("26df"))}.bind(null,e)).catch(e.oe)}},props:{list:{type:Array,default:[]},type:{type:String,default:""}},onReady:function(){},data:function(){return{}},watch:{list:function(n){}},methods:{imageClick:function(t){"goods"===this.type&&n.navigateTo({url:"/pages/goods/detail?id="+t.id})}}};t.default=u}).call(this,e("df3c")["default"])},f2d7:function(n,t,e){"use strict";e.r(t);var o=e("9872"),u=e("a817");for(var i in u)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(i);e("4db8");var l=e("828b"),r=Object(l["a"])(u["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=r.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/list/list-quota-create-component',
    {
        'components/list/list-quota-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("f2d7"))
        })
    },
    [['components/list/list-quota-create-component']]
]);
