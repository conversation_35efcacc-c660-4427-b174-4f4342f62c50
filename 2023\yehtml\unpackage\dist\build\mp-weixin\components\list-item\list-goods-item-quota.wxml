<view class="goods-box-wrapper"><view class="goods-box"><view data-event-opts="{{[['tap',[['goDetail',['$0'],['item']]]]]}}" bindtap="__e"><view class="goods-title-box"><view class="goods-title">{{item.name}}</view></view><view class="goods-dsc-box"><view class="goods-dsc">{{item.characteristic}}</view></view><view class="goods-tags-box"><view class="goods-tags"><block wx:for="{{$root.l0}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><label style="{{'color:'+(tag.m0?'#e64340':'')+';'+('border:'+(tag.m1?'1px solid #e64340':'')+';')+('display:'+('inline-block')+';')+('margin-right:'+('5px')+';')}}" class="_span">{{''+tag.$orig+''}}</label></block></view></view><view class="goods-quota"><progress percent="{{100}}" show-info="{{false}}" stroke-width="3" font-size="12" border-radius="6" active="{{true}}"></progress><label class="num _span">{{"剩余"+item.stores}}</label></view></view><view class="buy-wrapper" style="display:flex;"><view class="price-score"><block wx:if="{{item.minPrice}}"><view class="item"><view class="original-price _div"><label class="_span">市场价：</label><view class="_br"></view><label class="price _span">{{"¥"+item.originalPrice}}</label></view><text>¥</text>{{item.minPrice+''}}</view></block><block wx:if="{{item.minScore}}"><view class="item"><text><image class="score-icon" src="/static/images/score.png"></image></text>{{item.minScore}}</view></block></view><view data-event-opts="{{[['tap',[['goDetail',['$0'],['item']]]]]}}" class="buy" bindtap="__e"><u-icon vue-id="016b770c-1" name="shopping-cart" color="#FFFFFF" size="28" bind:__l="__l"></u-icon></view></view></view></view>