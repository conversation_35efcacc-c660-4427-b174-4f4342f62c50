@charset "UTF-8";
/**
 * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量
 * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可
 * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 
 */
/* 变量 */
.zb-tooltip.data-v-5c27e2dc {
  position: relative;
}
.zb_tooltip_content.data-v-5c27e2dc {
  height: 100%;
  /* float: left; */
  position: relative;
  display: inline-block;
  /* overflow: hidden; */
}
.zb_tooltip__popper.data-v-5c27e2dc {
  /* transform-origin: center top; */
  background: var(--theme-bg-color);
  visibility: hidden;
  position: absolute;
  border-radius: 4px;
  font-size: 12px;
  padding: 10px;
  min-width: 10px;
  word-wrap: break-word;
  display: inline-block;
  white-space: nowrap;
  z-index: 9;
}
.zb_popper__icon.data-v-5c27e2dc {
  width: 0;
  height: 0;
  z-index: 9;
  position: absolute;
}
.zb_popper__arrow.data-v-5c27e2dc {
  bottom: -5px;
  /* transform-origin: center top; */
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-top: 6px solid var(--theme-bg-color);
}
.zb_popper__right.data-v-5c27e2dc {
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-right: 6px solid var(--theme-bg-color);
  left: -5px;
}
.zb_popper__left.data-v-5c27e2dc {
  border-top: 6px solid transparent;
  border-bottom: 6px solid transparent;
  border-left: 6px solid var(--theme-bg-color);
  right: -5px;
}
.zb_popper__up.data-v-5c27e2dc {
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid var(--theme-bg-color);
  top: -5px;
}
.fixed.data-v-5c27e2dc {
  position: absolute;
  width: 100vw;
  height: 100vh;
  position: fixed;
  left: 0;
  top: 0;
  pointer-events: auto;
  background: red;
  z-index: -1;
}

