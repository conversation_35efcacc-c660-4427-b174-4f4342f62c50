(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/goods-pop/goods-pop"],{2201:function(e,o,t){"use strict";t.d(o,"b",(function(){return i})),t.d(o,"c",(function(){return s})),t.d(o,"a",(function(){return n}));var n={uPopup:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-popup/u-popup")]).then(t.bind(null,"d8d0"))},uImage:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-image/u-image")]).then(t.bind(null,"73f7"))},uText:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-text/u-text")]).then(t.bind(null,"828c"))},uLine:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-line/u-line")]).then(t.bind(null,"198f"))},uTag:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-tag/u-tag")]).then(t.bind(null,"2216"))},uNumberBox:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-number-box/u-number-box")]).then(t.bind(null,"cc6c"))},uButton:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-button/u-button")]).then(t.bind(null,"9edc"))},uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(null,"5f3a"))},uBadge:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-badge/u-badge")]).then(t.bind(null,"d690"))}},i=function(){var e=this.$createElement;this._self._c},s=[]},"684e":function(e,o,t){"use strict";t.r(o);var n=t("f622"),i=t.n(n);for(var s in n)["default"].indexOf(s)<0&&function(e){t.d(o,e,(function(){return n[e]}))}(s);o["default"]=i.a},"829a":function(e,o,t){},"9db9":function(e,o,t){"use strict";var n=t("829a"),i=t.n(n);i.a},d923:function(e,o,t){"use strict";t.r(o);var n=t("2201"),i=t("684e");for(var s in i)["default"].indexOf(s)<0&&function(e){t.d(o,e,(function(){return i[e]}))}(s);t("9db9");var r=t("828b"),a=Object(r["a"])(i["default"],n["b"],n["c"],!1,null,"2f42cdbb",null,!1,n["a"],void 0);o["default"]=a.exports},f622:function(e,o,t){"use strict";(function(e){var n=t("47a9");Object.defineProperty(o,"__esModule",{value:!0}),o.default=void 0;var i=n(t("7eb4")),s=n(t("ee10")),r=t("0cdf"),a={name:"goods-pop",props:{show:{type:Boolean,default:!1},goodsDetail:{type:Object,default:null},skuList:{type:Array,default:null},kjid:void 0},data:function(){return{pic:void 0,price:void 0,score:void 0,buyNumber:1,min:1,max:0,propertyChildIds:void 0,propertyChildNames:void 0,goodsAddition:void 0,faved:!1,properties:void 0,lock:!1}},watch:{goodsDetail:{deep:!0,immediate:!0,handler:function(e,o){this._initData()}}},mounted:function(){},methods:{_initData:function(){!this.lock&&this.goodsDetail&&(this.pic=this.goodsDetail.basicInfo.pic,this.price=this.goodsDetail.basicInfo.minPrice,this.score=this.goodsDetail.basicInfo.minScore,this.goodsDetail.basicInfo.stores?this.min=1:this.min=0,this.max=this.goodsDetail.basicInfo.stores,this.goodsAddition=null,this.goodsDetail.basicInfo.hasAddition&&this._goodsAddition(),this.properties=this.goodsDetail.properties,r.showTabBarBadge(),this.goodsFavCheck())},close:function(){this.lock=!1,this.$emit("close")},skuSelect:function(e,o){var t=this;return(0,s.default)(i.default.mark((function n(){var s,r,a,d,u,c,l,p,f,h,g;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:for(t.lock=!0,t.buyNumber=1,s=t.goodsDetail.properties,r=s[e],a=r.childsCurGoods[o],d=e;d<s.length;d++)u=s[d],u.childsCurGoods.forEach((function(e){e.selected=!1})),u.selectedChild=null,s.splice(d,1,u);for(a.selected=!0,r.selectedChild=a,r.childsCurGoods.splice(o,1,a),s.splice(e,1,r),c=[],l=[],s.forEach((function(e){e.selectedChild&&(c.push(e.id+":"+e.selectedChild.id),l.push(e.name+":"+e.selectedChild.name))})),t.propertyChildIds=c,t.propertyChildNames=l,p=t.goodsDetail.skuList.filter((function(e){var o=!0;return c.forEach((function(t){-1==e.propertyChildIds.indexOf(t)&&(o=!1)})),o})),f=function(e){var o=s[e],t=p.findIndex((function(e){return-1!=e.propertyChildIds.indexOf(o.id+":")}));o.hidden=-1==t,o.childsCurGoods.forEach((function(e){t=p.findIndex((function(t){return-1!=t.propertyChildIds.indexOf(o.id+":"+e.id)})),e.hidden=-1==t})),s.splice(e,1,o)},h=e+1;h<s.length;h++)f(h);t.goodsDetail.subPics&&t.goodsDetail.subPics.length>0&&(g=t.goodsDetail.subPics.find((function(e){return e.optionValueId==a.id})),g&&(t.pic=g.pic)),t.properties=s,t.calculateGoodsPrice();case 21:case"end":return n.stop()}}),n)})))()},additionSelect:function(e,o){var t=this;return(0,s.default)(i.default.mark((function n(){var s,r;return i.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(s=t.goodsAddition[e],r=s.items[o],!r.selected){n.next=7;break}return r.selected=!1,s.items.splice(o,1,r),t.calculateGoodsPrice(),n.abrupt("return");case 7:0==s.type&&s.items.forEach((function(e){e.selected=!1})),r.selected=!0,s.items.splice(o,1,r),t.goodsAddition.splice(e,1,s),t.calculateGoodsPrice();case 12:case"end":return n.stop()}}),n)})))()},_goodsAddition:function(){var e=this;return(0,s.default)(i.default.mark((function o(){var t;return i.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return o.next=2,e.$wxapi.goodsAddition(e.goodsDetail.basicInfo.id);case 2:t=o.sent,0==t.code&&(e.goodsAddition=t.data);case 4:case"end":return o.stop()}}),o)})))()},calculateGoodsPrice:function(){var o=this;return(0,s.default)(i.default.mark((function t(){var n;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(o.propertyChildIds&&o.properties&&o.propertyChildIds.length==o.properties.length){t.next=6;break}return o.price=o.goodsDetail.basicInfo.minPrice,o.score=o.goodsDetail.basicInfo.minScore,o.goodsDetail.basicInfo.stores?o.min=1:o.min=0,o.max=o.goodsDetail.basicInfo.stores,t.abrupt("return");case 6:return t.next=8,o.$wxapi.goodsPriceV2({token:o.token,goodsId:o.goodsDetail.basicInfo.id,propertyChildIds:o.propertyChildIds.join()});case 8:if(n=t.sent,0==n.code){t.next=12;break}return e.showToast({title:n.msg,icon:"none"}),t.abrupt("return");case 12:o.price=n.data.price,o.score=n.data.score,n.data.stores?o.min=1:o.min=0,o.max=n.data.stores,o.goodsAddition&&o.goodsAddition.forEach((function(e){e.items.forEach((function(e){e.selected&&(o.price=(100*o.price+100*e.price)/100)}))}));case 17:case"end":return t.stop()}}),t)})))()},goCart:function(){this.close(),"vop_jd"==this.goodsDetail.basicInfo.supplyType&&e.setStorageSync("cart_tabIndex",1),e.switchTab({url:"/pages/cart/index"})},goodsFavCheck:function(){var e=this;return(0,s.default)(i.default.mark((function o(){var t,n;return i.default.wrap((function(o){while(1)switch(o.prev=o.next){case 0:return t={token:e.token,type:0,goodsId:e.goodsDetail.basicInfo.id},"vop_jd"==e.goodsDetail.basicInfo.supplyType&&(t.type=1,t.goodsId=e.goodsDetail.basicInfo.yyId),o.next=4,e.$wxapi.goodsFavCheckV2(t);case 4:n=o.sent,0==n.code?e.faved=!0:e.faved=!1;case 6:case"end":return o.stop()}}),o)})))()},addFav:function(){var o=this;return(0,s.default)(i.default.mark((function t(){var n,s,r,a;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,getApp().checkHasLoginedH5();case 2:if(t.sent){t.next=5;break}return e.navigateTo({url:"/pages/login/login"}),t.abrupt("return");case 5:if(n={token:o.token,type:0,goodsId:o.goodsDetail.basicInfo.id},"vop_jd"==o.goodsDetail.basicInfo.supplyType&&(n.type=1,n.goodsId=o.goodsDetail.basicInfo.yyId),!o.faved){t.next=14;break}return t.next=10,o.$wxapi.goodsFavDeleteV2(n);case 10:s=t.sent,0==s.code?o.faved=!1:e.showToast({title:s.msg,icon:"none"}),t.next=20;break;case 14:return r={pic:o.goodsDetail.basicInfo.pic,goodsName:o.goodsDetail.basicInfo.name,supplyType:o.goodsDetail.basicInfo.supplyType},n.extJsonStr=JSON.stringify(r),t.next=18,o.$wxapi.goodsFavAdd(n);case 18:a=t.sent,0==a.code?o.faved=!0:e.showToast({title:a.msg,icon:"none"});case 20:case"end":return t.stop()}}),t)})))()},checkOk:function(){if(this.properties&&(!this.propertyChildIds||this.propertyChildIds.length!=this.properties.length))return e.showToast({title:"请选择规格",icon:"none"}),!1;var o=!0;return this.goodsAddition&&this.goodsAddition.length>0&&this.goodsAddition.forEach((function(e){if(e.required){var t=e.items.find((function(e){return e.selected}));t||(o=!1)}})),!!o||(e.showToast({title:"请选择规格",icon:"none"}),!1)},addCart:function(){var o=this;return(0,s.default)(i.default.mark((function t(){var n,s,a;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,getApp().checkHasLoginedH5();case 2:if(t.sent){t.next=5;break}return e.navigateTo({url:"/pages/login/login"}),t.abrupt("return");case 5:if(o.checkOk()){t.next=7;break}return t.abrupt("return");case 7:if(!(o.buyNumber<1)){t.next=10;break}return e.showToast({title:"请选择购买数量",icon:"none"}),t.abrupt("return");case 10:if(n=[],o.properties&&o.properties.length>0&&o.properties.forEach((function(e){n.push({optionId:e.id,optionValueId:e.selectedChild.id})})),s=[],o.goodsAddition&&o.goodsAddition.length>0&&o.goodsAddition.forEach((function(e){e.items.forEach((function(e){e.selected&&s.push({id:e.id,pid:e.pid})}))})),"vop_jd"!=o.goodsDetail.basicInfo.supplyType){t.next=20;break}return t.next=17,o.$wxapi.jdvopCartAdd({token:o.token,goodsId:o.goodsDetail.basicInfo.yyId,number:o.buyNumber});case 17:a=t.sent,t.next=23;break;case 20:return t.next=22,o.$wxapi.shippingCarInfoAddItem(o.token,o.goodsDetail.basicInfo.id,o.buyNumber,n,s);case 22:a=t.sent;case 23:if(0==a.code){t.next=26;break}return e.showToast({title:a.msg,icon:"none"}),t.abrupt("return");case 26:o.close(),e.showToast({title:"加入购物车"}),r.showTabBarBadge();case 29:case"end":return t.stop()}}),t)})))()},tobuy:function(){var o=this;return(0,s.default)(i.default.mark((function t(){var n,s,r,a,d;return i.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,getApp().checkHasLoginedH5();case 2:if(t.sent){t.next=5;break}return e.navigateTo({url:"/pages/login/login"}),t.abrupt("return");case 5:if(o.checkOk()){t.next=7;break}return t.abrupt("return");case 7:if(!(o.buyNumber<1)){t.next=10;break}return e.showToast({title:"请选择购买数量",icon:"none"}),t.abrupt("return");case 10:n=[],o.properties&&o.properties.length>0&&o.properties.forEach((function(e){n.push({optionId:e.id,optionValueId:e.selectedChild.id,optionName:e.name,optionValueName:e.selectedChild.name})})),s=[],o.goodsAddition&&o.goodsAddition.length>0&&o.goodsAddition.forEach((function(e){e.items.forEach((function(o){o.selected&&s.push({id:o.id,pid:o.pid,name:e.name,pname:o.name})}))})),r=0,a=o.goodsDetail.basicInfo.id,"vop_jd"==o.goodsDetail.basicInfo.supplyType&&(r=1,a=o.goodsDetail.basicInfo.yyId),"jdJoycityPoints"==o.goodsDetail.basicInfo.supplyType&&(r=2,a=o.goodsDetail.basicInfo.yyIdStr),d=[{goodsId:a,goodsName:o.goodsDetail.basicInfo.name,number:o.buyNumber,pic:o.goodsDetail.basicInfo.pic,price:o.goodsDetail.basicInfo.minPrice,score:o.goodsDetail.basicInfo.minScore,sku:n,additions:s,goodsType:r,kjid:o.kjid?o.kjid:"",logisticsId:o.goodsDetail.basicInfo.logisticsId?o.goodsDetail.basicInfo.logisticsId:""}],e.setStorageSync("goodsList",d),e.navigateTo({url:"../pay/order?mod=buy"});case 21:case"end":return t.stop()}}),t)})))()}}};o.default=a}).call(this,t("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/goods-pop/goods-pop-create-component',
    {
        'components/goods-pop/goods-pop-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("d923"))
        })
    },
    [['components/goods-pop/goods-pop-create-component']]
]);
