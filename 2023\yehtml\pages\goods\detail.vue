<template>
  <view>
    <view cla ss="goods-detail" v-if="goodsDetail">
      <scroll-view class="main" scroll-y :scroll-into-view="curViewId" enable-back-to-top scroll-with-animation @scrolltolower="scrolltolower">
        <view id="basic">
          <view class="goods-images">
            <video v-if="videoMp4Src" :src="videoMp4Src" autoplay="true" loop="true" object-fit="cover" style='width:750rpx;height:750rpx;'></video>
            <u-swiper v-else :list="goodsDetail.pics" keyName="pic" radius="0" indicator circular height="750rpx"></u-swiper>
            <view v-if="deliveryTypeUrl !== ''" class="delivery-type">
              <img style="width: 100px;height: 56px;" :src="deliveryTypeUrl">
            </view>
          </view>
          <view class="price-share">
            <view v-if="curKanjiaprogress" class="price-score price-score2">
              <view class="item">
                <text>¥1</text>{{ curKanjiaprogress.kanjiaInfo.curPrice }}
              </view>
              <view class="item original">
                <text>¥1</text>{{ curGoodsKanjia.originalPrice }}
              </view>
            </view>
            <view v-else class="price-score price-score2">
              <view v-if="goodsType !== 'XSSC'" class="price-info">
                <view class="price-right">
                  <span class="prefix">￥</span>
                  <span>{{ goodsDetail.basicInfo.minPrice }}</span>
                </view>
                <view class="price-left">
                  <view class="line1">
                    <view class="original-price">
                      <span>原价￥{{ goodsDetail.basicInfo.originalPrice }}</span> / {{ goodsDetail.basicInfo.unit }}
                    </view>
                    <view class="sales" v-if="mySales > 10">销量：{{ mySales }}</view>
                  </view>

                  <view v-if="goodsType === 'XSQG'">
                    <view class="only">
                      仅剩<span>{{ goodsDetail.basicInfo.stores }}</span>{{ goodsDetail.basicInfo.unit }}</view>
                  </view>

                  <view v-if="goodsType === 'XSTG'">
                    <view class="delivery-time" style="display: inline-block;">到货时间：{{ goodsDetail.delivery_time }}</view><br />
                    <view class="only">
                      仅剩<span>{{ goodsDetail.basicInfo.stores }}</span>{{ goodsDetail.basicInfo.unit }}</view>
                  </view>
                </view>
              </view>
              <view v-if="goodsType === 'XSSC'" class="price-info">
                <view class="price-right">
                  <view class="original-price" style="text-decoration: none;">
                    <span>原价￥{{ goodsDetail.basicInfo.originalPrice }}</span><br />
                    <span>试吃价</span>
                  </view>
                  <span class="prefix">￥</span>
                  <span>{{ shichiPrice }}</span>
                </view>
                <view class="price-left">
                  <view class="max-total">试吃总数 {{ maxTotal }}只</view><br />
                  <view class="max-total">试吃起数 {{ minTotal }}只</view><br />
                  <view class="only">
                    剩余<span>{{ goodsDetail.basicInfo.stores }}</span>{{ goodsDetail.basicInfo.unit }}</view>
                  <view class="delivery-time" style="display: inline-block;">到货时间 {{ goodsDetail.delivery_time }}</view><br />
                </view>
              </view>
            </view>
          </view>
          <view class="goods-title u-line-3 pt16">
            <view class="box-left">
              <text>{{ goodsDetail.basicInfo.name }}</text>
              <view v-if="goodsType !== 'XSSC'" class="goods-tags">
                <span v-for="(tag, index) in filteredTags" :key="index" :style="{ color: shouldHighlight(tag) ? '#e64340' : '', border: shouldHighlight(tag) ? '1px solid #e64340' : '', display: 'inline-block', 'margin-right': '5px' }">
                  {{ tag }}
                </span>
              </view>
            </view>
            <!-- #ifdef MP -->
            <view class="btns">
              <view class="icon-btn">&nbsp;</view>
              <view class="icon-btn" @click="longButtonSet(1)">
                <u-icon name="weixin-circle-fill" color="#1BD66C" size="48rpx"></u-icon>
                <text>接龙</text>
                <button class="share" open-type="share"></button>
              </view>
              <view class="icon-btn" @click="longButtonSet(0)">
                <u-icon name="share-square" size="48rpx"></u-icon>
                <text>分享</text>
                <button class="share" open-type="share"></button>
              </view>
              <view class="icon-btn" @click="drawSharePic">
                <u-icon name="moments" size="48rpx"></u-icon>
                <text>海报</text>
              </view>
            </view>
            <!-- #endif -->
          </view>
          <view v-if="goodsDetail.basicInfo.characteristic" class="title-sub">
            {{ goodsDetail.basicInfo.characteristic }}
          </view>
          <view class="commission" v-if="goodsDetail.basicInfo.commissionType == 1">分享有赏，好友下单后可得
            {{goodsDetail.basicInfo.commission}} 积分奖励
          </view>
          <view class="commission" v-if="goodsDetail.basicInfo.commissionType == 2">分享有赏，好友下单后可得
            {{goodsDetail.basicInfo.commission}}元 现金奖励
          </view>
        </view>

        <block v-if="curGoodsKanjia">
          <u-gap height="20rpx" bgColor="#eee"></u-gap>
          <view class="label-title">
            <view class="icon"></view>商品砍价设置
          </view>
          <u-cell-group>
            <u-cell title="数量" :value="curGoodsKanjia.number + '份'"></u-cell>
            <u-cell title="已售" :value="curGoodsKanjia.numberBuy + '份'"></u-cell>
            <u-cell title="原价" :value="curGoodsKanjia.originalPrice"></u-cell>
            <u-cell title="底价" :value="curGoodsKanjia.minPrice"></u-cell>
            <u-cell title="截止" :value="curGoodsKanjia.dateEnd"></u-cell>
          </u-cell-group>
        </block>
        <block v-if="curKanjiaprogress">
          <u-gap height="20rpx" bgColor="#eee"></u-gap>
          <view class="label-title">
            <view class="icon"></view>当前砍价状态
          </view>
          <u-cell-group>
            <u-cell title="帮砍人数" :value="curKanjiaprogress.kanjiaInfo.helpNumber"></u-cell>
            <u-cell title="状态" :value="curKanjiaprogress.kanjiaInfo.statusStr"></u-cell>
            <u-cell title="参与时间" :value="curKanjiaprogress.kanjiaInfo.dateAdd"></u-cell>
          </u-cell-group>
        </block>
        <view v-if="curKanjiaprogress && curKanjiaprogress.kanjiaInfo.uid != uid" class="curKanjiaJoin">
          帮<text>{{curKanjiaprogress.joiner.nick}}</text> 砍价吧！
        </view>
        <view v-if="curGoodsKanjia && curKanjiaprogress" class="curKanjiaprogress">
          <u-line-progress :percentage="100 * (curGoodsKanjia.originalPrice - curKanjiaprogress.kanjiaInfo.curPrice) / (curGoodsKanjia.originalPrice - curGoodsKanjia.minPrice)" activeColor="#ff0000"></u-line-progress>
          <view class="curKanjiaprogress-bar">// 砍价完成进度 //</view>
        </view>

        <block v-if="curKanjiaprogress && curKanjiaprogress.helps && curKanjiaprogress.helps.length>0">
          <u-gap height="20rpx" bgColor="#eee"></u-gap>
          <view class="label-title">
            <view class="icon"></view>好友助力明细
          </view>
          <view class="kjlj" v-for="(item,index) in curKanjiaprogress.helps" :key="index">
            <image class="kjlj-l" :src="item.avatarUrl" mode="aspectFill" />
            <u-cell class="kjlj-r" :label="item.nick + ' ' + item.dateAdd + '帮砍'" size="large">
              <view slot="title" class="price-score">
                <view class="item"><text>¥</text>{{ item.cutPrice }}</view>
              </view>
            </u-cell>
          </view>
        </block>

        <view v-if="goodsType === 'XSSC'" class="wrapper-xssc-rule">
          <u-gap height="20rpx" bgColor="#eee"></u-gap>
          <view class="label-title">
            <view class="icon"></view>试吃规则
          </view>
          <view class="content-rule">
            <ul>
              <li>1. 试吃价包含宰杀费</li>
              <li>2. 每个试吃产品每个用户只能购买一次</li>
              <li>3. 试吃产品暂时以原价购买</li>
              <li>4. 试吃后进行评论，自动返还超出试吃价的部分<br />（即 原价 - 试吃价）</li>
              <li>5. 试吃后若不参与真实评论达3次，则取消之后的试吃资格</li>
            </ul>
          </view>
        </view>

        <view v-if="longPage === 1">
          <view class="tab1">
            <u-row customStyle="margin-bottom: 10px">
              <u-col span="4" justify="center" textAlign="center">
                <div class="tab line" :class="{ active: longActiveTab === 'tab1' }" @click="changeTab('tab1')">
                  <div style="height: 30rpx;">&nbsp;</div>
                  <div class="title">接龙</div>
                </div>
              </u-col>
              <u-col span="4" justify="center" textAlign="center">
                <div class="tab line" :class="{ active: longActiveTab === 'tab2' }" @click="changeTab('tab2')">
                  <div style="height: 30rpx;">&nbsp;</div>
                  <div class="title">详情</div>
                </div>
              </u-col>
            </u-row>
          </view>

          <view v-if="longActiveTab === 'tab2'" id="content">
            <view class="content">
              <view v-if="wxintroduction">
                <u-image v-for="(item,index) in wxintroduction" :src="item" mode="widthFix" width="750rpx" height="auto"></u-image>
              </view>
              <u-parse v-else :content="goodsDetail.content"></u-parse>
            </view>
          </view>

          <view v-if="longActiveTab === 'tab1'" style="margin: 0 20rpx;">
            <u-list v-if="longList.length > 0">
              <u-list-item v-for="(good, index) in longList">
                <u-cell :title="'用户' + good.account.userBase.nick + ' 于' + good.datePay + '购买成功'">
                  <u-avatar slot="icon" size="35" :src="good.account.userBase.avatarUrl" customStyle="margin: -3px 5px -3px 0"></u-avatar>
                </u-cell>
              </u-list-item>
            </u-list>

            <view v-if="!longList.length > 0" style="min-height: 600rpx;">
              <u-empty mode="data" icon="http://cdn.uviewui.com/uview/empty/data.png" text="暂无订单，您可以成为第一个接龙龙头哦">
              </u-empty>
            </view>
          </view>
        </view>

        <view v-if="longPage === 0">
          <view v-if="goodsType !== 'XSSC'" class="wrapper-comment">
            <u-gap height="20rpx" bgColor="#eee"></u-gap>
            <view class="label-title">
              <view class="icon"></view>评论
            </view>
            <view class="content-comment">
              <view>暂无评论</view>
            </view>
          </view>

          <u-gap height="20rpx" bgColor="#eee"></u-gap>
          <view class="label-title">
            <view class="icon"></view>详细介绍
          </view>
          <view id="content">
            <view class="content">
              <view v-if="wxintroduction">
                <u-image v-for="(item,index) in wxintroduction" :src="item" mode="widthFix" width="750rpx" height="auto"></u-image>
              </view>
              <u-parse v-else :content="goodsDetail.content"></u-parse>
            </view>
          </view>
        </view>

        <!-- <view id="reputation">
          <u-divider v-if="reputationList" text="用户评价"></u-divider>
          <view v-if="reputationList" class="reputation-box">
            <view v-for="(item,index) in reputationList" :key="index" class="album">
              <view class="album__avatar">
                <u-image class="image" :src="item.user.avatarUrl" shape="circle" width="120rpx" height="120rpx"></u-image>
              </view>
              <view class="album__content">
                <u-text :text="item.user.nick" type="primary" bold size="17"></u-text>
                <u-rate v-model="item.goods.goodReputation"></u-rate>
                <u-text margin="8rpx 0 8rpx 0" size="26rpx" color="#666666" :text="item.goods.goodReputationRemark"></u-text>
                <u-text margin="0 0 8rpx 0" size="24rpx" color="#666666" :text="item.goods.dateReputation"></u-text>
                <view style="height: 32rpx;"></view>
              </view>
            </view>
          </view>
        </view> -->
      </scroll-view>
      <view v-if="curGoodsKanjia">
        <view v-if="curKanjiaprogress && curKanjiaprogress.kanjiaInfo.uid == uid" class="bottom-btns">
          <!--  #ifdef MP-WEIXIN	|| MP-BAIDU -->
          <view class="icon-btn">
            <u-icon name="chat" size="48rpx"></u-icon>
            <text>客服</text>
            <button open-type='contact' :send-message-title="goodsDetail.basicInfo.name" :send-message-img="goodsDetail.basicInfo.pic" :send-message-path="'/pages/goods/detail?id='+goodsDetail.basicInfo.id" show-message-card></button>
          </view>
          <!--  #endif -->
          <view class="icon-btn" @click="addFav">
            <u-icon :name="faved ? 'heart-fill' : 'heart'" size="48rpx"></u-icon>
            <text>收藏</text>
          </view>
          <view class="btn">
            <u-button class="half-l" text="邀请好友助力" shape="circle" color="linear-gradient(90deg,#ffd01e, #ff8917)" open-type='share'>
            </u-button>
          </view>
          <view class="btn">
            <u-button class="half-r" text="用现价购买" shape="circle" color="linear-gradient(90deg, #ff6034, #ee0a24)" @click="kanjiabuy">
            </u-button>
          </view>
        </view>
        <view v-else-if="curKanjiaprogress && curKanjiaprogress.kanjiaInfo.uid != uid" class="bottom-btns">
          <view class="icon-btn">
            <u-icon name="share" size="48rpx"></u-icon>
            <text>邀请</text>
            <button open-type='share'></button>
          </view>
          <view class="btn">
            <u-button class="half-l" :text="myHelpDetail ? '您已助力' : '帮忙砍一刀'" shape="circle" :disabled="myHelpDetail ? true : false" color="linear-gradient(90deg,#ffd01e, #ff8917)" @click="helpKanjia">
            </u-button>
          </view>
          <view class="btn">
            <u-button class="half-r" text="我也要参与" shape="circle" color="linear-gradient(90deg, #ff6034, #ee0a24)" @click="joinKanjia">
            </u-button>
          </view>
        </view>
        <view v-else class="bottom-btns">
          <!--  #ifdef MP-WEIXIN	|| MP-BAIDU -->
          <view class="icon-btn">
            <u-icon name="chat" size="48rpx"></u-icon>
            <text>客服</text>
            <button open-type='contact' :send-message-title="goodsDetail.basicInfo.name" :send-message-img="goodsDetail.basicInfo.pic" :send-message-path="'/pages/goods/detail?id='+goodsDetail.basicInfo.id" show-message-card></button>
          </view>
          <!--  #endif -->
          <view class="icon-btn" @click="addFav">
            <u-icon :name="faved ? 'heart-fill' : 'heart'" size="48rpx"></u-icon>
            <text>收藏</text>
          </view>
          <view class="btn">
            <u-button class="half-l" text="发起砍价" shape="circle" color="linear-gradient(90deg,#ffd01e, #ff8917)" @click="joinKanjia">
            </u-button>
          </view>
          <view class="btn">
            <u-button class="half-r" text="原价购买" shape="circle" color="linear-gradient(90deg, #ff6034, #ee0a24)" @click="_showGoodsPop">
            </u-button>
          </view>
        </view>
      </view>
      <view v-else class="bottom-btns">
        <!--  #ifdef MP-WEIXIN	|| MP-BAIDU -->
        <view v-if="!cardPage" class="icon-btn">
          <u-icon name="chat" size="48rpx"></u-icon>
          <text>客服</text>
        </view>
        <!--  #endif -->
        <view v-if="!cardPage" class="icon-btn" @click="goCart">
          <u-icon name="shopping-cart" size="48rpx"></u-icon>
          <text>购物车</text>
          <u-badge type="error" :value="cartNumber" absolute :offset="[-10, -10]"></u-badge>
        </view>
        <view class="icon-btn" @click="addFav">
          <u-icon :name="faved ? 'heart-fill' : 'heart'" size="48rpx"></u-icon>
          <text>收藏</text>
        </view>
        <view v-if="cardPage" class="btn" style="margin-right: 10rpx;">
          <view>
            <u-button class="half-l" text="返回礼品列表" shape="circle" @click="_back"></u-button>
          </view>
        </view>
        <view v-if="!cardPage" class="btn" style="margin-right: 10rpx;">
          <u-button v-if="goodsDetail.category.id !== 391088 && goodsDetail.category.id !== 463740" class="half-l" text="加入购物车" shape="circle" color="linear-gradient(90deg,#ffd01e, #ff8917)" @click="_showGoodsPop"></u-button>
        </view>
        <view v-if="!cardPage" class="btn">
          <u-button v-if="goodsDetail.category.id !== 391088 && goodsDetail.category.id !== 463740" class="half-r" text="立即购买" shape="circle" color="linear-gradient(90deg, #ff6034, #ee0a24)" @click="_showGoodsPop"></u-button>
          <u-button v-if="goodsDetail.category.id === 463740 && goodsDetail.category.id === 463740" class="half-r" text="立即申请" shape="circle" color="linear-gradient(90deg, #ff6034, #ee0a24)" @click="_showGoodsPop"></u-button>
          <view v-if="goodsDetail.category.id === 391088">
            <u-button v-if="afterDeadline === false" class="half-r" text="立即预购" shape="circle" color="linear-gradient(90deg, #ff6034, #ee0a24)" @click="_showGoodsPop"></u-button>
            <u-button v-if="afterDeadline === true" class="half-r" text="立即预购" color="#777777" shape="circle" @click="_showGoodsAfterDeadline"></u-button>
          </view>
        </view>
      </view>
    </view>
    <goods-pop :show="showGoodsPop" :goodsDetail="goodsDetail" :kjid="kjid" @close="showGoodsPop = false"></goods-pop>
  </view>
</template>

<script>
  import empty from 'empty-value'
  import lPainter from '../../components/lime-painter/index.vue'
  import moment from 'moment'
  const TOOLS = require('@/common/tools')

  export default {
    components: {
      lPainter
    },
    data() {
      return {
        tabs: [{
            viewId: 'basic',
            name: '商品信息',
          },
          {
            viewId: 'content',
            name: '详细介绍',
          },
          {
            viewId: 'reputation',
            name: '用户评价',
          },
        ],
        curViewId: 'basic',
        timeData: {},
        goodsDetail: undefined,
        mySales: 0,
        goodsType: undefined,
        jdGoodsDetail: undefined,
        wxintroduction: undefined,
        faved: false,
        showGoodsPop: false,
        page: 1,
        reputationList: null,
        videoMp4Src: undefined,
        // 海报模板数据
        posterData: {},
        curGoodsKanjia: undefined,
        curKanjiaprogress: undefined,
        myHelpDetail: undefined,
        kjid: undefined,

        userInfo: undefined,

        // 海报
        posterObj: {}, //画板数据
        afterDeadline: false,

        deliveryTypeUrl: '',

        cardPage: 0,

        // 试吃
        maxTotal: 0,
        minTotal: 0,
        shichiPrice: 0,

        // 接龙
        longActiveTab: 'tab1',
        longPage: 0,
        longButton: 0,
        longImage: 'https://ye.niutouren.vip/static/images/long/long.png',
        longList: []
      }
    },
    computed: {
      filteredTags() {
        if (!empty(this.goodsDetail)) {
          let goodsTags = this.goodsDetail.basicInfo.tags
          if (goodsTags) {
            const tags = goodsTags.split(/[, ]+/);
            return tags.filter(tag => tag.trim() !== '');
          }
        }
        return [];
      }
    },
    onLoad(e) {
      if (e && e.card) {
        this.cardPage = 1
      }
      if (e && e.inviter_id) {
        this.$u.vuex('referrer', e.inviter_id)
      }
      // 读取小程序码中的邀请人编号
      if (e && e.scene) {
        const scene = decodeURIComponent(e.scene) // 处理扫码进商品详情页面的逻辑
        if (scene && scene.split(',').length >= 2) {
          e.id = scene.split(',')[0]
          this.$u.vuex('referrer', scene.split(',')[1])
        }
      }
      this._goodsDetail(e.id, e.supplyType, e.yyId)
      if (e && e.long_page === "1") {
        this.longPage = 1
        this._goodsLong(e.id)
      }
    },
    onShow() {
      this._userDetail()
    },
    onShareAppMessage(e) {
      let d
      d = {
        title: this.goodsDetail.basicInfo.name,
        path: '/pages/goods/detail?id=' + this.goodsDetail.basicInfo.id + '&inviter_id=' + this.uid
      }

      if (this.goodsType === 'XSSC') {
        d = {
          title: this.goodsDetail.basicInfo.name + ' 试吃啦!',
          path: '/pages/goods/detail?id=' + this.goodsDetail.basicInfo.id + '&inviter_id=' + this.uid
        }
      }

      if (this.longButton === 1) {
        this.checkImage('https://ye.niutouren.vip/static/images/long/' + this.goodsDetail.basicInfo.id + '.png')

        d = {
          title: this.goodsDetail.basicInfo.name + ' 低价接龙啦!',
          path: '/pages/goods/detail?id=' + this.goodsDetail.basicInfo.id + '&inviter_id=' + this.uid + '&long_page=1',
          imageUrl: this.longImage
        }
      }

      if (this.kjJoinUid) {
        _data.title = this.curKanjiaprogress.joiner.nick + '邀请您帮TA砍价'
        _data.path += '&kjJoinUid=' + this.kjJoinUid
      }
      return d
    },
    created() {

    },
    methods: {
      async _back() {
        uni.navigateBack()
      },
      async _userDetail() {
        // https://www.yuque.com/apifm/nu0f75/zgf8pu
        const res = await this.$wxapi.userDetail(this.token)
        if (res.code == 0) {
          this.userInfo = res.data
        }
      },
      shouldHighlight(tag) {
        return /[A-Za-z]\d+/.test(tag);
      },
      async _goodsLong(goodsId) {
        await uni.$u.http.post('https://ye.niutouren.vip/api/order', {
          'type': 'long',
          'good_id': goodsId,
        }).then(res => {
          if (!empty(res)) {
            this.longList = res
          }

          //console.log('long is', this.longList)
        }).catch(err => {
          //
        })
      },
      async _goodsDetail(goodsId, supplyType, yyId) {
        if (goodsId) {
          // https://www.yuque.com/apifm/nu0f75/vuml8a
          const res = await this.$wxapi.goodsDetail(goodsId, this.token)
          if (res.code != 0) {
            uni.showToast({
              title: res.msg,
              icon: 'none'
            })
            setTimeout(() => {
              uni.navigateBack()
            }, 3000)
            return
          }
          this.goodsDetail = res.data
          this.mySales = this.goodsDetail.basicInfo.numberSells
          if (!empty(res.data.extJson)) {
            if (res.data.extJson['sales_base']) {
              this.mySales = this.mySales + parseFloat(res.data.extJson['sales_base'])
            }
            if (res.data.extJson['deadline']) {
              this.goodsDetail.deadline = moment(res.data.extJson['deadline'], 'YYYYMMDD').format(
                'YYYY年MM月DD日 HH:mm')

              let currentTimestamp = moment().unix()
              let deadlineTimestamp = moment(res.data.extJson['deadline'], 'YYYY-MM-DD').unix()

              if (currentTimestamp > deadlineTimestamp) {
                this.afterDeadline = true
              }
            }
            if (res.data.extJson['delivery_time']) {
              this.goodsDetail.delivery_time = moment(res.data.extJson['delivery_time'], 'YYYYMMDD').format(
                'YYYY年MM月DD日 HH:mm')
            }

            if (res.data.extJson['delivery_type']) {
              if (res.data.extJson['delivery_type'] === '预购') {
                this.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-yg.png'
              }
              if (res.data.extJson['delivery_type'] === '当日达') {
                this.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-drd.png'
              }
              if (res.data.extJson['delivery_type'] === '次日达') {
                this.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-crd.png'
              }
              if (res.data.extJson['delivery_type'] === '后日达') {
                this.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-hrd.png'
              }
            }


            if (res.data.extJson['max_total']) {
              this.maxTotal = res.data.extJson['max_total']
            }
            if (res.data.extJson['min_total']) {
              this.minTotal = res.data.extJson['min_total']
            }
            if (res.data.extJson['shichiPrice']) {
              this.shichiPrice = res.data.extJson['shichiPrice']
            }
          }

          if (!empty(res.data.category.key)) {
            this.goodsType = res.data.category.key
          }
          if (res.data.basicInfo.videoId) {
            this.getVideoSrc(res.data.basicInfo.videoId)
          }
          if (res.data.basicInfo.kanjia) {
            this.kanjiaSet()
          }
        } else {
          // 不是api工厂商品
          this.goodsDetail = {
            basicInfo: {
              yyId: yyId,
              yyIdStr: yyId,
              supplyType: supplyType,
              pic: '',
              name: '',
              stores: 999999
            },
            pics: []
          }
        }

        // 检测是否收藏
        this.goodsFavCheck()
        this._reputationList()
        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {
          this.jdvopGoodsDetail(this.goodsDetail.basicInfo.yyId)
        }
        if (this.goodsDetail.basicInfo.supplyType == 'jdJoycityPoints') {
          this.joycityPointsGoodsDetail(this.goodsDetail.basicInfo.yyIdStr)
        }
      },
      async jdvopGoodsDetail(skuId) {
        // https://www.yuque.com/apifm/nu0f75/ar77dc
        const res = await this.$wxapi.jdvopGoodsDetail(skuId)
        if (res.code != 0) {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 3000)
          return
        }
        this.jdGoodsDetail = res.data
        this.goodsDetail.basicInfo.minPrice = this.jdGoodsDetail.price.priceSale
        this.goodsDetail.basicInfo.originalPrice = this.jdGoodsDetail.price.priceJd
        this.goodsDetail.basicInfo.name = this.jdGoodsDetail.price.skuName
        this.goodsDetail.basicInfo.pic = this.jdGoodsDetail.imageDomain + this.jdGoodsDetail.price.pic
        if (this.jdGoodsDetail.info.wxintroduction) {
          this.wxintroduction = JSON.parse(this.jdGoodsDetail.info.wxintroduction)
        }
        this.jdvopGoodsSkuImages(skuId)
      },
      async jdvopGoodsSkuImages(skuId) {
        // https://www.yuque.com/apifm/nu0f75/pvcu30
        const res = await this.$wxapi.jdvopGoodsSkuImages(skuId)
        if (res.code == 0) {
          const pics = res.data
          pics.forEach(ele => {
            ele.pic = this.jdGoodsDetail.imageDomain + ele.path
          })
          this.goodsDetail.pics = pics
        }
      },
      async joycityPointsGoodsDetail(skuId) {
        const res = await this.$wxapi.joycityPointsGoodsDetail(skuId)
        if (res.code != 0) {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
          setTimeout(() => {
            uni.navigateBack()
          }, 3000)
          return
        }
        const pics = []
        res.data.providerImgUrls.split(',').forEach(ele => {
          pics.push({
            pic: ele
          })
        })
        this.goodsDetail.pics = pics
        this.goodsDetail.basicInfo.name = res.data.goodsName
        this.goodsDetail.basicInfo.minPrice = 0
        this.goodsDetail.basicInfo.minScore = res.data.goodsPrice
        this.goodsDetail.basicInfo.originalPrice = res.data.suggestedPrice
        if (res.data.pics && res.data.pics.length > 0) {
          this.wxintroduction = res.data.pics
        } else {
          this.goodsDetail.content = res.data.usageGuide
        }
      },
      goCart() {
        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {
          uni.setStorageSync('cart_tabIndex', 1)
        }
        uni.navigateTo({
          url: "/pages/cart/index"
        })
      },
      async goodsFavCheck() {
        const data = {
          token: this.token,
          type: 0,
          goodsId: this.goodsDetail.basicInfo.id
        }
        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {
          data.type = 1
          data.goodsId = this.goodsDetail.basicInfo.yyId
        }
        // https://www.yuque.com/apifm/nu0f75/ugf7y9
        const res = await this.$wxapi.goodsFavCheckV2(data)
        if (res.code == 0) {
          this.faved = true
        } else {
          this.faved = false
        }
      },
      async addFav() {
        if (!await getApp().checkHasLoginedH5()) {
          uni.navigateTo({
            url: "/pages/login/login"
          })
          return
        }
        const data = {
          token: this.token,
          type: 0,
          goodsId: this.goodsDetail.basicInfo.id
        }
        if (this.goodsDetail.basicInfo.supplyType == 'vop_jd') {
          data.type = 1
          data.goodsId = this.goodsDetail.basicInfo.yyId
        }
        if (this.faved) {
          // 取消收藏 https://www.yuque.com/apifm/nu0f75/zy4sil
          const res = await this.$wxapi.goodsFavDeleteV2(data)
          if (res.code == 0) {
            this.faved = false
          } else {
            uni.showToast({
              title: res.msg,
              icon: 'none'
            })
          }
        } else {
          const extJsonStr = {
            pic: this.goodsDetail.basicInfo.pic,
            goodsName: this.goodsDetail.basicInfo.name,
            supplyType: this.goodsDetail.basicInfo.supplyType
          }
          data.extJsonStr = JSON.stringify(extJsonStr)
          // 加入收藏 https://www.yuque.com/apifm/nu0f75/mr1471
          const res = await this.$wxapi.goodsFavAdd(data)
          if (res.code == 0) {
            this.faved = true
          } else {
            uni.showToast({
              title: res.msg,
              icon: 'none'
            })
          }
        }
      },
      tabclick(e) {
        this.curViewId = this.tabs[e.index].viewId
      },
      _showGoodsAfterDeadline() {
        uni.showToast({
          title: '已过截止时间',
          icon: 'error'
        })
      },
      // 弹出商品购买弹窗
      async _showGoodsPop() {
        this.showGoodsPop = true
        this.kjid = null
      },
      kanjiabuy() {
        // 砍价用现在的价格购买
        this.goodsDetail.basicInfo.minPrice = this.curKanjiaprogress.kanjiaInfo.curPrice
        this.kjid = this.curGoodsKanjia.id
        this.showGoodsPop = true
      },
      scrolltolower() {
        this.page += 1
        this._reputationList()
      },
      async _reputationList() {
        // https://www.yuque.com/apifm/nu0f75/cusiow
        const res = await this.$wxapi.goodsReputationV2({
          goodsId: this.goodsDetail.basicInfo.id,
          page: this.page,
          pageSize: 10
        })
        if (res.code == 0) {
          res.data.result.forEach(ele => {
            if (ele.user && !ele.user.avatarUrl) {
              //ele.user.avatarUrl = '/static/images/empty.jpg'
            }
            if (ele.user && !ele.user.nick) {
              ele.user.nick = '匿名用户'
            }
            if (ele.goods.goodReputation == 0) {
              ele.goods.goodReputation = 1
            } else if (ele.goods.goodReputation == 1) {
              ele.goods.goodReputation = 3
            } else if (ele.goods.goodReputation == 2) {
              ele.goods.goodReputation = 5
            }
          })
          if (this.page == 1) {
            this.reputationList = res.data.result
          } else {
            this.reputationList = this.reputationList.concat(res.data.result)
          }
        }
      },
      async getVideoSrc(videoId) {
        const res = await this.$wxapi.videoDetail(videoId)
        if (res.code == 0) {
          this.videoMp4Src = res.data.fdMp4
        }
      },
      async drawSharePic() {
        const _pic = this.goodsDetail.basicInfo.pic;
        const _minPriceOriginal = this.goodsDetail.basicInfo.minPriceOriginal;
        const _tags = this.goodsDetail.basicInfo.tags;
        const _id = this.goodsDetail.basicInfo.id;
        const _name = this.goodsDetail.basicInfo.name;
        const _unit = this.goodsDetail.basicInfo.unit;

        uni.navigateTo({
          url: '/packageFx/partner/poster?pic=' + encodeURIComponent(_pic) + '&minPriceOriginal=' + encodeURIComponent(_minPriceOriginal) + '&tags=' + encodeURIComponent(_tags) + '&id=' + encodeURIComponent(_id) + '&unit=' + encodeURIComponent(_unit) + '&name=' + encodeURIComponent(_name)
        });
      },
      async kanjiaSet() {
        const res = await this.$wxapi.kanjiaSet(this.goodsDetail.basicInfo.id)
        if (res.code == 0) {
          this.curGoodsKanjia = res.data[0]
          this.curGoodsKanjia.dateEnd = this.curGoodsKanjia.dateEnd.replace(/00:00:00/g, '')
          this.curGoodsKanjia.dateEnd = this.curGoodsKanjia.dateEnd.replace(/ /g, '')
          this.kanjiaprogress()
        }
      },
      async kanjiaprogress() {
        let kjJoinUid = this.kjJoinUid
        if (!kjJoinUid) {
          kjJoinUid = this.uid
        }
        const res = await this.$wxapi.kanjiaDetail(this.curGoodsKanjia.id, kjJoinUid)
        if (res.code == 0) {
          this.curKanjiaprogress = res.data
          this.kanjiaHelpDetail()
        }
      },
      async joinKanjia() {
        // 报名参加砍价活动
        if (!this.curGoodsKanjia) {
          return
        }
        uni.showLoading({
          title: '加载中'
        })
        const res = await this.$wxapi.kanjiaJoin(this.token, this.curGoodsKanjia.id)
        uni.hideLoading()
        if (res.code == 2000) {
          getApp().autoLogin(true)
          return
        }
        if (res.code == 0) {
          this.$u.vuex('kjJoinUid', this.uid)
          this.myHelpDetail = null
          this.kanjiaSet()
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        }
      },
      async helpKanjia() {
        console.log(this.curGoodsKanjia);
        console.log(this.token);
        const res = await this.$wxapi.kanjiaHelp(this.token, this.curGoodsKanjia.id, this.curKanjiaprogress
          .kanjiaInfo.uid, '')
        if (res.code != 0) {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
          return;
        }
        this.myHelpDetail = res.data
        uni.showModal({
          title: '成功',
          content: '成功帮TA砍掉 ' + res.data.cutPrice + ' 元',
          showCancel: false,
          confirmText: '知道了'
        })
        this.kanjiaSet()
      },
      async kanjiaHelpDetail() {
        const res = await this.$wxapi.kanjiaHelpDetail(this.token, this.curGoodsKanjia.id, this
          .curKanjiaprogress.kanjiaInfo.uid)
        if (res.code == 0) {
          this.myHelpDetail = res.data
        }
      },
      async _pingtuanSet() {
        const res = await this.$wxapi.pingtuanSet(this.goodsDetail.basicInfo.id)
        if (res.code == 0) {
          this.pingtuanSet = res.data
          // 如果是拼团商品， 默认显示拼团价格
          this.goodsDetail.basicInfo.minPrice = this.goodsDetail.basicInfo.pingtuanPrice
        }
      },
      onChangeTimeData(e) {
        this.timeData = e
      },
      longButtonSet(e) {
        this.longButton = e
      },
      changeTab(tab) {
        this.longActiveTab = tab
      },
      checkImage(imageUrl) {
        wx.getImageInfo({
          src: imageUrl,
          success: res => {
            this.longImage = imageUrl
          },
          fail: err => {
            console.log(imageUrl + ' does not exist')
          }
        })
      }
    }
  }
</script>
<style scoped lang="scss">
  .goods-detail {
    width: 100vw;
    height: calc(100vh);
    overflow: hidden;
    display: flex;
    flex-direction: column;

    .main {
      flex: 1;
      overflow: hidden;
    }
  }

  .price-share {
    display: flex;
    justify-content: space-between;
    padding: 8rpx 32rpx;
    align-items: center;

    .price {
      color: #e64340;
      font-size: 64rpx;
      margin-top: 12rpx;
      padding-right: 32rpx;

      text {
        margin-left: 16rpx;
        color: #666666;
        font-size: 26rpx;
        text-decoration: line-through;
      }
    }
  }

  .title {
    padding: 0rpx 32rpx;
    color: #293539;
    position: relative;
  }

  .title-sub {
    padding: 16rpx 32rpx;
    color: #666666;
    font-size: 26rpx;
  }

  .commission {
    padding: 16rpx 32rpx;
    color: #e64340;
    font-size: 24rpx;
  }

  .amount {
    text-align: center;
    font-size: 68rpx;
    color: #a78845;
    margin: 64rpx 0;

    text {
      font-size: 48rpx;
      padding-right: 3px;
    }
  }

  .content {
    margin-top: 32rpx;
    padding-bottom: 200rpx;

    image {
      height: auto;
    }

    div {
      height: auto;
    }
  }

  .bottom-btns {
    display: flex;
    background: #FFF;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 16rpx 32rpx 16rpx 32rpx;
    border-top: 1px solid #DCDBDD;

    .icon-btn {
      position: relative;
      display: flex;
      flex-direction: column;
      align-items: center;
      font-size: 24rpx;
      color: #333;
      margin-right: 32rpx;

      button {
        position: absolute;
        height: 100%;
        width: 100%;
        opacity: 0;
        z-index: 99;
      }
    }

    .btn {
      flex: 1;

      .half-l {
        border-top-right-radius: 0;
        border-bottom-right-radius: 0;
      }

      .half-r {
        border-top-left-radius: 0;
        border-bottom-left-radius: 0;
      }
    }
  }

  .reputation-box {
    padding: 32rpx;
  }

  .album {
    @include flex;
    align-items: flex-start;

    &__avatar {
      padding: 5px;
      border-radius: 3px;
    }

    &__content {
      margin-left: 10px;
      flex: 1;
    }
  }

  .haibaopop {
    width: 100vw;
    height: 100vh;
  }

  .goods-images {
    position: relative;
  }

  .delivery-type {
    position: absolute;
    left: 0;
    bottom: -20rpx;
  }

  .goods-title {
    padding: 20rpx 32rpx;
    position: relative;
    overflow: inherit;

    .box-left {
      max-width: 70%;
      min-height: 100rpx;

      text {
        font-weight: bold;
        font-size: 42rpx;
      }
    }

    .btns {
      position: absolute;
      display: flex;
      flex-wrap: wrap;
      top: -80rpx;
      right: 60rpx;
      width: 240rpx;
      justify-content: flex-end;

      text {
        font-size: 24rpx;
        font-weight: normal;
      }

      .icon-btn {
        position: relative;
        display: flex;
        width: 60rpx;
        flex-direction: column;
        align-items: center;
        font-size: 24rpx;
        color: #333;
        margin-left: 48rpx;
        margin-bottom: 20rpx;

        .share {
          position: absolute;
          width: 100%;
          min-height: 80rpx;
          opacity: 0;
        }
      }

      .btn {
        flex: 1;

        .half-l {
          border-top-right-radius: 0;
          border-bottom-right-radius: 0;
        }

        .half-r {
          border-top-left-radius: 0;
          border-bottom-left-radius: 0;
        }
      }
    }
  }

  .curKanjiaJoin {
    padding: 32rpx;
    font-size: 28rpx;

    text {
      color: #e64340;
      font-weight: bold;
      padding: 0 8rpx;
      font-size: 32rpx;
    }
  }

  .curKanjiaprogress {
    padding: 32rpx;
  }

  .price-score2 {
    .item {
      font-size: 64rpx;
    }
  }

  .curKanjiaprogress-bar {
    text-align: center;
    font-size: 20rpx;
    color: #999;
    margin-top: 20rpx;
  }

  .kjlj {
    display: flex;
    background-color: #ffffff;
    padding: 0 32rpx;
    align-items: center;

    .kjlj-l {
      width: 88rpx;
      height: 88rpx;
      border-radius: 50%;
      flex-shrink: 0;
    }

    .kjlj-r {
      flex: 1;
    }
  }

  .price-info {
    font-weight: bolder;
  }

  .price-info .price-left {
    display: inline-block;
    font-weight: normal;
    text-align: left;
  }

  .price-info .price-left .line1 {
    display: flex;
    flex-wrap: nowrap;
  }

  .price-info .price-left .sales {
    font-size: 22rpx;
    color: #324A43;
    margin-left: 10rpx;
  }

  .price-info .price-right {
    font-size: 70rpx;
    display: inline-block;
    letter-spacing: -2px;
    margin-right: 20rpx;
  }

  .price-info .prefix,
  .price-info .suffix {
    font-size: 32rpx;
  }

  .price-info .original-price {
    font-size: 20rpx;
    text-decoration: line-through;
    line-height: 32rpx;
    color: #858996;
    letter-spacing: 0;
    font-weight: normal;
  }

  .max-total,
  .min-total {
    font-size: 22rpx;
    line-height: 24rpx;
    color: #858996;
    margin: 10rpx 0;
  }

  .only {
    font-size: 22rpx;
    line-height: 24rpx;
    color: #858996;
  }

  .only span {
    background-color: #324A43;
    border-radius: 4px;
    display: inline-block;
    text-align: center;
    width: 16px;
    height: 16px;
    line-height: 16px;
    margin: 0 8rpx;
    color: #FFFFFF;
  }

  .delivery-time {
    font-size: 22rpx;
    color: #324A43;
  }

  .price-score text.time__custom__item {
    font-size: 18rpx;
  }


  .poster {
    padding: 24rpx 40rpx;

    .footer-btn {
      margin-top: 24rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;

      view {
        width: 319rpx;
        height: 66rpx;
        border-radius: 40rpx;
        border: 1px solid #e6c70a;
        font-size: 26rpx;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #e6c70a;
        line-height: 66rpx;
        text-align: center;
      }

      .save {
        background: #e6c70a;
        color: #FFFFFF;
      }
    }
  }

  .poster-btn {
    position: fixed;
    bottom: 0;
    width: 100%;
    height: 140rpx;
    background: #FFFFFF;
    border-radius: 32rpx 32rpx 0px 0px;
    display: flex;
    align-items: center;
    justify-content: space-around;

    >view {
      width: 80rpx;
      height: 80rpx;
      position: relative;
      border-radius: 10rpx;
      border: 4rpx solid #fff;

      &.is-check {
        border: 4rpx solid #e6c70a;
      }

      image {
        width: 100%;
        height: 100%;
      }

      view {
        width: 30rpx;
        height: 30rpx;
        position: absolute;
        right: -15rpx;
        bottom: -15rpx;
        background: url('https://s.yun-live.com/images/20210201/311c01265c1aa508418f6bae10d67602.png') no-repeat;
        background-size: 100% 100%;
      }
    }
  }

  .wrapper-xssc-rule .content-rule {
    margin-bottom: 60rpx;
  }

  .wrapper-xssc-rule ul {
    font-size: 24rpx;
    color: #324A43;
    margin: 0 30rpx;
  }

  .content-comment {
    margin: 0 30rpx 30rpx 30rpx;
    font-size: 14px;
  }

  .tab1 {
    margin: 0;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    border-bottom-left-radius: 20rpx;
    border-bottom-right-radius: 20rpx;
    background-image: linear-gradient(to bottom, #f4f4f4, #ffffff);
  }

  .tab1 .tab.line {
    position: relative;
    width: 100%;
    padding-bottom: 2rpx;
  }

  .tab1 .tab.line::after {
    content: "";
    position: absolute;
    top: 20%;
    right: 0;
    bottom: 10%;
    width: 1px;
    background-color: #f1f1f1;
  }

  .tab1 .tab .title {
    font-size: 40rpx;
    font-weight: bolder;
    margin-bottom: 20rpx;
    color: #858996;
  }

  .tab1 .tab.active .title {
    color: #293539;
  }

  .tab1 .tab .dsc {
    font-size: 30rpx;
    height: 60rpx;
  }

  .tab1 .tab.active {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  .tab1 .tab.active .dsc {
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    font-size: 32rpx;
    line-height: 50rpx;
    color: #FFFFFF;
  }
</style>