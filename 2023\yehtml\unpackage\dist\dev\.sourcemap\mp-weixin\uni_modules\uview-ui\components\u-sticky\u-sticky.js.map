{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-sticky/u-sticky.vue?c90b", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-sticky/u-sticky.vue?768a", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-sticky/u-sticky.vue?5463", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-sticky/u-sticky.vue?de68", "uni-app:///uni_modules/uview-ui/components/u-sticky/u-sticky.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-sticky/u-sticky.vue?5bcd", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-sticky/u-sticky.vue?0069"], "names": ["name", "mixins", "data", "cssSticky", "stickyTop", "elId", "left", "width", "height", "fixed", "computed", "style", "sticky<PERSON>ontent", "uZindex", "mounted", "methods", "init", "initObserveContent", "<PERSON><PERSON><PERSON><PERSON>", "thresholds", "contentObserver", "top", "setFixed", "disconnectObserver", "observer", "getStickyTop", "checkSupportCssSticky", "checkComputedStyle", "uni", "computedStyle", "resolve", "checkCssStickyForH5", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnBA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACgBvrB;;;;;;;;;;;;;;;;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,eAeA;EACAA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;IACA;EACA;;EACAC;IACAC;MACA;MACA;QACA;UACAA;UACAA;UACAA;QACA;UACAA;QACA;MACA;QACA;;QAKAA;MAEA;MACAA;MACA;IACA;IACA;IACAC;MACA;MACA;QACAD;QACAA;QACAA;QACAA;QACAA;MACA;MACA;IACA;IACAE;MACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;MACA;MACA;MACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;QACA;QACAC;MACA;MACA;MACAC;QACAC;MACA;MACA;MACAD;QACA;MACA;MACA;IACA;IACAE;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACAC;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAQA;gBACA;kBACA;gBACA;;gBAEA;gBAAA;gBAAA,OAEA;cAAA;gBAAA;gBAGA;gBACA;kBACA;gBACA;;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAIA;IACA;IACAC;MAAA;MACA;;MAEA;QACAC;UACAC;QACA;UACAC;QACA;MACA;IAEA;IACA;IACA;IACAC;MACA;IAAA;EAcA;EACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzMA;AAAA;AAAA;AAAA;AAA0xC,CAAgB,8uCAAG,EAAC,C;;;;;;;;;;;ACA9yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-sticky/u-sticky.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-sticky.vue?vue&type=template&id=e18bd96e&scoped=true&\"\nvar renderjs\nimport script from \"./u-sticky.vue?vue&type=script&lang=js&\"\nexport * from \"./u-sticky.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-sticky.vue?vue&type=style&index=0&id=e18bd96e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e18bd96e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-sticky/u-sticky.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-sticky.vue?vue&type=template&id=e18bd96e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.style])\n  var s1 = _vm.__get_style([_vm.stickyContent])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-sticky.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-sticky.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t\tclass=\"u-sticky\"\r\n\t\t:id=\"elId\"\r\n\t\t:style=\"[style]\"\r\n\t>\r\n\t\t<view\r\n\t\t\t:style=\"[stickyContent]\"\r\n\t\t\tclass=\"u-sticky__content\"\r\n\t\t>\r\n\t\t\t<slot />\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';;\r\n\t/**\r\n\t * sticky 吸顶\r\n\t * @description 该组件与CSS中position: sticky属性实现的效果一致，当组件达到预设的到顶部距离时， 就会固定在指定位置，组件位置大于预设的顶部距离时，会重新按照正常的布局排列。\r\n\t * @tutorial https://www.uviewui.com/components/sticky.html\r\n\t * @property {String ｜ Number}\toffsetTop\t\t吸顶时与顶部的距离，单位px（默认 0 ）\r\n\t * @property {String ｜ Number}\tcustomNavHeight\t自定义导航栏的高度 （h5 默认44  其他默认 0 ）\r\n\t * @property {Boolean}\t\t\tdisabled\t\t是否开启吸顶功能 （默认 false ）\r\n\t * @property {String}\t\t\tbgColor\t\t\t组件背景颜色（默认 '#ffffff' ）\r\n\t * @property {String ｜ Number}\tzIndex\t\t\t吸顶时的z-index值\r\n\t * @property {String ｜ Number}\tindex\t\t\t自定义标识，用于区分是哪一个组件\r\n\t * @property {Object}\t\t\tcustomStyle\t\t组件的样式，对象形式\r\n\t * @event {Function} fixed\t\t组件吸顶时触发\r\n\t * @event {Function} unfixed\t组件取消吸顶时触发\r\n\t * @example <u-sticky offsetTop=\"200\"><view>塞下秋来风景异，衡阳雁去无留意</view></u-sticky>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-sticky',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tcssSticky: false, // 是否使用css的sticky实现\r\n\t\t\t\tstickyTop: 0, // 吸顶的top值，因为可能受自定义导航栏影响，最终的吸顶值非offsetTop值\r\n\t\t\t\telId: uni.$u.guid(),\r\n\t\t\t\tleft: 0, // js模式时，吸顶的内容因为处于postition: fixed模式，为了和原来保持一致的样式，需要记录并重新设置它的left，height，width属性\r\n\t\t\t\twidth: 'auto',\r\n\t\t\t\theight: 'auto',\r\n\t\t\t\tfixed: false, // js模式时，是否处于吸顶模式\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tstyle() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\tif(!this.disabled) {\r\n\t\t\t\t\tif (this.cssSticky) {\r\n\t\t\t\t\t\tstyle.position = 'sticky'\r\n\t\t\t\t\t\tstyle.zIndex = this.uZindex\r\n\t\t\t\t\t\tstyle.top = uni.$u.addUnit(this.stickyTop)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\tstyle.height = this.fixed ? this.height + 'px' : 'auto'\r\n\t\t\t\t\t}\r\n\t\t\t\t} else {\r\n\t\t\t\t\t// 无需吸顶时，设置会默认的relative(nvue)和非nvue的static静态模式即可\r\n\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\tstyle.position = 'relative'\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t\tstyle.position = 'static'\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t\tstyle.backgroundColor = this.bgColor\r\n\t\t\t\treturn uni.$u.deepMerge(uni.$u.addStyle(this.customStyle), style)\r\n\t\t\t},\r\n\t\t\t// 吸顶内容的样式\r\n\t\t\tstickyContent() {\r\n\t\t\t\tconst style = {}\r\n\t\t\t\tif (!this.cssSticky) {\r\n\t\t\t\t\tstyle.position = this.fixed ? 'fixed' : 'static'\r\n\t\t\t\t\tstyle.top = this.stickyTop + 'px'\r\n\t\t\t\t\tstyle.left = this.left + 'px'\r\n\t\t\t\t\tstyle.width = this.width == 'auto' ? 'auto' : this.width + 'px'\r\n\t\t\t\t\tstyle.zIndex = this.uZindex\r\n\t\t\t\t}\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\tuZindex() {\r\n\t\t\t\treturn this.zIndex ? this.zIndex : uni.$u.zIndex.sticky\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit() {\r\n\t\t\t\tthis.getStickyTop()\r\n\t\t\t\t// 判断使用的模式\r\n\t\t\t\tthis.checkSupportCssSticky()\r\n\t\t\t\t// 如果不支持css sticky，则使用js方案，此方案性能比不上css方案\r\n\t\t\t\tif (!this.cssSticky) {\r\n\t\t\t\t\t!this.disabled && this.initObserveContent()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tinitObserveContent() {\r\n\t\t\t\t// 获取吸顶内容的高度，用于在js吸顶模式时，给父元素一个填充高度，防止\"塌陷\"\r\n\t\t\t\tthis.$uGetRect('#' + this.elId).then((res) => {\r\n\t\t\t\t\tthis.height = res.height\r\n\t\t\t\t\tthis.left = res.left\r\n\t\t\t\t\tthis.width = res.width\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.observeContent()\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tobserveContent() {\r\n\t\t\t\t// 先断掉之前的观察\r\n\t\t\t\tthis.disconnectObserver('contentObserver')\r\n\t\t\t\tconst contentObserver = uni.createIntersectionObserver({\r\n\t\t\t\t\t// 检测的区间范围\r\n\t\t\t\t\tthresholds: [0.95, 0.98, 1]\r\n\t\t\t\t})\r\n\t\t\t\t// 到屏幕顶部的高度时触发\r\n\t\t\t\tcontentObserver.relativeToViewport({\r\n\t\t\t\t\ttop: -this.stickyTop\r\n\t\t\t\t})\r\n\t\t\t\t// 绑定观察的元素\r\n\t\t\t\tcontentObserver.observe(`#${this.elId}`, res => {\r\n\t\t\t\t\tthis.setFixed(res.boundingClientRect.top)\r\n\t\t\t\t})\r\n\t\t\t\tthis.contentObserver = contentObserver\r\n\t\t\t},\r\n\t\t\tsetFixed(top) {\r\n\t\t\t\t// 判断是否出于吸顶条件范围\r\n\t\t\t\tconst fixed = top <= this.stickyTop\r\n\t\t\t\tthis.fixed = fixed\r\n\t\t\t},\r\n\t\t\tdisconnectObserver(observerName) {\r\n\t\t\t\t// 断掉观察，释放资源\r\n\t\t\t\tconst observer = this[observerName]\r\n\t\t\t\tobserver && observer.disconnect()\r\n\t\t\t},\r\n\t\t\tgetStickyTop() {\r\n\t\t\t\tthis.stickyTop = uni.$u.getPx(this.offsetTop) + uni.$u.getPx(this.customNavHeight)\r\n\t\t\t},\r\n\t\t\tasync checkSupportCssSticky() {\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\t// H5，一般都是现代浏览器，是支持css sticky的，这里使用创建元素嗅探的形式判断\r\n\t\t\t\tif (this.checkCssStickyForH5()) {\r\n\t\t\t\t\tthis.cssSticky = true\r\n\t\t\t\t}\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// 如果安卓版本高于8.0，依然认为是支持css sticky的(因为安卓7在某些机型，可能不支持sticky)\r\n\t\t\t\tif (uni.$u.os() === 'android' && Number(uni.$u.sys().system) > 8) {\r\n\t\t\t\t\tthis.cssSticky = true\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// APP-Vue和微信平台，通过computedStyle判断是否支持css sticky\r\n\t\t\t\t// #ifdef APP-VUE || MP-WEIXIN\r\n\t\t\t\tthis.cssSticky = await this.checkComputedStyle()\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// ios上，从ios6开始，都是支持css sticky的\r\n\t\t\t\tif (uni.$u.os() === 'ios') {\r\n\t\t\t\t\tthis.cssSticky = true\r\n\t\t\t\t}\r\n\r\n\t\t\t\t// nvue，是支持css sticky的\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.cssSticky = true\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 在APP和微信小程序上，通过uni.createSelectorQuery可以判断是否支持css sticky\r\n\t\t\tcheckComputedStyle() {\r\n\t\t\t\t// 方法内进行判断，避免在其他平台生成无用代码\r\n\t\t\t\t// #ifdef APP-VUE || MP-WEIXIN\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tuni.createSelectorQuery().in(this).select('.u-sticky').fields({\r\n\t\t\t\t\t\tcomputedStyle: [\"position\"]\r\n\t\t\t\t\t}).exec(e => {\r\n\t\t\t\t\t\tresolve('sticky' === e[0].position)\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// H5通过创建元素的形式嗅探是否支持css sticky\r\n\t\t\t// 判断浏览器是否支持sticky属性\r\n\t\t\tcheckCssStickyForH5() {\r\n\t\t\t\t// 方法内进行判断，避免在其他平台生成无用代码\r\n\t\t\t\t// #ifdef H5\r\n\t\t\t\tconst vendorList = ['', '-webkit-', '-ms-', '-moz-', '-o-'],\r\n\t\t\t\t\tvendorListLength = vendorList.length,\r\n\t\t\t\t\tstickyElement = document.createElement('div')\r\n\t\t\t\tfor (let i = 0; i < vendorListLength; i++) {\r\n\t\t\t\t\tstickyElement.style.position = vendorList[i] + 'sticky'\r\n\t\t\t\t\tif (stickyElement.style.position !== '') {\r\n\t\t\t\t\t\treturn true\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\treturn false;\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tbeforeDestroy() {\r\n\t\t\tthis.disconnectObserver('contentObserver')\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t.u-sticky {\r\n\t\t/* #ifdef APP-VUE || MP-WEIXIN */\r\n\t\t// 此处默认写sticky属性，是为了给微信和APP通过uni.createSelectorQuery查询是否支持css sticky使用\r\n\t\tposition: sticky;\r\n\t\t/* #endif */\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-sticky.vue?vue&type=style&index=0&id=e18bd96e&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-sticky.vue?vue&type=style&index=0&id=e18bd96e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688733647\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}