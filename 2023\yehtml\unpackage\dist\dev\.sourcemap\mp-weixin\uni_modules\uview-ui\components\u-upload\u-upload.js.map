{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-upload/u-upload.vue?1d7d", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-upload/u-upload.vue?d34a", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-upload/u-upload.vue?eafe", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-upload/u-upload.vue?88ad", "uni-app:///uni_modules/uview-ui/components/u-upload/u-upload.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-upload/u-upload.vue?b760", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-upload/u-upload.vue?263d"], "names": ["name", "mixins", "data", "lists", "isInCount", "watch", "fileList", "immediate", "handler", "methods", "formatFileList", "maxCount", "Object", "isImage", "isVideo", "deletable", "chooseFile", "multiple", "disabled", "capture", "accept", "compressed", "maxDuration", "sizeType", "camera", "then", "catch", "onBeforeRead", "beforeRead", "useBeforeRead", "res", "file", "callback", "ok", "getDetail", "index", "onAfterRead", "maxSize", "afterRead", "deleteItem", "onPreviewImage", "uni", "urls", "current", "fail", "onPreviewVideo", "event", "wx", "sources", "filter", "map", "type", "onClickPreview"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,kVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5EA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+HvrB;AAGA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhCA,eAiCA;EACAA;EACAC;EACAC;IACA;MAIAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACAC;MACAC;QACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA,qBAEA,KADAJ;QAAAA;QAAAK,WACA,KADAA;MAEA;QAAA,OACAC;UACA;UACAC;UACAC;UACAC;QACA;MAAA,EACA;MACA;MACA;IACA;IACAC;MAAA;MACA,IACAL,WAIA,KAJAA;QACAM,WAGA,KAHAA;QACAd,QAEA,KAFAA;QACAe,WACA,KADAA;MAEA;MACA;MACA;MACA;QACAC;MACA;QACAA;MACA;MACA,uBACAP;QACAQ;QACAH;QACAE;QACAE;QACAC;QACAC;QACAC;MACA;QACAb;MACA,GACA,CACAc;QACA;MACA,GACAC;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA,IACAC,aAEA,KAFAA;QACAC,gBACA,KADAA;MAEA;MACA;MACA;QACA;QACAC;MACA;MACA;QACAA;UACA,aACA,cACAlB;YACAmB;UACA;YACAC;cACAC;YACA;UACA,GACA;QACA;MACA;MACA;QACA;MACA;MACA;QACAH;UAAA;QAAA;MACA;QACA;MACA;IACA;IACAI;MACA;QACAlC;QACAmC;MACA;IACA;IACAC;MACA,IACAC,UAEA,KAFAA;QACAC,YACA,KADAA;MAEA,qCACAP;QAAA;MAAA,KACAA;MACA;QACA;UACAA;QACA;QACA;MACA;MACA;QACAO;MACA;MACA;QACAP;MACA;IACA;IACAQ;MACA,WACA,UACA3B;QACAmB;MACA,GACA;IACA;IACA;IACAS;MAAA;MACA;MACAC;QACA;QACAC;UAAA;QAAA;UAAA;QAAA;QACAC;QACAC;UACAH;QACA;MACA;IACA;IACAI;MACA;MACA,IACAV,QACAW,4BADAX;MAEA,IACAhC,QACA,UADAA;MAEA4C;QACAC,eACAC;UAAA;QAAA,GACAC;UAAA,OACAtC;YACAuC;UACA;QAAA,EACA;QACAR;QACAC;UACAH;QACA;MACA;IACA;IACAW;MACA,IACAjB,QACAW,4BADAX;MAEA;MACA,WACA,gBACAvB,8DACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjWA;AAAA;AAAA;AAAA;AAA0xC,CAAgB,8uCAAG,EAAC,C;;;;;;;;;;;ACA9yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-upload/u-upload.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-upload.vue?vue&type=template&id=69e2a36e&scoped=true&\"\nvar renderjs\nimport script from \"./u-upload.vue?vue&type=script&lang=js&\"\nexport * from \"./u-upload.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-upload.vue?vue&type=style&index=0&id=69e2a36e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"69e2a36e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-upload/u-upload.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-upload.vue?vue&type=template&id=69e2a36e&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-loading-icon/u-loading-icon\" */ \"@/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.$u.addStyle(_vm.customStyle)])\n  var l0 = _vm.previewImage\n    ? _vm.__map(_vm.lists, function (item, index) {\n        var $orig = _vm.__get_orig(item)\n        var g0 =\n          item.isImage || (item.type && item.type === \"image\")\n            ? _vm.$u.addUnit(_vm.width)\n            : null\n        var g1 =\n          item.isImage || (item.type && item.type === \"image\")\n            ? _vm.$u.addUnit(_vm.height)\n            : null\n        return {\n          $orig: $orig,\n          g0: g0,\n          g1: g1,\n        }\n      })\n    : null\n  var g2 =\n    _vm.isInCount && !(_vm.$slots.default || _vm.$slots.$default)\n      ? _vm.$u.addUnit(_vm.width)\n      : null\n  var g3 =\n    _vm.isInCount && !(_vm.$slots.default || _vm.$slots.$default)\n      ? _vm.$u.addUnit(_vm.height)\n      : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        l0: l0,\n        g2: g2,\n        g3: g3,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-upload.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-upload.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-upload\" :style=\"[$u.addStyle(customStyle)]\">\r\n\t\t<view class=\"u-upload__wrap\" >\r\n\t\t\t<template v-if=\"previewImage\">\r\n\t\t\t\t<view\r\n\t\t\t\t    class=\"u-upload__wrap__preview\"\r\n\t\t\t\t    v-for=\"(item, index) in lists\"\r\n\t\t\t\t    :key=\"index\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<image\r\n\t\t\t\t\t    v-if=\"item.isImage || (item.type && item.type === 'image')\"\r\n\t\t\t\t\t    :src=\"item.thumb || item.url\"\r\n\t\t\t\t\t    :mode=\"imageMode\"\r\n\t\t\t\t\t    class=\"u-upload__wrap__preview__image\"\r\n\t\t\t\t\t    @tap=\"onPreviewImage(item)\"\r\n\t\t\t\t\t\t:style=\"[{\r\n\t\t\t\t\t\t\twidth: $u.addUnit(width),\r\n\t\t\t\t\t\t\theight: $u.addUnit(height)\r\n\t\t\t\t\t\t}]\"\r\n\t\t\t\t\t/>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t    v-else\r\n\t\t\t\t\t    class=\"u-upload__wrap__preview__other\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t    color=\"#80CBF9\"\r\n\t\t\t\t\t\t    size=\"26\"\r\n\t\t\t\t\t\t    :name=\"item.isVideo || (item.type && item.type === 'video') ? 'movie' : 'folder'\"\r\n\t\t\t\t\t\t></u-icon>\r\n\t\t\t\t\t\t<text class=\"u-upload__wrap__preview__other__text\">{{item.isVideo || (item.type && item.type === 'video') ? '视频' : '文件'}}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t    class=\"u-upload__status\"\r\n\t\t\t\t\t    v-if=\"item.status === 'uploading' || item.status === 'failed'\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"u-upload__status__icon\">\r\n\t\t\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t\t    v-if=\"item.status === 'failed'\"\r\n\t\t\t\t\t\t\t    name=\"close-circle\"\r\n\t\t\t\t\t\t\t    color=\"#ffffff\"\r\n\t\t\t\t\t\t\t    size=\"25\"\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t<u-loading-icon\r\n\t\t\t\t\t\t\t    size=\"22\"\r\n\t\t\t\t\t\t\t    mode=\"circle\"\r\n\t\t\t\t\t\t\t    color=\"#ffffff\"\r\n\t\t\t\t\t\t\t    v-else\r\n\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t    v-if=\"item.message\"\r\n\t\t\t\t\t\t    class=\"u-upload__status__message\"\r\n\t\t\t\t\t\t>{{ item.message }}</text>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t    class=\"u-upload__deletable\"\r\n\t\t\t\t\t    v-if=\"item.status !== 'uploading' && (deletable || item.deletable)\"\r\n\t\t\t\t\t    @tap.stop=\"deleteItem(index)\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<view class=\"u-upload__deletable__icon\">\r\n\t\t\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t\t    name=\"close\"\r\n\t\t\t\t\t\t\t    color=\"#ffffff\"\r\n\t\t\t\t\t\t\t    size=\"10\"\r\n\t\t\t\t\t\t\t></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view\r\n\t\t\t\t\t    class=\"u-upload__success\"\r\n\t\t\t\t\t    v-if=\"item.status === 'success'\"\r\n\t\t\t\t\t>\r\n\t\t\t\t\t\t<!-- #ifdef APP-NVUE -->\r\n\t\t\t\t\t\t<image\r\n\t\t\t\t\t\t    :src=\"successIcon\"\r\n\t\t\t\t\t\t    class=\"u-upload__success__icon\"\r\n\t\t\t\t\t\t></image>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<!-- #ifndef APP-NVUE -->\r\n\t\t\t\t\t\t<view class=\"u-upload__success__icon\">\r\n\t\t\t\t\t\t\t<u-icon\r\n\t\t\t\t\t\t\t    name=\"checkmark\"\r\n\t\t\t\t\t\t\t    color=\"#ffffff\"\r\n\t\t\t\t\t\t\t    size=\"12\"\r\n\t\t\t\t\t\t\t></u-icon>\r\n\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t\r\n\t\t\t</template>\r\n\t\t\t\r\n\t\t\t<template v-if=\"isInCount\">\r\n\t\t\t\t<view\r\n\t\t\t\t    v-if=\"$slots.default || $slots.$default\"\r\n\t\t\t\t    @tap=\"chooseFile\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<slot />\r\n\t\t\t\t</view>\r\n\t\t\t\t<view\r\n\t\t\t\t    v-else\r\n\t\t\t\t    class=\"u-upload__button\"\r\n\t\t\t\t    :hover-class=\"!disabled ? 'u-upload__button--hover' : ''\"\r\n\t\t\t\t    hover-stay-time=\"150\"\r\n\t\t\t\t    @tap=\"chooseFile\"\r\n\t\t\t\t    :class=\"[disabled && 'u-upload__button--disabled']\"\r\n\t\t\t\t\t:style=\"[{\r\n\t\t\t\t\t\twidth: $u.addUnit(width),\r\n\t\t\t\t\t\theight: $u.addUnit(height)\r\n\t\t\t\t\t}]\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<u-icon\r\n\t\t\t\t\t    :name=\"uploadIcon\"\r\n\t\t\t\t\t    size=\"26\"\r\n\t\t\t\t\t    :color=\"uploadIconColor\"\r\n\t\t\t\t\t></u-icon>\r\n\t\t\t\t\t<text\r\n\t\t\t\t\t    v-if=\"uploadText\"\r\n\t\t\t\t\t    class=\"u-upload__button__text\"\r\n\t\t\t\t\t>{{ uploadText }}</text>\r\n\t\t\t\t</view>\r\n\t\t\t</template>\r\n\t\t</view>\r\n\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport {\r\n\t\tchooseFile\r\n\t} from './utils';\r\n\timport mixin from './mixin.js';\r\n\timport props from './props.js';\r\n\r\n\t/**\r\n\t * upload 上传\r\n\t * @description 该组件用于上传图片场景\r\n\t * @tutorial https://uviewui.com/components/upload.html\r\n\t * @property {String}\t\t\taccept\t\t\t\t接受的文件类型, 可选值为all media image file video （默认 'image' ）\r\n\t * @property {String | Array}\tcapture\t\t\t\t图片或视频拾取模式，当accept为image类型时设置capture可选额外camera可以直接调起摄像头（默认 ['album', 'camera'] ）\r\n\t * @property {Boolean}\t\t\tcompressed\t\t\t当accept为video时生效，是否压缩视频，默认为true（默认 true ）\r\n\t * @property {String}\t\t\tcamera\t\t\t\t当accept为video时生效，可选值为back或front（默认 'back' ）\r\n\t * @property {Number}\t\t\tmaxDuration\t\t\t当accept为video时生效，拍摄视频最长拍摄时间，单位秒（默认 60 ）\r\n\t * @property {String}\t\t\tuploadIcon\t\t\t上传区域的图标，只能内置图标（默认 'camera-fill' ）\r\n\t * @property {String}\t\t\tuploadIconColor\t\t上传区域的图标的字体颜色，只能内置图标（默认 #D3D4D6 ）\r\n\t * @property {Boolean}\t\t\tuseBeforeRead\t\t是否开启文件读取前事件（默认 false ）\r\n\t * @property {Boolean}\t\t\tpreviewFullImage\t是否显示组件自带的图片预览功能（默认 true ）\r\n\t * @property {String | Number}\tmaxCount\t\t\t最大上传数量（默认 52 ）\r\n\t * @property {Boolean}\t\t\tdisabled\t\t\t是否启用（默认 false ）\r\n\t * @property {String}\t\t\timageMode\t\t\t预览上传的图片时的裁剪模式，和image组件mode属性一致（默认 'aspectFill' ）\r\n\t * @property {String}\t\t\tname\t\t\t\t标识符，可以在回调函数的第二项参数中获取\r\n\t * @property {Array}\t\t\tsizeType\t\t\t所选的图片的尺寸, 可选值为original compressed（默认 ['original', 'compressed'] ）\r\n\t * @property {Boolean}\t\t\tmultiple\t\t\t是否开启图片多选，部分安卓机型不支持 （默认 false ）\r\n\t * @property {Boolean}\t\t\tdeletable\t\t\t是否展示删除按钮（默认 true ）\r\n\t * @property {String | Number}\tmaxSize\t\t\t\t文件大小限制，单位为byte （默认 Number.MAX_VALUE ）\r\n\t * @property {Array}\t\t\tfileList\t\t\t显示已上传的文件列表\r\n\t * @property {String}\t\t\tuploadText\t\t\t上传区域的提示文字\r\n\t * @property {String | Number}\twidth\t\t\t\t内部预览图片区域和选择图片按钮的区域宽度（默认 80 ）\r\n\t * @property {String | Number}\theight\t\t\t\t内部预览图片区域和选择图片按钮的区域高度（默认 80 ）\r\n\t * @property {Object}\t\t\tcustomStyle\t\t\t组件的样式，对象形式\r\n\t * @event {Function} afterRead\t\t读取后的处理函数\r\n\t * @event {Function} beforeRead\t\t读取前的处理函数\r\n\t * @event {Function} oversize\t\t文件超出大小限制\r\n\t * @event {Function} clickPreview\t点击预览图片\r\n\t * @event {Function} delete \t\t删除图片\r\n\t * @example <u-upload :action=\"action\" :fileList=\"fileList\" ></u-upload>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-upload\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, mixin,props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tsuccessIcon: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACgAAAAoCAYAAACM/rhtAAAAAXNSR0IArs4c6QAAAERlWElmTU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAKKADAAQAAAABAAAAKAAAAAB65masAAACP0lEQVRYCc3YXygsURwH8K/dpcWyG3LF5u/6/+dKVylSypuUl6uUPMifKMWL8oKEB1EUT1KeUPdR3uTNUsSLxb2udG/cbvInNuvf2rVnazZ/ZndmZ87snjM1Z+Z3zpzfp9+Z5mEAhlvjRtZgCKs+gnPAOcAkkMOR4jEHfItjDvgRxxSQD8cM0BuOCaAvXNCBQrigAsXgggYUiwsK0B9cwIH+4gIKlIILGFAqLiBAOTjFgXJxigJp4BQD0sIpAqSJow6kjSNAFTnRaHJwLenD6Mud52VQAcrBfTd2oyq+HtGaGGWAcnAVcXWoM3bCZrdi+ncPfaAcXE5UKVpdW/vitGPqqAtn98d0gXJwX7Qp6MmegUYVhvmTIezdmHlxJCjpHRTCFerLkRRu4k0aqdajN3sWOo0BK//msHa+xDuPC/oNFMKRhTtM4xjIX0SCNpXL4+7VIaHuyiWEp2L7ahWLf8fejfPdqPmC3mJicORZUp1CQzm+GiphvljGk+PBvWRbxii+xVTj5M6CiZ/tsDufvaXyxEUDxeLIyvu3m0iOyEFWVAkydcVYdyFrE9tQk9iMq6f/GNlvwt3LjQfh60LUrw9/cFyyMJUW/XkLSNMV4Mi6C5ML+ui4x5ClAX9sB9w0wV6wglJwJCv5fOxcr6EstgbGiEw4XcfUry4cWrcEUW8n+ARKxXEJHhw2WG43UKSvwI/TSZgvl7kh0b3XLZaLEy0QmMgLZAVH7J+ALOE+AVnDvQOyiPMAWcW5gSzjCPAV+78S5WE0GrQAAAAASUVORK5CYII=',\r\n\t\t\t\t// #endif\r\n\t\t\t\tlists: [],\r\n\t\t\t\tisInCount: true,\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\t// 监听文件列表的变化，重新整理内部数据\r\n\t\t\tfileList: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler() {\r\n\t\t\t\t\tthis.formatFileList()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tformatFileList() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tfileList = [], maxCount\r\n\t\t\t\t} = this;\r\n\t\t\t\tconst lists = fileList.map((item) =>\r\n\t\t\t\t\tObject.assign(Object.assign({}, item), {\r\n\t\t\t\t\t\t// 如果item.url为本地选择的blob文件的话，无法判断其为video还是image，此处优先通过accept做判断处理\r\n\t\t\t\t\t\tisImage: this.accept === 'image' || uni.$u.test.image(item.url || item.thumb),\r\n\t\t\t\t\t\tisVideo: this.accept === 'video' || uni.$u.test.video(item.url || item.thumb),\r\n\t\t\t\t\t\tdeletable: typeof(item.deletable) === 'boolean' ? item.deletable : this.deletable,\r\n\t\t\t\t\t})\r\n\t\t\t\t);\r\n\t\t\t\tthis.lists = lists\r\n\t\t\t\tthis.isInCount = lists.length < maxCount\r\n\t\t\t},\r\n\t\t\tchooseFile() {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tmaxCount,\r\n\t\t\t\t\tmultiple,\r\n\t\t\t\t\tlists,\r\n\t\t\t\t\tdisabled\r\n\t\t\t\t} = this;\r\n\t\t\t\tif (disabled) return;\r\n\t\t\t\t// 如果用户传入的是字符串，需要格式化成数组\r\n\t\t\t\tlet capture;\r\n\t\t\t\ttry {\r\n\t\t\t\t\tcapture = uni.$u.test.array(this.capture) ? this.capture : this.capture.split(',');\r\n\t\t\t\t}catch(e) {\r\n\t\t\t\t\tcapture = [];\r\n\t\t\t\t}\r\n\t\t\t\tchooseFile(\r\n\t\t\t\t\t\tObject.assign({\r\n\t\t\t\t\t\t\taccept: this.accept,\r\n\t\t\t\t\t\t\tmultiple: this.multiple,\r\n\t\t\t\t\t\t\tcapture: capture,\r\n\t\t\t\t\t\t\tcompressed: this.compressed,\r\n\t\t\t\t\t\t\tmaxDuration: this.maxDuration,\r\n\t\t\t\t\t\t\tsizeType: this.sizeType,\r\n\t\t\t\t\t\t\tcamera: this.camera,\r\n\t\t\t\t\t\t}, {\r\n\t\t\t\t\t\t\tmaxCount: maxCount - lists.length,\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t)\r\n\t\t\t\t\t.then((res) => {\r\n\t\t\t\t\t\tthis.onBeforeRead(multiple ? res : res[0]);\r\n\t\t\t\t\t})\r\n\t\t\t\t\t.catch((error) => {\r\n\t\t\t\t\t\tthis.$emit('error', error);\r\n\t\t\t\t\t});\r\n\t\t\t},\r\n\t\t\t// 文件读取之前\r\n\t\t\tonBeforeRead(file) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tbeforeRead,\r\n\t\t\t\t\tuseBeforeRead,\r\n\t\t\t\t} = this;\r\n\t\t\t\tlet res = true\r\n\t\t\t\t// beforeRead是否为一个方法\r\n\t\t\t\tif (uni.$u.test.func(beforeRead)) {\r\n\t\t\t\t\t// 如果用户定义了此方法，则去执行此方法，并传入读取的文件回调\r\n\t\t\t\t\tres = beforeRead(file, this.getDetail());\r\n\t\t\t\t}\r\n\t\t\t\tif (useBeforeRead) {\r\n\t\t\t\t\tres = new Promise((resolve, reject) => {\r\n\t\t\t\t\t\tthis.$emit(\r\n\t\t\t\t\t\t\t'beforeRead',\r\n\t\t\t\t\t\t\tObject.assign(Object.assign({\r\n\t\t\t\t\t\t\t\tfile\r\n\t\t\t\t\t\t\t}, this.getDetail()), {\r\n\t\t\t\t\t\t\t\tcallback: (ok) => {\r\n\t\t\t\t\t\t\t\t\tok ? resolve() : reject();\r\n\t\t\t\t\t\t\t\t},\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t);\r\n\t\t\t\t\t});\r\n\t\t\t\t}\r\n\t\t\t\tif (!res) {\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (uni.$u.test.promise(res)) {\r\n\t\t\t\t\tres.then((data) => this.onAfterRead(data || file));\r\n\t\t\t\t} else {\r\n\t\t\t\t\tthis.onAfterRead(file);\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetDetail(index) {\r\n\t\t\t\treturn {\r\n\t\t\t\t\tname: this.name,\r\n\t\t\t\t\tindex: index == null ? this.fileList.length : index,\r\n\t\t\t\t};\r\n\t\t\t},\r\n\t\t\tonAfterRead(file) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tmaxSize,\r\n\t\t\t\t\tafterRead\r\n\t\t\t\t} = this;\r\n\t\t\t\tconst oversize = Array.isArray(file) ?\r\n\t\t\t\t\tfile.some((item) => item.size > maxSize) :\r\n\t\t\t\t\tfile.size > maxSize;\r\n\t\t\t\tif (oversize) {\r\n\t\t\t\t\tthis.$emit('oversize', Object.assign({\r\n\t\t\t\t\t\tfile\r\n\t\t\t\t\t}, this.getDetail()));\r\n\t\t\t\t\treturn;\r\n\t\t\t\t}\r\n\t\t\t\tif (typeof afterRead === 'function') {\r\n\t\t\t\t\tafterRead(file, this.getDetail());\r\n\t\t\t\t}\r\n\t\t\t\tthis.$emit('afterRead', Object.assign({\r\n\t\t\t\t\tfile\r\n\t\t\t\t}, this.getDetail()));\r\n\t\t\t},\r\n\t\t\tdeleteItem(index) {\r\n\t\t\t\tthis.$emit(\r\n\t\t\t\t\t'delete',\r\n\t\t\t\t\tObject.assign(Object.assign({}, this.getDetail(index)), {\r\n\t\t\t\t\t\tfile: this.fileList[index],\r\n\t\t\t\t\t})\r\n\t\t\t\t);\r\n\t\t\t},\r\n\t\t\t// 预览图片\r\n\t\t\tonPreviewImage(item) {\r\n\t\t\t\tif (!item.isImage || !this.previewFullImage) return\r\n\t\t\t\tuni.previewImage({\r\n\t\t\t\t\t// 先filter找出为图片的item，再返回filter结果中的图片url\r\n\t\t\t\t\turls: this.lists.filter((item) => this.accept === 'image' || uni.$u.test.image(item.url || item.thumb)).map((item) => item.url || item.thumb),\r\n\t\t\t\t\tcurrent: item.url || item.thumb,\r\n\t\t\t\t\tfail() {\r\n\t\t\t\t\t\tuni.$u.toast('预览图片失败')\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonPreviewVideo(event) {\r\n\t\t\t\tif (!this.data.previewFullImage) return;\r\n\t\t\t\tconst {\r\n\t\t\t\t\tindex\r\n\t\t\t\t} = event.currentTarget.dataset;\r\n\t\t\t\tconst {\r\n\t\t\t\t\tlists\r\n\t\t\t\t} = this.data;\r\n\t\t\t\twx.previewMedia({\r\n\t\t\t\t\tsources: lists\r\n\t\t\t\t\t\t.filter((item) => isVideoFile(item))\r\n\t\t\t\t\t\t.map((item) =>\r\n\t\t\t\t\t\t\tObject.assign(Object.assign({}, item), {\r\n\t\t\t\t\t\t\t\ttype: 'video'\r\n\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t),\r\n\t\t\t\t\tcurrent: index,\r\n\t\t\t\t\tfail() {\r\n\t\t\t\t\t\tuni.$u.toast('预览视频失败')\r\n\t\t\t\t\t},\r\n\t\t\t\t});\r\n\t\t\t},\r\n\t\t\tonClickPreview(event) {\r\n\t\t\t\tconst {\r\n\t\t\t\t\tindex\r\n\t\t\t\t} = event.currentTarget.dataset;\r\n\t\t\t\tconst item = this.data.lists[index];\r\n\t\t\t\tthis.$emit(\r\n\t\t\t\t\t'clickPreview',\r\n\t\t\t\t\tObject.assign(Object.assign({}, item), this.getDetail(index))\r\n\t\t\t\t);\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import '../../libs/css/components.scss';\r\n\t$u-upload-preview-border-radius: 2px !default;\r\n\t$u-upload-preview-margin: 0 8px 8px 0 !default;\r\n\t$u-upload-image-width:80px !default;\r\n\t$u-upload-image-height:$u-upload-image-width;\r\n\t$u-upload-other-bgColor: rgb(242, 242, 242) !default;\r\n\t$u-upload-other-flex:1 !default;\r\n\t$u-upload-text-font-size:11px !default;\r\n\t$u-upload-text-color:$u-tips-color !default;\r\n\t$u-upload-text-margin-top:2px !default;\r\n\t$u-upload-deletable-right:0 !default;\r\n\t$u-upload-deletable-top:0 !default;\r\n\t$u-upload-deletable-bgColor:rgb(55, 55, 55) !default;\r\n\t$u-upload-deletable-height:14px !default;\r\n\t$u-upload-deletable-width:$u-upload-deletable-height;\r\n\t$u-upload-deletable-boder-bottom-left-radius:100px !default;\r\n\t$u-upload-deletable-zIndex:3 !default;\r\n\t$u-upload-success-bottom:0 !default;\r\n\t$u-upload-success-right:0 !default;\r\n\t$u-upload-success-border-style:solid !default;\r\n\t$u-upload-success-border-top-color:transparent !default;\r\n\t$u-upload-success-border-left-color:transparent !default;\r\n\t$u-upload-success-border-bottom-color: $u-success !default;\r\n\t$u-upload-success-border-right-color:$u-upload-success-border-bottom-color;\r\n\t$u-upload-success-border-width:9px !default;\r\n\t$u-upload-icon-top:0px !default;\r\n\t$u-upload-icon-right:0px !default;\r\n\t$u-upload-icon-h5-top:1px !default;\r\n\t$u-upload-icon-h5-right:0 !default;\r\n\t$u-upload-icon-width:16px !default;\r\n\t$u-upload-icon-height:$u-upload-icon-width;\r\n\t$u-upload-success-icon-bottom:-10px !default;\r\n\t$u-upload-success-icon-right:-10px !default;\r\n\t$u-upload-status-right:0 !default;\r\n\t$u-upload-status-left:0 !default;\r\n\t$u-upload-status-bottom:0 !default;\r\n\t$u-upload-status-top:0 !default;\r\n\t$u-upload-status-bgColor:rgba(0, 0, 0, 0.5) !default;\r\n\t$u-upload-status-icon-Zindex:1 !default;\r\n\t$u-upload-message-font-size:12px !default;\r\n\t$u-upload-message-color:#FFFFFF !default;\r\n\t$u-upload-message-margin-top:5px !default;\r\n\t$u-upload-button-width:80px !default;\r\n\t$u-upload-button-height:$u-upload-button-width;\r\n\t$u-upload-button-bgColor:rgb(244, 245, 247) !default;\r\n\t$u-upload-button-border-radius:2px !default;\r\n\t$u-upload-botton-margin: 0 8px 8px 0 !default;\r\n\t$u-upload-text-font-size:11px !default;\r\n\t$u-upload-text-color:$u-tips-color !default;\r\n\t$u-upload-text-margin-top: 2px !default;\r\n\t$u-upload-hover-bgColor:rgb(230, 231, 233) !default;\r\n\t$u-upload-disabled-opacity:.5 !default;\r\n\r\n\t.u-upload {\r\n\t\t@include flex(column);\r\n\t\tflex: 1;\r\n\r\n\t\t&__wrap {\r\n\t\t\t@include flex;\r\n\t\t\tflex-wrap: wrap;\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t&__preview {\r\n\t\t\t\tborder-radius: $u-upload-preview-border-radius;\r\n\t\t\t\tmargin: $u-upload-preview-margin;\r\n\t\t\t\tposition: relative;\r\n\t\t\t\toverflow: hidden;\r\n\t\t\t\t@include flex;\r\n\r\n\t\t\t\t&__image {\r\n\t\t\t\t\twidth: $u-upload-image-width;\r\n\t\t\t\t\theight: $u-upload-image-height;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&__other {\r\n\t\t\t\t\twidth: $u-upload-image-width;\r\n\t\t\t\t\theight: $u-upload-image-height;\r\n\t\t\t\t\tbackground-color: $u-upload-other-bgColor;\r\n\t\t\t\t\tflex: $u-upload-other-flex;\r\n\t\t\t\t\t@include flex(column);\r\n\t\t\t\t\tjustify-content: center;\r\n\t\t\t\t\talign-items: center;\r\n\r\n\t\t\t\t\t&__text {\r\n\t\t\t\t\t\tfont-size: $u-upload-text-font-size;\r\n\t\t\t\t\t\tcolor: $u-upload-text-color;\r\n\t\t\t\t\t\tmargin-top: $u-upload-text-margin-top;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__deletable {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: $u-upload-deletable-top;\r\n\t\t\tright: $u-upload-deletable-right;\r\n\t\t\tbackground-color: $u-upload-deletable-bgColor;\r\n\t\t\theight: $u-upload-deletable-height;\r\n\t\t\twidth: $u-upload-deletable-width;\r\n\t\t\t@include flex;\r\n\t\t\tborder-bottom-left-radius: $u-upload-deletable-boder-bottom-left-radius;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tz-index: $u-upload-deletable-zIndex;\r\n\r\n\t\t\t&__icon {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttransform: scale(0.7);\r\n\t\t\t\ttop: $u-upload-icon-top;\r\n\t\t\t\tright: $u-upload-icon-right;\r\n\t\t\t\t/* #ifdef H5 */\r\n\t\t\t\ttop: $u-upload-icon-h5-top;\r\n\t\t\t\tright: $u-upload-icon-h5-right;\r\n\t\t\t\t/* #endif */\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__success {\r\n\t\t\tposition: absolute;\r\n\t\t\tbottom: $u-upload-success-bottom;\r\n\t\t\tright: $u-upload-success-right;\r\n\t\t\t@include flex;\r\n\t\t\t// 由于weex(nvue)为阿里巴巴的KPI(部门业绩考核)的laji产物，不支持css绘制三角形\r\n\t\t\t// 所以在nvue下使用图片，非nvue下使用css实现\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tborder-style: $u-upload-success-border-style;\r\n\t\t\tborder-top-color: $u-upload-success-border-top-color;\r\n\t\t\tborder-left-color: $u-upload-success-border-left-color;\r\n\t\t\tborder-bottom-color: $u-upload-success-border-bottom-color;\r\n\t\t\tborder-right-color: $u-upload-success-border-right-color;\r\n\t\t\tborder-width: $u-upload-success-border-width;\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\t/* #endif */\r\n\r\n\t\t\t&__icon {\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\ttransform: scale(0.7);\r\n\t\t\t\tbottom: $u-upload-success-icon-bottom;\r\n\t\t\t\tright: $u-upload-success-icon-right;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\t/* #ifdef APP-NVUE */\r\n\t\t\t\twidth: $u-upload-icon-width;\r\n\t\t\t\theight: $u-upload-icon-height;\r\n\t\t\t\t/* #endif */\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__status {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: $u-upload-status-top;\r\n\t\t\tbottom: $u-upload-status-bottom;\r\n\t\t\tleft: $u-upload-status-left;\r\n\t\t\tright: $u-upload-status-right;\r\n\t\t\tbackground-color: $u-upload-status-bgColor;\r\n\t\t\t@include flex(column);\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\r\n\t\t\t&__icon {\r\n\t\t\t\tposition: relative;\r\n\t\t\t\tz-index: $u-upload-status-icon-Zindex;\r\n\t\t\t}\r\n\r\n\t\t\t&__message {\r\n\t\t\t\tfont-size: $u-upload-message-font-size;\r\n\t\t\t\tcolor: $u-upload-message-color;\r\n\t\t\t\tmargin-top: $u-upload-message-margin-top;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__button {\r\n\t\t\t@include flex(column);\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\twidth: $u-upload-button-width;\r\n\t\t\theight: $u-upload-button-height;\r\n\t\t\tbackground-color: $u-upload-button-bgColor;\r\n\t\t\tborder-radius: $u-upload-button-border-radius;\r\n\t\t\tmargin: $u-upload-botton-margin;\r\n\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\tbox-sizing: border-box;\r\n\t\t\t/* #endif */\r\n\r\n\t\t\t&__text {\r\n\t\t\t\tfont-size: $u-upload-text-font-size;\r\n\t\t\t\tcolor: $u-upload-text-color;\r\n\t\t\t\tmargin-top: $u-upload-text-margin-top;\r\n\t\t\t}\r\n\r\n\t\t\t&--hover {\r\n\t\t\t\tbackground-color: $u-upload-hover-bgColor;\r\n\t\t\t}\r\n\r\n\t\t\t&--disabled {\r\n\t\t\t\topacity: $u-upload-disabled-opacity;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-upload.vue?vue&type=style&index=0&id=69e2a36e&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-upload.vue?vue&type=style&index=0&id=69e2a36e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692292816\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}