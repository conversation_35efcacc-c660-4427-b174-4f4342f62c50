<template>
  <view>
    <u-sticky bgColor="#ffffff">
      <u-subsection activeColor="#e64340" :list="tabs" :current="current" @change="tabchange"></u-subsection>
    </u-sticky>
    <page-box-empty v-if="!coupons || coupons.length == 0" title="暂无礼品卡" sub-title="可以去看看购买礼品卡哦～" :show-btn="false" />
    <view v-if="current == 0" class="coupons" v-for="(item,index) in coupons" :key="index">
      <view @click="goDetail(item.type)">
        <image class="icon" :src="item.pic"></image>
        <view class="id">id: {{item.id}}</view>
      </view>
      <view class="profile">
        <view class="name" @click="goDetail(item.type)">
          <view class="t">礼品卡</view>
          <view class="n">{{item.name}}</view>
        </view>
        <view class="price">
          <view v-if="item.money > 0" class="amount"><text>￥</text>{{item.money}}</view>
        </view>
        <view v-if="item.cardSend === 1" class="message">{{ item.cardMessage }}</view>
        <view v-if="item.cardSend !== 1" class="btn" @click="currentIDGet(item)">
          <view style="margin-right: 10rpx;">
            <u-button @click="couponsUse(item)" type="success" shape="circle" text="提货" size="small"></u-button>
          </view>
          <view>
            <u-button @click="sendRemarks" type="warning" shape="circle" text="赠送" size="small"></u-button>
          </view>
        </view>
      </view>
    </view>
    <view v-if="current == 1" class="coupons" v-for="(item,index) in coupons" :key="index">
      <view>
        <image class="icon" :src="item.pic"></image>
        <view class="id">id: {{item.id}}</view>
      </view>
      <view class="profile">
        <view class="name">
          <view class="t">礼品卡</view>
          <view class="n">{{item.name}}</view>
        </view>
        <view class="price">
          <view v-if="item.money > 0" class="amount"><text>￥</text>{{item.money}}</view>
        </view>
        <view class="dsc">
          <view>{{item.remark}}</view>
        </view>
      </view>
    </view>
    <u-modal :show="shareSendRemarksPop" negativeTop="100" title="请输入附言" :showConfirmButton="false"
      :showCancelButton="false" content="">
      <view style="display: block;width: 100%">
        <view>
          <u-textarea count maxlength="30" border="surround" @change="sendRemarksInput" v-model="remarks"
            :placeholder="remarksPlaceholder"></u-textarea>
        </view>
        <view style="margin-top: 20rpx;display: flex;">
          <u-button @click="onShareAppMessage" open-type="share" type="warning" shape="circle"
            :text="remarksButton"></u-button>
          <view style="width: 20rpx;"></view>
          <u-button @click="sendRemarksCancel" shape="circle" text="取消"></u-button>
        </view>
      </view>
    </u-modal>
    <u-overlay :show="cardPhysicalPop">
      <view class="cardPhysicalPop">
        <view style="position: relative;">
          <image src="https://ye.niutouren.vip/static/images/pop/bg-card-physical.png" mode="widthFix"></image>
          <view class="remarks">
            <list-card-physical-tip :list="goods"></list-card-physical-tip>
          </view>
        </view>
        <view style="display: flex;">
          <view style="margin-right: 30rpx;">
            <u-button @click="_cardPhysicalYes" type="warning">接收</u-button>
          </view>
          <view>
            <u-button @click="_cardPhysicalNo">取消</u-button>
          </view>
        </view>
      </view>
    </u-overlay>
    <u-overlay :show="sharePositionOwnPop">
      <view class="sharePositionOwnPop">
        <view style="position: relative;">
          <image src="https://ye.niutouren.vip/static/images/pop/bg-gift-message.png" mode="widthFix"></image>
          <view class="remarks">{{ shareRemarks }}</view>
          <view @click="_sharedMessageClose" style="position: absolute;bottom: 50rpx;width: 100%;height: 80rpx;">&nbsp;
          </view>
        </view>
      </view>
    </u-overlay>
    <u-overlay :show="sharePositionIndexPop">
      <view class="sharePositionIndexPop">
        <view style="position: relative;">
          <image src="https://ye.niutouren.vip/static/images/pop/bg-gift.png" mode="widthFix"></image>
          <view class="remarks">{{ shareRemarks }}</view>
        </view>
        <view style="display: flex;">
          <!-- <view style="margin-right: 30rpx;">
            <u-button @click="goDetail(shareCardGoodID)" type="primary">详情</u-button>
          </view> -->
          <view style="margin-right: 30rpx;">
            <u-button @click="_sharedYes" type="warning">接受</u-button>
          </view>
          <view style="margin-right: 30rpx;">
            <u-button @click="_sharedForward" type="success">转发</u-button>
          </view>
          <view>
            <u-button @click="_sharedNo">放弃</u-button>
          </view>
        </view>
      </view>
    </u-overlay>
  </view>
</template>

<script>
  import empty from 'empty-value'
  import listCardPhysicalTip from '@/components/list/list-card-physical-tip.vue'
  const TOOLS = require('@/common/tools')

  export default {
    components: {
      listCardPhysicalTip
    },
    data() {
      return {
        cardPhysicalPop: false,
        cardPhysical: {},
        goods: [],

        shareSendRemarksPop: false,
        remarks: '',
        remarksPlaceholder: '示例: 刘先生, 小李给您送一只土鸡试试味道',
        remarksButton: '确认赠送',

        sharePath: '',
        shareImageUrl: '',
        shareRemarks: '',

        sharePositionIndexPop: false,
        sharePositionOwnPop: false,
        shareCurrentID: undefined,
        shareCurrentPic: undefined,
        shareCurrentName: undefined,

        shareCardID: undefined,
        shareCardKey: undefined,
        shareCardItem: undefined,
        shareCardGoodID: undefined,

        tabs: [{
            name: '可用',
            status: '0'
          },
          {
            name: '已赠',
            status: '1, 2, 3'
          },
        ],
        current: 0,
        coupons: [],
        curItem: undefined,
        couponPwd: undefined,
      }
    },
    onLoad(e) {
      const url = decodeURIComponent(e.q)

      // Card Physical id
      let cardPhysicalID = null;
      if (url.includes('ye.niutouren.vip/qr')) {
        const queryIndex = url.indexOf('?');
        if (queryIndex !== -1) {
          const queryStr = url.slice(queryIndex + 1);
          const pairs = queryStr.split('&');
          for (let pair of pairs) {
            const [key, value] = pair.split('=');
            if (decodeURIComponent(key) === 'id') {
              cardPhysicalID = decodeURIComponent(value);
              break;
            }
          }
        }
      }
      if (!empty(cardPhysicalID)) {
        this.cardPhysicalGet(cardPhysicalID)
      }

      let cardID = e.cardID
      let cardKey = e.cardKey
      let remarks = e.remarks
      let goodID = e.goodID

      let pic = e.pic
      let referrerUid = e.referrerUid ? e.referrerUid : 0

      if (!empty(cardID) && !empty(cardKey)) {
        this.shareCardID = cardID
        this.shareCardKey = cardKey
        this.shareRemarks = remarks
        this.shareCardGoodID = goodID

        // 用于转发
        this.shareCurrentPic = pic
        this.sharePath = '/packageFx/coupons/my?cardID=' + cardID + '&cardKey=' + cardKey + '&goodID=' + goodID

        if (String(referrerUid) === String(this.uid)) {
          this.cardStatusGet(cardID, true)
        } else {
          this.cardStatusGet(cardID, false, true)
        }

        this._myCoupons(0)
      } else {
        this._myCoupons(0)
      }
    },
    onShow() {

    },
    methods: {
      sendRemarks() {
        this.shareSendRemarksPop = true
      },
      sendRemarksCancel() {
        this.shareSendRemarksPop = false
      },
      sendRemarksInput(e) {
        this.remarks = e
      },
      async cardPhysicalGet(cardPhysicalID) {
        await uni.$u.http.post('https://ye.niutouren.vip/api/qr', {
          id: 2,
        }).then(res => {
          console.log('card res is', res)
          if (!empty(res.id)) {
            this._goods()
            this.cardPhysicalPop = true
          }
        })
      },
      async cardStatusGet(cardID, ownPop = false, HandledPop = false, own = false) {
        let message = ''
        await uni.$u.http.post('https://ye.niutouren.vip/api/json', {
          refId: cardID,
          type: 'card_share',
          action: 'list',
        }).then(res => {
          if (res.code == 0) {
            if (!empty(res.data.result)) {
              const cardData = JSON.parse(res.data.result[0].content)
              if (!empty(cardData.status)) {
                if (cardData.status === -1) {
                  message = '该礼品卡已被拒绝'
                }
                if (cardData.status === 1) {
                  message = '该礼品卡已经赠送，对方还未接收'
                }
                if (cardData.status === 2) {
                  message = '该礼品卡已经被领取'
                }

                if (HandledPop && cardData.status === 1) {
                  this.sharePositionIndexPop = true
                }
                if (HandledPop && cardData.status !== 1) {
                  this.shareRemarks = message
                  this.sharePositionOwnPop = true
                }

                // 自己的窗口弹出
                if (ownPop) {
                  this.shareRemarks = message
                  this.sharePositionOwnPop = true
                }
              }
            }
          }
        })

        return message
      },
      /**
       * 前端无权限删除不同用户的json，只能进入后端处理
       * @param {Object} status
       * @param {Object} cardID
       */
      async _couponsShareChange(cardID, status) {
        await uni.$u.http.post('https://ye.niutouren.vip/api/json', {
          refId: cardID,
          type: 'card_share',
          action: 'list',
        }).then(res => {
          if (res.code == 0) {
            if (!empty(res.data.result)) {
              const cardData = res.data.result[0]
              uni.$u.http.post('https://ye.niutouren.vip/api/json', {
                id: cardData.id,
                type: 'card_share',
                action: 'set',
                content: JSON.stringify({
                  status: status
                }),
              })
            }
          }
        })
      },
      async currentIDGet(item) {
        this.shareCurrentID = item.id
        this.shareCurrentPic = item.pic
        this.shareCurrentName = item.name

        this._couponsShareOpen()
      },
      async couponsUse(item) {
        const goodsList = [{
          goodsId: item.type,
          goodsName: item.name,
          number: 1,
          pic: item.pic,
          price: item.money,
          score: 0,
          sku: [],
          additions: [],
          goodsType: 0,
          kjid: ''
        }]
        uni.setStorageSync('goodsList', goodsList)
        uni.navigateTo({
          url: '/pages/pay/order?mod=buy&card=' + item.id
        })
      },
      onShareAppMessage(e) {
        this.shareSendRemarksPop = false

        if (this.sharePath !== '') {
          // 赠送生成分享码成功, 在服务器做标记，1，赠送还没收
          this.$wxapi.jsonSet({
            token: this.token,
            type: 'card_share',
            refId: this.shareCurrentID,
            content: JSON.stringify({
              status: 1
            }),
          })

          return {
            title: this.remarks,
            path: this.sharePath + '&remarks=' + this.remarks + '&pic=' + this.shareCurrentPic + '&referrerUid=' + this
              .uid,
            imageUrl: this.shareCurrentPic,
          }
        }
      },
      async _couponsShareOpen() {
        const res = await this.$wxapi.couponsShareOpen(this.token, this.shareCurrentID)
        if (res.code == 0) {
          this.sharePath = '/packageFx/coupons/my?cardID=' + this.shareCurrentID + '&cardKey=' + res.data
        }
      },
      async _sharedMessageClose() {
        this.sharePositionOwnPop = false
      },
      async _sharedYes() {
        const res = await this.$wxapi.couponsShareFetch(this.token, this.shareCardID, this.shareCardKey, this
          .shareCardGoodID)
        if (res.code == 0) {
          this.sharePositionIndexPop = false
          this._couponsShareChange(this.shareCardID, 2)
          this._myCoupons(0)
        }
      },
      async _sharedNo() {
        this.sharePositionIndexPop = false
        this._couponsShareChange(this.shareCardID, -1)
      },
      async _cardPhysicalYes() {
        console.log('is ok')
      },
      async _cardPhysicalNo() {
        this.cardPhysicalPop = false
      },
      async _sharedForward() {
        this.sharePositionIndexPop = false
        this.remarksPlaceholder = '转发：代我接收该卡，填写地址就可以领取'
        this.remarksButton = '确认转发'
        this.sendRemarks()
      },
      async tabchange(e) {
        this.current = e

        if (this.current == 0) {
          this._myCoupons(0)
        }
        if (this.current == 1) {
          this._myCoupons('1, 2, 3')
        }
      },
      async _myCoupons(status) {
        this.coupons = null
        const res = await this.$wxapi.myCoupons({
          token: this.token,
          status
        })

        if (res.code == 0) {
          console.log('coupons is', res.data)

          res.data.forEach(async ele => {
            if (ele.dateEnd) {
              ele.dateEnd = ele.dateEnd.split('')[0]
            }

            ele.cardSend = 0
            let cardMessage = ''

            cardMessage = await this.cardStatusGet(ele.id)
            if (cardMessage !== '') {
              ele.cardMessage = cardMessage
              ele.cardSend = 1
            }

            this.coupons = res.data
          })
        }
      },
      async getCounpon(item, pwd) {
        if (!await getApp().checkHasLoginedH5()) {
          uni.navigateTo({
            url: "/pages/login/login"
          })
          return
        }
        this.curItem = item
        if (item.pwd && !pwd) {
          this.couponPwd = ''
          return
        }
        // https://www.yuque.com/apifm/nu0f75/dhxcpu
        const res = await this.$wxapi.fetchCoupons({
          id: item.id,
          token: this.token,
          pwd: this.couponPwd ? this.couponPwd : ''
        })
        if (res.code == 700) {
          if (pwd) {
            uni.showToast({
              title: '口令输入有误',
              icon: 'none'
            })
          } else {
            uni.showToast({
              title: '礼品卡不存在',
              icon: 'none'
            })
          }
          return;
        }
        if (res.code == 20001 || res.code == 20002) {
          uni.showModal({
            title: '错误',
            content: '来晚了',
            showCancel: false
          })
          return;
        }
        if (res.code == 20003) {
          uni.showModal({
            title: '错误',
            content: '你领过了，别贪心哦~',
            showCancel: false
          })
          return;
        }
        if (res.code == 30001) {
          uni.showModal({
            title: '错误',
            content: '您的积分不足',
            showCancel: false
          })
          return;
        }
        if (res.code == 20004) {
          uni.showModal({
            title: '错误',
            content: '已过期~',
            showCancel: false
          })
          return;
        }
        if (res.code == 0) {
          uni.showToast({
            title: '领取成功',
            icon: 'success'
          })
        } else {
          uni.showModal({
            title: '错误',
            content: res.msg,
            showCancel: false
          })
        }
      },
      goIndex() {
        uni.switchTab({
          url: '../index/index'
        })
      },
      goDetail(id) {
        uni.navigateTo({
          url: '/pages/goods/detail?id=' + id + '&card=1'
        })
      },
      async _goods() {
        let categoryId = 463740

        // https://www.yuque.com/apifm/nu0f75/wg5t98
        const res = await this.$wxapi.goodsv2({
          categoryId: categoryId,
          showExtJson: true,
          pageSize: 1
        })
        if (res.code == 0) {
          let _goods = []
          let goods = []
          let extJsonMap = []

          _goods = res.data.result
          if (!empty(_goods)) {
            _goods.forEach(async (good, index) => {
              good.image = good.pic
              good.title = good.name

              if (!empty(res.data.extJsonMap)) {
                if (good.id in res.data.extJsonMap) {
                  good.ext = res.data.extJsonMap[good.id]
                  if (!empty(good.ext['deadline'])) {
                    good.ext['deadline'] = TOOLS.translateTimeDifference(good.ext['deadline'])
                  }
                  if (good.ext['max_total']) {
                    good.ext['max_total'] = good.ext['max_total']
                  }
                  if (good.ext['min_total']) {
                    good.ext['min_total'] = good.ext['min_total']
                  }
                  if (good.ext['shichiPrice']) {
                    good.ext['shichiPrice'] = good.ext['shichiPrice']
                  }
                }
              }

              goods.push(good)
            })
          }
          this.goods = goods
        }
      },
    }
  }
</script>
<style scoped lang="scss">
  .coupons {
    display: flex;
    justify-content: space-between;
    margin-top: 24rpx;
    margin-left: 24rpx;
    margin-bottom: 24rpx;
    width: 702rpx;
    min-height: 268rpx;
    background-color: #FFFFFF;
    box-shadow: 0 0 16rpx 0 rgba(36, 44, 69, 0.20);
    border-radius: 8rpx;
  }

  .coupons .icon {
    margin-left: 64rpx;
    margin-top: 32rpx;
    width: 160rpx;
    height: 160rpx;
    border-radius: 10rpx;
  }

  .coupons .id {
    font-size: 20rpx;
    color: #999;
    margin-left: 64rpx;
  }

  .coupons .message {
    font-size: 20rpx;
    color: #999;
    margin-top: 10rpx;
  }

  .coupons .profile {
    margin-left: 10rpx;
    padding-bottom: 20rpx;
    width: 100%;
  }

  .coupons .profile .name {
    display: flex;
    margin-top: 32rpx;
  }

  .coupons .profile .name .t {
    width: 80rpx;
    height: 30rpx;
    background: #FEB21C;
    border-radius: 4rpx;

    font-family: PingFangSC-Medium;
    font-size: 20rpx;
    color: #FFFFFF;
    letter-spacing: 0;
    line-height: 30rpx;
    text-align: center;
  }

  .coupons .profile .name .n {
    margin-left: 16rpx;
    margin-right: 24rpx;
    font-family: PingFangSC-Medium;
    font-size: 30rpx;
    color: #333333;
    letter-spacing: 0;
    line-height: 30rpx;
  }

  .coupons .profile .price {
    display: flex;
    align-items: baseline;
    margin-top: 24rpx;
  }

  .coupons .profile .price .tj {
    font-family: PingFangSC-Regular;
    font-size: 20rpx;
    color: #999999;
    letter-spacing: 0;
    line-height: 20rpx;
  }

  .coupons .profile .price .amount {
    font-family: PingFangSC-Medium;
    font-size: 56rpx;
    color: #FEB21C;
    letter-spacing: 0;
    line-height: 56rpx;
    margin-right: 24rpx;
  }

  .coupons .profile .price .amount text {
    margin-left: 16rpx;
    font-family: PingFangSC-Regular;
    font-size: 20rpx;
    color: #FEB21C;
    letter-spacing: 0;
    line-height: 20rpx;
  }

  .disabled1 {
    background: #999999 !important;
    color: #FFFFFF !important;
  }

  .disabled2 {
    color: #999999 !important;
  }

  .coupons .profile .btn {
    display: flex;
    flex-direction: row !important;
    width: 30rpx;
    margin-top: 20rpx;
  }

  .bottom {
    width: 100vw;
    height: 24rpx;
  }


  .pwd-coupons-mask {
    position: fixed;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.3);
    top: 0;
    left: 0;
  }

  .pwd-coupons {
    position: fixed;
    top: 300rpx;
    left: 100rpx;
    width: 550rpx;
    background: #fff;
    border-radius: 12rpx;
  }

  .pwd-coupons .t {
    margin-top: 32rpx;
    text-align: center;
    font-size: 32rpx;
    font-weight: bold;
  }

  .pwd-coupons .input {
    margin: 32rpx;
    border: 1rpx solid #666;
    border-radius: 8rpx;
    height: 88rpx;
    line-height: 88rpx;
  }

  .pwd-coupons button {
    margin: 32rpx;
  }

  .koulingcoupon {
    margin-top: 32rpx;
  }

  .block-btn {
    margin: 32rpx 0;
  }

  .hecheng {
    margin-top: 16rpx;
  }

  .sharePositionIndexPop {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .sharePositionIndexPop .remarks {
    position: absolute;
    top: 280rpx;
    left: 70rpx;
    color: #333333;
    font-size: 34rpx;
    width: 300rpx;
    height: 200rpx;
    overflow: hidden;
  }

  .sharePositionIndexPop image {
    width: 420rpx;
  }

  .sharePositionIndexPop .close {
    margin-top: 32rpx;
  }

  .sharePositionOwnPop {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .sharePositionOwnPop .remarks {
    position: absolute;
    top: 250rpx;
    left: 70rpx;
    color: #333333;
    font-size: 34rpx;
    width: 300rpx;
    height: 200rpx;
    overflow: hidden;
  }

  .sharePositionOwnPop image {
    width: 420rpx;
  }

  .sharePositionOwnPop .close {
    margin-top: 32rpx;
  }

  .sharePositionIndexPop {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .sharePositionIndexPop .remarks {
    position: absolute;
    top: 280rpx;
    left: 70rpx;
    color: #333333;
    font-size: 34rpx;
    width: 300rpx;
    height: 200rpx;
    overflow: hidden;
  }

  .sharePositionIndexPop image {
    width: 420rpx;
  }

  .sharePositionIndexPop .close {
    margin-top: 32rpx;
  }

  .cardPhysicalPop {
    width: 100vw;
    height: 100vh;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
  }

  .cardPhysicalPop .remarks {
    position: absolute;
    top: 330rpx;
    left: 70rpx;
    color: #333333;
    font-size: 34rpx;
    width: 560rpx;
    height: 380rpx;
    overflow: hidden;
  }

  .cardPhysicalPop image {
    width: 700rpx;
  }

  .cardPhysicalPop .close {
    margin-top: 32rpx;
  }

  .dsc {
    font-size: 12px;
    margin-top: 20rpx;
    color: #333;
  }
</style>