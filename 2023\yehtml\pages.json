{
  "pages": [{
      "path": "pages/index/start",
      "style": {
        "navigationStyle": "custom"
      }
    },
    {
      "path": "pages/shop/select",
      "style": {
        "navigationBarTitleText": "选择门店"
      }
    },
    {
      "path": "pages/index/index",
      "style": {
        "navigationStyle": "custom",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/notice/list",
      "style": {
        "navigationBarTitleText": "公告"
      }
    },
    {
      "path": "pages/notice/detail",
      "style": {
        "navigationBarTitleText": "公告详情"
      }
    },
    {
      "path": "pages/category/category",
      "style": {
        "navigationBarTitleText": "商品分类"
      }
    },
    {
      "path": "pages/promotion/list",
      "style": {
        "navigationBarTitleText": "活动"
      }
    },
    {
      "path": "pages/card/gift",
      "style": {
        "navigationBarTitleText": "礼品卡"
      }
    },
    {
      "path": "pages/cart/index",
      "style": {
        "navigationBarTitleText": "购物车"
      }
    },
    {
      "path": "pages/pay/order",
      "style": {
        "navigationBarTitleText": "确认订单"
      }
    },
    {
      "path": "pages/goods/list",
      "style": {
        "navigationBarTitleText": "商品列表"
      }
    },
    {
      "path": "pages/goods/list-vop",
      "style": {
        "navigationBarTitleText": "商品列表"
      }
    },
    {
      "path": "pages/goods/fav",
      "style": {
        "navigationBarTitleText": "我的收藏"
      }
    },
    {
      "path": "pages/goods/detail",
      "style": {
        "navigationBarTitleText": "商品详情"
      }
    },
    {
      "path": "pages/order/index",
      "style": {
        "navigationBarTitleText": "我的订单"
      }
    },
    {
      "path": "pages/order/detail",
      "style": {
        "navigationBarTitleText": "订单详情"
      }
    },
    {
      "path": "pages/my/index",
      "style": {
        "navigationStyle": "custom"
      }
    },

    {
      "path": "pages/asset/balance",
      "style": {
        "navigationBarTitleText": "我的资产"
      }
    },
    {
      "path": "pages/asset/cashlog",
      "style": {
        "navigationBarTitleText": "资金明细",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/asset/recharge",
      "style": {
        "navigationBarTitleText": "在线充值"
      }
    },
    {
      "path": "pages/score/index",
      "style": {
        "navigationBarTitleText": "我的积分"
      }
    },
    {
      "path": "pages/score/logs",
      "style": {
        "navigationBarTitleText": "积分明细",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/score/excharge",
      "style": {
        "navigationBarTitleText": "积分券兑换积分"
      }
    },
    {
      "path": "pages/score/sign",
      "style": {
        "navigationBarTitleText": "每日签到"
      }
    },
    {
      "path": "pages/score/signlog",
      "style": {
        "navigationBarTitleText": "签到记录",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/growth/index",
      "style": {
        "navigationBarTitleText": "我的成长值"
      }
    },
    {
      "path": "pages/growth/logs",
      "style": {
        "navigationBarTitleText": "成长值明细",
        "enablePullDownRefresh": true
      }
    },
    {
      "path": "pages/growth/excharge",
      "style": {
        "navigationBarTitleText": "积分兑换成长值"
      }
    },
    {
      "path": "pages/refund/apply",
      "style": {
        "navigationBarTitleText": "申请售后"
      }
    },
    {
      "path": "pages/login/login",
      "style": {
        "navigationBarTitleText": "登陆"
      }
    },
    {
      "path": "pages/login/reg",
      "style": {
        "navigationBarTitleText": "注册新用户"
      }
    },
    {
      "path": "pages/login/resetpwd",
      "style": {
        "navigationBarTitleText": "忘记密码找回"
      }
    },
    {
      "path": "pages/agreement",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/notagree",
      "style": {
        "navigationBarTitleText": ""
      }
    },
    {
      "path": "pages/my/info-menu",
      "style": {
        "navigationBarTitleText": "个人信息"
      }
    },
    {
      "path": "pages/my/info",
      "style": {
        "navigationBarTitleText": "修改资料"
      }
    },
    {
      "path": "pages/my/cardlogs",
      "style": {
        "navigationBarTitleText": "会员卡消费记录"
      }
    }
  ],
  "subPackages": [{
    "root": "packageFx",
    "pages": [{
        "path": "water/list",
        "style": {
          "navigationBarTitleText": "山泉水专供"
        }
      },
      {
        "path": "partner/share",
        "style": {
          "navigationBarTitleText": "个人合伙人"
        }
      },
      {
        "path": "partner/poster",
        "style": {
          "navigationBarTitleText": "生成海报"
        }
      },
      {
        "path": "address/index",
        "style": {
          "navigationBarTitleText": "收货地址"
        }
      },
      {
        "path": "address/addSite",
        "style": {
          "navigationBarTitleText": "收货地址"
        }
      },
      {
        "path": "about/about",
        "style": {
          "navigationBarTitleText": ""
        }
      },
      {
        "path": "my/grade",
        "style": {
          "navigationBarTitleText": "会员等级"
        }
      },
      {
        "path": "coupons/index",
        "style": {
          "navigationBarTitleText": "礼品卡"
        }
      },
      {
        "path": "coupons/list",
        "style": {
          "navigationBarTitleText": "礼品卡列表"
        }
      },
      {
        "path": "coupons/my",
        "style": {
          "navigationBarTitleText": "我的礼品卡"
        }
      },
      {
        "path": "help/list",
        "style": {
          "navigationBarTitleText": "帮助中心"
        }
      },
      {
        "path": "help/detail",
        "style": {
          "navigationBarTitleText": ""
        }
      },
      {
        "path": "live/index",
        "style": {
          "navigationBarTitleText": "好礼直播"
        }
      },

      {
        "path": "search/index",
        "style": {
          "navigationBarTitleText": "搜一搜"
        }
      },
      {
        "path": "index/index",
        "style": {
          "navigationBarTitleText": "分销中心"
        }
      },
      {
        "path": "apply/index",
        "style": {
          "navigationBarTitleText": "成为分销商"
        }
      },
      {
        "path": "apply/form",
        "style": {
          "navigationBarTitleText": "申请成为分销商"
        }
      },
      {
        "path": "myusers/index",
        "style": {
          "navigationBarTitleText": "我的团队",
          "enablePullDownRefresh": true
        }
      },
      {
        "path": "commisionLog/index",
        "style": {
          "navigationBarTitleText": "推广订单"
        }
      },
      {
        "path": "report/team",
        "style": {
          "navigationBarTitleText": "团队月报表",
          "enablePullDownRefresh": true
        }
      },
      {
        "path": "report/city",
        "style": {
          "navigationBarTitleText": "合伙人月报表",
          "enablePullDownRefresh": true
        }
      },
      {
        "path": "myusers/myusers-detail",
        "style": {
          "navigationBarTitleText": "用户详情"
        }
      }
    ]
  }],
  "preloadRule": {
    "packageFx/index/index": {
      "network": "all",
      "packages": ["__APP__"]
    }
  },
  "globalStyle": {
    "navigationBarTextStyle": "black",
    "navigationBarTitleText": "",
    "navigationBarBackgroundColor": "#FFFFFF",
    "backgroundColor": "#FFFFFF"
  },
  "condition": { //模式配置，仅开发期间生效
    "current": 0, //当前激活的模式(list 的索引项)
    "list": [{
      "name": "", //模式名称
      "path": "", //启动页面，必选
      "query": "" //启动参数，在页面的onLoad函数里面得到
    }]
  }
}