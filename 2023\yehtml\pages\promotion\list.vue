<template>
  <view class="index container-wrapper">
    <view class="main-wrapper">
      <view class="top-image-wrapper">
        <div class="top-image">&nbsp;</div>
      </view>
      <view class="main">
        <view>
          <tab-promotion></tab-promotion>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import empty from 'empty-value'
  import tabPromotion from '@/components/tabs/tab-promotion'

  export default {
    components: {
      tabPromotion
    },
    data() {
      return {
        tabIndex: 0,
      }
    }
  }
</script>
<style scoped lang="scss">
  .index.container-wrapper {
    padding: 0;
    margin: 0;
    background: linear-gradient(90deg, #35641e, #80a933) fixed;
  }

  .index {
    .main-wrapper {
      position: relative;
      margin-bottom: 40rpx;
    }

    .top-image {
      width: 100%;
      height: 250px;
      background-image: url('https://ye.niutouren.vip/static/images/promotion/bg.png');
      background-size: cover;
      background-position: bottom center;
    }
  }

  .main {
    background: #FFFFFF;
    margin: 0 20rpx;
    margin-top: -20rpx;
    padding: 10rpx;
    border-radius: 20rpx;
  }
</style>