<template>
  <view>
    <view class="grade-flex-column">
      <swiper class="swiper" :circular="circular" :indicator-dots="indicatorDots" :autoplay="autoplay" :interval="interval" :duration="duration" :indicator-color="indicatorColor" :indicator-active-color="activeColor[current]" :previous-margin="leftRightMargin" :next-margin="leftRightMargin" @change="changeItem">
        <swiper-item v-for="x in 3" :key="x">
          <view class="grade-member-card grade-flex-column" :class="memberLevel + '-color'">
            <view class="member-info">
              <view class="grade-flex-column">
                <text class="member-name">{{ memberName[current] }}会员</text>
                <view v-if="memberEqual">
                  <text class="member-end">会员到期时间：2025.12.30</text>
                </view>
              </view>
              <text v-if="!memberEqual" class="member-status" :class="'status-' + (current + 1)" @click="userLevelBuy(current)">我要升级</text>
            </view>
            <view class="grow-up-info grade-flex-column">
              <view class="grow-up">
                <text></text>
                <text v-if="memberEqual">您已经是{{ memberName[current] }}会员</text>
                <text v-if="!memberEqual">您还不是{{ memberName[current] }}会员</text>
              </view>
              <!-- <view class="grow-up-progress" :class="'progress-' + (current + 1)"></view> -->
            </view>
          </view>
        </swiper-item>
      </swiper>
      <view class="tip-swiper">* 左右滑动卡片选择会员等级，更多的权益</view>

      <view class="grade-list">
        <view class="grade-list-title">
          <text>当前已成功解锁<text class="red-strong">{{ equity.length }}项</text>功能权益</text>
        </view>
        <view class="grade-list-content">
          <view class="grade-content-item" v-for="(nav,index) in equity" :key="index">
            <text @click="tipClick" class="grade-content-item-icon" :class="memberLevel + '-color'">
              <text class="iconfont">{{ nav.icon }}</text></text>
            <text class="grade-content-item-text">{{ nav.text }}</text>
            <zb-tooltip :placement="nav.position" ref="tooltip">
              <view slot="content">
                <view class="desc-wrapper">
                  <text>{{ nav.desc }}</text>
                </view>
              </view>
              <view class="tipBlock"></view>
            </zb-tooltip>
          </view>
        </view>
      </view>

      <view class="grade-faq grade-flex-column">
        <view class="grade-faq-title">如何成为{{ memberName[current] }}会员</view>
        <view class="grade-faq-content grade-flex-column">
          <view class="grade-faq-content-item" v-for="(nav,index) in open" :key="index">
            <view class="grade-faq-item-info">
              <view>
                <text class="grade-faq-item-icon" :class="nav.class">
                  <text class="iconfont">{{ nav.icon }}</text>
                </text>
              </view>
              <view class="grade-flex-column">
                <text class="grade-faq-item-text">{{ nav.text }}</text>
                <text class="grade-faq-item-desc">{{ nav.desc }}</text>
              </view>
            </view>
            <view class="grade-faq-open" @click="userLevelBuy(current)">立即开通</view>
          </view>
        </view>
      </view>

      <view class="grade-faq grade-flex-column">
        <view class="grade-faq-title">常见问题</view>
        <view class="grade-faq-content grade-flex-column">
          <view class="grade-faq-content-item" v-for="(nav,index) in faq" :key="index">
            <view class="grade-faq-item-info" style="width: 100%;">
              <view>
                <text class="grade-faq-item-icon" :class="nav.class">
                  <text class="iconfont">{{ nav.icon }}</text>
                </text>
              </view>
              <view class="grade-flex-column">
                <text class="grade-faq-item-text">{{ nav.text }}</text>
                <text class="grade-faq-item-desc">{{ nav.desc }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import zbTooltip from '@/uni_modules/zb-tooltip/components/zb-tooltip/zb-tooltip'

  export default {
    components: {
      zbTooltip
    },
    data() {
      return {
        circular: false,
        indicatorDots: false,
        autoplay: false,
        interval: 2000,
        duration: 500,
        indicatorColor: '#e9e9e9',
        indicatorActiveColor: 'red',
        leftRightMargin: '50rpx',
        current: 0,
        memberLevel: 'member1',
        levelId: 5596,
        userDetail: undefined,
        balance: 0,
        memberLevelDetail: [],
        activeColor: ['#dbdfeb', '#ffe3cb', '#f7dbac'],
        memberName: ['轻享', '心享', '尊享'],
        memberEqual: false,
        equity: [{
            icon: "\u{e621}",
            text: '专属特价',
            desc: '全场9.5折(预购、抢购等活动除外)',
            position: 'bottom-start'
          },
          {
            icon: "\u{e6c1}",
            text: '优先配送',
            desc: '优先配送免排队，好食材快人一步',
            position: 'bottom-start'
          },
          {
            icon: "\u{e739}",
            text: '专属客服',
            desc: '贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题',
            position: 'bottom-start'
          },
          {
            icon: "\u{e677}",
            text: '新品先购',
            desc: '野贝农上市新品之前，优先获得选购权',
            position: 'bottom-end'
          }
        ],
        open: [{
            icon: '\u{e621}',
            text: '购买轻享会员年卡',
            desc: '费用为188元/年',
            class: 'icon-color-1'
          },
          {
            icon: '\u{e621}',
            text: '预存',
            desc: '在野贝农小程序平台充值预存1000元',
            class: 'icon-color-1'
          }
        ],
        faq: [{
            icon: '\u{e739}',
            text: '预存的费用有限制吗？',
            desc: '没有限制，您可以购买野贝农平台所有商品',
            class: 'icon-color-1'
          },
          {
            icon: '\u{e739}',
            text: '预存的费用能提现吗？',
            desc: '不能，根据国家法律，充值的费用是不允许提现的',
            class: 'icon-color-4'
          }
        ]
      }
    },
    onLoad(e) {
      this._getUserAmount()
      this._userLevelDetail()
    },
    methods: {
      async _userDetail() {
        // https://www.uviewui.com/components/form.html
        const res = await this.$wxapi.userDetail(this.token)
        if (res.code == 0) {
          this.userDetail = res.data.base

          console.log('this.userDetail is', this.userDetail)
        }
      },
      userLevelBuy(current) {
        let priceId
        switch (current) {
          case 0:
            priceId = 553
            break
          case 1:
            priceId = 554
            break
          case 2:
            priceId = 555
            break
        }

        this._userLevelBuy(priceId)
      },
      async _userLevelBuy(priceId) {
        // https://www.yuque.com/apifm/nu0f75/svpnky
        const res = await this.$wxapi.userLevelBuy(this.token, priceId)

        let pass = true
        switch (priceId) {
          case 553:
            if (this.balance < 1000) {
              uni.showToast({
                title: '余额不足1000',
                icon: 'error'
              })

              pass = false
            }
            break
          case 554:
            if (this.balance < 3000) {
              uni.showToast({
                title: '余额不足3000',
                icon: 'error'
              })

              pass = false
            }
            break
          case 555:
            if (this.balance < 6000) {
              uni.showToast({
                title: '余额不足6000',
                icon: 'error'
              })

              pass = false
            }
            break
        }

        if (pass === true) {
          if (res.code == 0) {
            uni.showToast({
              title: '升级成功',
              icon: 'none'
            })
          } else {
            if (res.code == 20000) {
              uni.showToast({
                title: res.msg,
                icon: 'none'
              })
            }
          }
        }
      },
      async _getUserAmount() {
        // https://www.yuque.com/apifm/nu0f75/wrqkcb
        const res = await this.$wxapi.userAmount(this.token)
        if (res.code == 0) {
          this.balance = res.data.balance.toFixed(2)
        }
      },
      async _userLevelDetail() {
        // https://www.yuque.com/apifm/nu0f75/svpnky
        const res = await this.$wxapi.userLevelDetail(this.levelId)
        if (res.code == 0) {
          this.memberLevelDetail = res.data
          console.log('memberLevelDetail is', this.memberLevelDetail)

          let level = res.data.info.level
          this.memberEqual = false
        }
      },
      tipClick(e) {

      },
      changeItem(e) {
        this.current = e.detail.current
        this.memberLevel = "member" + (e.detail.current + 1)

        if (this.current === 0) {
          this.levelId = 5596
          this.equity = [{
              icon: "\u{e621}",
              text: '专属特价',
              desc: '全场9.5折(预购、抢购等活动除外)',
              position: 'bottom-start'
            },
            {
              icon: "\u{e6c1}",
              text: '优先配送',
              desc: '优先配送免排队，好食材快人一步',
              position: 'bottom-start'
            },
            {
              icon: "\u{e739}",
              text: '专属客服',
              desc: '贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题',
              position: 'bottom-start'
            },
            {
              icon: "\u{e677}",
              text: '新品先购',
              desc: '野贝农上市新品之前，优先获得选购权',
              position: 'bottom-end'
            }
          ]

          this.open = [{
              icon: '\u{e621}',
              text: '购买轻享会员年卡',
              desc: '费用为188元/年',
              class: 'icon-color-1'
            },
            {
              icon: '\u{e621}',
              text: '预存',
              desc: '在野贝农小程序平台充值预存1000元',
              class: 'icon-color-1'
            }
          ]
        }
        if (this.current === 1) {
          this.levelId = 5597
          this.equity = [{
              icon: "\u{e621}",
              text: '专属特价',
              desc: '全场9折(预购、抢购等活动除外)',
              position: 'bottom-start'
            },
            {
              icon: "\u{e6c1}",
              text: '优先配送',
              desc: '优先配送免排队，好食材快人一步',
              position: 'bottom-start'
            },
            {
              icon: "\u{e739}",
              text: '专属客服',
              desc: '贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题',
              position: 'bottom-start'
            },
            {
              icon: "\u{e677}",
              text: '新品先购',
              desc: '野贝农上市新品之前，优先获得选购权',
              position: 'bottom-end'
            },
            {
              icon: '\u{e62f}',
              text: '免费试吃',
              desc: '野贝农新品上市前，都会邀请会员免费试吃',
              position: 'bottom-start'
            }, {
              icon: '\u{e631}',
              text: '线下聚会',
              desc: '有资格参加我们野贝农组织的线下活动：美食艺术家活动',
              position: 'bottom-start'
            }
          ]

          this.open = [{
              icon: '\u{e621}',
              text: '购买轻享会员年卡',
              desc: '费用为288元/年',
              class: 'icon-color-1'
            },
            {
              icon: '\u{e621}',
              text: '预存',
              desc: '在野贝农小程序平台充值预存3000元',
              class: 'icon-color-1'
            }
          ]
        }
        if (this.current === 2) {
          this.levelId = 5598
          this.equity = [{
              icon: "\u{e621}",
              text: '专属特价',
              desc: '全场8.8折(预购、抢购等活动除外)',
              position: 'bottom-start'
            },
            {
              icon: "\u{e6c1}",
              text: '优先配送',
              desc: '优先配送免排队，好食材快人一步',
              position: 'bottom-start'
            },
            {
              icon: "\u{e739}",
              text: '专属客服',
              desc: '贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题',
              position: 'bottom-start'
            },
            {
              icon: "\u{e677}",
              text: '新品先购',
              desc: '野贝农上市新品之前，优先获得选购权',
              position: 'bottom-end'
            },
            {
              icon: '\u{e62f}',
              text: '免费试吃',
              desc: '野贝农新品上市前，都会邀请会员免费试吃',
              position: 'bottom-start'
            }, {
              icon: '\u{e631}',
              text: '线下聚会',
              desc: '有资格参加我们野贝农组织的线下活动：美食艺术家活动',
              position: 'bottom-start'
            },
            {
              icon: '\u{e62a}',
              text: '免费游玩',
              desc: '作为我们的最高级别的会员，可以受邀免费参加我们深山养殖地游玩活动',
              position: 'bottom-start'
            },
            {
              icon: '\u{e705}',
              text: '成为合伙人',
              desc: '做为我们的最高级别的会员，会获得成为我们野贝农渠道合伙人的资格',
              position: 'bottom-end'
            }
          ]

          this.open = [{
              icon: '\u{e621}',
              text: '购买轻享会员年卡',
              desc: '费用为388元/年',
              class: 'icon-color-1'
            },
            {
              icon: '\u{e621}',
              text: '预存',
              desc: '在野贝农小程序平台充值预存6000元',
              class: 'icon-color-1'
            }
          ]
        }

        // Load be the change
        this._userLevelDetail()
      }
    }
  }
</script>

<style>
  @font-face {
    font-family: 'iconfont';
    /* Project id 4420575 */
    src: url('//at.alicdn.com/t/c/font_4420575_45yzt13kqmb.woff2?t=1705988231706') format('woff2'),
      url('//at.alicdn.com/t/c/font_4420575_45yzt13kqmb.woff?t=1705988231706') format('woff'),
      url('//at.alicdn.com/t/c/font_4420575_45yzt13kqmb.ttf?t=1705988231706') format('truetype');
  }

  .iconfont {
    font-family: iconfont;
  }

  page,
  view {
    display: flex;
  }

  page {
    background-color: #fff;
  }

  .grade-flex-column {
    flex-direction: column;
  }
</style>
<style lang="scss" scoped>
  $head-color: #5b8cff; //5b8cff 4194fc
  $white-color: #fff;
  $radius: 5rpx;
  $border-color: #efefef;
  $color-1: #6eacfe;
  $color-2: #52f0cf;
  $color-3: #ffcd46;
  $color-4: #ff727d;
  $list-item-height: 100rpx;
  $list-margin: 15rpx;

  .swiper {
    width: 100vw;
    padding: 30rpx 0 0;
    height: 390rpx;
    box-sizing: border-box;
  }

  .tip-swiper {
    margin-left: 80rpx;
    margin-top: -50rpx;
    margin-bottom: 30rpx;
    font-size: 20rpx;
    color: #999;
  }

  .grade-member-card {
    height: calc(100% - 60rpx);
    background-color: #d7dfeb;
    color: #555d6e;
    border-radius: 30rpx;
    padding: 30rpx 35rpx 30rpx 40rpx;
    box-sizing: border-box;
    justify-content: space-between;

    width: calc(100vw - 120rpx);
    margin: 0 auto;
  }

  .member-info {
    justify-content: space-between;
    font-weight: bold;
    font-size: 38rpx;

    .member-end {
      font-size: 22rpx;
    }

    .member-status {
      background-color: #f3f7f7;
      font-weight: bold;
      font-size: 22rpx;
      border-radius: 30rpx;
      padding: 0 25rpx;
      box-sizing: border-box;
      height: 45rpx;
      line-height: 45rpx;
    }

    .status-1 {
      background-color: #f3f7f7;
    }

    .status-2 {
      background-color: #fef6eb;
    }

    .status-3 {
      background-color: #fef6eb;
    }

    .status-4 {
      background-color: #424862;
    }

    .status-5 {
      background-color: #625959;
    }
  }

  .grow-up-info {
    margin-bottom: 10rpx;
  }

  .grow-up {
    justify-content: space-between;
    font-weight: bold;
    font-size: 24rpx;
  }

  .grow-up-progress {
    margin-top: 10rpx;
    width: 100%;
    height: 11rpx;
    background-color: #b8bdc7;
    border-radius: 10rpx;
    overflow: hidden;
  }

  .grow-up-progress::after {
    content: '';
    display: inline-block;
    width: 30%;
    height: 100%;
    background-color: #f7fbfb;
  }

  .progress-1 {
    background-color: #b8bdc7;
  }

  .progress-2 {
    background-color: #e2bd81;
  }

  .progress-3 {
    background-color: #f7cfa6;
  }

  .progress-4 {
    background-color: #232b45;
  }

  .progress-5 {
    background-color: #030303;
  }

  .grade-list {
    width: 100%;
    background-color: #FFFFFF;
    margin: 0 auto 0;
    padding-bottom: $list-margin;
    flex-direction: column;
    border-radius: $radius;
  }

  .grade-list-title {
    padding: 0rpx 50rpx 30rpx;
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
    justify-content: flex-start;
  }

  .red-strong {
    color: red;
  }

  .grade-list-content {
    box-sizing: border-box;
    padding: 25rpx 40rpx 0 40rpx;
    flex-wrap: wrap;

    .grade-content-item {
      position: relative;
      width: 25%;
      box-sizing: border-box;
      padding: 0 0 35rpx 0rpx;
      align-items: center;
      flex-direction: column;

      .grade-content-item-icon {
        font-size: 50rpx;
        font-weight: bold;
        font-family: texticons;
        border-radius: 50%;
        padding: 15rpx;
        width: 50rpx;
        height: 50rpx;
        display: flex;
        justify-content: center;
        align-items: center;
        margin-bottom: 10rpx;
      }

      .grade-content-item-text {
        color: #666;
        font-size: 24rpx;
        font-weight: 400;
      }

      .grade-content-item-desc {
        color: #999;
        font-size: 24rpx;
        font-weight: 300;
        margin-top: 0rpx;
      }

      .desc-wrapper {
        display: flex;
        flex-wrap: wrap;
      }

      .desc-wrapper text {
        width: 200rpx;
        word-break: break-all;
        white-space: pre-wrap;
      }
    }

    .tipBlock {
      width: 100rpx;
      height: 100rpx;
      position: absolute;
      top: -100rpx;
      left: -50rpx;
    }
  }

  .member1-color {
    background-image: linear-gradient(#dbdfeb, #e3ebf3);
    color: #555d6e;
  }

  .member2-color {
    background-image: linear-gradient(to right, #f3d39a, #f7dfb5);
    color: #a36a09;
  }

  .member3-color {
    background-image: linear-gradient(to right, #ffe3cb, #ffebdc);
    color: #b7734f;
  }

  .member4-color {
    background-image: linear-gradient(#28304a, #4a4e67);
    color: #ffe4de;
  }

  .member5-color {
    background-image: linear-gradient(to right, #0d0b0b, #454546);
    color: #ffe3cf;
  }

  .grade-faq {
    background-color: #fff;
    margin: 0 auto 40rpx;
    width: calc(100vw - 80rpx);
    box-sizing: border-box;

    flex-direction: column;
    border-radius: 30rpx;
    border: solid 0rpx #eaf7ef;
    box-shadow: 0rpx 1rpx 10rpx rgba(0, 0, 0, 0.1);
    overflow: hidden;

    .grade-faq-title {
      background-image: linear-gradient(#eaf7ef, #fff);
      padding: 30rpx 40rpx 0rpx;
      box-sizing: border-box;
      font-size: 30rpx;
      color: #333;
      font-weight: 600;
    }
  }

  .grade-faq-content {
    padding: 20rpx 30rpx 10rpx 40rpx;

    .grade-faq-content-item {
      align-items: center;
      justify-content: space-between;
      margin-top: 15rpx;
      margin-bottom: 30rpx;
    }

    .grade-faq-item-info {
      align-items: center;
      width: 70%;
    }

    .grade-faq-item-icon {
      font-size: 50rpx;
      font-weight: bold;
      font-family: texticons;
      border-radius: 20rpx;
      padding: 20rpx;
      color: #fff;
      margin-right: 20rpx;
    }

    .grade-faq-item-text {
      color: #333;
      font-size: 29rpx;
      font-weight: 400;
    }

    .grade-faq-item-desc {
      color: #999;
      font-size: 23rpx;
      font-weight: 300;
      margin-top: 5rpx;
    }
  }

  .grade-faq-open {
    background-color: #10b575;
    color: #fff;
    font-weight: bold;
    font-size: 24rpx;
    border-radius: 30rpx;
    padding: 0 30rpx;
    box-sizing: border-box;
    height: 60rpx;
    line-height: 60rpx;
  }

  .icon-color-1 {
    background-image: linear-gradient(#72aefe, #4896ff);
  }

  .icon-color-2 {
    background-image: linear-gradient(#5ff9d7, #39debd);
  }

  .icon-color-3 {
    background-image: linear-gradient(#ffd155, #ffbc04);
  }

  .icon-color-4 {
    background-image: linear-gradient(#ff808b, #ff6872);
  }
</style>