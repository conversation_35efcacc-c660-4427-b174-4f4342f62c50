{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/info.vue?cf47", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/info.vue?4389", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/info.vue?0437", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/info.vue?0181", "uni-app:///pages/my/info.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/info.vue?ac7f", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/my/info.vue?2464"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "userDetail", "avatarUrl", "showSex", "actions", "name", "sex", "rules", "nick", "required", "message", "trigger", "form", "mobile", "created", "mounted", "onReady", "onLoad", "onShow", "methods", "_userDetail", "res", "submit", "uni", "title", "icon", "_submit", "d", "token", "showCancel", "content", "confirmText", "success", "sexSelect", "getPhoneNumber", "console", "_getPhoneNumber", "duration"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,aAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACwD;AACL;AACsC;;;AAGzF;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,0EAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,aAAa,gUAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,kVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAA+pB,CAAgB,+qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC+BnrB;AAAA,eACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;QACAC;MACA,GACA;QACAA;MACA,EACA;MACAC;MACAC;QACAC;UACAC;UACAC;UACA;UACAC;QACA;MACA;MACAC;QACAJ;QACAK;MACA;IACA;EACA;EACAC,6BAEA;EACAC,6BAEA;EACAC;IACA;EACA;EACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBACA;kBACA;oBACA;kBACA;kBACA;oBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MACA;QACA;MACA;QACAC;UACAC;UACAC;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAC;kBACApB;gBACA;gBAAA,KACA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAa;gBACA;kBACAM;gBACA;cAAA;gBAEA;kBACAA;gBACA;gBACA;kBACAA;gBACA;gBAAA;gBAAA,OACA;cAAA;gBAAAN;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAE;kBACAC;kBACAC;gBACA;gBAAA;cAAA;gBAGAF;kBACAM;kBACAL;kBACAM;kBACAC;kBACAC;oBACAT,kBAEA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAU;MACA;IACA;IACAC;MACA;QACA;QACA;QACA;QACA;QACA;QACAC;QACA;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAf;gBACA;kBACAE;oBACAC;oBACAC;oBACAY;kBACA;kBACA;kBACA;gBACA;kBACAd;oBACAC;oBACAM;oBACAD;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5KA;AAAA;AAAA;AAAA;AAAsxC,CAAgB,0uCAAG,EAAC,C;;;;;;;;;;;ACA1yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/my/info.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/my/info.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./info.vue?vue&type=template&id=1318ea48&scoped=true&\"\nvar renderjs\nimport script from \"./info.vue?vue&type=script&lang=js&\"\nexport * from \"./info.vue?vue&type=script&lang=js&\"\nimport style0 from \"./info.vue?vue&type=style&index=0&id=1318ea48&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1318ea48\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/my/info.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info.vue?vue&type=template&id=1318ea48&scoped=true&\"", "var components\ntry {\n  components = {\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n    uForm: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form/u-form\" */ \"@/uni_modules/uview-ui/components/u-form/u-form.vue\"\n      )\n    },\n    uFormItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-form-item/u-form-item\" */ \"@/uni_modules/uview-ui/components/u-form-item/u-form-item.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uActionSheet: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-action-sheet/u-action-sheet\" */ \"@/uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.showSex = true\n    }\n    _vm.e1 = function ($event) {\n      _vm.showSex = false\n    }\n  }\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <u-cell title=\"头像\" :border=\"false\">\r\n      <view slot=\"value\">\r\n        <image class=\"avatarUrl\" :src=\"userDetail.avatarUrl\" mode=\"aspectFill\"></image>\r\n      </view>\r\n    </u-cell>\r\n    <view class=\"form-box\">\r\n      <u-form ref=\"uForm\" label-width=\"130rpx\" :model=\"form\">\r\n        <u-form-item label=\"昵称\" prop=\"nick\" required>\r\n          <u-input v-model=\"form.nick\" type=\"text\" clearable placeholder=\"请输入昵称\"></u-input>\r\n        </u-form-item>\r\n        <u-form-item label=\"性别\" @click=\"showSex = true;\" required>\r\n          <u-input v-model=\"sex\" disabled disabledColor=\"#ffffff\" placeholder=\"请选择性别\" border=\"none\"></u-input>\r\n          <u-icon slot=\"right\" name=\"arrow-right\"></u-icon>\r\n        </u-form-item>\r\n        <u-form-item label=\"手机号\" required>\r\n          <u-input v-model=\"form.mobile\" disabled disabledColor=\"#ffffff\" placeholder=\"未绑定\" border=\"none\"></u-input>\r\n          <u-button slot=\"right\" type=\"success\" :text=\"form.mobile ? '重新绑定' : '立即绑定'\" size=\"mini\" open-type=\"getPhoneNumber\" @getphonenumber=\"getPhoneNumber\"></u-button>\r\n        </u-form-item>\r\n      </u-form>\r\n    </view>\r\n    <view class=\"submit-btn\">\r\n      <u-button type=\"success\" @click=\"submit\">保存</u-button>\r\n    </view>\r\n    <u-action-sheet :show=\"showSex\" :actions=\"actions\" title=\"请选择性别\" description=\"如果选择保密会报错\" @close=\"showSex = false\" @select=\"sexSelect\">\r\n    </u-action-sheet>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  const PAY = require('@/common/pay.js')\r\n  export default {\r\n    data() {\r\n      return {\r\n        userDetail: undefined,\r\n        avatarUrl: undefined,\r\n        showSex: false,\r\n        actions: [{\r\n            name: '男',\r\n          },\r\n          {\r\n            name: '女',\r\n          },\r\n        ],\r\n        sex: '',\r\n        rules: {\r\n          nick: [{\r\n            required: true,\r\n            message: '不能为空',\r\n            // 可以单个或者同时写两个触发验证方式 \r\n            trigger: ['change', 'blur'],\r\n          }],\r\n        },\r\n        form: {\r\n          nick: null,\r\n          mobile: null,\r\n        },\r\n      }\r\n    },\r\n    created() {\r\n\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    onReady() {\r\n      this.$refs.uForm.setRules(this.rules);\r\n    },\r\n    onLoad(e) {\r\n      this._userDetail()\r\n    },\r\n    onShow() {\r\n\r\n    },\r\n    methods: {\r\n      async _userDetail() {\r\n        // https://www.uviewui.com/components/form.html\r\n        const res = await this.$wxapi.userDetail(this.token)\r\n        if (res.code == 0) {\r\n          this.userDetail = res.data.base\r\n          this.form.nick = res.data.base.nick\r\n          if (res.data.base.gender == 1) {\r\n            this.sex = '男'\r\n          }\r\n          if (res.data.base.gender == 2) {\r\n            this.sex = '女'\r\n          }\r\n          this.form.mobile = res.data.base.mobile\r\n        }\r\n      },\r\n      submit() {\r\n        this.$refs.uForm.validate().then(res => {\r\n          this._submit()\r\n        }).catch(errors => {\r\n          uni.showToast({\r\n            title: '表单请填写完整',\r\n            icon: 'none'\r\n          })\r\n        })\r\n      },\r\n      async _submit() {\r\n        const d = {\r\n          token: this.token,\r\n          nick: this.form.nick\r\n        }\r\n        if (this.avatarUrl) {\r\n          const res = await this.$wxapi.uploadFile(this.token, this.avatarUrl)\r\n          if (res.code == 0) {\r\n            d.avatarUrl = res.data.url\r\n          }\r\n        }\r\n        if (this.sex == '男') {\r\n          d.gender = 1\r\n        }\r\n        if (this.sex == '女') {\r\n          d.gender = 2\r\n        }\r\n        const res = await this.$wxapi.modifyUserInfo(d)\r\n        if (res.code != 0) {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n          return\r\n        }\r\n        uni.showModal({\r\n          showCancel: false,\r\n          title: '成功',\r\n          content: '编辑成功',\r\n          confirmText: '知道了',\r\n          success: () => {\r\n            uni.navigateBack({\r\n\r\n            })\r\n          }\r\n        })\r\n      },\r\n      sexSelect(e) {\r\n        this.sex = e.name\r\n      },\r\n      getPhoneNumber(e) {\r\n        if (!e.detail.errMsg || e.detail.errMsg != \"getPhoneNumber:ok\") {\r\n          // wx.showModal({\r\n          // \ttitle: '提示',\r\n          // \tcontent: e.detail.errMsg,\r\n          // \tshowCancel: false\r\n          // })\r\n          console.error(e)\r\n          return;\r\n        }\r\n        this._getPhoneNumber(e)\r\n      },\r\n      async _getPhoneNumber(e) {\r\n        const res = await this.$wxapi.bindMobileWxappV2(this.token, e.detail.code)\r\n        if (res.code == 0) {\r\n          uni.showToast({\r\n            title: '绑定成功',\r\n            icon: 'success',\r\n            duration: 2000\r\n          })\r\n          this.$u.vuex('mobile', res.data)\r\n          this.form.mobile = res.data\r\n        } else {\r\n          uni.showModal({\r\n            title: '提示',\r\n            content: res.msg,\r\n            showCancel: false\r\n          })\r\n        }\r\n      }\r\n    }\r\n  }\r\n</script>\r\n<style scoped lang=\"scss\">\r\n  .form-box {\r\n    padding: 0 32rpx 32rpx 32rpx;\r\n  }\r\n\r\n  .avatarUrl {\r\n    width: 88rpx;\r\n    height: 88rpx;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info.vue?vue&type=style&index=0&id=1318ea48&scoped=true&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./info.vue?vue&type=style&index=0&id=1318ea48&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688725021\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}