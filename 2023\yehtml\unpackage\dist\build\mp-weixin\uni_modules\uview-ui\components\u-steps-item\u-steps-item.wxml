<view data-ref="u-steps-item" class="{{['u-steps-item','data-v-42166ffb','vue-ref','u-steps-item--'+parentData.direction]}}"><block wx:if="{{index+1<childLength}}"><view class="{{['u-steps-item__line','data-v-42166ffb','u-steps-item__line--'+parentData.direction]}}" style="{{$root.s0}}"></view></block><view class="{{['u-steps-item__wrapper','data-v-42166ffb','u-steps-item__wrapper--'+parentData.direction,parentData.dot&&'u-steps-item__wrapper--'+parentData.direction+'--dot']}}"><block wx:if="{{$slots.icon}}"><slot name="icon"></slot></block><block wx:else><block wx:if="{{parentData.dot}}"><view class="u-steps-item__wrapper__dot data-v-42166ffb" style="{{'background-color:'+(statusColor)+';'}}"></view></block><block wx:else><block wx:if="{{parentData.activeIcon||parentData.inactiveIcon}}"><view class="u-steps-item__wrapper__icon data-v-42166ffb"><u-icon vue-id="6f1215dd-1" name="{{index<=parentData.current?parentData.activeIcon:parentData.inactiveIcon}}" size="{{iconSize}}" color="{{index<=parentData.current?parentData.activeColor:parentData.inactiveColor}}" class="data-v-42166ffb" bind:__l="__l"></u-icon></view></block><block wx:else><view class="u-steps-item__wrapper__circle data-v-42166ffb" style="{{'background-color:'+(statusClass==='process'?parentData.activeColor:'transparent')+';'+('border-color:'+(statusColor)+';')}}"><block wx:if="{{statusClass==='process'||statusClass==='wait'}}"><text class="u-steps-item__wrapper__circle__text data-v-42166ffb" style="{{'color:'+(index==parentData.current?'#ffffff':parentData.inactiveColor)+';'}}">{{index+1}}</text></block><block wx:else><u-icon vue-id="6f1215dd-2" color="{{statusClass==='error'?'error':parentData.activeColor}}" size="12" name="{{statusClass==='error'?'close':'checkmark'}}" class="data-v-42166ffb" bind:__l="__l"></u-icon></block></view></block></block></block></view><view class="{{['u-steps-item__content','data-v-42166ffb','u-steps-item__content--'+parentData.direction]}}" style="{{$root.s1}}"><u--text vue-id="6f1215dd-3" text="{{title}}" type="{{parentData.current==index?'main':'content'}}" lineHeight="20px" size="{{parentData.current==index?14:13}}" class="data-v-42166ffb" bind:__l="__l"></u--text><block wx:if="{{$slots.desc}}"><slot name="desc"></slot></block><block wx:else><u--text vue-id="6f1215dd-4" text="{{desc}}" type="tips" size="12" class="data-v-42166ffb" bind:__l="__l"></u--text></block></view></view>