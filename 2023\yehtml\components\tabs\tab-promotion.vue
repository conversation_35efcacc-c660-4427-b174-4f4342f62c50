<template>
  <view class="tab-promotion">
    <!-- 添加公告栏 -->
    <view class="notice-bar-wrap">
      <u-notice-bar 
        :text="['开业中，欢迎大家下单']" 
        mode="column"
        color="#fa3534" 
        bgColor="#fdf6ec"
        :show-icon="true"
      ></u-notice-bar>
    </view>
    
    <u-row customStyle="margin-bottom: 10px">
      <u-col span="3" justify="center" textAlign="center">
        <div class="tab" :class="{ active: activeTab === 'tab4' }" @click="changeTab('tab4')">
          <div style="height: 30rpx;">&nbsp;</div>
          <div class="title">优惠套餐</div>
          <div class="dsc">优惠划算</div>
        </div>
      </u-col>
      <u-col span="3" justify="center" textAlign="center">
        <div class="tab line" :class="{ active: activeTab === 'tab1' }" @click="changeTab('tab1')">
          <div style="height: 30rpx;">&nbsp;</div>
          <div class="title">限数试吃</div>
          <div class="dsc">数量有限</div>
        </div>
      </u-col>
      <u-col span="3" justify="center" textAlign="center">
        <div class="tab line" :class="{ active: activeTab === 'tab2' }" @click="changeTab('tab2')">
          <div style="height: 30rpx;">&nbsp;</div>
          <div class="title">限时预购</div>
          <div class="dsc">更加实惠</div>
        </div>
      </u-col>
      <u-col span="3" justify="center" textAlign="center">
        <div class="tab line" :class="{ active: activeTab === 'tab3' }" @click="changeTab('tab3')">
          <div style="height: 30rpx;">&nbsp;</div>
          <div class="title">珍品抢购</div>
          <div class="dsc">仅此一份</div>
        </div>
      </u-col>
    </u-row>
    <view v-show="activeTab === 'tab1'">
      <list-limited-time :list="goods"></list-limited-time>
    </view>
    <view v-show="activeTab === 'tab2'">
      <list-quota :list="goods"></list-quota>
    </view>
    <view v-show="activeTab === 'tab3'">
      <list-quota :list="goods"></list-quota>
    </view>
    <view v-show="activeTab === 'tab4'">
      <list-quota :list="goods"></list-quota>
    </view>
  </view>
</template>

<script>
  import list2 from '@/components/list/list2'
  import listQuota from '@/components/list/list-quota'
  import listLimitedTime from '@/components/list/list-limited-time'
  import empty from 'empty-value'

  const TOOLS = require('@/common/tools')

  export default {
    components: {
      list2,
      listQuota,
      listLimitedTime
    },
    data() {
      return {
        activeTab: 'tab4',
        goods: [],
      }
    },
    mounted() {
      this._goods()
    },
    watch: {
      list: function(val) {
        //console.log('watch list is', val)
      }
    },
    methods: {
      changeTab(tab) {
        let that = this
        that.activeTab = tab
        that.goods = []
        that._goods()
      },
      async _goods() {
        let categoryId = 0
        switch (this.activeTab) {
          case 'tab1':
            categoryId = '463740'
            break;
          case 'tab2':
            categoryId = '391088'
            break;
          case 'tab3':
            categoryId = '390766'
            break;
          case 'tab4':
            categoryId = '468445,468446,468447'
            break;

          default:
            break;
        }

        // https://www.yuque.com/apifm/nu0f75/wg5t98
        const res = await this.$wxapi.goodsv2({
          categoryId: categoryId,
          showExtJson: true
        })
        if (res.code == 0) {
          let _goods = []
          let goods = []
          let extJsonMap = []

          _goods = res.data.result
          if (!empty(_goods)) {
            _goods.forEach(async (good, index) => {
              good.image = good.pic
              good.title = good.name

              if (!empty(res.data.extJsonMap)) {
                if (good.id in res.data.extJsonMap) {
                  good.ext = res.data.extJsonMap[good.id]
                  if (!empty(good.ext['deadline'])) {
                    good.ext['deadline'] = TOOLS.translateTimeDifference(good.ext['deadline'])
                  }
                  if (good.ext['max_total']) {
                    good.ext['max_total'] = good.ext['max_total']
                  }
                  if (good.ext['min_total']) {
                    good.ext['min_total'] = good.ext['min_total']
                  }
                  if (good.ext['shichiPrice']) {
                    good.ext['shichiPrice'] = good.ext['shichiPrice']
                  }
                }
              }

              goods.push(good)
            })
          }
          this.goods = goods
        }
      },
    },
  }
</script>
<style>
  .tab-promotion {
    margin: 0 12px;
  }
  
  .notice-bar-wrap {
    padding: 10rpx 20rpx;
    margin: 10rpx 0 15rpx 0;
    border-radius: 8rpx;
  }

  .tab.line {
    position: relative;
    width: 100%;
  }

  .tab.line::after {
    content: "";
    position: absolute;
    top: 20%;
    right: 0;
    bottom: 10%;
    width: 1px;
    background-color: #d3d3d3;
  }

  .tab .title {
    font-size: 34rpx;
    font-weight: bolder;
    margin-bottom: 10rpx;
  }

  .tab .dsc {
    font-size: 26rpx;
    height: 60rpx;
  }

  .tab.active {
    background-image: url("/static/images/tab-bg.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  .tab.active .dsc {
    background-image: url("/static/images/tab-dsc-bg.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    font-size: 28rpx;
    line-height: 56rpx;
    color: #FFFFFF;
  }
</style>