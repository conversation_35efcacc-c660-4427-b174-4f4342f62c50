import Vue from 'vue'
import App from './App'
import WXAPI from 'apifm-uniapp'

// vuex
import store from './store'

// 引入全局uView
import uView from '@/uni_modules/uview-ui'

import mixin from './common/mixin'

Vue.prototype.$store = store

Vue.config.productionTip = false

App.mpType = 'app'

Vue.use(uView)

Vue.prototype.$wxapi = WXAPI

// 引入uView提供的对vuex的简写法文件
let vuexStore = require('@/store/$u.mixin.js');
Vue.mixin(vuexStore);

// #ifdef MP
// 引入uView对小程序分享的mixin封装
const mpShare = require('@/uni_modules/uview-ui/libs/mixin/mpShare.js')
Vue.mixin(mpShare)
// #endif

Vue.mixin(mixin)

// 引入文本截断
let VueTruncate = require('vue-truncate-filter')
Vue.use(VueTruncate)

// i18n部分的配置
// 引入语言包，注意路径
import Chinese from '@/common/locales/zh.js';
import English from '@/common/locales/en.js';

// VueI18n
import VueI18n from '@/common/vue-i18n.min.js';

// VueI18n
Vue.use(VueI18n);

import share from '@/components/wxShare.js'
Vue.mixin(share)

const lang = uni.getStorageSync('lang')
const i18n = new VueI18n({
  // 默认语言
  locale: lang ? lang : 'zh',
  // 引入语言文件
  messages: {
    'zh': Chinese,
    'en': English,
  }
});

// 由于微信小程序的运行机制问题，需声明如下一行，H5和APP非必填
Vue.prototype._i18n = i18n;

const app = new Vue({
  i18n,
  store,
  ...App
})

// 引入请求封装
require('./util/request/index')(app)

app.$mount()