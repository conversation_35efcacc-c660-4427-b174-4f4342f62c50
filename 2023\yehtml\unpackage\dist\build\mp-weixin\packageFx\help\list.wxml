<view class="category-page-box data-v-14a1f4a5"><view class="category-page data-v-14a1f4a5"><view class="main data-v-14a1f4a5"><scroll-view class="u-tab-view menu-scroll-view data-v-14a1f4a5" scroll-y="{{true}}" scroll-with-animation="{{true}}"><block wx:for="{{category}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['u-tab-item','data-v-14a1f4a5',current==index?'u-tab-item-active':'']}}" data-current="{{index}}" data-event-opts="{{[['tap',[['categoryChange',[index]]]]]}}" catchtap="__e"><text class="u-line-1 data-v-14a1f4a5">{{item.name}}</text></view></block></scroll-view><scroll-view class="goods-container data-v-14a1f4a5" scroll-y="{{true}}" scroll-top="{{scrolltop}}"><block wx:if="{{!cmsArticles}}"><page-box-empty vue-id="8daf8922-1" title="暂无记录" sub-title="当前类目无法为你检索到相关信息～" show-btn="{{false}}" class="data-v-14a1f4a5" bind:__l="__l"></page-box-empty></block><block wx:for="{{cmsArticles}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-cell vue-id="{{'8daf8922-2-'+index}}" title="{{item.title}}" isLink="{{true}}" clickable="{{true}}" url="{{'/packageFx/help/detail?id='+item.id}}" class="data-v-14a1f4a5" bind:__l="__l"></u-cell></block></scroll-view></view></view></view>