{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-text/u-text.vue?2d5f", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-text/u-text.vue?6f7c", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-text/u-text.vue?cfca", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-text/u-text.vue?6de1", "uni-app:///uni_modules/uview-ui/components/u-text/u-text.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-text/u-text.vue?177b", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-text/u-text.vue?c78c"], "names": ["name", "mixins", "computed", "valueStyle", "textDecoration", "fontWeight", "wordWrap", "fontSize", "style", "isNvue", "isMp", "mp", "data", "methods", "clickHandler", "uni", "phoneNumber"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,kSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9DA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACwErrB;AACA;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA3BA,eA4BA;EACAA;EAEAC;EAKAC;IACAC;MACA;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA,oBACAC;MACA;MACA;IACA;IACAC;MACA;MAIA;IACA;IACAC;MACA;MAEAC;MAEA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MACA;MACA;QACAC;UACAC;QACA;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5JA;AAAA;AAAA;AAAA;AAAwxC,CAAgB,4uCAAG,EAAC,C;;;;;;;;;;;ACA5yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-text/u-text.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-text.vue?vue&type=template&id=50004b49&scoped=true&\"\nvar renderjs\nimport script from \"./u-text.vue?vue&type=script&lang=js&\"\nexport * from \"./u-text.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-text.vue?vue&type=style&index=0&id=50004b49&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"50004b49\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-text/u-text.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-text.vue?vue&type=template&id=50004b49&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uLink: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-link/u-link\" */ \"@/uni_modules/uview-ui/components/u-link/u-link.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 =\n    _vm.show && _vm.mode === \"price\" ? _vm.__get_style([_vm.valueStyle]) : null\n  var g0 = _vm.show && _vm.prefixIcon ? _vm.$u.addStyle(_vm.iconStyle) : null\n  var s1 =\n    _vm.show && !(_vm.mode === \"link\") && _vm.openType && _vm.isMp\n      ? _vm.__get_style([_vm.valueStyle])\n      : null\n  var s2 =\n    _vm.show && !(_vm.mode === \"link\") && !(_vm.openType && _vm.isMp)\n      ? _vm.__get_style([_vm.valueStyle])\n      : null\n  var g1 = _vm.show && _vm.suffixIcon ? _vm.$u.addStyle(_vm.iconStyle) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        s1: s1,\n        s2: s2,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-text.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-text.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view\r\n        class=\"u-text\"\r\n        :class=\"[]\"\r\n        v-if=\"show\"\r\n        :style=\"{\r\n            margin: margin,\r\n\t\t\tjustifyContent: align === 'left' ? 'flex-start' : align === 'center' ? 'center' : 'flex-end'\r\n        }\"\r\n        @tap=\"clickHandler\"\r\n    >\r\n        <text\r\n            :class=\"['u-text__price', type && `u-text__value--${type}`]\"\r\n            v-if=\"mode === 'price'\"\r\n            :style=\"[valueStyle]\"\r\n            >￥</text\r\n        >\r\n        <view class=\"u-text__prefix-icon\" v-if=\"prefixIcon\">\r\n            <u-icon\r\n                :name=\"prefixIcon\"\r\n                :customStyle=\"$u.addStyle(iconStyle)\"\r\n            ></u-icon>\r\n        </view>\r\n        <u-link\r\n            v-if=\"mode === 'link'\"\r\n            :text=\"value\"\r\n            :href=\"href\"\r\n            underLine\r\n        ></u-link>\r\n        <template v-else-if=\"openType && isMp\">\r\n            <button\r\n                class=\"u-reset-button u-text__value\"\r\n                :style=\"[valueStyle]\"\r\n                :data-index=\"index\"\r\n                :openType=\"openType\"\r\n                @getuserinfo=\"onGetUserInfo\"\r\n                @contact=\"onContact\"\r\n                @getphonenumber=\"onGetPhoneNumber\"\r\n                @error=\"onError\"\r\n                @launchapp=\"onLaunchApp\"\r\n                @opensetting=\"onOpenSetting\"\r\n                :lang=\"lang\"\r\n                :session-from=\"sessionFrom\"\r\n                :send-message-title=\"sendMessageTitle\"\r\n                :send-message-path=\"sendMessagePath\"\r\n                :send-message-img=\"sendMessageImg\"\r\n                :show-message-card=\"showMessageCard\"\r\n                :app-parameter=\"appParameter\"\r\n            >\r\n                {{ value }}\r\n            </button>\r\n        </template>\r\n        <text\r\n            v-else\r\n            class=\"u-text__value\"\r\n            :style=\"[valueStyle]\"\r\n            :class=\"[\r\n                type && `u-text__value--${type}`,\r\n                lines && `u-line-${lines}`\r\n            ]\"\r\n            >{{ value }}</text\r\n        >\r\n        <view class=\"u-text__suffix-icon\" v-if=\"suffixIcon\">\r\n            <u-icon\r\n                :name=\"suffixIcon\"\r\n                :customStyle=\"$u.addStyle(iconStyle)\"\r\n            ></u-icon>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport value from './value.js'\r\nimport button from '../../libs/mixin/button.js'\r\nimport openType from '../../libs/mixin/openType.js'\r\nimport props from './props.js'\r\n/**\r\n * Text 文本\r\n * @description 此组件集成了文本类在项目中的常用功能，包括状态，拨打电话，格式化日期，*替换，超链接...等功能。 您大可不必在使用特殊文本时自己定义，text组件几乎涵盖您能使用的大部分场景。\r\n * @tutorial https://www.uviewui.com/components/loading.html\r\n * @property {String} \t\t\t\t\ttype\t\t主题颜色\r\n * @property {Boolean} \t\t\t\t\tshow\t\t是否显示（默认 true ）\r\n * @property {String | Number}\t\t\ttext\t\t显示的值\r\n * @property {String}\t\t\t\t\tprefixIcon\t前置图标\r\n * @property {String} \t\t\t\t\tsuffixIcon\t后置图标\r\n * @property {String} \t\t\t\t\tmode\t\t文本处理的匹配模式 text-普通文本，price-价格，phone-手机号，name-姓名，date-日期，link-超链接\r\n * @property {String} \t\t\t\t\thref\t\tmode=link下，配置的链接\r\n * @property {String | Function} \t\tformat\t\t格式化规则\r\n * @property {Boolean} \t\t\t\t\tcall\t\tmode=phone时，点击文本是否拨打电话（默认 false ）\r\n * @property {String} \t\t\t\t\topenType\t小程序的打开方式\r\n * @property {Boolean} \t\t\t\t\tbold\t\t是否粗体，默认normal（默认 false ）\r\n * @property {Boolean} \t\t\t\t\tblock\t\t是否块状（默认 false ）\r\n * @property {String | Number} \t\t\tlines\t\t文本显示的行数，如果设置，超出此行数，将会显示省略号\r\n * @property {String} \t\t\t\t\tcolor\t\t文本颜色（默认 '#303133' ）\r\n * @property {String | Number} \t\t\tsize\t\t字体大小（默认 15 ）\r\n * @property {Object | String} \t\t\ticonStyle\t图标的样式 （默认 {fontSize: '15px'} ）\r\n * @property {String} \t\t\t\t\tdecoration\t文字装饰，下划线，中划线等，可选值 none|underline|line-through（默认 'none' ）\r\n * @property {Object | String | Number}\tmargin\t\t外边距，对象、字符串，数值形式均可（默认 0 ）\r\n * @property {String | Number} \t\t\tlineHeight\t文本行高\r\n * @property {String} \t\t\t\t\talign\t\t文本对齐方式，可选值left|center|right（默认 'left' ）\r\n * @property {String} \t\t\t\t\twordWrap\t文字换行，可选值break-word|normal|anywhere（默认 'normal' ）\r\n * @event {Function} click  点击触发事件\r\n * @example <u--text text=\"我用十年青春,赴你最后之约\"></u--text>\r\n */\r\nexport default {\r\n    name: 'u--text',\r\n    // #ifdef MP\r\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, value, button, openType, props],\r\n    // #endif\r\n    // #ifndef MP\r\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, value, props],\r\n    // #endif\r\n    computed: {\r\n        valueStyle() {\r\n            const style = {\r\n                textDecoration: this.decoration,\r\n                fontWeight: this.bold ? 'bold' : 'normal',\r\n                wordWrap: this.wordWrap,\r\n                fontSize: uni.$u.addUnit(this.size)\r\n            }\r\n            !this.type && (style.color = this.color)\r\n            this.isNvue && this.lines && (style.lines = this.lines)\r\n            this.lineHeight &&\r\n                (style.lineHeight = uni.$u.addUnit(this.lineHeight))\r\n            !this.isNvue && this.block && (style.display = 'block')\r\n            return uni.$u.deepMerge(style, uni.$u.addStyle(this.customStyle))\r\n        },\r\n        isNvue() {\r\n            let nvue = false\r\n            // #ifdef APP-NVUE\r\n            nvue = true\r\n            // #endif\r\n            return nvue\r\n        },\r\n        isMp() {\r\n            let mp = false\r\n            // #ifdef MP\r\n            mp = true\r\n            // #endif\r\n            return mp\r\n        }\r\n    },\r\n    data() {\r\n        return {}\r\n    },\r\n    methods: {\r\n        clickHandler() {\r\n            // 如果为手机号模式，拨打电话\r\n            if (this.call && this.mode === 'phone') {\r\n                uni.makePhoneCall({\r\n                    phoneNumber: this.text\r\n                })\r\n            }\r\n            this.$emit('click')\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '../../libs/css/components.scss';\r\n\r\n.u-text {\r\n    @include flex(row);\r\n    align-items: center;\r\n    flex-wrap: nowrap;\r\n    flex: 1;\r\n\t/* #ifndef APP-NVUE */\r\n\twidth: 100%;\r\n\t/* #endif */\r\n\r\n    &__price {\r\n        font-size: 14px;\r\n        color: $u-content-color;\r\n    }\r\n\r\n    &__value {\r\n        font-size: 14px;\r\n        @include flex;\r\n        color: $u-content-color;\r\n        flex-wrap: wrap;\r\n        // flex: 1;\r\n        text-overflow: ellipsis;\r\n        align-items: center;\r\n\r\n        &--primary {\r\n            color: $u-primary;\r\n        }\r\n\r\n        &--warning {\r\n            color: $u-warning;\r\n        }\r\n\r\n        &--success {\r\n            color: $u-success;\r\n        }\r\n\r\n        &--info {\r\n            color: $u-info;\r\n        }\r\n\r\n        &--error {\r\n            color: $u-error;\r\n        }\r\n\r\n        &--main {\r\n            color: $u-main-color;\r\n        }\r\n\r\n        &--content {\r\n            color: $u-content-color;\r\n        }\r\n\r\n        &--tips {\r\n            color: $u-tips-color;\r\n        }\r\n\r\n        &--light {\r\n            color: $u-light-color;\r\n        }\r\n    }\r\n}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-text.vue?vue&type=style&index=0&id=50004b49&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-text.vue?vue&type=style&index=0&id=50004b49&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688736997\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}