<template>
  <view class="tabbar">
    <u-tabbar :value="tabIndex" activeColor="#223F36" @change="tabChange" :fixed="true" :placeholder="true"
      :safeAreaInsetBottom="true">
      <u-tabbar-item text="活动">
        <image style="width: 40rpx;height: 40rpx;" slot="active-icon" src="@/static/images/nav/coupon-on.png"
          mode="aspectFit"></image>
        <image style="width: 40rpx;height: 40rpx;" slot="inactive-icon" src="@/static/images/nav/coupon-off.png"
          mode="aspectFit"></image>
      </u-tabbar-item>
      <u-tabbar-item text="分类">
        <image style="width: 40rpx;height: 40rpx;" slot="active-icon" src="@/static/images/nav/fl-on.png"
          mode="aspectFit"></image>
        <image style="width: 40rpx;height: 40rpx;" slot="inactive-icon" src="@/static/images/nav/fl-off.png"
          mode="aspectFit"></image>
      </u-tabbar-item>
      <u-tabbar-item>
        <image style="width: 100rpx;height: 100rpx;" slot="active-icon" src="@/static/images/nav/home-on.png"
          mode="aspectFit"></image>
        <image style="width: 100rpx;height: 100rpx;" slot="inactive-icon" src="@/static/images/nav/home-on.png"
          mode="aspectFit"></image>
      </u-tabbar-item>
      <u-tabbar-item text="购物车">
        <image style="width: 40rpx;height: 40rpx;" slot="active-icon" src="@/static/images/nav/cart-on.png"
          mode="aspectFit"></image>
        <image style="width: 40rpx;height: 40rpx;" slot="inactive-icon" src="@/static/images/nav/cart-off.png"
          mode="aspectFit"></image>
      </u-tabbar-item>
      <u-tabbar-item text="我的">
        <image style="width: 40rpx;height: 40rpx;" slot="active-icon" src="@/static/images/nav/my-on.png"
          mode="aspectFit"></image>
        <image style="width: 40rpx;height: 40rpx;" slot="inactive-icon" src="@/static/images/nav/my-off.png"
          mode="aspectFit"></image>
      </u-tabbar-item>
    </u-tabbar>
  </view>
</template>

<script>
  export default {
    props: {
      tabIndex: {
        type: Number,
        default: 0,
      },
    },
    data() {
      return {}
    },
    methods: {
      tabChange(index) {
        if (index === 0) {
          uni.navigateTo({
            url: '/pages/promotion/list',
          })
        }
        if (index === 1) {
          uni.navigateTo({
            url: '/pages/category/category',
          })
        }
        if (index === 2) {
          uni.navigateTo({
            url: '/pages/index/index',
          })
        }
        if (index === 3) {
          uni.navigateTo({
            url: '/pages/cart/index',
          })
        }
        if (index === 4) {
          uni.navigateTo({
            url: '/pages/my/index',
          })
        }
      }
    },
  }
</script>