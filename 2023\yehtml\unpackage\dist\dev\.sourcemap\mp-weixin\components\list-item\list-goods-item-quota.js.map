{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-quota.vue?7c8e", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-quota.vue?480f", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-quota.vue?dd4d", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-quota.vue?d2e9", "uni-app:///components/list-item/list-goods-item-quota.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-quota.vue?9d68", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-quota.vue?a4dc", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-quota.vue?cb9f", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-quota.vue?062d"], "names": ["props", "item", "type", "default", "onReady", "console", "data", "timeData", "computed", "filteredTags", "methods", "onChangeTimeData", "goDetail", "uni", "url", "should<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkI;AAClI;AACyE;AACL;AACa;AACC;;;AAGlF;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,2FAAM;AACR,EAAE,gGAAM;AACR,EAAE,yGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAgrB,CAAgB,gsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eC0CpsB;EACAA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;UAAA;QAAA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAA49B,CAAgB,+8BAAG,EAAC,C;;;;;;;;;;;ACAh/B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAA+wC,CAAgB,muCAAG,EAAC,C;;;;;;;;;;;ACAnyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/list-item/list-goods-item-quota.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./list-goods-item-quota.vue?vue&type=template&id=211403e8&\"\nvar renderjs\nimport script from \"./list-goods-item-quota.vue?vue&type=script&lang=js&\"\nexport * from \"./list-goods-item-quota.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list-goods-item-quota.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./list-goods-item-quota.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/list-item/list-goods-item-quota.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-quota.vue?vue&type=template&id=211403e8&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.filteredTags, function (tag, index) {\n    var $orig = _vm.__get_orig(tag)\n    var m0 = _vm.shouldHighlight(tag)\n    var m1 = _vm.shouldHighlight(tag)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-quota.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-quota.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"goods-box-wrapper\">\r\n    <view class=\"goods-box\">\r\n      <view @click=\"goDetail(item)\">\r\n        <view class=\"goods-title-box\">\r\n          <view class=\"goods-title\">{{ item.name }}</view>\r\n        </view>\r\n        <view class=\"goods-dsc-box\">\r\n          <view class=\"goods-dsc\">{{ item.characteristic }}</view>\r\n        </view>\r\n        <view class=\"goods-tags-box\">\r\n          <view class=\"goods-tags\">\r\n            <span v-for=\"(tag, index) in filteredTags\" :key=\"index\" :style=\"{ color: shouldHighlight(tag) ? '#e64340' : '', border: shouldHighlight(tag) ? '1px solid #e64340' : '', display: 'inline-block', 'margin-right': '5px' }\">\r\n              {{ tag }}\r\n            </span>\r\n          </view>\r\n        </view>\r\n        <view class=\"goods-quota\">\r\n          <progress :percent=\"100\" :show-info=\"false\" stroke-width=\"3\" font-size=\"12\" border-radius=\"6\" :active=\"true\" />\r\n          <span class=\"num\">剩余{{ item.stores }}</span>\r\n        </view>\r\n      </view>\r\n      <view class=\"buy-wrapper\" style=\"display: flex;\">\r\n        <view class=\"price-score\">\r\n          <view v-if=\"item.minPrice\" class=\"item\">\r\n            <div class=\"original-price\">\r\n              <span>市场价：</span><br /><span class=\"price\">¥{{item.originalPrice}}</span>\r\n            </div><text>¥</text>{{item.minPrice}}\r\n          </view>\r\n          <view v-if=\"item.minScore\" class=\"item\"><text>\r\n              <image class=\"score-icon\" src=\"/static/images/score.png\"></image>\r\n            </text>{{item.minScore}}</view>\r\n        </view>\r\n        <view class=\"buy\" @click=\"goDetail(item)\">\r\n          <u-icon name=\"shopping-cart\" color=\"#FFFFFF\" size=\"28\"></u-icon>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    props: {\r\n      item: {\r\n        type: Object,\r\n        default: {},\r\n      },\r\n    },\r\n    onReady() {\r\n      console.log('item is', this.item)\r\n    },\r\n    data() {\r\n      return {\r\n        timeData: {}\r\n      }\r\n    },\r\n    computed: {\r\n      filteredTags() {\r\n        if (this.item.tags) {\r\n          const tags = this.item.tags.split(/[, ]+/);\r\n          return tags.filter(tag => tag.trim() !== '');\r\n        }\r\n        return [];\r\n      }\r\n    },\r\n    methods: {\r\n      onChangeTimeData(e) {\r\n        this.timeData = e\r\n      },\r\n      goDetail(item) {\r\n        uni.navigateTo({\r\n          url: '/pages/goods/detail?id=' + item.id\r\n        })\r\n      },\r\n      shouldHighlight(tag) {\r\n        return /[A-Za-z]\\d+/.test(tag);\r\n      }\r\n    },\r\n  }\r\n</script>\r\n<style>\r\n  .goods-box-wrapper {\r\n    margin-top: 24rpx;\r\n    border-radius: 5px;\r\n    padding-bottom: 10rpx;\r\n    text-align: left;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .goods-box {\r\n    padding: 0 10rpx;\r\n  }\r\n\r\n  .goods-box .goods-title-box {\r\n    line-height: 18px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    word-break: break-all;\r\n  }\r\n\r\n  .goods-box .goods-title {\r\n    color: #2b2b2b;\r\n    font-size: 30rpx;\r\n  }\r\n\r\n  .goods-box .goods-dsc-box {\r\n    margin-top: 4rpx;\r\n  }\r\n\r\n  .goods-box .goods-dsc {\r\n    color: #858996;\r\n    font-size: 24rpx;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .goods-box .goods-tags span {\r\n    font-size: 22rpx;\r\n    font-weight: normal;\r\n    color: #858996;\r\n    border: 1px #D9DBDF solid;\r\n    margin-right: 10rpx;\r\n    border-radius: 2px;\r\n    padding: 0 4rpx;\r\n    line-height: 32rpx;\r\n  }\r\n\r\n  .goods-box .goods-price-container {\r\n    display: flex;\r\n    align-items: baseline;\r\n  }\r\n\r\n  .goods-box .goods-price {\r\n    overflow: hidden;\r\n    font-size: 34rpx;\r\n    color: #F20C32;\r\n    margin-left: 24rpx;\r\n  }\r\n\r\n  .goods-box .goods-price2 {\r\n    overflow: hidden;\r\n    font-size: 26rpx;\r\n    color: #aaa;\r\n    text-decoration: line-through;\r\n    margin-left: 20rpx;\r\n  }\r\n\r\n  .goods-box .buy-wrapper {\r\n    margin-top: 10rpx;\r\n    display: flex;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .goods-box .buy {\r\n    width: 52rpx;\r\n    height: 52rpx;\r\n    background-color: #34B764;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    padding: 4rpx;\r\n  }\r\n\r\n  .goods-box .price-score text {\r\n    margin-top: 6rpx;\r\n  }\r\n\r\n  .goods-box .original-price {\r\n    color: #858996;\r\n    font-size: 20rpx;\r\n    font-weight: 100;\r\n    line-height: 20rpx;\r\n  }\r\n\r\n  .goods-box .original-price .price {\r\n    text-decoration: line-through;\r\n  }\r\n\r\n  .goods-quota {\r\n    margin-top: 20rpx;\r\n    width: 300rpx;\r\n    position: relative;\r\n    width: 200rpx;\r\n  }\r\n\r\n  .goods-quota .num {\r\n    position: absolute;\r\n    right: -80rpx;\r\n    top: -12rpx;\r\n    font-size: 22rpx;\r\n  }\r\n</style>\r\n<style lang=\"scss\">\r\n  .time {\r\n    &__custom {\r\n      background-color: #e64340;\r\n    }\r\n\r\n    &__doc {\r\n      color: #324A43;\r\n      padding: 0px 2px;\r\n      margin-top: 5px;\r\n    }\r\n\r\n    &__item {\r\n      color: #606266;\r\n      font-size: 10px;\r\n      margin-right: 2px;\r\n    }\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-quota.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-quota.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688733635\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-quota.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-quota.vue?vue&type=style&index=1&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688737825\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}