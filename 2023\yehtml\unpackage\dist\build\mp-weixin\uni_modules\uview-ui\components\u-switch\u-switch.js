(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uview-ui/components/u-switch/u-switch"],{"1bcf":function(t,i,e){"use strict";e.r(i);var n=e("6654"),a=e("9e0a");for(var u in a)["default"].indexOf(u)<0&&function(t){e.d(i,t,(function(){return a[t]}))}(u);e("9ee2");var s=e("828b"),o=Object(s["a"])(a["default"],n["b"],n["c"],!1,null,"21721208",null,!1,n["a"],void 0);i["default"]=o.exports},6654:function(t,i,e){"use strict";e.d(i,"b",(function(){return a})),e.d(i,"c",(function(){return u})),e.d(i,"a",(function(){return n}));var n={uLoadingIcon:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-loading-icon/u-loading-icon")]).then(e.bind(null,"e83a"))}},a=function(){var t=this,i=t.$createElement,e=(t._self._c,t.__get_style([t.switchStyle,t.$u.addStyle(t.customStyle)])),n=t.__get_style([t.bgStyle]),a=t.__get_style([t.nodeStyle]);t.$mp.data=Object.assign({},{$root:{s0:e,s1:n,s2:a}})},u=[]},"791a":function(t,i,e){},"9e0a":function(t,i,e){"use strict";e.r(i);var n=e("f537"),a=e.n(n);for(var u in n)["default"].indexOf(u)<0&&function(t){e.d(i,t,(function(){return n[t]}))}(u);i["default"]=a.a},"9ee2":function(t,i,e){"use strict";var n=e("791a"),a=e.n(n);a.a},f537:function(t,i,e){"use strict";(function(t){var n=e("47a9");Object.defineProperty(i,"__esModule",{value:!0}),i.default=void 0;var a=n(e("3ddb")),u={name:"u-switch",mixins:[t.$u.mpMixin,t.$u.mixin,a.default],watch:{value:{immediate:!0,handler:function(i){i!==this.inactiveValue&&i!==this.activeValue&&t.$u.error("v-model绑定的值必须为inactiveValue、activeValue二者之一")}}},data:function(){return{bgColor:"#ffffff"}},computed:{isActive:function(){return this.value===this.activeValue},switchStyle:function(){var i={};return i.width=t.$u.addUnit(2*this.size+2),i.height=t.$u.addUnit(Number(this.size)+2),this.customInactiveColor&&(i.borderColor="rgba(0, 0, 0, 0)"),i.backgroundColor=this.isActive?this.activeColor:this.inactiveColor,i},nodeStyle:function(){var i={};i.width=t.$u.addUnit(this.size-this.space),i.height=t.$u.addUnit(this.size-this.space);var e=this.isActive?t.$u.addUnit(this.space):t.$u.addUnit(this.size);return i.transform="translateX(-".concat(e,")"),i},bgStyle:function(){var i={};return i.width=t.$u.addUnit(2*Number(this.size)-this.size/2),i.height=t.$u.addUnit(this.size),i.backgroundColor=this.inactiveColor,i.transform="scale(".concat(this.isActive?0:1,")"),i},customInactiveColor:function(){return"#fff"!==this.inactiveColor&&"#ffffff"!==this.inactiveColor}},methods:{clickHandler:function(){var t=this;if(!this.disabled&&!this.loading){var i=this.isActive?this.inactiveValue:this.activeValue;this.asyncChange||this.$emit("input",i),this.$nextTick((function(){t.$emit("change",i)}))}}}};i.default=u}).call(this,e("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uview-ui/components/u-switch/u-switch-create-component',
    {
        'uni_modules/uview-ui/components/u-switch/u-switch-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("1bcf"))
        })
    },
    [['uni_modules/uview-ui/components/u-switch/u-switch-create-component']]
]);
