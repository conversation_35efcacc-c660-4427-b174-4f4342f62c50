<view><block wx:if="{{applyStatus==-1}}"><view class="noApply"><u-empty vue-id="4572730a-1" mode="car" text="诚邀您成为分销商" icon="http://cdn.uviewui.com/uview/empty/car.png" bind:__l="__l"></u-empty><view class="block-btn"><van-button vue-id="4572730a-2" type="primary" block="{{true}}" round="{{true}}" bind:click="goForm" bind:__l="__l" vue-slots="{{['default']}}">免费申请 等待管理员审核</van-button></view><block wx:if="{{setting.canBuy}}"><view class="block-btn"><u-button vue-id="4572730a-3" type="warning" round="{{true}}" text="{{'直接支付'+setting.price+'元，立即免审开通'}}" data-event-opts="{{[['^click',[['buy']]]]}}" bind:click="__e" bind:__l="__l"></u-button></view></block></view></block><block wx:if="{{applyStatus==0}}"><view class="noApply"><u-empty vue-id="4572730a-4" mode="car" text="感谢您的支持，请等待管理员审核" icon="http://cdn.uviewui.com/uview/empty/car.png" bind:__l="__l"></u-empty><view class="block-btn"><u-button vue-id="4572730a-5" type="primary" round="{{true}}" text="先去逛逛" data-event-opts="{{[['^click',[['goShop']]]]}}" bind:click="__e" bind:__l="__l"></u-button></view></view></block><block wx:if="{{applyStatus==1}}"><view class="noApply"><u-empty vue-id="4572730a-6" mode="car" text="{{'很遗憾，您的申请没有通过'+applyInfo.remark}}" icon="http://cdn.uviewui.com/uview/empty/car.png" bind:__l="__l"></u-empty><view class="block-btn"><u-button vue-id="4572730a-7" type="error" round="{{true}}" text="回首页" data-event-opts="{{[['^click',[['goShop']]]]}}" bind:click="__e" bind:__l="__l"></u-button></view></view></block><block wx:if="{{applyStatus==2}}"><view class="noApply"><u-icon vue-id="4572730a-8" name="checkmark-circle" color="#07c160" size="240" bind:__l="__l"></u-icon><view>恭喜您成为分销商</view><view class="block-btn" style="margin-top:240rpx;"><u-button vue-id="4572730a-9" type="primary" round="{{true}}" text="前往分销中心" data-event-opts="{{[['^click',[['goFx']]]]}}" bind:click="__e" bind:__l="__l"></u-button></view></view></block></view>