<template>
  <view class="list2">
    <view v-for="(item, index) in list" :key="index" slot="slot{{index}}">
      <view class="item-wrapper">
        <u-row justify="space-between" gutter="10">
          <u-col span="4">
            <u-image width="200rpx" height="200rpx" :src="item.pic" radius="10"></u-image>
          </u-col>
          <u-col span="8">
            <listGoodsItemQuota :item="item"></listGoodsItemQuota>
          </u-col>
        </u-row>
      </view>
    </view>
  </view>

</template>

<script>
  import empty from 'empty-value'
  import listGoodsItemQuota from '@/components/list-item/list-goods-item-quota'

  export default {
    components: {
      listGoodsItemQuota
    },
    props: {
      list: {
        type: Array,
        default: [],
      },
      type: {
        type: String,
        default: '',
      },
    },
    onReady() {},
    data() {
      return {}
    },
    watch: {
      list: function(val) {
        //console.log('watch list is', val)
      }
    },
    methods: {
      imageClick(item) {
        let that = this
        if (that.type === 'goods') {
          uni.navigateTo({
            url: '/pages/goods/detail?id=' + item.id
          })
        }
      }
    },
  }
</script>
<style>
  .list2 {
    padding: 20rpx 0;
  }

  .item-wrapper {
    box-shadow: 0 10rpx 20rpx 0 rgba(0, 0, 0, 0.3);
    border-radius: 20rpx;
    background: #FFFFFF;
    color: #2C3E50;
    padding: 16rpx;
    margin-bottom: 20rpx;
  }
</style>
