<template>
  <view class="index container-wrapper">
    <view class="category-page-box">
      <view class="category-page">
        <view class="search">
          <u-search placeholder="输入关键词搜索" searchIconSize="28.6" height="41.6" v-model="kw" :showAction="false"
            @search="search"></u-search>
        </view>
        <view class="_main">
          <view class="tab1">
            <u-row customStyle="margin-bottom: 10px">
              <u-col span="4" justify="center" textAlign="center">
                <div class="tab line" :class="{ active: activeTab === 'tab1' }" @click="changeTab('tab1')">
                  <div style="height: 30rpx;">&nbsp;</div>
                  <div class="title">土鲜多</div>
                  <!-- <div class="dsc">深山好货</div> -->
                </div>
              </u-col>
              <u-col span="4" justify="center" textAlign="center">
                <div class="tab line" :class="{ active: activeTab === 'tab2' }" @click="changeTab('tab2')">
                  <div style="height: 30rpx;">&nbsp;</div>
                  <div class="title">山泉多</div>
                  <!-- <div class="dsc">甘甜健康</div> -->
                </div>
              </u-col>
              <u-col span="4" justify="center" textAlign="center">
                <div class="tab" :class="{ active: activeTab === 'tab3' }" @click="changeTab('tab3')">
                  <div style="height: 30rpx;">&nbsp;</div>
                  <div class="title">好礼多</div>
                  <!-- <div class="dsc">礼品多多</div> -->
                </div>
              </u-col>
            </u-row>
          </view>
          <view class="main">
            <scroll-view class="u-tab-view menu-scroll-view" scroll-y="true" scroll-with-animation="true">
              <view v-for="(item,index) in firstCategories" :key="index" class="u-tab-item"
                :class="[current==index ? 'u-tab-item-active' : '']" :data-current="index" @tap.stop="swichMenu(index)">
                <text class="u-line-1">{{item.name}}</text>
              </view>
            </scroll-view>
            <scroll-view class="goods-container" scroll-y="true" :scroll-top="scrolltop">
              <u-empty v-if="!goodsList" mode="list" text="暂无商品" marginTop="200rpx" />
              <list3 :list="goodsList"></list3>
            </scroll-view>
          </view>
        </view>
      </view>
      <goods-pop :show="showGoodsPop" :goodsDetail="goodsDetail" @close="showGoodsPop = false"></goods-pop>
    </view>
  </view>
</template>

<script>
  import empty from 'empty-value'
  import list3 from '@/components/list/list3'

  const TOOLS = require('@/common/tools')
  export default {
    components: {
      list3
    },
    data() {
      return {
        tabIndex: 1,
        kw: '',
        categorySelected: {},
        activeCategory: 0,
        categories: undefined,
        firstCategories: undefined,
        adPosition: undefined,
        current: 0, // 预设当前项的值
        scrolltop: 0,
        goodsList: undefined,
        skuCurGoods: undefined,
        page: 1,
        // 下面为弹出商品详情
        showGoodsPop: false,
        goodsDetail: undefined,

        activeTab: 'tab1',
      }
    },
    created() {

    },
    mounted() {

    },
    onReady() {

    },
    onLoad(e) {
      // #ifdef  MP-WEIXIN || MP-ALIPAY || MP-BAIDU || MP-TOUTIAO || MP-QQ
      uni.showShareMenu({
        withShareTicket: true,
      })
      // #endif

      // 切到“山泉水”
      // TODO: 未做其他分类判断
      if (e && e.tid) {
        if (e.tid === '1') {
          this._categories('YBN2')
        }
        if (e.tid === '2') {
          this._categories('YBN3')
        }
      } else {
        this._categories()
      }

      TOOLS.showTabBarBadge()
    },
    onShow() {

    },
    onShareAppMessage(e) {
      return {
        title: '"' + this.sysconfigMap.mallName + '" ' + this.sysconfigMap.share_profile,
        path: '/pages/index/index?inviter_id=' + this.uid
      }
    },
    methods: {
      async _categories(key = 'YBN1') {
        uni.showLoading({
          title: '',
        })
        // https://www.yuque.com/apifm/nu0f75/racmle
        const res = await this.$wxapi.goodsCategory()
        uni.hideLoading()
        let categorySelected = this.categorySelected
        if (res.code == 0) {
          const categories = res.data.filter(ele => {
            return !ele.vopCid1 && !ele.vopCid2 && ele.key === key;
          })

          const firstCategories = categories.filter(ele => {
            return ele.level == 1
          })
          categorySelected = firstCategories[0]

          this.current = 0

          if (!empty(categories)) {
            this.categories = categories
            this.firstCategories = firstCategories
            this.categorySelected = categorySelected
            this.getGoodsList(0)
          }
        }
      },
      changeTab(tab) {
        this.activeTab = tab

        if (tab === 'tab1') {
          this._categories('YBN1')
        }
        if (tab === 'tab2') {
          this._categories('YBN2')
        }
        if (tab === 'tab3') {
          this._categories('YBN3')
        }
      },
      // 点击左边的栏目切换
      async swichMenu(index) {
        if (index == this.current) return;
        this.current = index;
        // 如果为0，意味着尚未初始化
        if (this.menuHeight == 0 || this.menuItemHeight == 0) {
          await this.getElRect('menu-scroll-view', 'menuHeight');
          await this.getElRect('u-tab-item', 'menuItemHeight');
        }
        this.scrollTop = 0
        this.getGoodsList(index)
      },
      async getGoodsList(categoryIndex) {
        uni.showLoading({
          title: '',
        })
        const category = this.firstCategories[categoryIndex]
        // https://www.yuque.com/apifm/nu0f75/wg5t98
        const res = await this.$wxapi.goodsv2({
          categoryId: category.id,
          page: this.page,
          pageSize: 20
        })
        uni.hideLoading()
        if (res.code == 700) {
          if (this.page == 1) {
            this.goodsList = null
          } else {
            uni.showToast({
              title: '没有更多了',
              icon: 'none'
            })
          }
          return
        }
        if (res.code != 0) {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
          return
        }
        if (this.page == 1) {
          this.goodsList = res.data.result
        } else {
          this.goodsList = this.goodsList.concat(res.data.result)
        }
      },
      // 弹出商品购买弹窗
      async _showGoodsPop(item) {
        if (!await getApp().checkHasLoginedH5()) {
          uni.navigateTo({
            url: "/pages/login/login"
          })
          return
        }
        // https://www.yuque.com/apifm/nu0f75/vuml8a
        const res = await this.$wxapi.goodsDetail(item.id, this.token)
        if (res.code != 0) {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
          return
        }
        this.goodsDetail = res.data
        this.showGoodsPop = true
      },
      async addCart(item) {
        console.log('token', this.token);
        if (!await getApp().checkHasLoginedH5()) {
          uni.navigateTo({
            url: "/pages/login/login"
          })
          return
        }
        let res
        if (item.supplyType == 'vop_jd') {
          // https://www.yuque.com/apifm/nu0f75/yum741
          res = await this.$wxapi.jdvopCartAdd({
            token: this.token,
            goodsId: item.yyId,
            number: 1
          })
        } else {
          // https://www.yuque.com/apifm/nu0f75/et6m6m
          res = await this.$wxapi.shippingCarInfoAddItem(this.token, item.id, 1, [], [])
        }
        if (res.code != 0) {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
          return
        }
        TOOLS.showTabBarBadge()
        uni.showToast({
          title: '加入购物车'
        })
      },
      search(v) {
        uni.navigateTo({
          url: '/pages/goods/list?kw=' + v,
        })
      },
      searchscan() {
        uni.scanCode({
          scanType: ['barCode', 'qrCode', 'datamatrix', 'pdf417'],
          success: res => {
            this.kw = res.result
            uni.navigateTo({
              url: '/pages/goods/list?kw=' + res.result,
            })
          }
        })
      }
    }
  }
</script>
<style scoped lang="scss">
  .index.container-wrapper {
    padding: 0;
    margin: 0;
    background: linear-gradient(90deg, #35641e, #80a933) fixed;
    height: 100vh;
  }

  .category-page {
    display: flex;
    flex-direction: column;
    width: 100vw;
    height: 100vh;

    .search {
      margin: 100rpx 0 100rpx 60rpx;
      width: 400rpx;
      padding: 16rpx;
      display: flex;
      align-items: center;
    }

    ._main {
      background: #fff;
      border-radius: 10px 10px 0 0;
    }

    .main {
      flex: 1;
      overflow: hidden;
      display: flex;

      .u-tab-view {
        width: 180rpx;
        height: 100%;
        background-color: #f6f6f6;
      }

      .u-tab-item {
        height: 110rpx;
        background: #FFFFFF;
        box-sizing: border-box;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 26rpx;
        color: #444;
        font-weight: 400;
        line-height: 1;
      }

      .u-tab-item-active {
        position: relative;
        color: #000;
        font-size: 30rpx;
        font-weight: 600;
        background: #F6F6F6;
      }

      .u-tab-item-active::before {
        content: "";
        position: absolute;
        border-left: 4px solid #e64340;
        height: 60rpx;
        left: 0;
        top: 25rpx;
      }

      .goods-container {
        flex: 1;
        height: 100%;
        background: #F6F6F6;
        padding: 30rpx 16rpx;

        .goodsList {
          margin-bottom: 32rpx;
          padding: 0 8rpx;
          display: flex;

          .goods-info {
            flex: 1;
            margin-left: 24rpx;
            position: relative;

            .t {
              font-weight: bold;
              color: #333;
              font-size: 28rpx;
            }

            .t2 {
              color: #666;
              font-size: 26rpx;
            }

            .price {
              color: #e64340;
              font-size: 40rpx;
              display: flex;
              align-items: center;

              font {
                font-size: 22rpx;
              }
            }

            .addCar {
              position: absolute;
              right: 24rpx;
              bottom: 16rpx;
            }
          }
        }
      }
    }

  }
</style>
<style lang="scss">
  .tab1 {
    margin: 0 12px;
  }

  .tab1 .tab.line {
    position: relative;
    width: 100%;
    padding-bottom: 2rpx;
  }

  .tab1 .tab.line::after {
    content: "";
    position: absolute;
    top: 20%;
    right: 0;
    bottom: 10%;
    width: 1px;
    background-color: #d3d3d3;
  }

  .tab1 .tab .title {
    font-size: 40rpx;
    font-weight: bolder;
    margin-bottom: 20rpx;
  }

  .tab1 .tab .dsc {
    font-size: 30rpx;
    height: 60rpx;
  }

  .tab1 .tab.active {
    //background-image: url("/static/images/tab-bg.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  .tab1 .tab.active .dsc {
    //background-image: url("/static/images/tab-dsc-bg.png");
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    font-size: 32rpx;
    line-height: 50rpx;
    color: #FFFFFF;
  }

  .swiper {
    height: 420rpx;
  }
</style>