<view class="data-v-f49ee22e"><view class="form-box data-v-f49ee22e"><u-form vue-id="4394a780-1" label-width="130rpx" model="{{form}}" data-ref="uForm" class="data-v-f49ee22e vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('4394a780-2')+','+('4394a780-1')}}" label="消耗积分" prop="deductionScore" required="{{true}}" class="data-v-f49ee22e" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('4394a780-3')+','+('4394a780-2')}}" type="number" clearable="{{true}}" focus="{{true}}" placeholder="请输入积分数量" value="{{form.deductionScore}}" data-event-opts="{{[['^input',[['__set_model',['$0','deductionScore','$event',[]],['form']]]]]}}" class="data-v-f49ee22e" bind:__l="__l"></u-input></u-form-item></u-form></view><view class="submit-btn data-v-f49ee22e"><u-button vue-id="4394a780-4" type="success" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" class="data-v-f49ee22e" bind:__l="__l" vue-slots="{{['default']}}">立即兑换</u-button></view></view>