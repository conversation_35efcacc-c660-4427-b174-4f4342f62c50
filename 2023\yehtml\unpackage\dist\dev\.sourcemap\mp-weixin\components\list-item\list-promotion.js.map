{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-promotion.vue?e27d", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-promotion.vue?12cc", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-promotion.vue?386c", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-promotion.vue?3598", "uni-app:///components/list-item/list-promotion.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-promotion.vue?6563", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-promotion.vue?970a"], "names": ["props", "item", "type", "default", "onReady", "data", "methods", "goDetail", "uni", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACkE;AACL;AACa;;;AAG1E;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAyqB,CAAgB,yrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;eCS7rB;EACAA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACA;EACA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3BA;AAAA;AAAA;AAAA;AAAq9B,CAAgB,w8BAAG,EAAC,C;;;;;;;;;;;ACAz+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/list-item/list-promotion.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./list-promotion.vue?vue&type=template&id=660fcabc&\"\nvar renderjs\nimport script from \"./list-promotion.vue?vue&type=script&lang=js&\"\nexport * from \"./list-promotion.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list-promotion.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/list-item/list-promotion.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-promotion.vue?vue&type=template&id=660fcabc&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-promotion.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-promotion.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"promotion-box\" :style=\"{background: item.bgColor, color: item.textColor}\">\r\n    <view class=\"title\" @click=\"goDetail(item)\">{{ item.title }}</view>\r\n    <view class=\"dsc\" @click=\"goDetail(item)\">{{ item.dsc }}</view>\r\n    <view @click=\"goDetail(item)\" class=\"button\" :style=\"{background: item.buttonBgColor, color: item.buttonColor}\">{{ item.buttonText }}</view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    props: {\r\n      item: {\r\n        type: Object,\r\n        default: {},\r\n      },\r\n    },\r\n    onReady() {},\r\n    data() {\r\n      return {}\r\n    },\r\n    methods: {\r\n      goDetail(item) {\r\n        uni.navigateTo({\r\n          url: item.path\r\n        })\r\n      }\r\n    },\r\n  }\r\n</script>\r\n<style>\r\n  .promotion-box {\r\n    overflow: hidden;\r\n    text-align: left;\r\n    overflow: hidden;\r\n    background: #FFF9D7;\r\n    text-align: center;\r\n    padding: 10rpx 0;\r\n  }\r\n\r\n  .title {\r\n    font-size: 46rpx;\r\n    font-weight: bold;\r\n    padding: 0 4rpx;\r\n  }\r\n\r\n  .dsc {\r\n    margin: 10rpx 0;\r\n    font-size: 24rpx;\r\n  }\r\n\r\n  .button {\r\n    display: inline-block;\r\n    white-space: nowrap;\r\n    padding: 6rpx 16rpx;\r\n    border-radius: 2rpx 8rpx;\r\n    font-size: 24rpx;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-promotion.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-promotion.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688730681\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}