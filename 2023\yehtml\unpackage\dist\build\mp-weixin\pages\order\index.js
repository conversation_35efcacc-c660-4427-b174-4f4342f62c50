(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/order/index"],{"074b":function(t,e,n){"use strict";var r=n("43f4"),a=n.n(r);a.a},"22e3":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return o})),n.d(e,"a",(function(){return r}));var r={uSticky:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-sticky/u-sticky")]).then(n.bind(null,"2a0c"))},uTabs:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-tabs/u-tabs")]).then(n.bind(null,"3ebd"))},pageBoxEmpty:function(){return n.e("components/page-box-empty/page-box-empty").then(n.bind(null,"bc43"))},uButton:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-button/u-button")]).then(n.bind(null,"9edc"))}},a=function(){var t=this,e=t.$createElement,n=(t._self._c,!t.orderList||0==t.orderList.length),r=n?null:t.__map(t.orderList,(function(e,n){var r=t.__get_orig(e),a=e.status>0&&!e.isEnd?1==e.refundStatus||t.refundApplyedOrderIds.includes(e.id+""):null;return{$orig:r,g1:a}}));t.$mp.data=Object.assign({},{$root:{g0:n,l0:r}})},o=[]},"2c99":function(t,e,n){},"43f4":function(t,e,n){},9683:function(t,e,n){"use strict";var r=n("2c99"),a=n.n(r);a.a},a68f:function(t,e,n){"use strict";(function(t){var r=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=r(n("7eb4")),o=r(n("ee10")),s=n("6fc5"),u={data:function(){return{refundApplyedOrderIds:void 0,orderList:void 0,dataList:void 0,list:[{name:"全部",status:""},{name:"待付款",status:"0"},{name:"待发货",status:"1"},{name:"待收货",status:"2"},{name:"待评价",status:"3"},{name:"售后",status:"99"}],current:0}},onLoad:function(t){if(t.status){var e=this.list.findIndex((function(e){return e.status==t.status}));-1!=e&&(this.current=1*e)}this.change({index:this.current})},onShow:function(){this.refundApplyedOrderIds=t.getStorageSync("refundApplyedOrderIds")},methods:{_orderStatistics:function(){var t=this;return(0,o.default)(a.default.mark((function e(){var n;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.$wxapi.orderStatisticsv2({token:t.token});case 2:n=e.sent,0==n.code&&(t.list[0].count=n.data.count_id_no_pay,t.list[1].count=n.data.count_id_no_transfer,t.list[2].count=n.data.count_id_no_confirm,t.list.splice(0,0));case 4:case"end":return e.stop()}}),e)})))()},getOrderList:function(){var t=this;return(0,o.default)(a.default.mark((function e(){var n,r,o,s;return a.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return n=t.list[t.current],t.orderList=null,r={token:t.token,status:n.status},99==n.status&&(r.refundStatus=1,r.status=""),e.next=6,t.$wxapi.orderList(r);case 6:o=e.sent,0==o.code&&(s=o.data.goodsMap,o.data.orderList.forEach((function(t){t.goodsList=s[t.id]})),t.orderList=o.data.orderList);case 8:case"end":return e.stop()}}),e)})))()},goIndex:function(){t.switchTab({url:"../index/index"})},change:function(t){this.current=t.index,this._orderStatistics(),this.getOrderList()},close:function(e){var n=this;return(0,o.default)(a.default.mark((function r(){return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.showModal({title:"请确认",content:"确定要取消该订单吗？",success:function(t){t.confirm&&n._close(e)}});case 1:case"end":return r.stop()}}),r)})))()},_close:function(e){var n=this;return(0,o.default)(a.default.mark((function r(){var o;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,n.$wxapi.orderClose(n.token,e);case 2:o=r.sent,0!=o.code?t.showToast({title:o.msg,icon:"none"}):(t.showToast({title:"已取消"}),n.change({index:n.current}));case 4:case"end":return r.stop()}}),r)})))()},orderDelete:function(e){var n=this;return(0,o.default)(a.default.mark((function r(){return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.showModal({title:"请确认",content:"确定要删除该订单吗？删除后无法恢复！",success:function(t){t.confirm&&n._orderDelete(e)}});case 1:case"end":return r.stop()}}),r)})))()},_orderDelete:function(e){var n=this;return(0,o.default)(a.default.mark((function r(){var o;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,n.$wxapi.orderDelete(n.token,e);case 2:o=r.sent,0!=o.code?t.showToast({title:o.msg,icon:"none"}):(t.showToast({title:"删除成功"}),n.change({index:n.current}));case 4:case"end":return r.stop()}}),r)})))()},pay:function(e){var n=this;return(0,o.default)(a.default.mark((function r(){var o,u,i,c,d;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,n.$wxapi.userAmount(n.token);case 2:if(o=r.sent,0,0!=o.code&&t.showToast({title:o.msg,icon:"none"}),u=o.data.balance,i=n.orderList[e],c=(i.amountReal-u).toFixed(2),!(c<=0)){r.next=15;break}return r.next=11,n.$wxapi.orderPay(n.token,i.id);case 11:d=r.sent,0!=d.code?t.showToast({title:d.msg,icon:"none"}):(t.showToast({title:"支付成功"}),n.change({index:1})),r.next=16;break;case 15:s.pay("wxpay",{appid:getApp().globalData.wxpayOpenAppId},c,"支付订单 ："+i.id,"支付订单 ："+i.id,{type:0,id:i.id},(function(){n.change({index:1})}),(function(e){t.showToast({title:"支付失败",icon:"none"})}));case 16:case"end":return r.stop()}}),r)})))()},godetail:function(e){t.navigateTo({url:"./detail?id="+e})},refund:function(e){var n=this;return(0,o.default)(a.default.mark((function r(){var o,s;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(5!=e.type){r.next=16;break}return o=e.goodsList[0].goodsIdStr,t.setStorageSync("afsGoodsId",o),r.next=5,n.$wxapi.joycityPointsCanApplyAfterSale({token:n.token,orderId:e.id,goodsId:o});case 5:if(s=r.sent,0==s.code){r.next=9;break}return t.showToast({title:s.msg,icon:"none"}),r.abrupt("return");case 9:if(s.data.canApply){r.next=12;break}return t.showToast({title:s.data.cannotApplyTip,icon:"none"}),r.abrupt("return");case 12:if(2!=s.data.supportMethod){r.next=15;break}return t.showToast({title:"请联系客服进行售后:"+s.data.afsHotLine,icon:"none"}),r.abrupt("return");case 15:t.setStorageSync("supportAfsTypeList",s.data.supportAfsTypeList);case 16:t.setStorageSync("orderType",e.type),t.navigateTo({url:"../refund/apply?orderId="+e.id});case 18:case"end":return r.stop()}}),r)})))()},refundCancel:function(e){var n=this;return(0,o.default)(a.default.mark((function r(){return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.showModal({title:"请确认",content:"确定要撤销售后吗？",success:function(t){t.confirm&&n._refundCancel(e)}});case 1:case"end":return r.stop()}}),r)})))()},_refundCancel:function(e){var n=this;return(0,o.default)(a.default.mark((function r(){var o;return a.default.wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,n.$wxapi.refundApplyCancel(n.token,e.id);case 2:o=r.sent,0==o.code?(t.showToast({title:"已撤销",icon:"none"}),n.getOrderList()):t.showToast({title:o.msg,icon:"none"});case 4:case"end":return r.stop()}}),r)})))()}}};e.default=u}).call(this,n("df3c")["default"])},bb43:function(t,e,n){"use strict";n.r(e);var r=n("22e3"),a=n("dd73");for(var o in a)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(o);n("074b"),n("9683");var s=n("828b"),u=Object(s["a"])(a["default"],r["b"],r["c"],!1,null,"5d899414",null,!1,r["a"],void 0);e["default"]=u.exports},dd73:function(t,e,n){"use strict";n.r(e);var r=n("a68f"),a=n.n(r);for(var o in r)["default"].indexOf(o)<0&&function(t){n.d(e,t,(function(){return r[t]}))}(o);e["default"]=a.a},f9dd:function(t,e,n){"use strict";(function(t,e){var r=n("47a9");n("96bd");r(n("3240"));var a=r(n("bb43"));t.__webpack_require_UNI_MP_PLUGIN__=n,e(a.default)}).call(this,n("3223")["default"],n("df3c")["createPage"])}},[["f9dd","common/runtime","common/vendor"]]]);