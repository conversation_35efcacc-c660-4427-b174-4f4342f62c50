(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/notice/detail"],{"1a6d":function(e,n,t){"use strict";var i=t("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=i(t("7eb4")),o=i(t("ee10")),a={data:function(){return{noticeDetail:void 0}},created:function(){},mounted:function(){},onReady:function(){},onLoad:function(e){this._noticeDetail(e.id)},onShow:function(){},onShareAppMessage:function(e){return{title:'"'+this.sysconfigMap.mallName+'" '+this.sysconfigMap.share_profile,path:"/pages/notice/detail?inviter_id="+this.uid+"&id="+this.noticeDetail.id}},methods:{_noticeDetail:function(e){var n=this;return(0,o.default)(u.default.mark((function t(){var i;return u.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,n.$wxapi.noticeDetail(e);case 2:i=t.sent,0==i.code&&(n.noticeDetail=i.data);case 4:case"end":return t.stop()}}),t)})))()}}};n.default=a},"4f59":function(e,n,t){"use strict";t.r(n);var i=t("1a6d"),u=t.n(i);for(var o in i)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return i[e]}))}(o);n["default"]=u.a},7118:function(e,n,t){},"73eb":function(e,n,t){"use strict";var i=t("7118"),u=t.n(i);u.a},"88bb":function(e,n,t){"use strict";t.d(n,"b",(function(){return u})),t.d(n,"c",(function(){return o})),t.d(n,"a",(function(){return i}));var i={uEmpty:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-empty/u-empty")]).then(t.bind(null,"c57e"))},uCell:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-cell/u-cell")]).then(t.bind(null,"819c"))},uLine:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-line/u-line")]).then(t.bind(null,"198f"))},uParse:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-parse/u-parse")]).then(t.bind(null,"da08"))}},u=function(){var e=this.$createElement;this._self._c},o=[]},a0c9:function(e,n,t){"use strict";(function(e,n){var i=t("47a9");t("96bd");i(t("3240"));var u=i(t("c360"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(u.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},c360:function(e,n,t){"use strict";t.r(n);var i=t("88bb"),u=t("4f59");for(var o in u)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return u[e]}))}(o);t("73eb");var a=t("828b"),c=Object(a["a"])(u["default"],i["b"],i["c"],!1,null,"74b05a3e",null,!1,i["a"],void 0);n["default"]=c.exports}},[["a0c9","common/runtime","common/vendor"]]]);