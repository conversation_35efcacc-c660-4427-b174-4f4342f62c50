<template>
  <view class="index container-wrapper">
    <view class="main-wrapper">
      <view class="top-image-wrapper">
        <div class="top-image">&nbsp;</div>
      </view>
      <view class="main">
        <view>
          <tab-water></tab-water>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
  import empty from 'empty-value'
  import tabWater from '@/components/tabs/tab-water'

  export default {
    components: {
      tabWater,
    }
  }
</script>
<style scoped lang="scss">
  .index.container-wrapper {
    padding: 0;
    margin: 0;
    background: #1f61a4;
  }

  .index {
    .main-wrapper {
      position: relative;
    }

    .top-image {
      width: 100%;
      height: 250px;
      background-image: url('https://ye.niutouren.vip/static/images/water/bg.png');
      background-size: cover;
      background-position: bottom center;
    }
  }

  .main {
    background: #FFFFFF;
    margin: 0 20rpx;
    margin-top: -20rpx;
    padding: 10rpx;
    border-radius: 20rpx;
  }
</style>