<template>
  <view class="navbar">
    <view class="nav-box" :style="{ paddingTop: statusBarHeight + 10 + 'rpx' }">
      <view class="nav">
        <u-icon size="22" :color="'#' + color" name="arrow-left" @click="navBack"></u-icon>
      </view>
      <view v-if="title !== ''" class="nav-title">{{ title }}</view>
    </view>
  </view>
</template>

<script>
  import empty from 'empty-value'
  import common from '@/common/common.js'

  export default {
    props: {
      statusBarHeight: {
        type: Number,
        default: 0,
      },
      title: {
        type: String,
        default: '',
      },
      color: {
        type: String,
        default: '222222',
      },
    },
    data() {
      return {}
    },
    methods: {
      navBack() {
        common.back()
      },
    },
  }
</script>
<style>
  .nav-box {
    width: 100%;
    position: absolute;
    margin: 20rpx;
  }

  .nav {
    position: absolute;
  }

  .nav-title {
    text-align: center;
  }
</style>
