<template>
  <view class="slot">
    <view class="left" v-if="data">
      <button class="avatar-wrapper" open-type="chooseAvatar" @chooseavatar="onChooseAvatar" plain="true">
        <u-avatar size="60" :src="data.base.avatarUrl"></u-avatar>
      </button>
      <view class="info" @click="editNick">
        <view class="username">{{ data.base.nick || '请设置昵称' }}</view>
        <view class="desc">{{ data.base.id }}</view>
      </view>
    </view>
    <view class="left" v-if="needLogin">
      <u-avatar size="60"></u-avatar>
      <view class="info" @click="goLogin">
        <view class="username">登录</view>
        <view class="desc">点击登录跳转至登录页面</view>
      </view>
    </view>
  </view>
</template>

<script>
  const USER = require('@/common/user.js')

  export default {
    name: 'user-info',
    props: {
      data: {
        type: Object,
        default: () => {
          return {};
        }
      },
      needLogin: {
        type: Boolean,
        default: false
      }
    },
    data() {
      return {}
    },
    methods: {
      async onChooseAvatar(e) {
        let that = this
        const avatarUrl = e.detail.avatarUrl

        if (avatarUrl) {
          const res = await this.$wxapi.uploadFile(this.token, avatarUrl)

          if (res.code == 0) {
            const d = {
              token: this.token,
              avatarUrl: res.data.url
            }
            const _res = await this.$wxapi.modifyUserInfo(d)
            uni.navigateTo({
              url: '/pages/my/info'
            })
          }
        }
      },
      goLogin() {
        // #ifdef MP
        USER.autoLogin()
        // #endif
        // #ifdef H5
        const ua = window.navigator.userAgent.toLowerCase();
        if (ua.match(/MicroMessenger/i) == 'micromessenger') {
          USER.autoLogin()
        } else {
          uni.navigateTo({
            url: '/pages/login/login'
          })
        }
        // #endif
        // #ifndef MP || H5
        uni.navigateTo({
          url: '/pages/login/login'
        })
        // #endif
      },
      editNick() {
        uni.navigateTo({
          url: '/pages/my/info'
        })
      }
    }
  };
</script>

<style lang="scss" scoped>
  .slot {
    color: #FFFFFF;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 48rpx;
    position: relative;
    z-index: 0;

    .left {
      display: flex;
      align-items: center;

      .avatar-wrapper {
        border: none;
        background: none;
        outline: none;
        padding: 0;
      }

      .info {
        margin-left: 24rpx;
        display: flex;
        align-content: space-between;
        flex-wrap: wrap;

        .username {
          font-size: 32rpx;
          width: 100%;
          color: #FFF;
          margin-bottom: 16rpx;
        }

        .desc {
          font-size: 24rpx;
          width: 100%;
          color: #FFF;
          display: -webkit-box;
          overflow: hidden;
          -webkit-line-clamp: 1;
          -webkit-box-orient: vertical;
        }
      }
    }

    .right {
      display: flex;
      align-items: center;
    }
  }
</style>