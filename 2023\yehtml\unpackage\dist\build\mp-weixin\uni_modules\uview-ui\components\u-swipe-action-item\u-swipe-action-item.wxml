<wxs src="./index.wxs" module="wxs"></wxs>
<view data-ref="u-swipe-action-item" class="u-swipe-action-item data-v-5e2cdc41 vue-ref"><view class="u-swipe-action-item__right data-v-5e2cdc41"><block wx:if="{{$slots.button}}"><slot name="button"></slot></block><block wx:else><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-ref="{{'u-swipe-action-item__right__button-'+index}}" data-event-opts="{{[['tap',[['buttonClickHandler',['$0',index],[[['options','',index]]]]]]]}}" class="u-swipe-action-item__right__button data-v-5e2cdc41 vue-ref-in-for" style="{{'align-items:'+(item.$orig.style&&item.$orig.style.borderRadius?'center':'stretch')+';'}}" bindtap="__e"><view class="u-swipe-action-item__right__button__wrapper data-v-5e2cdc41" style="{{item.s0}}"><block wx:if="{{item.$orig.icon}}"><u-icon vue-id="{{'724ce206-1-'+index}}" name="{{item.$orig.icon}}" color="{{item.$orig.style&&item.$orig.style.color?item.$orig.style.color:'#ffffff'}}" size="{{item.$orig.iconSize?item.g0:item.$orig.style&&item.$orig.style.fontSize?item.g1*1.2:17}}" customStyle="{{item.a0}}" class="data-v-5e2cdc41" bind:__l="__l"></u-icon></block><block wx:if="{{item.$orig.text}}"><text class="u-swipe-action-item__right__button__wrapper__text u-line-1 data-v-5e2cdc41" style="{{'color:'+(item.$orig.style&&item.$orig.style.color?item.$orig.style.color:'#ffffff')+';'+('font-size:'+(item.$orig.style&&item.$orig.style.fontSize?item.$orig.style.fontSize:'16px')+';')+('line-height:'+(item.$orig.style&&item.$orig.style.fontSize?item.$orig.style.fontSize:'16px')+';')}}">{{item.$orig.text}}</text></block></view></view></block></block></view><view class="u-swipe-action-item__content data-v-5e2cdc41" status="{{status}}" change:status="{{wxs.statusChange}}" size="{{size}}" change:size="{{wxs.sizeChange}}" bindtouchstart="{{wxs.touchstart}}" bindtouchmove="{{wxs.touchmove}}" bindtouchend="{{wxs.touchend}}"><slot></slot></view></view>