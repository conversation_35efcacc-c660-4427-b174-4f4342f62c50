<view data-event-opts="{{[['tap',[['clickHandler',['$event']]]]]}}" class="u-tabbar-item data-v-0087ad7e" style="{{$root.s0}}" bindtap="__e"><view class="u-tabbar-item__icon data-v-0087ad7e"><block wx:if="{{icon}}"><u-icon vue-id="b562e1b2-1" name="{{icon}}" color="{{isActive?parentData.activeColor:parentData.inactiveColor}}" size="{{20}}" class="data-v-0087ad7e" bind:__l="__l"></u-icon></block><block wx:else><block wx:if="{{isActive}}"><slot name="active-icon"></slot></block><block wx:else><slot name="inactive-icon"></slot></block></block><u-badge vue-id="b562e1b2-2" absolute="{{true}}" offset="{{[0,dot?'34rpx':badge>9?'14rpx':'20rpx']}}" customStyle="{{badgeStyle}}" isDot="{{dot}}" value="{{badge||(dot?1:null)}}" show="{{dot||badge>0}}" class="data-v-0087ad7e" bind:__l="__l"></u-badge></view><block wx:if="{{$slots.text}}"><slot name="text"></slot></block><block wx:else><text class="u-tabbar-item__text data-v-0087ad7e" style="{{'color:'+(isActive?parentData.activeColor:parentData.inactiveColor)+';'}}">{{text}}</text></block></view>