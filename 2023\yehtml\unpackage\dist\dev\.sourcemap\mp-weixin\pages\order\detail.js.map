{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/order/detail.vue?d924", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/order/detail.vue?397d", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/order/detail.vue?4ae5", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/order/detail.vue?bdbe", "uni-app:///pages/order/detail.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/order/detail.vue?5ea2", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/order/detail.vue?8517"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "orderDetail", "refundApplyInfo", "joycityPointsAfterSaleOrderInfo", "created", "mounted", "onReady", "onLoad", "onShow", "methods", "_orderDetail", "res", "uni", "title", "icon", "orderDelivery", "content", "success", "_orderDelivery", "refundApplyDetail", "joycityPointsSearchAfsOrderDetail", "token", "afterSaleId"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,eAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+H;AAC/H;AAC0D;AACL;AACsC;;;AAG3F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,6FAAM;AACR,EAAE,sGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,oTAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,sUAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC/DA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCuErrB;EACAC;IACA;MACAC;MACAC;MACAC;IACA;EACA;EACAC,6BAEA;EACAC,6BAEA;EACAC,6BAEA;EACAC;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAC;kBACAC;kBACAC;gBACA;gBACAF;gBAAA;cAAA;gBAGA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAG;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACAH;kBACAC;kBACAG;kBACAC;oBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAP;gBACA;kBACAC;oBACAC;kBACA;kBACA;gBACA;kBACAD;oBACAC;oBACAC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAK;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAR;gBACA;kBACA;kBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;kBACAC;kBACAC;gBACA;cAAA;gBAHAX;gBAIA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7JA;AAAA;AAAA;AAAA;AAAwxC,CAAgB,4uCAAG,EAAC,C;;;;;;;;;;;ACA5yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/order/detail.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/order/detail.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./detail.vue?vue&type=template&id=57d42baa&scoped=true&\"\nvar renderjs\nimport script from \"./detail.vue?vue&type=script&lang=js&\"\nexport * from \"./detail.vue?vue&type=script&lang=js&\"\nimport style0 from \"./detail.vue?vue&type=style&index=0&id=57d42baa&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"57d42baa\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/order/detail.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=template&id=57d42baa&scoped=true&\"", "var components\ntry {\n  components = {\n    uCellGroup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell-group/u-cell-group\" */ \"@/uni_modules/uview-ui/components/u-cell-group/u-cell-group.vue\"\n      )\n    },\n    uCell: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-cell/u-cell\" */ \"@/uni_modules/uview-ui/components/u-cell/u-cell.vue\"\n      )\n    },\n    uAlbum: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-album/u-album\" */ \"@/uni_modules/uview-ui/components/u-album/u-album.vue\"\n      )\n    },\n    uDivider: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-divider/u-divider\" */ \"@/uni_modules/uview-ui/components/u-divider/u-divider.vue\"\n      )\n    },\n    uSteps: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-steps/u-steps\" */ \"@/uni_modules/uview-ui/components/u-steps/u-steps.vue\"\n      )\n    },\n    uStepsItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-steps-item/u-steps-item\" */ \"@/uni_modules/uview-ui/components/u-steps-item/u-steps-item.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view v-if=\"orderDetail\" class=\"to-pay-order\">\r\n\t\t<u-cell-group title=\"订单信息\">\r\n\t\t\t<u-cell title=\"订单ID\" :value=\"orderDetail.orderInfo.id\"></u-cell>\r\n\t\t\t<u-cell title=\"订单号\" :value=\"orderDetail.orderInfo.orderNumber\"></u-cell>\r\n\t\t\t<u-cell v-if=\"orderDetail.orderInfo.type == 5\" title=\"京东订单号\" :value=\"orderDetail.orderInfo.orderNumberOuter\"></u-cell>\r\n\t\t</u-cell-group>\r\n\t\t<u-cell-group v-if=\"refundApplyInfo\" title=\"用户申请售后信息\">\r\n\t\t\t<u-cell title=\"售后类型\" :value=\"refundApplyInfo.baseInfo.typeStr\"></u-cell>\r\n\t\t\t<u-cell title=\"售后原因\" :value=\"refundApplyInfo.baseInfo.reason\"></u-cell>\r\n\t\t\t<u-cell v-if=\"refundApplyInfo.baseInfo.outerOrderId\" title=\"售后订单号\" :value=\"refundApplyInfo.baseInfo.outerOrderId\"></u-cell>\r\n\t\t\t<u-cell title=\"申请时间\" :value=\"refundApplyInfo.baseInfo.dateAdd\"></u-cell>\r\n\t\t\t<u-cell v-if=\"refundApplyInfo.baseInfo.remark\" title=\"备注\" :value=\"refundApplyInfo.baseInfo.remark\"></u-cell>\r\n\t\t\t<u-cell title=\"状态\" :value=\"refundApplyInfo.baseInfo.statusStr\"></u-cell>\r\n\t\t</u-cell-group>\r\n\t\t<view class=\"refundApplyInfo-pics\" v-if=\"refundApplyInfo && refundApplyInfo.pics\">\r\n\t\t\t<u-album :urls=\"refundApplyInfo.pics\" keyName=\"pic\" multipleSize=\"125\"></u-album>\r\n\t\t</view>\r\n\t\t<u-cell-group v-if=\"joycityPointsAfterSaleOrderInfo\" title=\"售后进度\">\r\n\t\t\t<u-cell v-for=\"(item,index) in joycityPointsAfterSaleOrderInfo.afsLogs\" :title=\"item.operator + item.operationDesc\" :label=\"item.createdTime\"></u-cell>\r\n\t\t</u-cell-group>\r\n\t\t<u-divider text=\"收货地址\"></u-divider>\r\n\t\t<u-cell v-if=\"orderDetail.logistics\" icon=\"map\" :border=\"false\" :title=\"orderDetail.logistics.linkMan + ' ' + orderDetail.logistics.mobile\" :label=\"orderDetail.logistics.provinceStr + orderDetail.logistics.cityStr + orderDetail.logistics.areaStr + orderDetail.logistics.address\"></u-cell>\r\n\t\t<u-divider text=\"商品信息\"></u-divider>\r\n\t\t<view class=\"order\">\r\n\t\t\t<view class=\"item\" v-for=\"(item, index) in orderDetail.goods\" :key=\"item.id\">\r\n\t\t\t\t<view class=\"left\"><image :src=\"item.pic\" mode=\"aspectFill\"></image></view>\r\n\t\t\t\t<view class=\"content\">\r\n\t\t\t\t\t<view class=\"title u-line-2\">{{ item.goodsName }}</view>\r\n\t\t\t\t\t<view class=\"type\">{{ item.property }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t\t<view class=\"right\">\r\n\t\t\t\t\t<view class=\"price\">\r\n\t\t\t\t\t\t￥{{ item.amountSingle }}\r\n\t\t\t\t\t</view>\r\n\t\t\t\t\t<view class=\"number\">x{{ item.number }}</view>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<view class=\"total\">\r\n\t\t\t\t共 {{ orderDetail.orderInfo.goodsNumber }} 件商品 合计:\r\n\t\t\t\t<text class=\"total-price\">\r\n\t\t\t\t\t￥{{ orderDetail.orderInfo.amountReal }}\r\n\t\t\t\t</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<u-divider v-if=\"orderDetail.orderInfo.remark\" text=\"订单备注\"></u-divider>\r\n\t\t<view v-if=\"orderDetail.orderInfo.remark\" class=\"remark\">\r\n\t\t\t{{ orderDetail.orderInfo.remark }}\r\n\t\t</view>\r\n\t\t<view v-if=\"orderDetail.orderInfo.remark2\" class=\"remark\">\r\n\t\t\t{{ orderDetail.orderInfo.remark2 }}\r\n\t\t</view>\r\n\t\t<u-divider text=\"合计\"></u-divider>\r\n\t\t<u-cell :border=\"false\" title=\"商品金额\" :value=\"'¥' + orderDetail.orderInfo.amount\" :arrow=\"false\"></u-cell>\r\n\t\t<u-cell :border=\"false\" title=\"快递费\" :value=\"'¥' + orderDetail.orderInfo.amountLogistics\" :arrow=\"false\"></u-cell>\r\n\t\t<u-cell :border=\"false\" title=\"总计\" :value=\"'¥' + orderDetail.orderInfo.amountReal\" :arrow=\"false\"></u-cell>\r\n\t\t<template v-if=\"orderDetail.logisticsTraces\">\r\n\t\t\t<u-divider text=\"快递信息\"></u-divider>\r\n\t\t\t<view class=\"logisticsTraces\">\r\n\t\t\t\t<u-steps dot direction=\"column\">\r\n\t\t\t\t\t<u-steps-item v-for=\"(item, index) in orderDetail.logisticsTraces\" :key=\"index\" :title=\"item.AcceptStation\" :desc=\"item.AcceptTime\"></u-steps-item>\r\n\t\t\t\t</u-steps>\r\n\t\t\t</view>\r\n\t\t</template>\r\n\t\t<view v-if=\"orderDetail.orderInfo.status == 2\" class=\"submit safe-area-inset-bottom\">\r\n\t\t\t<u-button type=\"success\" @click=\"orderDelivery\">确认收货</u-button>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\texport default {\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\torderDetail: undefined,\r\n\t\t\t\trefundApplyInfo: undefined,\r\n\t\t\t\tjoycityPointsAfterSaleOrderInfo: undefined\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t\r\n\t\t},\r\n\t\tonReady() {\r\n\t\t\t\r\n\t\t},\r\n\t\tonLoad(e) {\r\n\t\t\tthis._orderDetail(e.id)\r\n\t\t},\r\n\t\tonShow() {\r\n\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tasync _orderDetail(orderId) {\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/oamel8\r\n\t\t\t\tconst res = await this.$wxapi.orderDetail(this.token, orderId)\r\n\t\t\t\tif (res.code != 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t\tuni.navigateBack()\r\n\t\t\t\t\treturn\r\n\t\t\t\t}\r\n\t\t\t\tthis.orderDetail = res.data\r\n\t\t\t\tif(res.data.orderInfo.refundStatus != 0 || res.data.orderInfo.hasRefund) {\r\n\t\t\t\t\tthis.refundApplyDetail()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync orderDelivery() {\r\n\t\t\t\tuni.showModal({\r\n\t\t\t\t    title: '请确认',\r\n\t\t\t\t    content: '确定要确认收货吗？',\r\n\t\t\t\t    success: res => {\r\n\t\t\t\t        if (res.confirm) {\r\n\t\t\t\t            this._orderDelivery()\r\n\t\t\t\t        }\r\n\t\t\t\t    }\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync _orderDelivery() {\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/vy8eai\r\n\t\t\t\tconst res = await this.$wxapi.orderDelivery(this.token, this.orderDetail.orderInfo.id)\r\n\t\t\t\tif(res.code == 0) {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: '已收到货',\r\n\t\t\t\t\t})\r\n\t\t\t\t\tthis._orderDetail(this.orderDetail.orderInfo.id)\r\n\t\t\t\t} else {\r\n\t\t\t\t\tuni.showToast({\r\n\t\t\t\t\t\ttitle: res.msg,\r\n\t\t\t\t\t\ticon: 'none'\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync refundApplyDetail() {\r\n\t\t\t\t// https://www.yuque.com/apifm/nu0f75/rgng3x\r\n\t\t\t\tconst res = await this.$wxapi.refundApplyDetail(this.token, this.orderDetail.orderInfo.id)\r\n\t\t\t\tif(res.code == 0) {\r\n\t\t\t\t\tthis.refundApplyInfo = res.data[0]\r\n\t\t\t\t\tif(this.orderDetail.orderInfo.type == 5) {\r\n\t\t\t\t\t\tthis.joycityPointsSearchAfsOrderDetail(this.refundApplyInfo.baseInfo.outerOrderId)\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tasync joycityPointsSearchAfsOrderDetail(afterSaleId) {\r\n\t\t\t\tconst res = await this.$wxapi.joycityPointsSearchAfsOrderDetail({\r\n\t\t\t\t\ttoken: this.token,\r\n\t\t\t\t\tafterSaleId\r\n\t\t\t\t})\r\n\t\t\t\tif(res.code == 0) {\r\n\t\t\t\t\tthis.joycityPointsAfterSaleOrderInfo = res.data\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style scoped lang=\"scss\">\r\n.to-pay-order {\r\n\tpadding-bottom: 100rpx;\r\n\t.shop-section {\r\n\t\tmargin-top: 32rpx;\r\n\t}\r\n}\r\n.order {\r\n\twidth: 710rpx;\r\n\tbackground-color: #ffffff;\r\n\tmargin: 20rpx auto;\r\n\tborder-radius: 20rpx;\r\n\tbox-sizing: border-box;\r\n\tpadding: 20rpx;\r\n\tfont-size: 28rpx;\r\n\t.top {\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: space-between;\r\n\t\t.left {\r\n\t\t\tdisplay: flex;\r\n\t\t\talign-items: center;\r\n\t\t\t.store {\r\n\t\t\t\tmargin: 0 10rpx;\r\n\t\t\t\tfont-size: 32rpx;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.right {\r\n\t\t\tcolor: $u-warning-dark;\r\n\t\t}\r\n\t}\r\n\t.item {\r\n\t\tdisplay: flex;\r\n\t\tmargin: 20rpx 0 0;\r\n\t\t.left {\r\n\t\t\tmargin-right: 20rpx;\r\n\t\t\timage {\r\n\t\t\t\twidth: 200rpx;\r\n\t\t\t\theight: 200rpx;\r\n\t\t\t\tborder-radius: 10rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.content {\r\n\t\t\tflex: 1;\r\n\t\t\t.title {\r\n\t\t\t\tfont-size: 28rpx;\r\n\t\t\t\tline-height: 50rpx;\r\n\t\t\t}\r\n\t\t\t.type {\r\n\t\t\t\tmargin: 10rpx 0;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tcolor: $u-tips-color;\r\n\t\t\t}\r\n\t\t\t.delivery-time {\r\n\t\t\t\tcolor: #e5d001;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t\t.right {\r\n\t\t\tmargin-left: 10rpx;\r\n\t\t\tpadding-top: 20rpx;\r\n\t\t\ttext-align: right;\r\n\t\t\t.decimal {\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t\tmargin-top: 4rpx;\r\n\t\t\t}\r\n\t\t\t.number {\r\n\t\t\t\tcolor: $u-tips-color;\r\n\t\t\t\tfont-size: 24rpx;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\t.total {\r\n\t\tmargin-top: 20rpx;\r\n\t\ttext-align: right;\r\n\t\tfont-size: 24rpx;\r\n\t\t.total-price {\r\n\t\t\tfont-size: 32rpx;\r\n\t\t}\r\n\t}\r\n\t.bottom {\r\n\t\tdisplay: flex;\r\n\t\tmargin-top: 40rpx;\r\n\t\tpadding: 0 10rpx;\r\n\t\tjustify-content: space-between;\r\n\t\talign-items: center;\r\n\t\t.btn {\r\n\t\t\tline-height: 52rpx;\r\n\t\t\twidth: 160rpx;\r\n\t\t\tborder-radius: 26rpx;\r\n\t\t\tborder: 2rpx solid $u-border-color;\r\n\t\t\tfont-size: 26rpx;\r\n\t\t\ttext-align: center;\r\n\t\t\tcolor: $u-info-dark;\r\n\t\t}\r\n\t\t.evaluate {\r\n\t\t\tcolor: $u-warning-dark;\r\n\t\t\tborder-color: $u-warning-dark;\r\n\t\t}\r\n\t}\r\n}\r\n.remark {\r\n\tpadding: 32rpx;\r\n}\r\n.submit {\r\n\tposition: fixed;\r\n\twidth: 100vw;\r\n\tleft: 0;\r\n\tbottom: 0;\r\n\tz-index: 9;\r\n}\r\n.logisticsTraces {\r\n\tpadding: 32rpx;\r\n}\r\n.u-node {\r\n\t\twidth: 44rpx;\r\n\t\theight: 44rpx;\r\n\t\tborder-radius: 100rpx;\r\n\t\tdisplay: flex;\r\n\t\tjustify-content: center;\r\n\t\talign-items: center;\r\n\t\tbackground: #d0d0d0;\r\n\t}\r\n\t\r\n\t.u-order-title {\r\n\t\tcolor: #333333;\r\n\t\tfont-weight: bold;\r\n\t\tfont-size: 32rpx;\r\n\t}\r\n\t\r\n\t.u-order-desc {\r\n\t\tcolor: rgb(150, 150, 150);\r\n\t\tfont-size: 28rpx;\r\n\t\tmargin-bottom: 6rpx;\r\n\t}\r\n\t\r\n\t.u-order-time {\r\n\t\tcolor: rgb(200, 200, 200);\r\n\t\tfont-size: 26rpx;\r\n\t}\r\n\t.refundApplyInfo-pics {\r\n\t\tpadding: 16rpx;\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&id=57d42baa&scoped=true&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./detail.vue?vue&type=style&index=0&id=57d42baa&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692293932\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}