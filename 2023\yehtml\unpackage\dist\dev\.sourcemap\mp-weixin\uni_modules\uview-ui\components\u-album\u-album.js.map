{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-album/u-album.vue?7911", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-album/u-album.vue?b62a", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-album/u-album.vue?8d72", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-album/u-album.vue?8c02", "uni-app:///uni_modules/uview-ui/components/u-album/u-album.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-album/u-album.vue?4cfd", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-album/u-album.vue?8cfa"], "names": ["name", "mixins", "data", "singleWidth", "singleHeight", "singlePercent", "watch", "urls", "immediate", "handler", "computed", "imageStyle", "rowCount", "multipleSize", "uni", "addUnit", "addStyle", "rowLen", "allLen", "marginRight", "marginBottom", "index2", "index1", "style", "showUrls", "arr", "imageWidth", "imageHeight", "albumWidth", "width", "methods", "onPreviewTap", "current", "getSrc", "item", "getImageRect", "src", "success", "res", "fail", "getComponentWidth"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,KAAK;AACL;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC7EA;AAAA;AAAA;AAAA;AAAkqB,CAAgB,krBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACuDtrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAnBA,eAoBA;EACAA;EACAC;EACAC;IACA;MACA;MACAC;MACA;MACAC;MACA;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QACA;UACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;UAAAC;UAAAC;UAAAN;UAAA,UACAO;UAAAC;UAAAC;UACAC;UACAC;QACA;UACAC;UACAC;QACA;QACA;QACA;QACA;QACA,IACAC,uBACAC,qBACAD,8CAEAE;QACA;MACA;IACA;IACA;IACAC;MAAA;MACA;MACA;QACA;QACA;UACA;UACA;UACA;UACA;YACAC;UACA;UACAA;QACA;MACA;MACA;IACA;IACAC;MACA,sBACA,8DACA;IACA;IACAC;MACA,sBACA,+DACA;IACA;IACA;IACA;IACAC;MACA;MACA;QACAC;MACA;QACAA,QACA,8CACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;MACA;MACAjB;QACAkB;QACAzB;MACA;IACA;IACA;IACA0B;MACA,kCACA,iDACAC;IACA;IACA;IACA;IACA;IACAC;MAAA;MACA;MACArB;QACAsB;QACAC;UACA;UACA;UACA,mCACA,oBACAC;UACA,qCACA,oBACAA;QACA;QACAC;UACA;QACA;MACA;IACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA1B;cAAA;gBAEA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAWA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpOA;AAAA;AAAA;AAAA;AAAyxC,CAAgB,6uCAAG,EAAC,C;;;;;;;;;;;ACA7yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-album/u-album.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-album.vue?vue&type=template&id=02546599&scoped=true&\"\nvar renderjs\nimport script from \"./u-album.vue?vue&type=script&lang=js&\"\nexport * from \"./u-album.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-album.vue?vue&type=style&index=0&id=02546599&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"02546599\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-album/u-album.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-album.vue?vue&type=template&id=02546599&scoped=true&\"", "var components\ntry {\n  components = {\n    \"u-Text\": function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u--text/u--text\" */ \"@/uni_modules/uview-ui/components/u--text/u--text.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.__map(_vm.showUrls, function (arr, index) {\n    var $orig = _vm.__get_orig(arr)\n    var g0 = _vm.urls.length\n    var l0 = _vm.__map(arr, function (item, index1) {\n      var $orig = _vm.__get_orig(item)\n      var s0 = _vm.__get_style([_vm.imageStyle(index + 1, index1 + 1)])\n      var m0 = _vm.getSrc(item)\n      var g1 =\n        _vm.showMore &&\n        _vm.urls.length > _vm.rowCount * _vm.showUrls.length &&\n        index === _vm.showUrls.length - 1 &&\n        index1 === _vm.showUrls[_vm.showUrls.length - 1].length - 1\n      var g2 = g1 ? _vm.urls.length : null\n      return {\n        $orig: $orig,\n        s0: s0,\n        m0: m0,\n        g1: g1,\n        g2: g2,\n      }\n    })\n    return {\n      $orig: $orig,\n      g0: g0,\n      l0: l0,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item = _temp2.item\n      var _temp, _temp2\n      _vm.previewFullImage ? _vm.onPreviewTap(_vm.getSrc(item)) : \"\"\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-album.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-album.vue?vue&type=script&lang=js&\"", "<template>\r\n    <view class=\"u-album\">\r\n        <view\r\n            class=\"u-album__row\"\r\n            ref=\"u-album__row\"\r\n            v-for=\"(arr, index) in showUrls\"\r\n            :forComputedUse=\"albumWidth\"\r\n            :key=\"index\"\r\n        >\r\n            <view\r\n                class=\"u-album__row__wrapper\"\r\n                v-for=\"(item, index1) in arr\"\r\n                :key=\"index1\"\r\n                :style=\"[imageStyle(index + 1, index1 + 1)]\"\r\n                @tap=\"previewFullImage ? onPreviewTap(getSrc(item)) : ''\"\r\n            >\r\n                <image\r\n                    :src=\"getSrc(item)\"\r\n                    :mode=\"\r\n                        urls.length === 1\r\n                            ? imageHeight > 0\r\n                                ? singleMode\r\n                                : 'widthFix'\r\n                            : multipleMode\r\n                    \"\r\n                    :style=\"[\r\n                        {\r\n                            width: imageWidth,\r\n                            height: imageHeight\r\n                        }\r\n                    ]\"\r\n                ></image>\r\n                <view\r\n                    v-if=\"\r\n                        showMore &&\r\n                        urls.length > rowCount * showUrls.length &&\r\n                        index === showUrls.length - 1 &&\r\n                        index1 === showUrls[showUrls.length - 1].length - 1\r\n                    \"\r\n                    class=\"u-album__row__wrapper__text\"\r\n                >\r\n                    <u--text\r\n                        :text=\"`+${urls.length - maxCount}`\"\r\n                        color=\"#fff\"\r\n                        :size=\"multipleSize * 0.3\"\r\n                        align=\"center\"\r\n                        customStyle=\"justify-content: center\"\r\n                    ></u--text>\r\n                </view>\r\n            </view>\r\n        </view>\r\n    </view>\r\n</template>\r\n\r\n<script>\r\nimport props from './props.js'\r\n// #ifdef APP-NVUE\r\n// 由于weex为阿里的KPI业绩考核的产物，所以不支持百分比单位，这里需要通过dom查询组件的宽度\r\nconst dom = uni.requireNativePlugin('dom')\r\n// #endif\r\n\r\n/**\r\n * Album 相册\r\n * @description 本组件提供一个类似相册的功能，让开发者开发起来更加得心应手。减少重复的模板代码\r\n * @tutorial https://www.uviewui.com/components/album.html\r\n *\r\n * @property {Array}           urls             图片地址列表 Array<String>|Array<Object>形式\r\n * @property {String}          keyName          指定从数组的对象元素中读取哪个属性作为图片地址\r\n * @property {String | Number} singleSize       单图时，图片长边的长度  （默认 180 ）\r\n * @property {String | Number} multipleSize     多图时，图片边长 （默认 70 ）\r\n * @property {String | Number} space            多图时，图片水平和垂直之间的间隔 （默认 6 ）\r\n * @property {String}          singleMode       单图时，图片缩放裁剪的模式 （默认 'scaleToFill' ）\r\n * @property {String}          multipleMode     多图时，图片缩放裁剪的模式 （默认 'aspectFill' ）\r\n * @property {String | Number} maxCount         取消按钮的提示文字 （默认 9 ）\r\n * @property {Boolean}         previewFullImage 是否可以预览图片 （默认 true ）\r\n * @property {String | Number} rowCount         每行展示图片数量，如设置，singleSize和multipleSize将会无效\t（默认 3 ）\r\n * @property {Boolean}         showMore         超出maxCount时是否显示查看更多的提示 （默认 true ）\r\n *\r\n * @event    {Function}        albumWidth       某些特殊的情况下，需要让文字与相册的宽度相等，这里事件的形式对外发送  （回调参数 width ）\r\n * @example <u-album :urls=\"urls2\" @albumWidth=\"width => albumWidth = width\" multipleSize=\"68\" ></u-album>\r\n */\r\nexport default {\r\n    name: 'u-album',\r\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n    data() {\r\n        return {\r\n            // 单图的宽度\r\n            singleWidth: 0,\r\n            // 单图的高度\r\n            singleHeight: 0,\r\n            // 单图时，如果无法获取图片的尺寸信息，让图片宽度默认为容器的一定百分比\r\n            singlePercent: 0.6\r\n        }\r\n    },\r\n    watch: {\r\n        urls: {\r\n            immediate: true,\r\n            handler(newVal) {\r\n                if (newVal.length === 1) {\r\n                    this.getImageRect()\r\n                }\r\n            }\r\n        }\r\n    },\r\n    computed: {\r\n        imageStyle() {\r\n            return (index1, index2) => {\r\n                const { space, rowCount, multipleSize, urls } = this,\r\n                    { addUnit, addStyle } = uni.$u,\r\n                    rowLen = this.showUrls.length,\r\n                    allLen = this.urls.length\r\n                const style = {\r\n                    marginRight: addUnit(space),\r\n                    marginBottom: addUnit(space)\r\n                }\r\n                // 如果为最后一行，则每个图片都无需下边框\r\n                if (index1 === rowLen) style.marginBottom = 0\r\n                // 每行的最右边一张和总长度的最后一张无需右边框\r\n                if (\r\n                    index2 === rowCount ||\r\n                    (index1 === rowLen &&\r\n                        index2 === this.showUrls[index1 - 1].length)\r\n                )\r\n                    style.marginRight = 0\r\n                return style\r\n            }\r\n        },\r\n        // 将数组划分为二维数组\r\n        showUrls() {\r\n            const arr = []\r\n            this.urls.map((item, index) => {\r\n                // 限制最大展示数量\r\n                if (index + 1 <= this.maxCount) {\r\n                    // 计算该元素为第几个素组内\r\n                    const itemIndex = Math.floor(index / this.rowCount)\r\n                    // 判断对应的索引是否存在\r\n                    if (!arr[itemIndex]) {\r\n                        arr[itemIndex] = []\r\n                    }\r\n                    arr[itemIndex].push(item)\r\n                }\r\n            })\r\n            return arr\r\n        },\r\n        imageWidth() {\r\n            return uni.$u.addUnit(\r\n                this.urls.length === 1 ? this.singleWidth : this.multipleSize\r\n            )\r\n        },\r\n        imageHeight() {\r\n            return uni.$u.addUnit(\r\n                this.urls.length === 1 ? this.singleHeight : this.multipleSize\r\n            )\r\n        },\r\n        // 此变量无实际用途，仅仅是为了利用computed特性，让其在urls长度等变化时，重新计算图片的宽度\r\n        // 因为用户在某些特殊的情况下，需要让文字与相册的宽度相等，所以这里事件的形式对外发送\r\n        albumWidth() {\r\n            let width = 0\r\n            if (this.urls.length === 1) {\r\n                width = this.singleWidth\r\n            } else {\r\n                width =\r\n                    this.showUrls[0].length * this.multipleSize +\r\n                    this.space * (this.showUrls[0].length - 1)\r\n            }\r\n            this.$emit('albumWidth', width)\r\n            return width\r\n        }\r\n    },\r\n    methods: {\r\n        // 预览图片\r\n        onPreviewTap(url) {\r\n            const urls = this.urls.map((item) => {\r\n                return this.getSrc(item)\r\n            })\r\n            uni.previewImage({\r\n                current: url,\r\n                urls\r\n            })\r\n        },\r\n        // 获取图片的路径\r\n        getSrc(item) {\r\n            return uni.$u.test.object(item)\r\n                ? (this.keyName && item[this.keyName]) || item.src\r\n                : item\r\n        },\r\n        // 单图时，获取图片的尺寸\r\n        // 在小程序中，需要将网络图片的的域名添加到小程序的download域名才可能获取尺寸\r\n        // 在没有添加的情况下，让单图宽度默认为盒子的一定宽度(singlePercent)\r\n        getImageRect() {\r\n            const src = this.getSrc(this.urls[0])\r\n            uni.getImageInfo({\r\n                src,\r\n                success: (res) => {\r\n                    // 判断图片横向还是竖向展示方式\r\n                    const isHorizotal = res.width >= res.height\r\n                    this.singleWidth = isHorizotal\r\n                        ? this.singleSize\r\n                        : (res.width / res.height) * this.singleSize\r\n                    this.singleHeight = !isHorizotal\r\n                        ? this.singleSize\r\n                        : (res.height / res.width) * this.singleWidth\r\n                },\r\n                fail: () => {\r\n                    this.getComponentWidth()\r\n                }\r\n            })\r\n        },\r\n        // 获取组件的宽度\r\n        async getComponentWidth() {\r\n            // 延时一定时间，以获取dom尺寸\r\n            await uni.$u.sleep(30)\r\n            // #ifndef APP-NVUE\r\n            this.$uGetRect('.u-album__row').then((size) => {\r\n                this.singleWidth = size.width * this.singlePercent\r\n            })\r\n            // #endif\r\n\r\n            // #ifdef APP-NVUE\r\n            // 这里ref=\"u-album__row\"所在的标签为通过for循环出来，导致this.$refs['u-album__row']是一个数组\r\n            const ref = this.$refs['u-album__row'][0]\r\n            ref &&\r\n                dom.getComponentRect(ref, (res) => {\r\n                    this.singleWidth = res.size.width * this.singlePercent\r\n                })\r\n            // #endif\r\n        }\r\n    }\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '../../libs/css/components.scss';\r\n\r\n.u-album {\r\n    @include flex(column);\r\n\r\n    &__row {\r\n        @include flex(row);\r\n        flex-wrap: wrap;\r\n\r\n        &__wrapper {\r\n            position: relative;\r\n\r\n            &__text {\r\n                position: absolute;\r\n                top: 0;\r\n                left: 0;\r\n                right: 0;\r\n                bottom: 0;\r\n                background-color: rgba(0, 0, 0, 0.3);\r\n                @include flex(row);\r\n                justify-content: center;\r\n                align-items: center;\r\n            }\r\n        }\r\n    }\r\n}\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-album.vue?vue&type=style&index=0&id=02546599&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-album.vue?vue&type=style&index=0&id=02546599&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692294115\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}