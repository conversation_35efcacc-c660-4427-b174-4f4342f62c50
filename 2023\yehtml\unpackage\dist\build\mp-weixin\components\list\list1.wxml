<view class="list1"><view><custom-waterfalls-flow class="vue-ref" vue-id="4e394389-1" value="{{list}}" column="{{2}}" columnSpace="{{1.5}}" seat="{{2}}" data-ref="waterfallsFlowRef" data-event-opts="{{[['^imageClick',[['imageClick']]]]}}" bind:imageClick="__e" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view slot="slot{{index}}"><view class="badge-box"><block wx:if="{{(item.categoryId===383898||item.categoryId===383899||item.categoryId===383900||item.categoryId===383901||item.categoryId===383902||item.categoryId===383903||item.categoryId===383904||item.categoryId===383905)&&item.promotionInsert!==1}}"><view class="badge"></view></block></view><block wx:if="{{item.deliveryTypeUrl}}"><view class="delivery-type"><image style="width:45px;height:25px;" src="{{item.deliveryTypeUrl}}" class="_img"></image></view></block><block wx:if="{{item.promotionInsert===1}}"><view><list-promotion vue-id="{{('4e394389-2-'+index)+','+('4e394389-1')}}" item="{{item}}" bind:__l="__l"></list-promotion></view></block><block wx:if="{{item.promotionInsert!==1}}"><view><list-goods-item1 vue-id="{{('4e394389-3-'+index)+','+('4e394389-1')}}" item="{{item}}" bind:__l="__l"></list-goods-item1></view></block></view></block></custom-waterfalls-flow></view></view>