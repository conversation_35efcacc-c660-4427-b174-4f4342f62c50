{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-switch/u-switch.vue?bd91", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-switch/u-switch.vue?3b5d", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-switch/u-switch.vue?f677", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-switch/u-switch.vue?b9ec", "uni-app:///uni_modules/uview-ui/components/u-switch/u-switch.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-switch/u-switch.vue?6cca", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-switch/u-switch.vue?acec"], "names": ["name", "mixins", "watch", "value", "immediate", "handler", "uni", "data", "bgColor", "computed", "isActive", "switchStyle", "style", "nodeStyle", "bgStyle", "customInactiveColor", "methods", "clickHandler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kVAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8BvrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAlBA,eAmBA;EACAA;EACAC;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACAC;MACA;MACA;MACAC;MACAA;MACA;MACA;MACA;MACA;QACAA;MACA;MACAA;MACA;IACA;IACAC;MACA;MACA;MACAD;MACAA;MACA;MACAA;MACA;IACA;IACAE;MACA;MACA;MACAF;MACAA;MACAA;MACA;MACAA;MACA;IACA;IACAG;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;QACA;QACA;UACA;QACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5HA;AAAA;AAAA;AAAA;AAA0xC,CAAgB,8uCAAG,EAAC,C;;;;;;;;;;;ACA9yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-switch/u-switch.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-switch.vue?vue&type=template&id=3a8aa7a9&scoped=true&\"\nvar renderjs\nimport script from \"./u-switch.vue?vue&type=script&lang=js&\"\nexport * from \"./u-switch.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-switch.vue?vue&type=style&index=0&id=3a8aa7a9&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3a8aa7a9\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-switch/u-switch.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-switch.vue?vue&type=template&id=3a8aa7a9&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-loading-icon/u-loading-icon\" */ \"@/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.switchStyle, _vm.$u.addStyle(_vm.customStyle)])\n  var s1 = _vm.__get_style([_vm.bgStyle])\n  var s2 = _vm.__get_style([_vm.nodeStyle])\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        s2: s2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-switch.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-switch.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t    class=\"u-switch\"\r\n\t    :class=\"[disabled && 'u-switch--disabled']\"\r\n\t    :style=\"[switchStyle, $u.addStyle(customStyle)]\"\r\n\t    @tap=\"clickHandler\"\r\n\t>\r\n\t\t<view\r\n\t\t    class=\"u-switch__bg\"\r\n\t\t    :style=\"[bgStyle]\"\r\n\t\t>\r\n\t\t</view>\r\n\t\t<view\r\n\t\t    class=\"u-switch__node\"\r\n\t\t    :class=\"[value && 'u-switch__node--on']\"\r\n\t\t    :style=\"[nodeStyle]\"\r\n\t\t    ref=\"u-switch__node\"\r\n\t\t>\r\n\t\t\t<u-loading-icon\r\n\t\t\t    :show=\"loading\"\r\n\t\t\t    mode=\"circle\"\r\n\t\t\t    timingFunction='linear'\r\n\t\t\t    :color=\"value ? activeColor : '#AAABAD'\"\r\n\t\t\t    :size=\"size * 0.6\"\r\n\t\t\t/>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t/**\r\n\t * switch 开关选择器\r\n\t * @description 选择开关一般用于只有两个选择，且只能选其一的场景。\r\n\t * @tutorial https://www.uviewui.com/components/switch.html\r\n\t * @property {Boolean}\t\t\t\t\t\tloading\t\t\t是否处于加载中（默认 false ）\r\n\t * @property {Boolean}\t\t\t\t\t\tdisabled\t\t是否禁用（默认 false ）\r\n\t * @property {String | Number}\t\t\t\tsize\t\t\t开关尺寸，单位px （默认 25 ）\r\n\t * @property {String}\t\t\t\t\t\tactiveColor\t\t打开时的背景色 （默认 '#2979ff' ）\r\n\t * @property {String} \t\t\t\t\t\tinactiveColor\t关闭时的背景色 （默认 '#ffffff' ）\r\n\t * @property {Boolean | String | Number}\tvalue\t\t\t通过v-model双向绑定的值 （默认 false ）\r\n\t * @property {Boolean | String | Number}\tactiveValue\t\t打开选择器时通过change事件发出的值 （默认 true ）\r\n\t * @property {Boolean | String | Number}\tinactiveValue\t关闭选择器时通过change事件发出的值 （默认 false ）\r\n\t * @property {Boolean}\t\t\t\t\t\tasyncChange\t\t是否开启异步变更，开启后需要手动控制输入值 （默认 false ）\r\n\t * @property {String | Number}\t\t\t\tspace\t\t\t圆点与外边框的距离 （默认 0 ）\r\n\t * @property {Object}\t\t\t\t\t\tcustomStyle\t\t定义需要用到的外部样式\r\n\t *\r\n\t * @event {Function} change 在switch打开或关闭时触发\r\n\t * @example <u-switch v-model=\"checked\" active-color=\"red\" inactive-color=\"#eee\"></u-switch>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-switch\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\twatch: {\r\n\t\t\tvalue: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(n) {\r\n\t\t\t\t\tif(n !== this.inactiveValue && n !== this.activeValue) {\r\n\t\t\t\t\t\tuni.$u.error('v-model绑定的值必须为inactiveValue、activeValue二者之一')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tbgColor: '#ffffff'\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\tisActive(){\r\n\t\t\t\treturn this.value === this.activeValue;\r\n\t\t\t},\r\n\t\t\tswitchStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\t// 这里需要加2，是为了腾出边框的距离，否则圆点node会和外边框紧贴在一起\r\n\t\t\t\tstyle.width = uni.$u.addUnit(this.size * 2 + 2)\r\n\t\t\t\tstyle.height = uni.$u.addUnit(Number(this.size) + 2)\r\n\t\t\t\t// style.borderColor = this.value ? 'rgba(0, 0, 0, 0)' : 'rgba(0, 0, 0, 0.12)'\r\n\t\t\t\t// 如果自定义了“非激活”演示，name边框颜色设置为透明(跟非激活颜色一致)\r\n\t\t\t\t// 这里不能简单的设置为非激活的颜色，否则打开状态时，会有边框，所以需要透明\r\n\t\t\t\tif(this.customInactiveColor) {\r\n\t\t\t\t\tstyle.borderColor = 'rgba(0, 0, 0, 0)'\r\n\t\t\t\t}\r\n\t\t\t\tstyle.backgroundColor = this.isActive ? this.activeColor : this.inactiveColor\r\n\t\t\t\treturn style;\r\n\t\t\t},\r\n\t\t\tnodeStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\t// 如果自定义非激活颜色，将node圆点的尺寸减少两个像素，让其与外边框距离更大一点\r\n\t\t\t\tstyle.width = uni.$u.addUnit(this.size - this.space)\r\n\t\t\t\tstyle.height = uni.$u.addUnit(this.size - this.space)\r\n\t\t\t\tconst translateX = this.isActive ? uni.$u.addUnit(this.space) : uni.$u.addUnit(this.size);\r\n\t\t\t\tstyle.transform = `translateX(-${translateX})`\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\tbgStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\t// 这里配置一个多余的元素在HTML中，是为了让switch切换时，有更良好的背景色扩充体验(见实际效果)\r\n\t\t\t\tstyle.width = uni.$u.addUnit(Number(this.size) * 2 - this.size / 2)\r\n\t\t\t\tstyle.height = uni.$u.addUnit(this.size)\r\n\t\t\t\tstyle.backgroundColor = this.inactiveColor\r\n\t\t\t\t// 打开时，让此元素收缩，否则反之\r\n\t\t\t\tstyle.transform = `scale(${this.isActive ? 0 : 1})`\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\tcustomInactiveColor() {\r\n\t\t\t\t// 之所以需要判断是否自定义了“非激活”颜色，是为了让node圆点离外边框更宽一点的距离\r\n\t\t\t\treturn this.inactiveColor !== '#fff' && this.inactiveColor !== '#ffffff'\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tclickHandler() {\r\n\t\t\t\tif (!this.disabled && !this.loading) {\r\n\t\t\t\t\tconst oldValue = this.isActive ? this.inactiveValue : this.activeValue\r\n\t\t\t\t\tif(!this.asyncChange) {\r\n\t\t\t\t\t\tthis.$emit('input', oldValue)\r\n\t\t\t\t\t}\r\n\t\t\t\t\t// 放到下一个生命周期，因为双向绑定的value修改父组件状态需要时间，且是异步的\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.$emit('change', oldValue)\r\n\t\t\t\t\t})\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-switch {\r\n\t\t@include flex(row);\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbox-sizing: border-box;\r\n\t\t/* #endif */\r\n\t\tposition: relative;\r\n\t\tbackground-color: #fff;\r\n\t\tborder-width: 1px;\r\n\t\tborder-radius: 100px;\r\n\t\ttransition: background-color 0.4s;\r\n\t\tborder-color: rgba(0, 0, 0, 0.12);\r\n\t\tborder-style: solid;\r\n\t\tjustify-content: flex-end;\r\n\t\talign-items: center;\r\n\t\t// 由于weex为阿里逗着玩的KPI项目，导致bug奇多，这必须要写这一行，\r\n\t\t// 否则在iOS上，点击页面任意地方，都会触发switch的点击事件\r\n\t\toverflow: hidden;\r\n\r\n\t\t&__node {\r\n\t\t\t@include flex(row);\r\n\t\t\talign-items: center;\r\n\t\t\tjustify-content: center;\r\n\t\t\tborder-radius: 100px;\r\n\t\t\tbackground-color: #fff;\r\n\t\t\tborder-radius: 100px;\r\n\t\t\tbox-shadow: 1px 1px 1px 0 rgba(0, 0, 0, 0.25);\r\n\t\t\ttransition-property: transform;\r\n\t\t\ttransition-duration: 0.4s;\r\n\t\t\ttransition-timing-function: cubic-bezier(0.3, 1.05, 0.4, 1.05);\r\n\t\t}\r\n\r\n\t\t&__bg {\r\n\t\t\tposition: absolute;\r\n\t\t\tborder-radius: 100px;\r\n\t\t\tbackground-color: #FFFFFF;\r\n\t\t\ttransition-property: transform;\r\n\t\t\ttransition-duration: 0.4s;\r\n\t\t\tborder-top-left-radius: 0;\r\n\t\t\tborder-bottom-left-radius: 0;\r\n\t\t\ttransition-timing-function: ease;\r\n\t\t}\r\n\r\n\t\t&--disabled {\r\n\t\t\topacity: 0.6;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-switch.vue?vue&type=style&index=0&id=3a8aa7a9&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-switch.vue?vue&type=style&index=0&id=3a8aa7a9&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688734850\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}