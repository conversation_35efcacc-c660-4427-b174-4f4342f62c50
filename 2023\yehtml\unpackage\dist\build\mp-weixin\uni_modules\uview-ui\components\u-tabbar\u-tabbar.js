(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uview-ui/components/u-tabbar/u-tabbar"],{"066a":function(t,e,n){"use strict";n.r(e);var u=n("7cc1"),a=n.n(u);for(var i in u)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(i);e["default"]=a.a},"7cc1":function(t,e,n){"use strict";(function(t){var u=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=u(n("7eb4")),i=u(n("ee10")),r=u(n("7698")),c={name:"u-tabbar",mixins:[t.$u.mpMixin,t.$u.mixin,r.default],data:function(){return{placeholderHeight:0}},computed:{tabbarStyle:function(){var e={zIndex:this.zIndex};return t.$u.deepMerge(e,t.$u.addStyle(this.customStyle))},updateChild:function(){return[this.value,this.activeColor,this.inactiveColor]},updatePlaceholder:function(){return[this.fixed,this.placeholder]}},watch:{updateChild:function(){this.updateChildren()},updatePlaceholder:function(){this.setPlaceholderHeight()}},created:function(){this.children=[]},mounted:function(){this.setPlaceholderHeight()},methods:{updateChildren:function(){this.children.length&&this.children.map((function(t){return t.updateFromParent()}))},setPlaceholderHeight:function(){var e=this;return(0,i.default)(a.default.mark((function n(){return a.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:if(e.fixed&&e.placeholder){n.next=2;break}return n.abrupt("return");case 2:return n.next=4,t.$u.sleep(20);case 4:e.$uGetRect(".u-tabbar__content").then((function(t){var n=t.height,u=void 0===n?50:n;e.placeholderHeight=u}));case 5:case"end":return n.stop()}}),n)})))()}}};e.default=c}).call(this,n("df3c")["default"])},9005:function(t,e,n){"use strict";var u=n("ba1c"),a=n.n(u);a.a},"974a":function(t,e,n){"use strict";n.d(e,"b",(function(){return a})),n.d(e,"c",(function(){return i})),n.d(e,"a",(function(){return u}));var u={uSafeBottom:function(){return Promise.all([n.e("common/vendor"),n.e("uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom")]).then(n.bind(null,"0fcf"))}},a=function(){var t=this.$createElement,e=(this._self._c,this.__get_style([this.tabbarStyle]));this.$mp.data=Object.assign({},{$root:{s0:e}})},i=[]},ba1c:function(t,e,n){},e585:function(t,e,n){"use strict";n.r(e);var u=n("974a"),a=n("066a");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("9005");var r=n("828b"),c=Object(r["a"])(a["default"],u["b"],u["c"],!1,null,"2a5dc234",null,!1,u["a"],void 0);e["default"]=c.exports}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uview-ui/components/u-tabbar/u-tabbar-create-component',
    {
        'uni_modules/uview-ui/components/u-tabbar/u-tabbar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("e585"))
        })
    },
    [['uni_modules/uview-ui/components/u-tabbar/u-tabbar-create-component']]
]);
