<view class="goods-box-wrapper"><view class="goods-box"><view data-event-opts="{{[['tap',[['goDetail',['$0'],['item']]]]]}}" bindtap="__e"><view class="goods-title-box"><view class="goods-title">{{item.name}}</view></view><view class="goods-tags-box"><view class="goods-tags"><block wx:for="{{$root.l0}}" wx:for-item="tag" wx:for-index="index" wx:key="index"><label style="{{'color:'+(tag.m0?'#e64340':'')+';'+('border:'+(tag.m1?'1px solid #e64340':'')+';')+('display:'+('inline-block')+';')+('margin-right:'+('5px')+';')}}" class="_span">{{''+tag.$orig+''}}</label></block></view></view><view class="goods-limited-time"><view class="count-down"><view style="display:inline-block;">截止剩余：</view><view style="display:inline-block;"><u-count-down vue-id="b7e5596c-1" time="{{item.ext['deadline']*1000}}" format="DD:HH:mm:ss" data-event-opts="{{[['^change',[['onChangeTimeData']]]]}}" bind:change="__e" bind:__l="__l" vue-slots="{{['default']}}"><view class="time"><view class="time__custom"><text class="time__custom__item">{{timeData.days}}</text></view><text class="time__doc">天</text><view class="time__custom"><text class="time__custom__item">{{timeData.hours>=10?timeData.hours:'0'+timeData.hours}}</text></view><text class="time__doc">时</text><view class="time__custom"><text class="time__custom__item">{{timeData.minutes>=10?timeData.minutes:'0'+timeData.minutes}}</text></view><text class="time__doc">分</text></view></u-count-down></view></view></view></view><view class="buy-wrapper" style="display:flex;"><view class="price-score"><block wx:if="{{item.minPrice}}"><view class="item"><view class="original-price _div"><label class="_span">市场价：</label><view class="_br"></view><label class="price _span">{{"¥"+item.minPrice}}</label></view><text>¥</text>{{item.ext['shichiPrice']+''}}</view></block><block wx:if="{{item.minScore}}"><view class="item"><text><image class="score-icon" src="/static/images/score.png"></image></text>{{item.minScore}}</view></block></view><view data-event-opts="{{[['tap',[['goDetail',['$0'],['item']]]]]}}" class="buy" bindtap="__e"><u-icon vue-id="b7e5596c-2" name="shopping-cart" color="#FFFFFF" size="28" bind:__l="__l"></u-icon></view></view></view></view>