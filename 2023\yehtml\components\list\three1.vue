<template>
  <view class="three1">
    <view class="delivery-time">
      <span class="text">到货时间</span>
      <span class="date">{{ list[0].ext.delivery_time }}</span>
    </view>
    <view>
      <u-row v-if="list[0].name" customStyle="padding: 12px;margin-bottom: 10px;" gutter="12">
        <u-col span="6">
          <view class="left" @click="goDetail(list[0])">
            <view v-if="deliveryTimeTip !== ''" class="status-badge">{{ deliveryTimeTip }}</view>
            <view class="left-top" :style="{ backgroundImage: 'url(' + list[0].pic + ')' }"></view>
            <view class="left-bottom">
              <view class="title">{{ list[0].name }}</view>
              <view class="dsc">{{ handleTags(list[0].tags) }}</view>
              <view class="price">
                <view class="price-left">
                  <view class="price-tip">特约预购价</view>
                  <view class="original-price"><span>原价￥{{ list[0].originalPrice }}</span> / {{ list[0].unit }}</view>
                </view>
                <view class="price-right">
                  <span class="prefix">￥</span>
                  <span>{{ list[0].minPrice }}</span>
                </view>
              </view>
              <view class="count-down">
                <view style="display: inline-block;">截止剩余：</view>
                <view style="display: inline-block;">
                  <u-count-down :time="list[0].ext['deadlineDifference'] * 1000" format="DD:HH:mm:ss" @change="onChangeTimeData">
                    <view class="time">
                      <view class="time__custom">
                        <text class="time__custom__item">{{ timeData.days }}</text>
                      </view>
                      <text class="time__doc">天</text>
                      <view class="time__custom">
                        <text class="time__custom__item">{{ timeData.hours > 10 ? timeData.hours : '0' + timeData.hours }}</text>
                      </view>
                      <text class="time__doc">时</text>
                      <view class="time__custom">
                        <text class="time__custom__item">{{ timeData.minutes > 10 ? timeData.minutes : '0' + timeData.minutes }}</text>
                      </view>
                      <text class="time__doc">分</text>
                    </view>
                  </u-count-down>
                </view>
              </view>
            </view>
          </view>
        </u-col>
        <u-col span="6">
          <view class="right" @click="goDetail(list[1])">
            <view class="right-top" :style="{ backgroundImage: 'url(' + list[1].pic + ')' }"></view>
            <view class="right-bottom">
              <view class="title">{{ list[1].name }}</view>
              <view class="price">
                <view class="price-left">
                  <view class="price-tip">立即预购</view>
                  <view class="original-price"><span>原价￥{{ list[1].originalPrice }}</span> / {{ list[1].unit }}</view>
                </view>
                <view class="price-right">
                  <span class="prefix">￥</span>
                  <span>{{ list[1].minPrice }}</span>
                </view>
              </view>
            </view>
          </view>
          <view class="right" style="margin-top: 20rpx;" @click="goDetail(list[2])">
            <view class="right-top" :style="{ backgroundImage: 'url(' + list[2].pic + ')' }"></view>
            <view class="right-bottom">
              <view class="title">{{ list[2].name }}</view>
              <view class="price">
                <view class="price-left">
                  <view class="price-tip">立即预购</view>
                  <view class="original-price"><span>原价￥{{ list[2].originalPrice }}</span> / {{ list[2].unit }}</view>
                </view>
                <view class="price-right">
                  <span class="prefix">￥</span>
                  <span>{{ list[2].minPrice }}</span>
                </view>
              </view>
            </view>
          </view>
        </u-col>
      </u-row>
      <view v-if="!list[0].name" style="padding: 12px;margin-bottom: 10px;">
        <u-skeleton rows="3"></u-skeleton>
      </view>
    </view>
  </view>
</template>

<script>
  import empty from 'empty-value'
  import moment from 'moment'

  export default {
    components: {},
    props: {
      list: {
        type: Array,
        default: [],
      },
      type: {
        type: String,
        default: '',
      }
    },
    created() {
      console.log('this list', this.list)

      let currentTimestamp = moment().unix()
      let deadlineTimestamp = moment(this.list[0].ext['deadline'], 'YYYY-MM-DD').unix()

      if (currentTimestamp > deadlineTimestamp) {
        this.deliveryTimeTip = '预购已经结束'
      } else {
        this.deliveryTimeTip = '正在预购中'
      }
    },
    data() {
      return {
        timeData: {},
        deliveryTimeTip: '',
      }
    },
    watch: {
      list: function(val) {
        console.log('watch list is', val)
      }
    },
    methods: {
      goDetail(item) {
        let that = this
        uni.navigateTo({
          url: '/pages/goods/detail?id=' + item.id
        })
      },
      onChangeTimeData(e) {
        this.timeData = e
      },
      handleTags(tag) {
        var result = tag.replace(/[,，]/g, ' ')
        return result
      }
    },
  }
</script>
<style>
  .three1 {
    position: relative;
  }

  .left {
    position: relative;
    border-radius: 8rpx;
    overflow: hidden;
    text-align: center;
  }

  .left .status-badge {
    position: absolute;
    top: 0;
    left: 0;
    padding: 10rpx;
    color: #fff;
    background: radial-gradient(circle, #8ab750, #568c0c);
    border-radius: 0 0 20rpx 0;
    font-size: 12px;
  }

  .left-top {
    height: 480rpx;
    background-size: cover;
    background-position: center;
  }

  .left-bottom {
    line-height: 50rpx;
    background: #87C232;
    background: radial-gradient(circle, #eead4d, #f2933a);
    color: #FFF;
    padding: 20rpx 10rpx;
  }

  .left-bottom .title {
    font-size: 46rpx;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .left-bottom .dsc {
    font-size: 26rpx;
    color: #dbe7cf;
  }

  .left-bottom .price-tip {
    font-size: 22rpx;
    color: #345409;
    background: #f5f9c3;
    border-radius: 20rpx;
    padding: 0 8rpx;
    display: inline-block;
    line-height: 36rpx;
  }

  .left-bottom .price {
    font-weight: bolder;
  }

  .left-bottom .price .price-left {
    display: inline-block;
    font-weight: normal;
    text-align: right;
  }

  .left-bottom .price .price-right {
    font-size: 80rpx;
    display: inline-block;
    letter-spacing: -2px;
  }

  .left-bottom .price .prefix,
  .left-bottom .price .suffix {
    font-size: 40rpx;
  }

  .left-bottom .price .original-price {
    font-size: 22rpx;
    text-decoration: line-through;
    line-height: 36rpx;
  }

  .right {
    color: #FFFFFF;
    border-radius: 8rpx;
    overflow: hidden;
    text-align: center;
  }

  .right-top {
    height: 200rpx;
    background-size: cover;
    background-position: top;
  }

  .right-bottom {
    line-height: 50rpx;
    background: #87C232;
    background: radial-gradient(circle, #eead4d, #f2933a);
    color: #FFF;
    padding: 20rpx 10rpx;
  }

  .right-bottom .title {
    font-size: 46rpx;
    font-weight: bold;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .right-bottom .price-tip {
    font-size: 20rpx;
    color: #345409;
    background: #f5f9c3;
    border-radius: 20rpx;
    padding: 0 8rpx;
    display: inline-block;
    line-height: 32rpx;
  }

  .right-bottom .price {
    font-weight: bolder;
  }

  .right-bottom .price .price-left {
    display: inline-block;
    font-weight: normal;
    text-align: right;
  }

  .right-bottom .price .price-right {
    font-size: 70rpx;
    display: inline-block;
    letter-spacing: -2px;
  }

  .right-bottom .price .prefix,
  .right-bottom .price .suffix {
    font-size: 32rpx;
  }

  .right-bottom .price .original-price {
    font-size: 20rpx;
    text-decoration: line-through;
    line-height: 32rpx;
  }

  .delivery-time {
    position: absolute;
    top: -47rpx;
    left: 250rpx;
    background: #F5F9C3;
    border-radius: 10rpx;
    margin-bottom: 10rpx;
  }

  .delivery-time .text {
    padding: 8rpx 20rpx;
    background: green;
    border-radius: 10rpx;
    font-size: 24rpx;
    color: #fff;
  }

  .delivery-time .date {
    padding: 8rpx 20rpx;
    font-size: 24rpx;
  }
</style>