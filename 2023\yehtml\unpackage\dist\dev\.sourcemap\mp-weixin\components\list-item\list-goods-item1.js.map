{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item1.vue?e354", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item1.vue?054c", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item1.vue?1c69", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item1.vue?4b50", "uni-app:///components/list-item/list-goods-item1.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item1.vue?40da", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item1.vue?6a0f"], "names": ["props", "item", "type", "default", "onReady", "data", "computed", "filteredTags", "methods", "goDetail", "uni", "url", "should<PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA6H;AAC7H;AACoE;AACL;AACa;;;AAG5E;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,sFAAM;AACR,EAAE,2FAAM;AACR,EAAE,oGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,+FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAA2qB,CAAgB,2rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCkC/rB;EACAA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;EACAC;IACA;EACA;EACAC;IACAC;MACA;QACA;QACA;UAAA;QAAA;MACA;MACA;IACA;EACA;EACAC;IACAC;MACAC;QACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChEA;AAAA;AAAA;AAAA;AAAu9B,CAAgB,08BAAG,EAAC,C;;;;;;;;;;;ACA3+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/list-item/list-goods-item1.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./list-goods-item1.vue?vue&type=template&id=33d05a20&\"\nvar renderjs\nimport script from \"./list-goods-item1.vue?vue&type=script&lang=js&\"\nexport * from \"./list-goods-item1.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list-goods-item1.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/list-item/list-goods-item1.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item1.vue?vue&type=template&id=33d05a20&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l0 = _vm.__map(_vm.filteredTags, function (tag, index) {\n    var $orig = _vm.__get_orig(tag)\n    var m0 = _vm.shouldHighlight(tag)\n    var m1 = _vm.shouldHighlight(tag)\n    return {\n      $orig: $orig,\n      m0: m0,\n      m1: m1,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item1.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item1.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"goods-box-wrapper\">\r\n    <view class=\"goods-box\">\r\n      <view @click=\"goDetail(item)\">\r\n        <view class=\"goods-title-box\">\r\n          <view class=\"goods-title\">{{ item.name }}</view>\r\n        </view>\r\n        <view v-if=\"item.characteristic\" class=\"goods-dsc-box\">\r\n          <view class=\"goods-dsc\">{{ item.characteristic }}</view>\r\n        </view>\r\n        <view class=\"goods-tags-box\">\r\n          <view class=\"goods-tags\">\r\n            <span v-for=\"(tag, index) in filteredTags\" :key=\"index\" :style=\"{ color: shouldHighlight(tag) ? '#e64340' : '', border: shouldHighlight(tag) ? '1px solid #e64340' : '', display: 'inline-block', 'margin-right': '5px' }\">\r\n              {{ tag }}\r\n            </span>\r\n          </view>\r\n        </view>\r\n      </view>\r\n      <view class=\"buy-wrapper\" style=\"display: flex;\">\r\n        <view class=\"price-score\">\r\n          <view v-if=\"item.minPrice\" class=\"item\"><text>¥</text>{{item.minPrice}}<span class=\"original-pice\">原价￥{{ item.originalPrice }}</span></view>\r\n          <view v-if=\"item.minScore\" class=\"item\"><text>\r\n              <image class=\"score-icon\" src=\"/static/images/score.png\"></image>\r\n            </text>{{item.minScore}}<span class=\"original-pice\">原价￥{{ item.originalPrice }}</span></view>\r\n        </view>\r\n        <view class=\"buy\" @click=\"goDetail(item)\">\r\n          <u-icon name=\"shopping-cart\" color=\"#FFFFFF\" size=\"24\"></u-icon>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    props: {\r\n      item: {\r\n        type: Object,\r\n        default: {},\r\n      },\r\n    },\r\n    onReady() {},\r\n    data() {\r\n      return {}\r\n    },\r\n    computed: {\r\n      filteredTags() {\r\n        if (this.item.tags) {\r\n          const tags = this.item.tags.split(/[, ]+/);\r\n          return tags.filter(tag => tag.trim() !== '');\r\n        }\r\n        return [];\r\n      }\r\n    },\r\n    methods: {\r\n      goDetail(item) {\r\n        uni.navigateTo({\r\n          url: '/pages/goods/detail?id=' + item.id\r\n        })\r\n      },\r\n      shouldHighlight(tag) {\r\n        return /[A-Za-z]\\d+/.test(tag);\r\n      }\r\n    },\r\n  }\r\n</script>\r\n<style>\r\n  .goods-box-wrapper {\r\n    margin-top: 24rpx;\r\n    border-radius: 5px;\r\n    padding-bottom: 10rpx;\r\n    text-align: left;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .goods-box {\r\n    padding: 0 18rpx;\r\n  }\r\n\r\n  .goods-box .goods-title-box {\r\n    line-height: 18px;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    word-break: break-all;\r\n  }\r\n\r\n  .goods-box .goods-title {\r\n    color: #2b2b2b;\r\n    font-size: 30rpx;\r\n  }\r\n\r\n  .goods-box .goods-dsc-box {\r\n    margin-top: 4rpx;\r\n  }\r\n\r\n  .goods-box .goods-dsc {\r\n    color: #858996;\r\n    font-size: 24rpx;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .goods-box .goods-tags span {\r\n    font-size: 22rpx;\r\n    font-weight: normal;\r\n    color: #858996;\r\n    border: 1px #D9DBDF solid;\r\n    margin-right: 10rpx;\r\n    border-radius: 2px;\r\n    padding: 0 4rpx;\r\n    line-height: 32rpx;\r\n  }\r\n\r\n  .goods-box .goods-price-container {\r\n    display: flex;\r\n    align-items: baseline;\r\n  }\r\n\r\n  .goods-box .goods-price {\r\n    overflow: hidden;\r\n    font-size: 34rpx;\r\n    color: #F20C32;\r\n    margin-left: 24rpx;\r\n  }\r\n\r\n  .goods-box .goods-price2 {\r\n    overflow: hidden;\r\n    font-size: 26rpx;\r\n    color: #aaa;\r\n    text-decoration: line-through;\r\n    margin-left: 20rpx;\r\n  }\r\n\r\n  .goods-box .buy-wrapper {\r\n    margin-top: 10rpx;\r\n    display: flex;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .goods-box .buy {\r\n    width: 52rpx;\r\n    height: 52rpx;\r\n    background-color: #34B764;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    padding: 4rpx;\r\n  }\r\n\r\n  .goods-box .price-score text {\r\n    margin-top: 6rpx;\r\n  }\r\n\r\n  .goods-box .price-score .original-pice {\r\n    font-size: 20rpx;\r\n    font-weight: normal;\r\n    margin-top: 10rpx;\r\n    margin-left: 6rpx;\r\n    text-decoration: line-through;\r\n    color: #858996;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item1.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item1.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688730694\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}