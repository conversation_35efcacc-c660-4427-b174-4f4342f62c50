{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/App.vue?0fd4", "webpack:///X:/Data/Web/ybn/2023/yehtml/App.vue?42e2", null, "webpack:///X:/Data/Web/ybn/2023/yehtml/App.vue?9cb6", "uni-app:///App.vue"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "<PERSON><PERSON>", "prototype", "$store", "store", "config", "productionTip", "App", "mpType", "use", "uView", "$wxapi", "WXAPI", "vuexStore", "require", "mixin", "mpShare", "VueTruncate", "VueI18n", "share", "lang", "uni", "getStorageSync", "i18n", "locale", "messages", "Chinese", "English", "_i18n", "app", "$mount", "globalData", "h5Domain", "goLogin", "subDomain", "merchantId", "version", "sysconfigkeys", "wxpayOpenAppId", "openAlipayProvider", "addressLevel", "onLaunch", "onShow", "onHide", "onPageNotFound", "console", "methods", "queryConfigBatch", "res", "sysconfigMap", "checkForUpdate", "updateManager", "title", "content", "success", "autoLogin", "WXAUTH", "isLogined", "setTimeout", "force", "checkHasLoginedH5", "_this", "wxmp<PERSON><PERSON>in", "code"], "mappings": ";;;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAGA;AAGA;AAEA;AA8BA;AACA;AAGA;AAKA;AAA2C;AAAA;AAlD3C;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAY1DC,YAAG,CAACC,SAAS,CAACC,MAAM,GAAGC,cAAK;AAE5BH,YAAG,CAACI,MAAM,CAACC,aAAa,GAAG,KAAK;AAEhCC,YAAG,CAACC,MAAM,GAAG,KAAK;AAElBP,YAAG,CAACQ,GAAG,CAACC,gBAAK,CAAC;AAEdT,YAAG,CAACC,SAAS,CAACS,MAAM,GAAGC,oBAAK;;AAE5B;AACA,IAAIC,SAAS,GAAGC,mBAAO,CAAC,8BAAqB,CAAC;AAC9Cb,YAAG,CAACc,KAAK,CAACF,SAAS,CAAC;;AAGpB;AACA,IAAMG,OAAO,GAAGF,mBAAO,CAAC,uDAA8C,CAAC;AACvEb,YAAG,CAACc,KAAK,CAACC,OAAO,CAAC;AAGlBf,YAAG,CAACc,KAAK,CAACA,cAAK,CAAC;;AAEhB;AACA,IAAIE,WAAW,GAAGH,mBAAO,CAAC,8BAAqB,CAAC;AAChDb,YAAG,CAACQ,GAAG,CAACQ,WAAW,CAAC;;AAEpB;AACA;;AAOA;AACAhB,YAAG,CAACQ,GAAG,CAACS,mBAAO,CAAC;AAGhBjB,YAAG,CAACc,KAAK,CAACI,gBAAK,CAAC;AAEhB,IAAMC,IAAI,GAAGC,GAAG,CAACC,cAAc,CAAC,MAAM,CAAC;AACvC,IAAMC,IAAI,GAAG,IAAIL,mBAAO,CAAC;EACvB;EACAM,MAAM,EAAEJ,IAAI,GAAGA,IAAI,GAAG,IAAI;EAC1B;EACAK,QAAQ,EAAE;IACR,IAAI,EAAEC,WAAO;IACb,IAAI,EAAEC;EACR;AACF,CAAC,CAAC;;AAEF;AACA1B,YAAG,CAACC,SAAS,CAAC0B,KAAK,GAAGL,IAAI;AAE1B,IAAMM,GAAG,GAAG,IAAI5B,YAAG;EACjBsB,IAAI,EAAJA,IAAI;EACJnB,KAAK,EAALA;AAAK,GACFG,YAAG,EACN;;AAEF;AACAO,mBAAO,CAAC,+BAAsB,CAAC,CAACe,GAAG,CAAC;AAEpC,UAAAA,GAAG,EAACC,MAAM,EAAE,C;;;;;;;;;;;;;AC7EZ;AAAA;AAAA;AAAA;AAA6vC,CAAgB,itCAAG,EAAC,C;;;;;;;;;;;ACAjxC;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACuD;AACL;AACc;;;AAGhE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,yEAAM;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAA8pB,CAAgB,8qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACClrB;AACA;AACA;AAAA,eACA;EACAC;IACAC;IACAC;IACAC;IAAA;IACAC;IAAA;IACAC;IACAC;IACAC;IAAA;IACAC;IAAA;IACAC;EACA;;EACAC;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACA;AACA;IACA;IACA;EACA;EACAC;IACA;MACA;IACA;IACA;MACA;IACA;IACA;MACA;MACA;MACA;IACA;MACA;IACA;EACA;EACAC;IACA;EAAA,CACA;EACAC;IACA;IACAC;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAV;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;cAAA;gBAAAW;gBACA;kBACAC;kBACAD;oBACAC;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAEA;MACAC;QACA;QACAN;MACA;MACAM;QACA9B;UACA+B;UACAC;UACAC;YACA;cACA;cACAH;YACA;UACA;QACA;MAEA;MACAA;QACA;MAAA,CACA;IAKA;IAEAI;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAGAC;cAAA;gBAAAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA;gBAAA,OACAD;cAAA;gBAAA;gBAAA,OACAA;cAAA;gBAEAE;kBACArC;gBACA;gBAAA,IAsBAsC;kBAAA;kBAAA;gBAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IA8BA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;gBAAA,IACAA;kBAAA;kBAAA;gBAAA;gBAAA,kCACA;cAAA;gBAAA;gBAAA,OAGAA;cAAA;gBAAAb;gBAAA,MACAA;kBAAA;kBAAA;gBAAA;gBACAa;gBAAA,kCACA;cAAA;gBAAA,kCAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAC;gBACA;cAAA;gBAFAf;gBAGA;kBACA;kBACA;kBACA;kBACAU;oBACArC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B", "file": "common/main.js", "sourcesContent": ["import 'uni-pages';\r\n// @ts-ignore\r\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import Vue from 'vue'\r\nimport App from './App'\r\nimport WXAPI from 'apifm-uniapp'\r\n\r\n// vuex\r\nimport store from './store'\r\n\r\n// 引入全局uView\r\nimport uView from '@/uni_modules/uview-ui'\r\n\r\nimport mixin from './common/mixin'\r\n\r\nVue.prototype.$store = store\r\n\r\nVue.config.productionTip = false\r\n\r\nApp.mpType = 'app'\r\n\r\nVue.use(uView)\r\n\r\nVue.prototype.$wxapi = WXAPI\r\n\r\n// 引入uView提供的对vuex的简写法文件\r\nlet vuexStore = require('@/store/$u.mixin.js');\r\nVue.mixin(vuexStore);\r\n\r\n\r\n// 引入uView对小程序分享的mixin封装\r\nconst mpShare = require('@/uni_modules/uview-ui/libs/mixin/mpShare.js')\r\nVue.mixin(mpShare)\r\n\r\n\r\nVue.mixin(mixin)\r\n\r\n// 引入文本截断\r\nlet VueTruncate = require('vue-truncate-filter')\r\nVue.use(VueTruncate)\r\n\r\n// i18n部分的配置\r\n// 引入语言包，注意路径\r\nimport Chinese from '@/common/locales/zh.js';\r\nimport English from '@/common/locales/en.js';\r\n\r\n// VueI18n\r\nimport VueI18n from '@/common/vue-i18n.min.js';\r\n\r\n// VueI18n\r\nVue.use(VueI18n);\r\n\r\nimport share from '@/components/wxShare.js'\r\nVue.mixin(share)\r\n\r\nconst lang = uni.getStorageSync('lang')\r\nconst i18n = new VueI18n({\r\n  // 默认语言\r\n  locale: lang ? lang : 'zh',\r\n  // 引入语言文件\r\n  messages: {\r\n    'zh': Chinese,\r\n    'en': English,\r\n  }\r\n});\r\n\r\n// 由于微信小程序的运行机制问题，需声明如下一行，H5和APP非必填\r\nVue.prototype._i18n = i18n;\r\n\r\nconst app = new Vue({\r\n  i18n,\r\n  store,\r\n  ...App\r\n})\r\n\r\n// 引入请求封装\r\nrequire('./util/request/index')(app)\r\n\r\napp.$mount()", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692292600\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "var render, staticRenderFns, recyclableRender, components\nvar renderjs\nimport script from \"./App.vue?vue&type=script&lang=js&\"\nexport * from \"./App.vue?vue&type=script&lang=js&\"\nimport style0 from \"./App.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"App.vue\"\nexport default component.exports", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./App.vue?vue&type=script&lang=js&\"", "<script>\r\n  const WXAUTH = require('@/common/wxauth.js')\r\n  const TTAUTH = require('@/common/ttauth.js')\r\n  const QQAUTH = require('@/common/qqauth.js')\r\n  export default {\r\n    globalData: {\r\n      h5Domain: 'https://flpt.jxsupplier.com',\r\n      goLogin: false,\r\n      subDomain: 'd43590c39068594110c15f0919145ab3', // jdjf0115\r\n      merchantId: 54426, // 42151\r\n      version: '2.0.0',\r\n      sysconfigkeys: 'mallName,shopMod,share_profile,recharge_amount_min,open_growth,shopping_cart_vop_open,needIdCheck',\r\n      wxpayOpenAppId: 'wx9b04553fd8c7b9c3', // 微信开放平台的移动端应用appID\r\n      openAlipayProvider: false, // 是否开通支付宝支付\r\n      addressLevel: 3, // 省市区到3级还是4级，可选 3 或者 4\r\n    },\r\n    onLaunch: function() {\r\n      // https://www.yuque.com/apifm/nu0f75/cdqz1n\r\n      this.$wxapi.setMerchantId(this.globalData.merchantId)\r\n      this.$wxapi.init(this.globalData.subDomain)\r\n      const _this = this\r\n      // 1.1.0版本之前关于http拦截器代码，已平滑移动到/common/http.interceptor.js中\r\n      // 注意，需要在/main.js中实例化Vue之后引入如下(详见文档说明)：\r\n      // import httpInterceptor from '@/common/http.interceptor.js'\r\n      // Vue.use(httpInterceptor, app)\r\n      // process.env.VUE_APP_PLATFORM 为通过js判断平台名称的方法，结果分别如下：\r\n      /**\r\n       * h5，app-plus(nvue下也为app-plus)，mp-weixin，mp-alipay......\r\n       */\r\n      this.checkForUpdate(); // 检查新版本\r\n      this.queryConfigBatch();\r\n    },\r\n    onShow(e) {\r\n      if (e && e.query && e.query.inviter_id) {\r\n        this.$u.vuex('referrer', e.query.inviter_id)\r\n      }\r\n      if (e && e.query && e.query.kjJoinUid) {\r\n        this.$u.vuex('kjJoinUid', e.query.kjJoinUid)\r\n      }\r\n      if (e && e.query && e.query.code) {\r\n        // 微信登陆\r\n        this.wxmpLogin(e.query.code)\r\n        return\r\n      } else {\r\n        this.autoLogin()\r\n      }\r\n    },\r\n    onHide: function() {\r\n      // console.log('App Hide，app不再展现在前台')\r\n    },\r\n    onPageNotFound(e) {\r\n      // 页面不存在 {path: '/1212', query: {a: '123'}, isEntryPage: true}\r\n      console.error(e)\r\n    },\r\n    methods: {\r\n      async queryConfigBatch() {\r\n        const sysconfigkeys = this.globalData.sysconfigkeys\r\n        if (!sysconfigkeys) {\r\n          return\r\n        }\r\n        // https://www.yuque.com/apifm/nu0f75/dis5tl\r\n        const res = await this.$wxapi.queryConfigBatch(sysconfigkeys)\r\n        if (res.code == 0) {\r\n          const sysconfigMap = {}\r\n          res.data.forEach(config => {\r\n            sysconfigMap[config.key] = config.value\r\n          })\r\n          this.$u.vuex('sysconfigMap', sysconfigMap)\r\n        }\r\n      },\r\n      checkForUpdate() {\r\n        // #ifdef MP\r\n        const updateManager = uni.getUpdateManager();\r\n        updateManager.onCheckForUpdate(function(res) {\r\n          // 请求完新版本信息的回调\r\n          console.log(res.hasUpdate);\r\n        });\r\n        updateManager.onUpdateReady(function(res) {\r\n          uni.showModal({\r\n            title: '更新提示',\r\n            content: '新版本已经准备好，是否重启应用？',\r\n            success(res) {\r\n              if (res.confirm) {\r\n                // 新的版本已经下载好，调用 applyUpdate 应用新版本并重启\r\n                updateManager.applyUpdate();\r\n              }\r\n            }\r\n          });\r\n\r\n        });\r\n        updateManager.onUpdateFailed(function(res) {\r\n          // 新的版本下载失败\r\n        });\r\n        // #endif\r\n        // #ifdef APP-PLUS\r\n        // APP 自动更新\r\n        // #endif\r\n      },\r\n\r\n      async autoLogin(force) {\r\n        // 自动登陆\r\n        // #ifdef MP-WEIXIN\r\n        const isLogined = await WXAUTH.checkHasLogined()\r\n        if (!isLogined) {\r\n          await WXAUTH.authorize()\r\n          await WXAUTH.bindSeller()\r\n        }\r\n        setTimeout(() => {\r\n          uni.$emit('loginOK', {})\r\n        }, 500)\r\n        // #endif\r\n        // #ifdef MP-QQ\r\n        const isLogined = await QQAUTH.checkHasLogined()\r\n        if (!isLogined) {\r\n          await QQAUTH.authorize()\r\n          await QQAUTH.bindSeller()\r\n        }\r\n        setTimeout(() => {\r\n          uni.$emit('loginOK', {})\r\n        }, 500)\r\n        // #endif\r\n        // #ifdef MP-TOUTIAO\r\n        const isLogined = await TTAUTH.checkHasLogined()\r\n        if (!isLogined) {\r\n          await TTAUTH.authorize()\r\n          await TTAUTH.bindSeller()\r\n        }\r\n        setTimeout(() => {\r\n          uni.$emit('loginOK', {})\r\n        }, 500)\r\n        // #endif\r\n        if (!force) {\r\n          return\r\n        }\r\n        // #ifdef H5\r\n        const isLogined = await this.checkHasLoginedH5()\r\n        if (!isLogined) {\r\n          // 判断是普通浏览器还是微信浏览器\r\n          const ua = window.navigator.userAgent.toLowerCase();\r\n          if (ua.match(/MicroMessenger/i) == 'micromessenger') {\r\n            // 微信内置浏览器打开的\r\n            // https://www.yuque.com/apifm/nu0f75/fpvc3m\r\n            const res = await this.$wxapi.siteStatistics()\r\n            const wxMpAppid = res.data.wxMpAppid\r\n            let _domian = this.globalData.h5Domain + '/pages/index/index'\r\n            _domian = encodeURIComponent(_domian)\r\n            console.log(_domian);\r\n            if (!this.globalData.goLogin) {\r\n              this.globalData.goLogin = true\r\n              window.parent.location.href = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' +\r\n                wxMpAppid + '&redirect_uri=' + _domian +\r\n                '&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect'\r\n            }\r\n          } else {\r\n            // 其他浏览器打开的， 按需登陆，不能直接跳转到登陆界面\r\n            uni.navigateTo({\r\n              url: \"/pages/login/login\"\r\n            })\r\n          }\r\n        }\r\n        // #endif\r\n      },\r\n      async checkHasLoginedH5() {\r\n        const _this = this.$vm ? this.$vm : this\r\n        if (!_this.token) {\r\n          return false\r\n        }\r\n        // https://www.yuque.com/apifm/nu0f75/mp9f59\r\n        const res = await _this.$wxapi.checkToken(_this.token)\r\n        if (res.code != 0) {\r\n          _this.$u.vuex('token', '')\r\n          return false\r\n        }\r\n        return true\r\n      },\r\n      async wxmpLogin(code) {\r\n        // https://www.yuque.com/apifm/nu0f75/lh6cd3\r\n        const res = await this.$wxapi.wxmpAuth({\r\n          code\r\n        })\r\n        if (res.code == 0) {\r\n          this.$u.vuex('token', res.data.token)\r\n          this.$u.vuex('uid', res.data.uid)\r\n          this.$u.vuex('openid', res.data.openid)\r\n          setTimeout(() => {\r\n            uni.$emit('loginOK', {})\r\n          }, 500)\r\n        }\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n  @import \"@/uni_modules/uview-ui/index.scss\";\r\n\r\n  .container-wrapper {\r\n    width: 100%;\r\n    overflow-x: hidden;\r\n    position: relative;\r\n  }\r\n\r\n  .container {\r\n    padding: 0 20rpx;\r\n  }\r\n\r\n  .list-image-wrapper {\r\n    width: 100%;\r\n    padding-bottom: 100%;\r\n    background-size: contain;\r\n    background-repeat: no-repeat;\r\n    background-position: center center;\r\n    border-radius: 20rpx;\r\n  }\r\n\r\n  .price-score {\r\n    position: relative;\r\n    display: flex;\r\n    color: #e64340;\r\n    font-size: 40rpx;\r\n    font-weight: bolder;\r\n\r\n    text {\r\n      padding: 0 6rpx;\r\n      font-size: 28rpx;\r\n    }\r\n\r\n    .item {\r\n      padding: 0 8rpx;\r\n      display: flex;\r\n      align-items: center;\r\n    }\r\n\r\n    .original {\r\n      margin-left: 32rpx;\r\n      color: #aaa;\r\n      text-decoration: line-through\r\n    }\r\n\r\n    .score-icon {\r\n      width: 28rpx;\r\n      height: 28rpx;\r\n      padding: 0 6rpx;\r\n    }\r\n  }\r\n\r\n  .count-down {\r\n    font-size: 22rpx;\r\n    color: #324A43;\r\n  }\r\n\r\n  .time {\r\n    @include flex;\r\n    align-items: center;\r\n    font-size: 9px;\r\n\r\n    &__custom {\r\n      margin-top: 4px;\r\n      width: 16px;\r\n      height: 16px;\r\n      background-color: #324A43;\r\n      border-radius: 4px;\r\n      /* #ifndef APP-NVUE */\r\n      display: flex;\r\n      /* #endif */\r\n      justify-content: center;\r\n      align-items: center;\r\n\r\n      &__item {\r\n        color: #fff;\r\n        font-size: 9px;\r\n        text-align: center;\r\n      }\r\n    }\r\n\r\n    &__doc {\r\n      color: #324A43;\r\n      padding: 0px 2px;\r\n      margin-top: 5px;\r\n    }\r\n\r\n    &__item {\r\n      color: #606266;\r\n      font-size: 10px;\r\n      margin-right: 2px;\r\n    }\r\n  }\r\n\r\n  .pt16 {\r\n    padding-top: 16rpx !important;\r\n  }\r\n\r\n  .submit-btn {\r\n    padding: 32rpx;\r\n  }\r\n\r\n  .form-box {\r\n    padding: 32rpx;\r\n  }\r\n\r\n  .mt32 {\r\n    margin-top: 32rpx;\r\n  }\r\n\r\n  .label-title {\r\n    font-size: 28rpx;\r\n    font-weight: bold;\r\n    color: #000000;\r\n    padding: 30rpx;\r\n    display: flex;\r\n    align-items: center;\r\n\r\n    .icon {\r\n      width: 6rpx;\r\n      height: 30rpx;\r\n      background: linear-gradient(360deg, #FFAD56 0%, #FF8A46 100%);\r\n      margin-right: 8rpx;\r\n    }\r\n  }\r\n\r\n  .goods-tags span {\r\n    font-size: 22rpx;\r\n    font-weight: normal;\r\n    color: #858996;\r\n    border: 1px #D9DBDF solid;\r\n    margin-right: 10rpx;\r\n    border-radius: 2px;\r\n    padding: 0 4rpx;\r\n    line-height: 32rpx;\r\n  }\r\n</style>"], "sourceRoot": ""}