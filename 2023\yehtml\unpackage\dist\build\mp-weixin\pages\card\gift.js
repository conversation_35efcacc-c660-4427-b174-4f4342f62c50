(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/card/gift"],{"748f":function(n,t,e){"use strict";(function(n,t){var u=e("47a9");e("96bd");u(e("3240"));var f=u(e("9d97"));n.__webpack_require_UNI_MP_PLUGIN__=e,t(f.default)}).call(this,e("3223")["default"],e("df3c")["createPage"])},"74e0":function(n,t,e){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;t.default={data:function(){return{}},created:function(){},mounted:function(){},onReady:function(){},onLoad:function(n){this.fetchDetail(n.id)},onShow:function(){},methods:{}}},"9d97":function(n,t,e){"use strict";e.r(t);var u=e("f4f6"),f=e("eec6");for(var c in f)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return f[n]}))}(c);var o=e("828b"),a=Object(o["a"])(f["default"],u["b"],u["c"],!1,null,"072f5b0e",null,!1,u["a"],void 0);t["default"]=a.exports},eec6:function(n,t,e){"use strict";e.r(t);var u=e("74e0"),f=e.n(u);for(var c in u)["default"].indexOf(c)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(c);t["default"]=f.a},f4f6:function(n,t,e){"use strict";e.d(t,"b",(function(){return u})),e.d(t,"c",(function(){return f})),e.d(t,"a",(function(){}));var u=function(){var n=this.$createElement;this._self._c},f=[]}},[["748f","common/runtime","common/vendor"]]]);