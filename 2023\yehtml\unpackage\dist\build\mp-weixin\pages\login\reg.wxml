<view class="data-v-384de68a"><u-empty vue-id="7936e3e1-1" mode="permission" text="注册开通新账号" marginTop="88rpx" class="data-v-384de68a" bind:__l="__l"></u-empty><view class="form-box data-v-384de68a"><u-form vue-id="7936e3e1-2" label-width="150rpx" model="{{form}}" data-ref="uForm" class="data-v-384de68a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('7936e3e1-3')+','+('7936e3e1-2')}}" label="手机号码" prop="mobile" required="{{true}}" class="data-v-384de68a" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('7936e3e1-4')+','+('7936e3e1-3')}}" type="number" clearable="{{true}}" maxlength="11" focus="{{true}}" placeholder="请输入手机号码" value="{{form.mobile}}" data-event-opts="{{[['^input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" class="data-v-384de68a" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('7936e3e1-5')+','+('7936e3e1-2')}}" label="图片验证码" prop="imgcode" required="{{true}}" class="data-v-384de68a" bind:__l="__l" vue-slots="{{['default','right']}}"><u-input bind:input="__e" vue-id="{{('7936e3e1-6')+','+('7936e3e1-5')}}" type="number" clearable="{{true}}" maxlength="4" focus="{{true}}" placeholder="请输入图片验证码" value="{{form.imgcode}}" data-event-opts="{{[['^input',[['__set_model',['$0','imgcode','$event',[]],['form']]]]]}}" class="data-v-384de68a" bind:__l="__l"></u-input><view slot="right" class="data-v-384de68a"><u-image vue-id="{{('7936e3e1-7')+','+('7936e3e1-5')}}" showLoading="{{true}}" src="{{imgsrc}}" width="200rpx" height="80rpx" data-event-opts="{{[['^click',[['changeImgCode']]]]}}" bind:click="__e" class="data-v-384de68a" bind:__l="__l"></u-image></view></u-form-item><u-form-item vue-id="{{('7936e3e1-8')+','+('7936e3e1-2')}}" label="短信验证码" prop="code" required="{{true}}" class="data-v-384de68a" bind:__l="__l" vue-slots="{{['default','right']}}"><u-input bind:input="__e" vue-id="{{('7936e3e1-9')+','+('7936e3e1-8')}}" type="number" clearable="{{true}}" maxlength="4" focus="{{true}}" placeholder="请输入短信验证码" value="{{form.code}}" data-event-opts="{{[['^input',[['__set_model',['$0','code','$event',[]],['form']]]]]}}" class="data-v-384de68a" bind:__l="__l"></u-input><view style="padding-left:24rpx;" slot="right" class="data-v-384de68a"><u-toast vue-id="{{('7936e3e1-10')+','+('7936e3e1-8')}}" data-ref="uToast" class="data-v-384de68a vue-ref" bind:__l="__l"></u-toast><u-code vue-id="{{('7936e3e1-11')+','+('7936e3e1-8')}}" seconds="{{seconds}}" keepRunning="{{true}}" data-ref="uCode" data-event-opts="{{[['^end',[['end']]],['^start',[['start']]],['^change',[['codeChange']]]]}}" bind:end="__e" bind:start="__e" bind:change="__e" class="data-v-384de68a vue-ref" bind:__l="__l"></u-code><u-button vue-id="{{('7936e3e1-12')+','+('7936e3e1-8')}}" type="success" size="small" data-event-opts="{{[['^tap',[['getCode']]]]}}" bind:tap="__e" class="data-v-384de68a" bind:__l="__l" vue-slots="{{['default']}}">{{tips}}</u-button></view></u-form-item><u-form-item vue-id="{{('7936e3e1-13')+','+('7936e3e1-2')}}" label="设置密码" prop="pwd" required="{{true}}" class="data-v-384de68a" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('7936e3e1-14')+','+('7936e3e1-13')}}" type="password" clearable="{{true}}" placeholder="请输入登陆密码" value="{{form.pwd}}" data-event-opts="{{[['^input',[['__set_model',['$0','pwd','$event',[]],['form']]]]]}}" class="data-v-384de68a" bind:__l="__l"></u-input></u-form-item></u-form></view><view class="submit data-v-384de68a"><u-button vue-id="7936e3e1-15" type="success" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" class="data-v-384de68a" bind:__l="__l" vue-slots="{{['default']}}">立即注册</u-button><view class="text-btns data-v-384de68a"><view data-event-opts="{{[['tap',[['goLogin',['$event']]]]]}}" bindtap="__e" class="data-v-384de68a">已有账号？直接登陆</view></view></view></view>