(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uview-ui/components/u-sticky/u-sticky"],{"2a0c":function(t,e,i){"use strict";i.r(e);var n=i("ce87"),s=i("6bb74");for(var c in s)["default"].indexOf(c)<0&&function(t){i.d(e,t,(function(){return s[t]}))}(c);i("e4d9");var o=i("828b"),u=Object(o["a"])(s["default"],n["b"],n["c"],!1,null,"fd44e92e",null,!1,n["a"],void 0);e["default"]=u.exports},"6bb74":function(t,e,i){"use strict";i.r(e);var n=i("db99"),s=i.n(n);for(var c in n)["default"].indexOf(c)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(c);e["default"]=s.a},8030:function(t,e,i){},ce87:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return s})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement,e=(this._self._c,this.__get_style([this.style])),i=this.__get_style([this.stickyContent]);this.$mp.data=Object.assign({},{$root:{s0:e,s1:i}})},s=[]},db99:function(t,e,i){"use strict";(function(t){var n=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var s=n(i("7eb4")),c=n(i("ee10")),o=n(i("6246")),u={name:"u-sticky",mixins:[t.$u.mpMixin,t.$u.mixin,o.default],data:function(){return{cssSticky:!1,stickyTop:0,elId:t.$u.guid(),left:0,width:"auto",height:"auto",fixed:!1}},computed:{style:function(){var e={};return this.disabled?e.position="static":this.cssSticky?(e.position="sticky",e.zIndex=this.uZindex,e.top=t.$u.addUnit(this.stickyTop)):e.height=this.fixed?this.height+"px":"auto",e.backgroundColor=this.bgColor,t.$u.deepMerge(t.$u.addStyle(this.customStyle),e)},stickyContent:function(){var t={};return this.cssSticky||(t.position=this.fixed?"fixed":"static",t.top=this.stickyTop+"px",t.left=this.left+"px",t.width="auto"==this.width?"auto":this.width+"px",t.zIndex=this.uZindex),t},uZindex:function(){return this.zIndex?this.zIndex:t.$u.zIndex.sticky}},mounted:function(){this.init()},methods:{init:function(){this.getStickyTop(),this.checkSupportCssSticky(),this.cssSticky||!this.disabled&&this.initObserveContent()},initObserveContent:function(){var t=this;this.$uGetRect("#"+this.elId).then((function(e){t.height=e.height,t.left=e.left,t.width=e.width,t.$nextTick((function(){t.observeContent()}))}))},observeContent:function(){var e=this;this.disconnectObserver("contentObserver");var i=t.createIntersectionObserver({thresholds:[.95,.98,1]});i.relativeToViewport({top:-this.stickyTop}),i.observe("#".concat(this.elId),(function(t){e.setFixed(t.boundingClientRect.top)})),this.contentObserver=i},setFixed:function(t){var e=t<=this.stickyTop;this.fixed=e},disconnectObserver:function(t){var e=this[t];e&&e.disconnect()},getStickyTop:function(){this.stickyTop=t.$u.getPx(this.offsetTop)+t.$u.getPx(this.customNavHeight)},checkSupportCssSticky:function(){var e=this;return(0,c.default)(s.default.mark((function i(){return s.default.wrap((function(i){while(1)switch(i.prev=i.next){case 0:return"android"===t.$u.os()&&Number(t.$u.sys().system)>8&&(e.cssSticky=!0),i.next=3,e.checkComputedStyle();case 3:e.cssSticky=i.sent,"ios"===t.$u.os()&&(e.cssSticky=!0);case 5:case"end":return i.stop()}}),i)})))()},checkComputedStyle:function(){var e=this;return new Promise((function(i){t.createSelectorQuery().in(e).select(".u-sticky").fields({computedStyle:["position"]}).exec((function(t){i("sticky"===t[0].position)}))}))},checkCssStickyForH5:function(){}},beforeDestroy:function(){this.disconnectObserver("contentObserver")}};e.default=u}).call(this,i("df3c")["default"])},e4d9:function(t,e,i){"use strict";var n=i("8030"),s=i.n(n);s.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uview-ui/components/u-sticky/u-sticky-create-component',
    {
        'uni_modules/uview-ui/components/u-sticky/u-sticky-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("2a0c"))
        })
    },
    [['uni_modules/uview-ui/components/u-sticky/u-sticky-create-component']]
]);
