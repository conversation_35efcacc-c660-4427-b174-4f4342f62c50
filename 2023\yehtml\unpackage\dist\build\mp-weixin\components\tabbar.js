(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/tabbar"],{"0353":function(e,n,t){"use strict";t.r(n);var a=t("c8e7"),u=t("ad78");for(var r in u)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return u[e]}))}(r);var o=t("828b"),i=Object(o["a"])(u["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=i.exports},a124:function(e,n,t){"use strict";(function(e){Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var t={props:{tabIndex:{type:Number,default:0}},data:function(){return{}},methods:{tabChange:function(n){0===n&&e.navigateTo({url:"/pages/promotion/list"}),1===n&&e.navigateTo({url:"/pages/category/category"}),2===n&&e.navigateTo({url:"/pages/index/index"}),3===n&&e.navigateTo({url:"/pages/cart/index"}),4===n&&e.navigateTo({url:"/pages/my/index"})}}};n.default=t}).call(this,t("df3c")["default"])},ad78:function(e,n,t){"use strict";t.r(n);var a=t("a124"),u=t.n(a);for(var r in a)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return a[e]}))}(r);n["default"]=u.a},c8e7:function(e,n,t){"use strict";t.d(n,"b",(function(){return u})),t.d(n,"c",(function(){return r})),t.d(n,"a",(function(){return a}));var a={uTabbar:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-tabbar/u-tabbar")]).then(t.bind(null,"e585"))},uTabbarItem:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-tabbar-item/u-tabbar-item")]).then(t.bind(null,"09ef"))}},u=function(){var e=this.$createElement;this._self._c},r=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/tabbar-create-component',
    {
        'components/tabbar-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("0353"))
        })
    },
    [['components/tabbar-create-component']]
]);
