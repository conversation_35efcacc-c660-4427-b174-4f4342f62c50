<template>
  <view class="list2">
    <view>
      <view class="item-wrapper">
        <view v-for="(item, index) in list" wx:key="index">
          <listGoodsItemCouponsIndex :item="item, index"></listGoodsItemCouponsIndex>
        </view>
      </view>
    </view>
  </view>

</template>

<script>
  import empty from 'empty-value'
  import listGoodsItemCouponsIndex from '@/components/list-item/list-goods-item-coupons-index'

  export default {
    components: {
      listGoodsItemCouponsIndex
    },
    props: {
      list: {
        type: Array,
        default: [],
      }
    },
    methods: {},
  }
</script>
<style>
  .list2 {
    padding: 20rpx 0;
  }

  .item-wrapper {
    box-shadow: 0 10rpx 20rpx 0 rgba(0, 0, 0, 0.3);
    border-radius: 20rpx;
    background: #FFFFFF;
    color: #2C3E50;
    padding: 16rpx;
    margin-bottom: 20rpx;
  }
</style>