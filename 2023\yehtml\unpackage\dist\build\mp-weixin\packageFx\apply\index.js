(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["packageFx/apply/index"],{"1ade":function(e,n,t){},"2f50":function(e,n,t){"use strict";t.d(n,"b",(function(){return u})),t.d(n,"c",(function(){return o})),t.d(n,"a",(function(){return a}));var a={uEmpty:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-empty/u-empty")]).then(t.bind(null,"c57e"))},uButton:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-button/u-button")]).then(t.bind(null,"9edc"))},uIcon:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-icon/u-icon")]).then(t.bind(null,"5f3a"))}},u=function(){var e=this.$createElement;this._self._c},o=[]},"4c4d":function(e,n,t){"use strict";(function(e,n){var a=t("47a9");t("96bd");a(t("3240"));var u=a(t("f93e"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(u.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},"5f9d":function(e,n,t){"use strict";(function(e){var a=t("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var u=a(t("7eb4")),o=a(t("ee10")),i={data:function(){return{wxlogin:!0,applyStatus:-2,applyInfo:{},canvasHeight:0}},methods:{onLoad:function(e){this.setting()},onShow:function(){var e=this;this.$wxapi.checkToken(this.token).then((function(n){0===n.code&&e.doneShow()}))},doneShow:function(){var e=this;return(0,o.default)(u.default.mark((function n(){var t,a;return u.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return t=e,n.next=3,e.$wxapi.userDetail(e.token);case 3:a=n.sent,e.$wxapi.fxApplyProgress(e.token).then((function(n){var u=a.data.base.isSeller?2:-1;2e3!=n.code?(700===n.code&&(t.applyStatus=u),0===n.code&&(u=a.data.base.isSeller?2:n.data.status,t.applyStatus=u,t.applyInfo=n.data)):e.wxlogin=!1}));case 5:case"end":return n.stop()}}),n)})))()},goForm:function(n){e.navigateTo({url:"/packageFx/apply/form"})},goShop:function(n){e.switchTab({url:"/pages/index/index"})},goFx:function(n){e.redirectTo({url:"/packageFx/index/index"})},setting:function(){var e=this;return(0,o.default)(u.default.mark((function n(){var t;return u.default.wrap((function(n){while(1)switch(n.prev=n.next){case 0:return n.next=2,e.$wxapi.fxSetting();case 2:t=n.sent,0==t.code&&(e.setting=t.data);case 4:case"end":return n.stop()}}),n)})))()},buy:function(){var n=this;return(0,o.default)(u.default.mark((function t(){var a,o,i;return u.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=n.token,t.next=3,n.$wxapi.userAmount(a);case 3:if(o=t.sent,0==o.code){t.next=7;break}return e.showToast({title:o.msg,icon:"none"}),t.abrupt("return");case 7:if(!(o.data.balance>=n.setting.price)){t.next=18;break}return t.next=10,n.$wxapi.fxBuy(a);case 10:if(o=t.sent,0==o.code){t.next=14;break}return e.showToast({title:o.msg,icon:"none"}),t.abrupt("return");case 14:e.showToast({title:"升级成功"}),setTimeout((function(){e.redirectTo({url:"/packageFx/index/index"})}),1e3),t.next=21;break;case 18:i=n.setting.price-o.data.balance,i=i.toFixed(2),wxpay.wxpay("fxsBuy",i,0,"/packageFx/index/index");case 21:case"end":return t.stop()}}),t)})))()}}};n.default=i}).call(this,t("df3c")["default"])},"7ece":function(e,n,t){"use strict";t.r(n);var a=t("5f9d"),u=t.n(a);for(var o in a)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return a[e]}))}(o);n["default"]=u.a},9710:function(e,n,t){"use strict";var a=t("1ade"),u=t.n(a);u.a},f93e:function(e,n,t){"use strict";t.r(n);var a=t("2f50"),u=t("7ece");for(var o in u)["default"].indexOf(o)<0&&function(e){t.d(n,e,(function(){return u[e]}))}(o);t("9710");var i=t("828b"),r=Object(i["a"])(u["default"],a["b"],a["c"],!1,null,null,null,!1,a["a"],void 0);n["default"]=r.exports}},[["4c4d","common/runtime","common/vendor"]]]);