<view class="data-v-d930a92a"><view class="search-box data-v-d930a92a"><u-search vue-id="8b14fa8c-1" placeholder="搜索门店" showAction="{{false}}" value="{{kw}}" data-event-opts="{{[['^search',[['search']]],['^input',[['__set_model',['','kw','$event',[]]]]]]}}" bind:search="__e" bind:input="__e" class="data-v-d930a92a" bind:__l="__l"></u-search></view><block wx:for="{{shops}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-cell-group class="shop-box data-v-d930a92a" vue-id="{{'8b14fa8c-2-'+index}}" bind:__l="__l" vue-slots="{{['default']}}"><u-cell vue-id="{{('8b14fa8c-3-'+index)+','+('8b14fa8c-2-'+index)}}" icon="bag" size="large" border="{{false}}" clickable="{{true}}" isLink="{{true}}" title="{{item.name}}" value="{{item.distance+'km'}}" data-event-opts="{{[['^click',[['goShop',[index]]]]]}}" bind:click="__e" class="data-v-d930a92a" bind:__l="__l"></u-cell><u-cell vue-id="{{('8b14fa8c-4-'+index)+','+('8b14fa8c-2-'+index)}}" icon="map" border="{{false}}" title="{{item.address}}" class="data-v-d930a92a" bind:__l="__l"></u-cell><block wx:if="{{item.openingHours}}"><u-cell vue-id="{{('8b14fa8c-5-'+index)+','+('8b14fa8c-2-'+index)}}" icon="clock" border="{{false}}" title="{{item.openingHours}}" class="data-v-d930a92a" bind:__l="__l"></u-cell></block><u-cell vue-id="{{('8b14fa8c-6-'+index)+','+('8b14fa8c-2-'+index)}}" icon="phone" border="{{false}}" title="{{item.linkPhone}}" class="data-v-d930a92a" bind:__l="__l"></u-cell></u-cell-group></block></view>