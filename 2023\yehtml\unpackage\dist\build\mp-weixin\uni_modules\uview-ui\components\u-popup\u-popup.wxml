<view class="u-popup data-v-599ead9e"><block wx:if="{{overlay}}"><u-overlay vue-id="7ae01353-1" show="{{show}}" duration="{{overlayDuration}}" customStyle="{{overlayStyle}}" opacity="{{overlayOpacity}}" data-event-opts="{{[['^click',[['overlayClick']]]]}}" bind:click="__e" class="data-v-599ead9e" bind:__l="__l"></u-overlay></block><u-transition vue-id="7ae01353-2" show="{{show}}" customStyle="{{transitionStyle}}" mode="{{position}}" duration="{{duration}}" data-event-opts="{{[['^afterEnter',[['afterEnter']]],['^click',[['clickHandler']]]]}}" bind:afterEnter="__e" bind:click="__e" class="data-v-599ead9e" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['noop',['$event']]]]]}}" class="u-popup__content data-v-599ead9e" style="{{$root.s0}}" catchtap="__e"><block wx:if="{{safeAreaInsetTop}}"><u-status-bar vue-id="{{('7ae01353-3')+','+('7ae01353-2')}}" class="data-v-599ead9e" bind:__l="__l"></u-status-bar></block><slot></slot><block wx:if="{{closeable}}"><view class="{{['u-popup__content__close','data-v-599ead9e','u-popup__content__close--'+closeIconPos]}}" hover-class="u-popup__content__close--hover" hover-stay-time="150" data-event-opts="{{[['tap',[['close',['$event']]]]]}}" catchtap="__e"><u-icon vue-id="{{('7ae01353-4')+','+('7ae01353-2')}}" name="close" color="#909399" size="18" bold="{{true}}" class="data-v-599ead9e" bind:__l="__l"></u-icon></view></block><block wx:if="{{safeAreaInsetBottom}}"><u-safe-bottom vue-id="{{('7ae01353-5')+','+('7ae01353-2')}}" class="data-v-599ead9e" bind:__l="__l"></u-safe-bottom></block></view></u-transition></view>