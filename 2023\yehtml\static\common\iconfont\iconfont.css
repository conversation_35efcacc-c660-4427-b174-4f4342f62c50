@font-face {
  font-family: "iconfont"; /* Project id 4284589 */
  src: url('iconfont.woff2?t=1697697715670') format('woff2'),
       url('iconfont.woff?t=1697697715670') format('woff'),
       url('iconfont.ttf?t=1697697715670') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-gift:before {
  content: "\f58b";
}

.icon-partner:before {
  content: "\e638";
}

.icon-vip:before {
  content: "\e615";
}

