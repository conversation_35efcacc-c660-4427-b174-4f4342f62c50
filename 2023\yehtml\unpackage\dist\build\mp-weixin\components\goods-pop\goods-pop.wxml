<block wx:if="{{show}}"><u-popup vue-id="f01c2a68-1" show="{{show}}" mode="bottom" round="32rpx" customStyle="{{({maxHeight:'80vh',overflow:'scroll'})}}" data-event-opts="{{[['^close',[['close']]]]}}" bind:close="__e" class="data-v-2f42cdbb" bind:__l="__l" vue-slots="{{['default']}}"><view class="goodsList-pop data-v-2f42cdbb"><u-image vue-id="{{('f01c2a68-2')+','+('f01c2a68-1')}}" showLoading="{{true}}" lazyLoad="{{true}}" src="{{pic}}" radius="16rpx" width="240rpx" height="240rpx" class="data-v-2f42cdbb" bind:__l="__l"></u-image><view class="goods-info data-v-2f42cdbb"><u-text class="t data-v-2f42cdbb" vue-id="{{('f01c2a68-3')+','+('f01c2a68-1')}}" lines="{{3}}" text="{{goodsDetail.basicInfo.name}}" bind:__l="__l"></u-text><block wx:if="{{goodsDetail.basicInfo.numberSells}}"><view class="t2 data-v-2f42cdbb">{{"已售:"+goodsDetail.basicInfo.numberSells+''}}</view></block><view class="price data-v-2f42cdbb"><block wx:if="{{price}}"><view class="data-v-2f42cdbb"><text class="data-v-2f42cdbb">¥</text>{{price}}</view></block><block wx:if="{{score}}"><view class="data-v-2f42cdbb"><text class="data-v-2f42cdbb">￠</text>{{score+''}}</view></block></view></view></view><u-line vue-id="{{('f01c2a68-4')+','+('f01c2a68-1')}}" dashed="{{true}}" margin="32rpx" class="data-v-2f42cdbb" bind:__l="__l"></u-line><block wx:for="{{properties}}" wx:for-item="item" wx:for-index="index" wx:key="id"><block wx:if="{{!item.hidden}}"><view class="skuList data-v-2f42cdbb"><view class="t data-v-2f42cdbb">{{item.name}}</view><view class="items data-v-2f42cdbb"><block wx:for="{{item.childsCurGoods}}" wx:for-item="item2" wx:for-index="index2" wx:key="id"><view class="item data-v-2f42cdbb"><u-tag vue-id="{{('f01c2a68-5-'+index+'-'+index2)+','+('f01c2a68-1')}}" show="{{!item2.hidden}}" type="{{item2.selected?'error':'info'}}" plain="{{item2.selected?false:true}}" text="{{item2.name}}" data-event-opts="{{[['^click',[['skuSelect',[index,index2]]]]]}}" bind:click="__e" class="data-v-2f42cdbb" bind:__l="__l"></u-tag></view></block></view></view></block></block><block wx:for="{{goodsAddition}}" wx:for-item="item" wx:for-index="index" wx:key="id"><view class="skuList data-v-2f42cdbb"><view class="t data-v-2f42cdbb">{{item.name}}</view><view class="items data-v-2f42cdbb"><block wx:for="{{item.items}}" wx:for-item="item2" wx:for-index="index2" wx:key="id"><view class="item data-v-2f42cdbb"><u-tag vue-id="{{('f01c2a68-6-'+index+'-'+index2)+','+('f01c2a68-1')}}" type="{{item2.selected?'error':'info'}}" plain="{{item2.selected?false:true}}" text="{{item2.name}}" data-event-opts="{{[['^click',[['additionSelect',[index,index2]]]]]}}" bind:click="__e" class="data-v-2f42cdbb" bind:__l="__l"></u-tag></view></block></view></view></block><block wx:if="{{goodsAddition||properties}}"><u-line vue-id="{{('f01c2a68-7')+','+('f01c2a68-1')}}" dashed="{{true}}" margin="32rpx" class="data-v-2f42cdbb" bind:__l="__l"></u-line></block><block wx:if="{{!kjid}}"><block class="data-v-2f42cdbb"><view class="buy-number data-v-2f42cdbb"><text class="data-v-2f42cdbb">购买数量</text><u-number-box bind:input="__e" vue-id="{{('f01c2a68-8')+','+('f01c2a68-1')}}" min="{{min}}" max="{{max}}" integer="{{true}}" value="{{buyNumber}}" data-event-opts="{{[['^input',[['__set_model',['','buyNumber','$event',[]]]]]]}}" class="data-v-2f42cdbb" bind:__l="__l"></u-number-box></view><u-line vue-id="{{('f01c2a68-9')+','+('f01c2a68-1')}}" dashed="{{true}}" margin="32rpx" class="data-v-2f42cdbb" bind:__l="__l"></u-line></block></block><block wx:if="{{kjid}}"><view class="data-v-2f42cdbb"><view class="btn data-v-2f42cdbb"><u-button vue-id="{{('f01c2a68-10')+','+('f01c2a68-1')}}" text="立即购买" shape="circle" color="linear-gradient(90deg, #ff6034, #ee0a24, #ff6034)" data-event-opts="{{[['^click',[['tobuy']]]]}}" bind:click="__e" class="data-v-2f42cdbb" bind:__l="__l"></u-button></view></view></block><block wx:else><view class="btns data-v-2f42cdbb"><view class="icon-btn data-v-2f42cdbb"><u-icon vue-id="{{('f01c2a68-11')+','+('f01c2a68-1')}}" name="chat" size="48rpx" class="data-v-2f42cdbb" bind:__l="__l"></u-icon><text class="data-v-2f42cdbb">客服</text><button open-type="contact" send-message-title="{{goodsDetail.basicInfo.name}}" send-message-img="{{goodsDetail.basicInfo.pic}}" send-message-path="{{'/pages/goods/detail?id='+goodsDetail.basicInfo.id}}" show-message-card="{{true}}" class="data-v-2f42cdbb"></button></view><view data-event-opts="{{[['tap',[['goCart',['$event']]]]]}}" class="icon-btn data-v-2f42cdbb" bindtap="__e"><u-icon vue-id="{{('f01c2a68-12')+','+('f01c2a68-1')}}" name="shopping-cart" size="48rpx" class="data-v-2f42cdbb" bind:__l="__l"></u-icon><text class="data-v-2f42cdbb">购物车</text><u-badge vue-id="{{('f01c2a68-13')+','+('f01c2a68-1')}}" type="error" value="{{cartNumber}}" absolute="{{true}}" offset="{{[-10,-10]}}" class="data-v-2f42cdbb" bind:__l="__l"></u-badge></view><view data-event-opts="{{[['tap',[['addFav',['$event']]]]]}}" class="icon-btn data-v-2f42cdbb" bindtap="__e"><u-icon vue-id="{{('f01c2a68-14')+','+('f01c2a68-1')}}" name="{{faved?'heart-fill':'heart'}}" size="48rpx" class="data-v-2f42cdbb" bind:__l="__l"></u-icon><text class="data-v-2f42cdbb">收藏</text></view><view class="btn data-v-2f42cdbb"><block wx:if="{{goodsDetail.category.id!==391088}}"><u-button class="half-l data-v-2f42cdbb" vue-id="{{('f01c2a68-15')+','+('f01c2a68-1')}}" text="加入购物车" shape="circle" color="linear-gradient(90deg,#ffd01e, #ff8917)" data-event-opts="{{[['^click',[['addCart']]]]}}" bind:click="__e" bind:__l="__l"></u-button></block></view><view class="btn data-v-2f42cdbb" style="margin-left:10rpx;"><u-button class="half-r data-v-2f42cdbb" vue-id="{{('f01c2a68-16')+','+('f01c2a68-1')}}" text="立即购买" shape="circle" color="linear-gradient(90deg, #ff6034, #ee0a24)" data-event-opts="{{[['^click',[['tobuy']]]]}}" bind:click="__e" bind:__l="__l"></u-button></view></view></block></u-popup></block>