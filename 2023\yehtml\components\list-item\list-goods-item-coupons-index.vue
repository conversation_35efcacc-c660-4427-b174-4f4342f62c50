<template>
  <view class="goods-box-wrapper">
    <view class="goods-box" @click="goto">
      <u-row justify="space-between" gutter="10">
        <u-col span="4">
          <u-image width="200rpx" height="200rpx" :src="item.pic" radius="10"></u-image>
        </u-col>
        <u-col span="8">
          <view>
            <view class="goods-title-box">
              <view class="goods-title">{{ item.title }}</view>
            </view>
            <view class="goods-dsc-box">
              <view class="goods-dsc">微信转发即赠，心意一键送达</view>
            </view>
            <view class="goods-tags-box">
              <view class="goods-tags">
                <span v-for="tag in tags" :key="tag" class="tag">{{ tag }}</span>
              </view>
            </view>
          </view>
          <view class="buy-wrapper" style="display: flex;">
            <view class="price-score">
              <view>
                <u-button type="success" shape="circle" text="立即购买 > " size="small"></u-button>
              </view>
            </view>
          </view>
        </u-col>
      </u-row>
    </view>
  </view>
</template>

<script>
  export default {
    props: {
      item: {
        type: Object,
        default: {},
      },
    },
    computed: {
      tags() {
        return this.item.tag.split(',');
      },
    },
    methods: {
      goto() {
        console.log('item is', this.item)
        uni.navigateTo({
          url: `/packageFx/coupons/list?pic=${encodeURIComponent(this.item.pic)}&tag=${encodeURIComponent(this.item.tag)}&title=${encodeURIComponent(this.item.title)}&type=${encodeURIComponent(this.item.type)}`
        })
      }
    },
  }
</script>
<style>
  .goods-box-wrapper {
    margin-top: 24rpx;
    border-radius: 5px;
    padding-bottom: 10rpx;
    text-align: left;
    overflow: hidden;
  }

  .goods-box {
    padding: 0 10rpx;
  }

  .goods-box .goods-title-box {
    line-height: 40rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    word-break: break-all;
  }

  .goods-box .goods-title {
    color: #03783F;
    font-size: 40rpx;
  }

  .goods-box .goods-dsc-box {
    margin-top: 20rpx;
  }

  .goods-box .goods-dsc {
    color: #858996;
    font-size: 24rpx;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .goods-box .goods-tags span {
    font-size: 22rpx;
    font-weight: normal;
    color: #858996;
    border: 1px #D9DBDF solid;
    margin-right: 10rpx;
    border-radius: 2px;
    padding: 0 4rpx;
    line-height: 32rpx;
  }

  .goods-box .goods-price-container {
    display: flex;
    align-items: baseline;
  }

  .goods-box .goods-price {
    overflow: hidden;
    font-size: 34rpx;
    color: #F20C32;
    margin-left: 24rpx;
  }

  .goods-box .goods-price2 {
    overflow: hidden;
    font-size: 26rpx;
    color: #aaa;
    text-decoration: line-through;
    margin-left: 20rpx;
  }

  .goods-box .buy-wrapper {
    margin-top: 10rpx;
    display: flex;
    justify-content: space-between;
  }

  .goods-box .buy {
    width: 52rpx;
    height: 52rpx;
    background-color: #34B764;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 4rpx;
  }

  .goods-box .original-price {
    color: #858996;
    font-size: 20rpx;
    font-weight: 100;
    line-height: 20rpx;
  }

  .goods-box .original-price .price {
    text-decoration: line-through;
  }

  .goods-quota {
    margin-top: 20rpx;
    width: 300rpx;
  }
</style>
<style lang="scss">
  .time {
    &__custom {
      background-color: #e64340;
    }

    &__doc {
      color: #324A43;
      padding: 0px 2px;
      margin-top: 5px;
    }

    &__item {
      color: #606266;
      font-size: 10px;
      margin-right: 2px;
    }
  }
</style>