<template>
	<view class="my container-wrapper">
		<view class="userinfo">
			<UserInfo :data="apiUserInfoMap" :needLogin="needLogin"></UserInfo>
		</view>

		<view class="vip-ad" @click="goGrade">
			<span class="text1">开通会员 专属优惠专享</span>
			<span class="text2">开通会员</span>
			<img src="https://ye.niutouren.vip/static/images/my/vipad.png" />
		</view>
		<view class="main-wrapper">
			<view class="tabs" v-if="showEnterpriseTabs">
				<u-tabs :list="tabsList" @click="changeTab" lineWidth="30" lineColor="#223F36" :activeStyle="{
                    color: '#303133',
                    fontWeight: 'bold',
                    transform: 'scale(1.05)'
                }" :inactiveStyle="{
                    color: '#606266',
                    transform: 'scale(1)'
                }" itemStyle="padding-left: 15px; padding-right: 15px; height: 34px;">
				</u-tabs>
			</view>

			<view v-if="!showEnterpriseTabs || isTab1Visible">
				<view class="user-count">
					<LabelCount :ops="mineCountOps"></LabelCount>
				</view>

				<view class="order-nav">
					<DialNav :mode="10" shadow :list="orderNavOps" nameSize="24rpx" imgSize="50rpx">
						<TitleOperate padding="30rpx 30rpx 0 30rpx" showMore title="我的订单" moreLabel="全部订单"
							@clickMore="$u.route({ url: '/pages/order/index' })"></TitleOperate>
					</DialNav>
				</view>

				<view class="other-nav">
					<DialNav marginTopLine="28rpx" :mode="8" shadow :list="favNavOps" nameSize="24rpx" imgSize="40rpx">
						<TitleOperate padding="30rpx 30rpx 0 30rpx" title="收藏夹"></TitleOperate>
					</DialNav>
				</view>

				<view class="other-nav">
					<DialNav marginTopLine="28rpx" :mode="8" shadow :list="otherNavOps" nameSize="24rpx" imgSize="40rpx">
						<TitleOperate padding="30rpx 30rpx 0 30rpx" title="常用功能"></TitleOperate>
					</DialNav>
				</view>
			</view>
			<view v-if="showEnterpriseTabs && isTab2Visible">
				<view class="user-count">
					<LabelCount :ops="mineCountOps"></LabelCount>
				</view>

				<view class="order-nav">
					<DialNav :mode="10" shadow :list="orderNavOps" nameSize="24rpx" imgSize="50rpx">
						<TitleOperate padding="30rpx 30rpx 0 30rpx" showMore title="企业订单" moreLabel="全部订单"
							@clickMore="$u.route({ url: '/pages/order/index' })"></TitleOperate>
					</DialNav>
				</view>
			</view>

			<view class="copyright">桂ICP备2024044061号</view>
		</view>

		<tabbar :tabIndex="tabIndex"></tabbar>
	</view>
</template>

<script>
	import empty from 'empty-value'
	import UserInfo from '@/pages/my/components/user-info'
	import LabelCount from '@/components/nav/label-count'
	import DialNav from '@/components/nav/dial-nav'
	import tabbar from '@/components/tabbar'
	import TitleOperate from '@/components/title-operate'

	const USER = require('@/common/user.js')

	export default {
		components: {
			TitleOperate,
			UserInfo,
			LabelCount,
			DialNav,
			tabbar,
		},
		data() {
			return {
				tabIndex: 4,

				isTab1Visible: true,
				isTab2Visible: false,

				needLogin: false,
				apiUserInfoMap: undefined,
				balance: 0,
				freeze: 0,
				score: 0,
				growth: 0,
				pic: 'https://uviewui.com/common/logo.png',
				show: true,
				version: getApp().globalData.version,
				version: undefined,
				cardMyList: undefined,

				count_id_no_confirm: 0,
				count_id_no_pay: 0,
				count_id_no_reputation: 0,
				count_id_no_transfer: 0,

				mineCountOps: [{
						label: '余额',
						count: 0,
						url: '/pages/asset/balance'
					},
					{
						label: '礼品卡',
						count: 0,
						url: '/packageFx/coupons/my'
					},
					{
						label: '积分',
						count: 0,
						url: '/pages/score/index'
					}
				],
				orderNavOps: [{
						name: '待付款',
						img: require('../../static/images/my/nav/mine-order-1.png'),
						url: '/pages/order/index?status=0'
					},
					{
						name: '待发货',
						img: require('../../static/images/my/nav/mine-order-2.png'),
						url: '/pages/order/index?status=1'
					},
					{
						name: '待收货',
						img: require('../../static/images/my/nav/mine-order-3.png'),
						url: '/pages/order/index?status=2'
					},
					{
						name: '待评论',
						img: require('../../static/images/my/nav/mine-order-4.png'),
						url: '/pages/order/index?status=3'
					},
					{
						name: '退款售后',
						img: require('../../static/images/my/nav/mine-order-5.png'),
						url: '/pages/order/index?status=99'
					}
				],
				otherNavOps: [{
						name: '使用帮助',
						img: require('../../static/images/my/nav/mine-setting-1.png'),
						url: '/packageFx/help/list'
					},
					/* {
					  name: '客服电话',
					  img: require('../../static/images/my/nav/mine-setting-2.png'),
					  url: '/packageFx/about/about?key=service'
					}, */
					{
						name: '关于我们',
						img: require('../../static/images/my/nav/mine-setting-3.png'),
						url: '/packageFx/about/about?key=aboutus'
					},
					{
						name: '收货地址',
						img: require('../../static/images/my/nav/mine-setting-4.png'),
						url: '/packageFx/address/index'
					},
					{
						name: '个人设置',
						img: require('../../static/images/my/nav/mine-setting-7.png'),
						url: '/pages/my/info-menu'
					}
				],
				favNavOps: [{
						name: '我喜欢的',
						img: require('../../static/images/my/nav/mine-setting-6-1.png'),
						url: '/pages/goods/fav?type=1'
					},
					{
						name: '朋友赠送',
						img: require('../../static/images/my/nav/mine-setting-6-2.png'),
						url: '/pages/goods/fav?type=2'
					},
					{
						name: '朋友推荐',
						img: require('../../static/images/my/nav/mine-setting-6-3.png'),
						url: '/pages/goods/fav?type=3'
					}
				]
			}
		},
		onLoad(e) {
			this.version = getApp().globalData.version
		},
		onShow() {
			this._myCoupons()
			this._userDetail()
			this._getUserAmount()
			this._cardMyList()
			this._orderStatistics()
		},
		methods: {
			async _userDetail() {
				// https://www.yuque.com/apifm/nu0f75/zgf8pu
				const res = await this.$wxapi.userDetail(this.token)
				if (res.code == 2000) {
					this.needLogin = true
				}
				if (res.code == 0) {
					this.apiUserInfoMap = res.data

					// 个人合伙人入口
					if (!empty(res.data.userLevel) && res.data.userLevel.level > 4) {
						this.otherNavOps.shift()
						const itemPartner = {
							name: '个人合伙人',
							img: require('../../static/images/my/nav/partner.png'),
							url: '/packageFx/partner/share'
						};
						this.otherNavOps.unshift(itemPartner);
					}

					// 平台合伙人入口
					if (!empty(res.data.userLevel) && res.data.userLevel.level > 5) {
						this.otherNavOps.shift()
						const itemPartner = {
							name: '平台合伙人',
							img: require('../../static/images/my/nav/partner.png'),
							url: '/packageFx/partner/share'
						};
						this.otherNavOps.unshift(itemPartner);
					}
				}
			},
			async _myCoupons() {
				const res = await this.$wxapi.myCoupons({
					token: this.token,
					status: 0,
					consumAmount: ''
				})
				if (res.code == 0) {
					this.mineCountOps[1].count = res.data.length
				}
			},
			async _getUserAmount() {
				// https://www.yuque.com/apifm/nu0f75/wrqkcb
				const res = await this.$wxapi.userAmount(this.token)
				if (res.code == 2000) {
					this.needLogin = true
				}
				if (res.code == 0) {
					this.balance = res.data.balance.toFixed(2),
						this.freeze = res.data.freeze.toFixed(2),
						this.score = res.data.score,
						this.growth = res.data.growth

					this.mineCountOps[0].count = this.balance
				}
			},
			handleOrderCount(count) {
				return count > 99 ? '99+' : count;
			},
			async _orderStatistics() {
				const res = await this.$wxapi.orderStatistics(this.token)
				if (res.code == 0) {
					const {
						count_id_no_confirm,
						count_id_no_pay,
						count_id_no_reputation,
						count_id_no_transfer,
					} = res.data || {}

					this.count_id_no_confirm = this.handleOrderCount(count_id_no_confirm)
					this.count_id_no_pay = this.handleOrderCount(count_id_no_pay)
					this.count_id_no_reputation = this.handleOrderCount(count_id_no_reputation)
					this.count_id_no_transfer = this.handleOrderCount(count_id_no_transfer)

					this.orderNavOps.forEach(ele => {
						if (ele.name === '待付款') {
							this.orderNavOps[0].badgeNum = count_id_no_pay
						}
						if (ele.name === '待发货') {
							this.orderNavOps[1].badgeNum = count_id_no_transfer
						}
						if (ele.name === '待收货') {
							this.orderNavOps[2].badgeNum = count_id_no_confirm
						}
					})
				}
			},
			orderGridClick(status) {
				if (status == 99) {
					uni.navigateTo({
						url: '/pages/order/index?status=' + status
					})
				} else {
					uni.navigateTo({
						url: '/pages/order/index?status=' + status
					})
				}
			},
			go(url) {
				if (url.indexOf('switchTab:') != -1) {
					uni.switchTab({
						url: url.substring(10)
					})
				} else {
					uni.navigateTo({
						url: url
					})
				}
			},
			goGrade() {
				uni.navigateTo({
					url: '/packageFx/my/grade'
				})
			},
			clearStorage() {
				uni.clearStorageSync()
				uni.showToast({
					title: '已清除'
				})
				uni.reLaunch({
					url: '../index/index'
				})
			},
			async loginout() {
				// https://www.yuque.com/apifm/nu0f75/mg77aq
				await this.$wxapi.loginout(this.token)
				this.$u.vuex('token', '')
				this.$u.vuex('uid', '')
				uni.reLaunch({
					url: '../index/index'
				})
			},
			async _cardMyList() {
				const res = await this.$wxapi.cardMyList(this.token)
				if (res.code == 0) {
					this.cardMyList = res.data
				}
			},
			changeTab(item) {
				if (item.index === 0) {
					this.isTab1Visible = true;
					this.isTab2Visible = false;
				} else if (item.index === 1) {
					this.isTab1Visible = false;
					this.isTab2Visible = true;
				}
			},
		},
		computed: {
			showEnterpriseTabs() {
				return this.apiUserInfoMap?.userLevel?.id === 33080
			},
			tabsList() {
				if (this.showEnterpriseTabs) {
					return [{
						name: '个人',
					}, {
						name: '企业',
					}]
				}
				return []
			}
		}
	}
</script>

<style lang="scss" scoped>
	.my.container-wrapper {
		background: linear-gradient(90deg, #80a933, #446B30) fixed;
		background-position: top center, center center, bottom center;
		background-size: 100% 33.33%;
		background-repeat: no-repeat;
	}

	.tabs {
		margin-bottom: 20rpx;
	}

	.tab1 {
		display: block;
	}

	.tab2 {
		display: none;
	}

	.userinfo {
		margin-top: 80rpx;
	}

	.main-wrapper {
		position: relative;
		padding: 30rpx;
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
		position: relative;
		background: #F7F8FA;
	}

	.vip-ad {
		display: flex;
		justify-content: center;
		align-items: center;
		position: relative;
	}

	.vip-ad img {
		width: 690rpx;
		height: 128rpx;
	}

	.vip-ad .text1 {
		font-size: 28rpx;
		font-weight: bold;
		position: absolute;
		left: 70rpx;
		top: 30rpx;
		color: #55301c;
	}

	.vip-ad .text2 {
		font-size: 28rpx;
		font-weight: bold;
		position: absolute;
		right: 110rpx;
		top: 46rpx;
		color: #55301c;
	}

	.user-count,
	.order-nav,
	.community-nav,
	.other-nav {
		position: relative;
		z-index: 0;
		padding: 0 0 30rpx 0;
	}

	.copyright {
		font-size: 11px;
		color: #999;
		text-align: center;
		margin-bottom: 20px;
	}
</style>