<template>
  <view class="address-list">
    <page-box-empty v-if="!siteList" title="暂无收货记录" sub-title="赶快添加一个收货地址吧～" :show-btn="true" btn-name="立即添加" url="/packageFx/address/addSite" />
    <u-swipe-action>
      <u-swipe-action-item v-for="(res, index) in siteList" :key="res.id" :options="options" :show="res.show" :index="index" :name="index" @click="click">
        <view class="item">
          <view class="top">
            <view class="name">{{ res.linkMan }}</view>
            <view class="phone">{{ res.mobile }}</view>
            <view v-if="res.isDefault" class="tag">
              <text class="red">默认</text>
            </view>
          </view>
          <view class="bottom">
            {{ res.provinceStr }} {{ res.cityStr }} {{ res.areaStr }} {{ res.xiaoqu }} {{ res.address }}
            <view v-if="select === 1" style="flex-direction: row !important;">
              <u-button type="warning" size="small" text="选择" @click="sel(index)"></u-button>
            </view>
          </view>
        </view>
      </u-swipe-action-item>
    </u-swipe-action>
    <view class="addSite" @tap="toAddSite(-1)">
      <view class="add">
        <u-icon name="plus" color="#ffffff" class="icon" size="32rpx"></u-icon>新建收货地址
      </view>
    </view>

    <view class="tip">* 每条地址向左滑动可以编辑/删除地址</view>
  </view>
</template>

<script>
  export default {
    data() {
      return {
        select: 0,
        siteList: undefined,
        options: [{
            text: '编辑',
            style: {
              backgroundColor: '#19be6b'
            }
          },
          {
            text: '删除',
            style: {
              backgroundColor: '#dd524d'
            }
          }
        ]
      };
    },
    onLoad(e) {
      if (e.select === '1') {
        this.select = 1
      }
    },
    onShow() {
      this.getData();
    },
    methods: {
      async getData() {
        // https://www.yuque.com/apifm/nu0f75/mmte1o
        const res = await this.$wxapi.queryAddressV2({
          token: this.token
        })
        if (res.code == 0) {
          res.data.result.forEach(ele => {
            ele.show = false
          })
          this.siteList = res.data.result
        } else {
          this.siteList = null
        }

        console.log('siteList is', this.siteList);
      },
      toAddSite(index) {
        const item = index == -1 ? null : this.siteList[index]
        uni.navigateTo({
          url: '/packageFx/address/addSite?id=' + (item ? item.id : '')
        });
      },
      async click(e) {
        // console.log(e); name: props参数name的值，index: 第几个按钮被点击
        const item = this.siteList[e.name]
        if (e.index == 0) {
          // 编辑
          this.toAddSite(e.name)
        }
        if (e.index == 1) {
          // 删除
          uni.showModal({
            title: '请确认',
            content: '确定要删除吗？',
            success: res => {
              if (res.confirm) {
                this._deleteItem(item)
              }
            }
          });
        }
      },
      async _deleteItem(item) {
        // https://www.yuque.com/apifm/nu0f75/gb0a2k
        const res = await this.$wxapi.deleteAddress(this.token, item.id)
        if (res.code == 0) {
          uni.showToast({
            title: '删除成功'
          })
          this.getData();
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        }
      },
      async sel(index) {
        const item = this.siteList[index]
        // https://www.yuque.com/apifm/nu0f75/cv6gh7
        const res = await this.$wxapi.updateAddress({
          token: this.token,
          id: item.id,
          isDefault: true
        })
        if (res.code == 0) {
          uni.navigateBack()
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        }
      },
    }
  };
</script>

<style lang="scss" scoped>
  .address-list {
    .empty {
      padding-top: 120rpx;
    }
  }

  .item {
    padding: 40rpx 20rpx;

    .top {
      display: flex;
      font-weight: bold;
      font-size: 34rpx;

      .phone {
        margin-left: 60rpx;
      }

      .tag {
        display: flex;
        font-weight: normal;
        align-items: center;

        text {
          display: block;
          width: 60rpx;
          height: 34rpx;
          line-height: 34rpx;
          color: #ffffff;
          font-size: 20rpx;
          border-radius: 6rpx;
          text-align: center;
          margin-left: 30rpx;
          background-color: rgb(49, 145, 253);
        }

        .red {
          background-color: red
        }
      }
    }

    .bottom {
      display: flex;
      margin-top: 20rpx;
      font-size: 28rpx;
      justify-content: space-between;
      align-items: center;
      color: #999999;
    }
  }

  .addSite {
    display: flex;
    justify-content: space-around;
    width: 600rpx;
    line-height: 80rpx;
    position: absolute;
    bottom: 30rpx;
    left: 80rpx;
    background-color: red;
    border-radius: 60rpx;
    font-size: 28rpx;

    .add {
      display: flex;
      align-items: center;
      color: #ffffff;

      .icon {
        margin-right: 10rpx;
      }
    }
  }

  .tip {
    width: 660rpx;
    margin: 16rpx auto;
    font-size: 24rpx;
    color: #858996;
  }
</style>