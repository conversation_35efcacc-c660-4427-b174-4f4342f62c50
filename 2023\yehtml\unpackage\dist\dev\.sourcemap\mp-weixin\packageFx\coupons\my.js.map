{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/my.vue?065e", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/my.vue?491c", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/my.vue?ec81", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/my.vue?5cab", "uni-app:///packageFx/coupons/my.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/my.vue?2a8e", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/coupons/my.vue?e446"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "listCardPhysicalTip", "data", "cardPhysicalPop", "cardPhysical", "goods", "shareSendRemarksPop", "remarks", "remarksPlaceholder", "<PERSON><PERSON><PERSON><PERSON>", "sharePath", "shareImageUrl", "shareRemarks", "sharePositionIndexPop", "sharePositionOwnPop", "shareCurrentID", "shareCurrentPic", "shareCurrentName", "shareCardID", "shareCardKey", "shareCardItem", "shareCardGoodID", "tabs", "name", "status", "current", "coupons", "curItem", "couponPwd", "onLoad", "pairs", "key", "value", "cardPhysicalID", "onShow", "methods", "sendRemarks", "sendRemarksCancel", "sendRemarksInput", "cardPhysicalGet", "uni", "id", "console", "cardStatusGet", "ownPop", "HandledPop", "own", "message", "refId", "type", "action", "_couponsShareChange", "content", "currentIDGet", "couponsUse", "goodsList", "goodsId", "goodsName", "number", "pic", "price", "score", "sku", "additions", "goodsType", "kjid", "url", "onShareAppMessage", "token", "title", "path", "uid", "imageUrl", "_couponsShareOpen", "res", "_sharedMessageClose", "_sharedYes", "_sharedNo", "_cardPhysicalYes", "_cardPhysicalNo", "_sharedForward", "tabchange", "_myCoupons", "ele", "cardMessage", "getCounpon", "getApp", "item", "pwd", "icon", "showCancel", "goIndex", "goDetail", "_goods", "categoryId", "showExtJson", "pageSize", "extJsonMap", "good"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,WAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA2H;AAC3H;AACsD;AACL;AACsC;;;AAGvF;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,wEAAM;AACR,EAAE,yFAAM;AACR,EAAE,kGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,6FAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,8SAEN;AACP,KAAK;AACL;AACA,aAAa,sUAEN;AACP,KAAK;AACL;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,0TAEN;AACP,KAAK;AACL;AACA,aAAa,mTAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAA6pB,CAAgB,6qBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACqHjrB;AAAA;AAAA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAEA;AAAA,eAEA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MACAC;MAEAC;MACAC;MACAC;MACAC;MAEAC;QACAC;QACAC;MACA,GACA;QACAD;QACAC;MACA,EACA;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;;IAEA;IACA;IACA;MACA;MACA;QACA;QACA;QAAA,2CACAC;UAAA;QAAA;UAAA;YAAA;YACA;cAAA;cAAAC;cAAAC;YACA;cACAC;cACA;YACA;UACA;QAAA;UAAA;QAAA;UAAA;QAAA;MACA;IACA;IACA;MACA;IACA;IAEA;IACA;IACA;IACA;IAEA;IACA;IAEA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;MACA;QACA;MACA;MAEA;IACA;MACA;IACA;EACA;EACAC,2BAEA;EACAC;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;kBACAC;gBACA;kBACAC;kBACA;oBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;QAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAAC;gBAAAC;gBAAAC;gBACAC;gBAAA;gBAAA,OACAP;kBACAQ;kBACAC;kBACAC;gBACA;kBACA;oBACA;sBACA;sBACA;wBACA;0BACAH;wBACA;wBACA;0BACAA;wBACA;wBACA;0BACAA;wBACA;wBAEA;0BACA;wBACA;wBACA;0BACA;0BACA;wBACA;;wBAEA;wBACA;0BACA;0BACA;wBACA;sBACA;oBACA;kBACA;gBACA;cAAA;gBAAA,kCAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;AACA;AACA;AACA;AACA;IACAI;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAX;kBACAQ;kBACAC;kBACAC;gBACA;kBACA;oBACA;sBACA;sBACAV;wBACAC;wBACAQ;wBACAC;wBACAE;0BACA5B;wBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA6B;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBAEA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA;gBACAzB;gBACAA;kBACA0B;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MAEA;QACA;QACA;UACAC;UACAnB;UACAD;UACAI;YACA5B;UACA;QACA;QAEA;UACA6C;UACAC,4GACAC;UACAC;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAAC;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACA,8FACAvD;cAAA;gBADAqD;gBAEA;kBACA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAG;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;gBACApC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAqC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAEA;kBACA;gBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA;gBAAA,OACA;kBACAd;kBACA5C;gBACA;cAAA;gBAHAkD;gBAKA;kBACAhC;kBAEAgC;oBAAA;sBAAA;sBAAA;wBAAA;0BAAA;4BAAA;8BACA;gCACAS;8BACA;8BAEAA;8BACAC;8BAAA;8BAAA,OAEA;4BAAA;8BAAAA;8BACA;gCACAD;gCACAA;8BACA;8BAEA;4BAAA;4BAAA;8BAAA;0BAAA;wBAAA;sBAAA;oBAAA,CACA;oBAAA;sBAAA;oBAAA;kBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAA;kBAAA;kBAAA;gBAAA;gBACA9C;kBACA0B;gBACA;gBAAA;cAAA;gBAGA;gBAAA,MACAqB;kBAAA;kBAAA;gBAAA;gBACA;gBAAA;cAAA;gBAAA;gBAAA,OAIA;kBACA9C;kBACA2B;kBACAoB;gBACA;cAAA;gBAJAd;gBAAA,MAKAA;kBAAA;kBAAA;gBAAA;gBACA;kBACAlC;oBACA6B;oBACAoB;kBACA;gBACA;kBACAjD;oBACA6B;oBACAoB;kBACA;gBACA;gBAAA;cAAA;gBAAA,MAGAf;kBAAA;kBAAA;gBAAA;gBACAlC;kBACA6B;kBACAjB;kBACAsC;gBACA;gBAAA;cAAA;gBAAA,MAGAhB;kBAAA;kBAAA;gBAAA;gBACAlC;kBACA6B;kBACAjB;kBACAsC;gBACA;gBAAA;cAAA;gBAAA,MAGAhB;kBAAA;kBAAA;gBAAA;gBACAlC;kBACA6B;kBACAjB;kBACAsC;gBACA;gBAAA;cAAA;gBAAA,MAGAhB;kBAAA;kBAAA;gBAAA;gBACAlC;kBACA6B;kBACAjB;kBACAsC;gBACA;gBAAA;cAAA;gBAGA;kBACAlD;oBACA6B;oBACAoB;kBACA;gBACA;kBACAjD;oBACA6B;oBACAjB;oBACAsC;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACAnD;QACA0B;MACA;IACA;IACA0B;MACApD;QACA0B;MACA;IACA;IACA2B;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC,qBAEA;gBAAA;gBAAA,OACA;kBACAA;kBACAC;kBACAC;gBACA;cAAA;gBAJAtB;gBAKA;kBACAmB;kBACAxF;kBACA4F;kBAEAJ;kBACA;oBACAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCACAK;gCACAA;gCAEA;kCACA;oCACAA;oCACA;sCACAA;oCACA;oCACA;sCACAA;oCACA;oCACA;sCACAA;oCACA;oCACA;sCACAA;oCACA;kCACA;gCACA;gCAEA7F;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CACA;sBAAA;wBAAA;sBAAA;oBAAA;kBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AChjBA;AAAA;AAAA;AAAA;AAAoxC,CAAgB,wuCAAG,EAAC,C;;;;;;;;;;;ACAxyC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packageFx/coupons/my.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packageFx/coupons/my.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./my.vue?vue&type=template&id=1ac746b3&scoped=true&\"\nvar renderjs\nimport script from \"./my.vue?vue&type=script&lang=js&\"\nexport * from \"./my.vue?vue&type=script&lang=js&\"\nimport style0 from \"./my.vue?vue&type=style&index=0&id=1ac746b3&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"1ac746b3\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packageFx/coupons/my.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=template&id=1ac746b3&scoped=true&\"", "var components\ntry {\n  components = {\n    uSticky: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-sticky/u-sticky\" */ \"@/uni_modules/uview-ui/components/u-sticky/u-sticky.vue\"\n      )\n    },\n    uSubsection: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-subsection/u-subsection\" */ \"@/uni_modules/uview-ui/components/u-subsection/u-subsection.vue\"\n      )\n    },\n    pageBoxEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/page-box-empty/page-box-empty\" */ \"@/components/page-box-empty/page-box-empty.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-modal/u-modal\" */ \"@/uni_modules/uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uTextarea: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-textarea/u-textarea\" */ \"@/uni_modules/uview-ui/components/u-textarea/u-textarea.vue\"\n      )\n    },\n    uOverlay: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-overlay/u-overlay\" */ \"@/uni_modules/uview-ui/components/u-overlay/u-overlay.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = !_vm.coupons || _vm.coupons.length == 0\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <u-sticky bgColor=\"#ffffff\">\r\n      <u-subsection activeColor=\"#e64340\" :list=\"tabs\" :current=\"current\" @change=\"tabchange\"></u-subsection>\r\n    </u-sticky>\r\n    <page-box-empty v-if=\"!coupons || coupons.length == 0\" title=\"暂无礼品卡\" sub-title=\"可以去看看购买礼品卡哦～\" :show-btn=\"false\" />\r\n    <view v-if=\"current == 0\" class=\"coupons\" v-for=\"(item,index) in coupons\" :key=\"index\">\r\n      <view @click=\"goDetail(item.type)\">\r\n        <image class=\"icon\" :src=\"item.pic\"></image>\r\n        <view class=\"id\">id: {{item.id}}</view>\r\n      </view>\r\n      <view class=\"profile\">\r\n        <view class=\"name\" @click=\"goDetail(item.type)\">\r\n          <view class=\"t\">礼品卡</view>\r\n          <view class=\"n\">{{item.name}}</view>\r\n        </view>\r\n        <view class=\"price\">\r\n          <view v-if=\"item.money > 0\" class=\"amount\"><text>￥</text>{{item.money}}</view>\r\n        </view>\r\n        <view v-if=\"item.cardSend === 1\" class=\"message\">{{ item.cardMessage }}</view>\r\n        <view v-if=\"item.cardSend !== 1\" class=\"btn\" @click=\"currentIDGet(item)\">\r\n          <view style=\"margin-right: 10rpx;\">\r\n            <u-button @click=\"couponsUse(item)\" type=\"success\" shape=\"circle\" text=\"提货\" size=\"small\"></u-button>\r\n          </view>\r\n          <view>\r\n            <u-button @click=\"sendRemarks\" type=\"warning\" shape=\"circle\" text=\"赠送\" size=\"small\"></u-button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <view v-if=\"current == 1\" class=\"coupons\" v-for=\"(item,index) in coupons\" :key=\"index\">\r\n      <view>\r\n        <image class=\"icon\" :src=\"item.pic\"></image>\r\n        <view class=\"id\">id: {{item.id}}</view>\r\n      </view>\r\n      <view class=\"profile\">\r\n        <view class=\"name\">\r\n          <view class=\"t\">礼品卡</view>\r\n          <view class=\"n\">{{item.name}}</view>\r\n        </view>\r\n        <view class=\"price\">\r\n          <view v-if=\"item.money > 0\" class=\"amount\"><text>￥</text>{{item.money}}</view>\r\n        </view>\r\n        <view class=\"dsc\">\r\n          <view>{{item.remark}}</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <u-modal :show=\"shareSendRemarksPop\" negativeTop=\"100\" title=\"请输入附言\" :showConfirmButton=\"false\"\r\n      :showCancelButton=\"false\" content=\"\">\r\n      <view style=\"display: block;width: 100%\">\r\n        <view>\r\n          <u-textarea count maxlength=\"30\" border=\"surround\" @change=\"sendRemarksInput\" v-model=\"remarks\"\r\n            :placeholder=\"remarksPlaceholder\"></u-textarea>\r\n        </view>\r\n        <view style=\"margin-top: 20rpx;display: flex;\">\r\n          <u-button @click=\"onShareAppMessage\" open-type=\"share\" type=\"warning\" shape=\"circle\"\r\n            :text=\"remarksButton\"></u-button>\r\n          <view style=\"width: 20rpx;\"></view>\r\n          <u-button @click=\"sendRemarksCancel\" shape=\"circle\" text=\"取消\"></u-button>\r\n        </view>\r\n      </view>\r\n    </u-modal>\r\n    <u-overlay :show=\"cardPhysicalPop\">\r\n      <view class=\"cardPhysicalPop\">\r\n        <view style=\"position: relative;\">\r\n          <image src=\"https://ye.niutouren.vip/static/images/pop/bg-card-physical.png\" mode=\"widthFix\"></image>\r\n          <view class=\"remarks\">\r\n            <list-card-physical-tip :list=\"goods\"></list-card-physical-tip>\r\n          </view>\r\n        </view>\r\n        <view style=\"display: flex;\">\r\n          <view style=\"margin-right: 30rpx;\">\r\n            <u-button @click=\"_cardPhysicalYes\" type=\"warning\">接收</u-button>\r\n          </view>\r\n          <view>\r\n            <u-button @click=\"_cardPhysicalNo\">取消</u-button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </u-overlay>\r\n    <u-overlay :show=\"sharePositionOwnPop\">\r\n      <view class=\"sharePositionOwnPop\">\r\n        <view style=\"position: relative;\">\r\n          <image src=\"https://ye.niutouren.vip/static/images/pop/bg-gift-message.png\" mode=\"widthFix\"></image>\r\n          <view class=\"remarks\">{{ shareRemarks }}</view>\r\n          <view @click=\"_sharedMessageClose\" style=\"position: absolute;bottom: 50rpx;width: 100%;height: 80rpx;\">&nbsp;\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </u-overlay>\r\n    <u-overlay :show=\"sharePositionIndexPop\">\r\n      <view class=\"sharePositionIndexPop\">\r\n        <view style=\"position: relative;\">\r\n          <image src=\"https://ye.niutouren.vip/static/images/pop/bg-gift.png\" mode=\"widthFix\"></image>\r\n          <view class=\"remarks\">{{ shareRemarks }}</view>\r\n        </view>\r\n        <view style=\"display: flex;\">\r\n          <!-- <view style=\"margin-right: 30rpx;\">\r\n            <u-button @click=\"goDetail(shareCardGoodID)\" type=\"primary\">详情</u-button>\r\n          </view> -->\r\n          <view style=\"margin-right: 30rpx;\">\r\n            <u-button @click=\"_sharedYes\" type=\"warning\">接受</u-button>\r\n          </view>\r\n          <view style=\"margin-right: 30rpx;\">\r\n            <u-button @click=\"_sharedForward\" type=\"success\">转发</u-button>\r\n          </view>\r\n          <view>\r\n            <u-button @click=\"_sharedNo\">放弃</u-button>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </u-overlay>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import empty from 'empty-value'\r\n  import listCardPhysicalTip from '@/components/list/list-card-physical-tip.vue'\r\n  const TOOLS = require('@/common/tools')\r\n\r\n  export default {\r\n    components: {\r\n      listCardPhysicalTip\r\n    },\r\n    data() {\r\n      return {\r\n        cardPhysicalPop: false,\r\n        cardPhysical: {},\r\n        goods: [],\r\n\r\n        shareSendRemarksPop: false,\r\n        remarks: '',\r\n        remarksPlaceholder: '示例: 刘先生, 小李给您送一只土鸡试试味道',\r\n        remarksButton: '确认赠送',\r\n\r\n        sharePath: '',\r\n        shareImageUrl: '',\r\n        shareRemarks: '',\r\n\r\n        sharePositionIndexPop: false,\r\n        sharePositionOwnPop: false,\r\n        shareCurrentID: undefined,\r\n        shareCurrentPic: undefined,\r\n        shareCurrentName: undefined,\r\n\r\n        shareCardID: undefined,\r\n        shareCardKey: undefined,\r\n        shareCardItem: undefined,\r\n        shareCardGoodID: undefined,\r\n\r\n        tabs: [{\r\n            name: '可用',\r\n            status: '0'\r\n          },\r\n          {\r\n            name: '已赠',\r\n            status: '1, 2, 3'\r\n          },\r\n        ],\r\n        current: 0,\r\n        coupons: [],\r\n        curItem: undefined,\r\n        couponPwd: undefined,\r\n      }\r\n    },\r\n    onLoad(e) {\r\n      const url = decodeURIComponent(e.q)\r\n\r\n      // Card Physical id\r\n      let cardPhysicalID = null;\r\n      if (url.includes('ye.niutouren.vip/qr')) {\r\n        const queryIndex = url.indexOf('?');\r\n        if (queryIndex !== -1) {\r\n          const queryStr = url.slice(queryIndex + 1);\r\n          const pairs = queryStr.split('&');\r\n          for (let pair of pairs) {\r\n            const [key, value] = pair.split('=');\r\n            if (decodeURIComponent(key) === 'id') {\r\n              cardPhysicalID = decodeURIComponent(value);\r\n              break;\r\n            }\r\n          }\r\n        }\r\n      }\r\n      if (!empty(cardPhysicalID)) {\r\n        this.cardPhysicalGet(cardPhysicalID)\r\n      }\r\n\r\n      let cardID = e.cardID\r\n      let cardKey = e.cardKey\r\n      let remarks = e.remarks\r\n      let goodID = e.goodID\r\n\r\n      let pic = e.pic\r\n      let referrerUid = e.referrerUid ? e.referrerUid : 0\r\n\r\n      if (!empty(cardID) && !empty(cardKey)) {\r\n        this.shareCardID = cardID\r\n        this.shareCardKey = cardKey\r\n        this.shareRemarks = remarks\r\n        this.shareCardGoodID = goodID\r\n\r\n        // 用于转发\r\n        this.shareCurrentPic = pic\r\n        this.sharePath = '/packageFx/coupons/my?cardID=' + cardID + '&cardKey=' + cardKey + '&goodID=' + goodID\r\n\r\n        if (String(referrerUid) === String(this.uid)) {\r\n          this.cardStatusGet(cardID, true)\r\n        } else {\r\n          this.cardStatusGet(cardID, false, true)\r\n        }\r\n\r\n        this._myCoupons(0)\r\n      } else {\r\n        this._myCoupons(0)\r\n      }\r\n    },\r\n    onShow() {\r\n\r\n    },\r\n    methods: {\r\n      sendRemarks() {\r\n        this.shareSendRemarksPop = true\r\n      },\r\n      sendRemarksCancel() {\r\n        this.shareSendRemarksPop = false\r\n      },\r\n      sendRemarksInput(e) {\r\n        this.remarks = e\r\n      },\r\n      async cardPhysicalGet(cardPhysicalID) {\r\n        await uni.$u.http.post('https://ye.niutouren.vip/api/qr', {\r\n          id: 2,\r\n        }).then(res => {\r\n          console.log('card res is', res)\r\n          if (!empty(res.id)) {\r\n            this._goods()\r\n            this.cardPhysicalPop = true\r\n          }\r\n        })\r\n      },\r\n      async cardStatusGet(cardID, ownPop = false, HandledPop = false, own = false) {\r\n        let message = ''\r\n        await uni.$u.http.post('https://ye.niutouren.vip/api/json', {\r\n          refId: cardID,\r\n          type: 'card_share',\r\n          action: 'list',\r\n        }).then(res => {\r\n          if (res.code == 0) {\r\n            if (!empty(res.data.result)) {\r\n              const cardData = JSON.parse(res.data.result[0].content)\r\n              if (!empty(cardData.status)) {\r\n                if (cardData.status === -1) {\r\n                  message = '该礼品卡已被拒绝'\r\n                }\r\n                if (cardData.status === 1) {\r\n                  message = '该礼品卡已经赠送，对方还未接收'\r\n                }\r\n                if (cardData.status === 2) {\r\n                  message = '该礼品卡已经被领取'\r\n                }\r\n\r\n                if (HandledPop && cardData.status === 1) {\r\n                  this.sharePositionIndexPop = true\r\n                }\r\n                if (HandledPop && cardData.status !== 1) {\r\n                  this.shareRemarks = message\r\n                  this.sharePositionOwnPop = true\r\n                }\r\n\r\n                // 自己的窗口弹出\r\n                if (ownPop) {\r\n                  this.shareRemarks = message\r\n                  this.sharePositionOwnPop = true\r\n                }\r\n              }\r\n            }\r\n          }\r\n        })\r\n\r\n        return message\r\n      },\r\n      /**\r\n       * 前端无权限删除不同用户的json，只能进入后端处理\r\n       * @param {Object} status\r\n       * @param {Object} cardID\r\n       */\r\n      async _couponsShareChange(cardID, status) {\r\n        await uni.$u.http.post('https://ye.niutouren.vip/api/json', {\r\n          refId: cardID,\r\n          type: 'card_share',\r\n          action: 'list',\r\n        }).then(res => {\r\n          if (res.code == 0) {\r\n            if (!empty(res.data.result)) {\r\n              const cardData = res.data.result[0]\r\n              uni.$u.http.post('https://ye.niutouren.vip/api/json', {\r\n                id: cardData.id,\r\n                type: 'card_share',\r\n                action: 'set',\r\n                content: JSON.stringify({\r\n                  status: status\r\n                }),\r\n              })\r\n            }\r\n          }\r\n        })\r\n      },\r\n      async currentIDGet(item) {\r\n        this.shareCurrentID = item.id\r\n        this.shareCurrentPic = item.pic\r\n        this.shareCurrentName = item.name\r\n\r\n        this._couponsShareOpen()\r\n      },\r\n      async couponsUse(item) {\r\n        const goodsList = [{\r\n          goodsId: item.type,\r\n          goodsName: item.name,\r\n          number: 1,\r\n          pic: item.pic,\r\n          price: item.money,\r\n          score: 0,\r\n          sku: [],\r\n          additions: [],\r\n          goodsType: 0,\r\n          kjid: ''\r\n        }]\r\n        uni.setStorageSync('goodsList', goodsList)\r\n        uni.navigateTo({\r\n          url: '/pages/pay/order?mod=buy&card=' + item.id\r\n        })\r\n      },\r\n      onShareAppMessage(e) {\r\n        this.shareSendRemarksPop = false\r\n\r\n        if (this.sharePath !== '') {\r\n          // 赠送生成分享码成功, 在服务器做标记，1，赠送还没收\r\n          this.$wxapi.jsonSet({\r\n            token: this.token,\r\n            type: 'card_share',\r\n            refId: this.shareCurrentID,\r\n            content: JSON.stringify({\r\n              status: 1\r\n            }),\r\n          })\r\n\r\n          return {\r\n            title: this.remarks,\r\n            path: this.sharePath + '&remarks=' + this.remarks + '&pic=' + this.shareCurrentPic + '&referrerUid=' + this\r\n              .uid,\r\n            imageUrl: this.shareCurrentPic,\r\n          }\r\n        }\r\n      },\r\n      async _couponsShareOpen() {\r\n        const res = await this.$wxapi.couponsShareOpen(this.token, this.shareCurrentID)\r\n        if (res.code == 0) {\r\n          this.sharePath = '/packageFx/coupons/my?cardID=' + this.shareCurrentID + '&cardKey=' + res.data\r\n        }\r\n      },\r\n      async _sharedMessageClose() {\r\n        this.sharePositionOwnPop = false\r\n      },\r\n      async _sharedYes() {\r\n        const res = await this.$wxapi.couponsShareFetch(this.token, this.shareCardID, this.shareCardKey, this\r\n          .shareCardGoodID)\r\n        if (res.code == 0) {\r\n          this.sharePositionIndexPop = false\r\n          this._couponsShareChange(this.shareCardID, 2)\r\n          this._myCoupons(0)\r\n        }\r\n      },\r\n      async _sharedNo() {\r\n        this.sharePositionIndexPop = false\r\n        this._couponsShareChange(this.shareCardID, -1)\r\n      },\r\n      async _cardPhysicalYes() {\r\n        console.log('is ok')\r\n      },\r\n      async _cardPhysicalNo() {\r\n        this.cardPhysicalPop = false\r\n      },\r\n      async _sharedForward() {\r\n        this.sharePositionIndexPop = false\r\n        this.remarksPlaceholder = '转发：代我接收该卡，填写地址就可以领取'\r\n        this.remarksButton = '确认转发'\r\n        this.sendRemarks()\r\n      },\r\n      async tabchange(e) {\r\n        this.current = e\r\n\r\n        if (this.current == 0) {\r\n          this._myCoupons(0)\r\n        }\r\n        if (this.current == 1) {\r\n          this._myCoupons('1, 2, 3')\r\n        }\r\n      },\r\n      async _myCoupons(status) {\r\n        this.coupons = null\r\n        const res = await this.$wxapi.myCoupons({\r\n          token: this.token,\r\n          status\r\n        })\r\n\r\n        if (res.code == 0) {\r\n          console.log('coupons is', res.data)\r\n\r\n          res.data.forEach(async ele => {\r\n            if (ele.dateEnd) {\r\n              ele.dateEnd = ele.dateEnd.split('')[0]\r\n            }\r\n\r\n            ele.cardSend = 0\r\n            let cardMessage = ''\r\n\r\n            cardMessage = await this.cardStatusGet(ele.id)\r\n            if (cardMessage !== '') {\r\n              ele.cardMessage = cardMessage\r\n              ele.cardSend = 1\r\n            }\r\n\r\n            this.coupons = res.data\r\n          })\r\n        }\r\n      },\r\n      async getCounpon(item, pwd) {\r\n        if (!await getApp().checkHasLoginedH5()) {\r\n          uni.navigateTo({\r\n            url: \"/pages/login/login\"\r\n          })\r\n          return\r\n        }\r\n        this.curItem = item\r\n        if (item.pwd && !pwd) {\r\n          this.couponPwd = ''\r\n          return\r\n        }\r\n        // https://www.yuque.com/apifm/nu0f75/dhxcpu\r\n        const res = await this.$wxapi.fetchCoupons({\r\n          id: item.id,\r\n          token: this.token,\r\n          pwd: this.couponPwd ? this.couponPwd : ''\r\n        })\r\n        if (res.code == 700) {\r\n          if (pwd) {\r\n            uni.showToast({\r\n              title: '口令输入有误',\r\n              icon: 'none'\r\n            })\r\n          } else {\r\n            uni.showToast({\r\n              title: '礼品卡不存在',\r\n              icon: 'none'\r\n            })\r\n          }\r\n          return;\r\n        }\r\n        if (res.code == 20001 || res.code == 20002) {\r\n          uni.showModal({\r\n            title: '错误',\r\n            content: '来晚了',\r\n            showCancel: false\r\n          })\r\n          return;\r\n        }\r\n        if (res.code == 20003) {\r\n          uni.showModal({\r\n            title: '错误',\r\n            content: '你领过了，别贪心哦~',\r\n            showCancel: false\r\n          })\r\n          return;\r\n        }\r\n        if (res.code == 30001) {\r\n          uni.showModal({\r\n            title: '错误',\r\n            content: '您的积分不足',\r\n            showCancel: false\r\n          })\r\n          return;\r\n        }\r\n        if (res.code == 20004) {\r\n          uni.showModal({\r\n            title: '错误',\r\n            content: '已过期~',\r\n            showCancel: false\r\n          })\r\n          return;\r\n        }\r\n        if (res.code == 0) {\r\n          uni.showToast({\r\n            title: '领取成功',\r\n            icon: 'success'\r\n          })\r\n        } else {\r\n          uni.showModal({\r\n            title: '错误',\r\n            content: res.msg,\r\n            showCancel: false\r\n          })\r\n        }\r\n      },\r\n      goIndex() {\r\n        uni.switchTab({\r\n          url: '../index/index'\r\n        })\r\n      },\r\n      goDetail(id) {\r\n        uni.navigateTo({\r\n          url: '/pages/goods/detail?id=' + id + '&card=1'\r\n        })\r\n      },\r\n      async _goods() {\r\n        let categoryId = 463740\r\n\r\n        // https://www.yuque.com/apifm/nu0f75/wg5t98\r\n        const res = await this.$wxapi.goodsv2({\r\n          categoryId: categoryId,\r\n          showExtJson: true,\r\n          pageSize: 1\r\n        })\r\n        if (res.code == 0) {\r\n          let _goods = []\r\n          let goods = []\r\n          let extJsonMap = []\r\n\r\n          _goods = res.data.result\r\n          if (!empty(_goods)) {\r\n            _goods.forEach(async (good, index) => {\r\n              good.image = good.pic\r\n              good.title = good.name\r\n\r\n              if (!empty(res.data.extJsonMap)) {\r\n                if (good.id in res.data.extJsonMap) {\r\n                  good.ext = res.data.extJsonMap[good.id]\r\n                  if (!empty(good.ext['deadline'])) {\r\n                    good.ext['deadline'] = TOOLS.translateTimeDifference(good.ext['deadline'])\r\n                  }\r\n                  if (good.ext['max_total']) {\r\n                    good.ext['max_total'] = good.ext['max_total']\r\n                  }\r\n                  if (good.ext['min_total']) {\r\n                    good.ext['min_total'] = good.ext['min_total']\r\n                  }\r\n                  if (good.ext['shichiPrice']) {\r\n                    good.ext['shichiPrice'] = good.ext['shichiPrice']\r\n                  }\r\n                }\r\n              }\r\n\r\n              goods.push(good)\r\n            })\r\n          }\r\n          this.goods = goods\r\n        }\r\n      },\r\n    }\r\n  }\r\n</script>\r\n<style scoped lang=\"scss\">\r\n  .coupons {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    margin-top: 24rpx;\r\n    margin-left: 24rpx;\r\n    margin-bottom: 24rpx;\r\n    width: 702rpx;\r\n    min-height: 268rpx;\r\n    background-color: #FFFFFF;\r\n    box-shadow: 0 0 16rpx 0 rgba(36, 44, 69, 0.20);\r\n    border-radius: 8rpx;\r\n  }\r\n\r\n  .coupons .icon {\r\n    margin-left: 64rpx;\r\n    margin-top: 32rpx;\r\n    width: 160rpx;\r\n    height: 160rpx;\r\n    border-radius: 10rpx;\r\n  }\r\n\r\n  .coupons .id {\r\n    font-size: 20rpx;\r\n    color: #999;\r\n    margin-left: 64rpx;\r\n  }\r\n\r\n  .coupons .message {\r\n    font-size: 20rpx;\r\n    color: #999;\r\n    margin-top: 10rpx;\r\n  }\r\n\r\n  .coupons .profile {\r\n    margin-left: 10rpx;\r\n    padding-bottom: 20rpx;\r\n    width: 100%;\r\n  }\r\n\r\n  .coupons .profile .name {\r\n    display: flex;\r\n    margin-top: 32rpx;\r\n  }\r\n\r\n  .coupons .profile .name .t {\r\n    width: 80rpx;\r\n    height: 30rpx;\r\n    background: #FEB21C;\r\n    border-radius: 4rpx;\r\n\r\n    font-family: PingFangSC-Medium;\r\n    font-size: 20rpx;\r\n    color: #FFFFFF;\r\n    letter-spacing: 0;\r\n    line-height: 30rpx;\r\n    text-align: center;\r\n  }\r\n\r\n  .coupons .profile .name .n {\r\n    margin-left: 16rpx;\r\n    margin-right: 24rpx;\r\n    font-family: PingFangSC-Medium;\r\n    font-size: 30rpx;\r\n    color: #333333;\r\n    letter-spacing: 0;\r\n    line-height: 30rpx;\r\n  }\r\n\r\n  .coupons .profile .price {\r\n    display: flex;\r\n    align-items: baseline;\r\n    margin-top: 24rpx;\r\n  }\r\n\r\n  .coupons .profile .price .tj {\r\n    font-family: PingFangSC-Regular;\r\n    font-size: 20rpx;\r\n    color: #999999;\r\n    letter-spacing: 0;\r\n    line-height: 20rpx;\r\n  }\r\n\r\n  .coupons .profile .price .amount {\r\n    font-family: PingFangSC-Medium;\r\n    font-size: 56rpx;\r\n    color: #FEB21C;\r\n    letter-spacing: 0;\r\n    line-height: 56rpx;\r\n    margin-right: 24rpx;\r\n  }\r\n\r\n  .coupons .profile .price .amount text {\r\n    margin-left: 16rpx;\r\n    font-family: PingFangSC-Regular;\r\n    font-size: 20rpx;\r\n    color: #FEB21C;\r\n    letter-spacing: 0;\r\n    line-height: 20rpx;\r\n  }\r\n\r\n  .disabled1 {\r\n    background: #999999 !important;\r\n    color: #FFFFFF !important;\r\n  }\r\n\r\n  .disabled2 {\r\n    color: #999999 !important;\r\n  }\r\n\r\n  .coupons .profile .btn {\r\n    display: flex;\r\n    flex-direction: row !important;\r\n    width: 30rpx;\r\n    margin-top: 20rpx;\r\n  }\r\n\r\n  .bottom {\r\n    width: 100vw;\r\n    height: 24rpx;\r\n  }\r\n\r\n\r\n  .pwd-coupons-mask {\r\n    position: fixed;\r\n    width: 100vw;\r\n    height: 100vh;\r\n    background: rgba(0, 0, 0, 0.3);\r\n    top: 0;\r\n    left: 0;\r\n  }\r\n\r\n  .pwd-coupons {\r\n    position: fixed;\r\n    top: 300rpx;\r\n    left: 100rpx;\r\n    width: 550rpx;\r\n    background: #fff;\r\n    border-radius: 12rpx;\r\n  }\r\n\r\n  .pwd-coupons .t {\r\n    margin-top: 32rpx;\r\n    text-align: center;\r\n    font-size: 32rpx;\r\n    font-weight: bold;\r\n  }\r\n\r\n  .pwd-coupons .input {\r\n    margin: 32rpx;\r\n    border: 1rpx solid #666;\r\n    border-radius: 8rpx;\r\n    height: 88rpx;\r\n    line-height: 88rpx;\r\n  }\r\n\r\n  .pwd-coupons button {\r\n    margin: 32rpx;\r\n  }\r\n\r\n  .koulingcoupon {\r\n    margin-top: 32rpx;\r\n  }\r\n\r\n  .block-btn {\r\n    margin: 32rpx 0;\r\n  }\r\n\r\n  .hecheng {\r\n    margin-top: 16rpx;\r\n  }\r\n\r\n  .sharePositionIndexPop {\r\n    width: 100vw;\r\n    height: 100vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n\r\n  .sharePositionIndexPop .remarks {\r\n    position: absolute;\r\n    top: 280rpx;\r\n    left: 70rpx;\r\n    color: #333333;\r\n    font-size: 34rpx;\r\n    width: 300rpx;\r\n    height: 200rpx;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .sharePositionIndexPop image {\r\n    width: 420rpx;\r\n  }\r\n\r\n  .sharePositionIndexPop .close {\r\n    margin-top: 32rpx;\r\n  }\r\n\r\n  .sharePositionOwnPop {\r\n    width: 100vw;\r\n    height: 100vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n\r\n  .sharePositionOwnPop .remarks {\r\n    position: absolute;\r\n    top: 250rpx;\r\n    left: 70rpx;\r\n    color: #333333;\r\n    font-size: 34rpx;\r\n    width: 300rpx;\r\n    height: 200rpx;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .sharePositionOwnPop image {\r\n    width: 420rpx;\r\n  }\r\n\r\n  .sharePositionOwnPop .close {\r\n    margin-top: 32rpx;\r\n  }\r\n\r\n  .sharePositionIndexPop {\r\n    width: 100vw;\r\n    height: 100vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n\r\n  .sharePositionIndexPop .remarks {\r\n    position: absolute;\r\n    top: 280rpx;\r\n    left: 70rpx;\r\n    color: #333333;\r\n    font-size: 34rpx;\r\n    width: 300rpx;\r\n    height: 200rpx;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .sharePositionIndexPop image {\r\n    width: 420rpx;\r\n  }\r\n\r\n  .sharePositionIndexPop .close {\r\n    margin-top: 32rpx;\r\n  }\r\n\r\n  .cardPhysicalPop {\r\n    width: 100vw;\r\n    height: 100vh;\r\n    display: flex;\r\n    flex-direction: column;\r\n    justify-content: center;\r\n    align-items: center;\r\n  }\r\n\r\n  .cardPhysicalPop .remarks {\r\n    position: absolute;\r\n    top: 330rpx;\r\n    left: 70rpx;\r\n    color: #333333;\r\n    font-size: 34rpx;\r\n    width: 560rpx;\r\n    height: 380rpx;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .cardPhysicalPop image {\r\n    width: 700rpx;\r\n  }\r\n\r\n  .cardPhysicalPop .close {\r\n    margin-top: 32rpx;\r\n  }\r\n\r\n  .dsc {\r\n    font-size: 12px;\r\n    margin-top: 20rpx;\r\n    color: #333;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=style&index=0&id=1ac746b3&scoped=true&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./my.vue?vue&type=style&index=0&id=1ac746b3&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692284336\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}