(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/list/list2"],{"2aab":function(n,e,t){"use strict";t.r(e);var o=t("ed07a"),u=t.n(o);for(var i in o)["default"].indexOf(i)<0&&function(n){t.d(e,n,(function(){return o[n]}))}(i);e["default"]=u.a},"91e9":function(n,e,t){},aa71:function(n,e,t){"use strict";t.r(e);var o=t("e240"),u=t("2aab");for(var i in u)["default"].indexOf(i)<0&&function(n){t.d(e,n,(function(){return u[n]}))}(i);t("c6d3");var l=t("828b"),a=Object(l["a"])(u["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);e["default"]=a.exports},c6d3:function(n,e,t){"use strict";var o=t("91e9"),u=t.n(o);u.a},e240:function(n,e,t){"use strict";t.d(e,"b",(function(){return u})),t.d(e,"c",(function(){return i})),t.d(e,"a",(function(){return o}));var o={uRow:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-row/u-row")]).then(t.bind(null,"f632"))},uCol:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-col/u-col")]).then(t.bind(null,"44b6"))},uImage:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-image/u-image")]).then(t.bind(null,"73f7"))}},u=function(){var n=this.$createElement;this._self._c},i=[]},ed07a:function(n,e,t){"use strict";(function(n){var o=t("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;o(t("bc37"));var u={components:{listGoodsItem2:function(){t.e("components/list-item/list-goods-item2").then(function(){return resolve(t("2172"))}.bind(null,t)).catch(t.oe)}},props:{list:{type:Array,default:[]},type:{type:String,default:""}},onReady:function(){},data:function(){return{}},watch:{list:function(n){console.log("watch list is",n)}},methods:{imageClick:function(e){"goods"===this.type&&n.navigateTo({url:"/pages/goods/detail?id="+e.id})}}};e.default=u}).call(this,t("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/list/list2-create-component',
    {
        'components/list/list2-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("aa71"))
        })
    },
    [['components/list/list2-create-component']]
]);
