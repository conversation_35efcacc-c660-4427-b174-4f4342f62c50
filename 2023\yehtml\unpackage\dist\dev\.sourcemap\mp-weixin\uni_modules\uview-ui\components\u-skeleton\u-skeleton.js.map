{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-skeleton/u-skeleton.vue?2a04", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-skeleton/u-skeleton.vue?a559", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-skeleton/u-skeleton.vue?098f", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-skeleton/u-skeleton.vue?a6e1", "uni-app:///uni_modules/uview-ui/components/u-skeleton/u-skeleton.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-skeleton/u-skeleton.vue?7b46", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-skeleton/u-skeleton.vue?9115"], "names": ["name", "mixins", "data", "width", "watch", "loading", "computed", "rowsArray", "uni", "row<PERSON>id<PERSON>", "rowHeight", "item", "rows", "uTitleWidth", "tWidth", "mounted", "methods", "init", "setNvueAnimation", "getComponentWidth"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrBA;AAAA;AAAA;AAAA;AAAqqB,CAAgB,qrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACmDzrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,eAiBA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACA;QACAC;MACA;MACA;MACA;QACA;UACA;UACAC,kHACA;UACAC;QACA;QACA;QACAC;QACA;QACA;UACA;UACAA;QACA;UACAA;QACA;QACAA;QACAC;MACA;MACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAC;MACA;QACAA;MACA;MACA;IACA;EAEA;EACAC;IACA;EACA;EACAC;IACAC;MACA;IAIA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAwBA;IACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEAX;cAAA;gBAEA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IASA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACpLA;AAAA;AAAA;AAAA;AAA4xC,CAAgB,gvCAAG,EAAC,C;;;;;;;;;;;ACAhzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-skeleton/u-skeleton.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-skeleton.vue?vue&type=template&id=3bb5bf6e&scoped=true&\"\nvar renderjs\nimport script from \"./u-skeleton.vue?vue&type=script&lang=js&\"\nexport * from \"./u-skeleton.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-skeleton.vue?vue&type=style&index=0&id=3bb5bf6e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3bb5bf6e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-skeleton/u-skeleton.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-skeleton.vue?vue&type=template&id=3bb5bf6e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.loading && _vm.avatar ? _vm.$u.addUnit(_vm.avatarSize) : null\n  var g1 = _vm.loading && _vm.avatar ? _vm.$u.addUnit(_vm.avatarSize) : null\n  var g2 = _vm.loading && _vm.title ? _vm.$u.addUnit(_vm.titleHeight) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        g1: g1,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-skeleton.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-skeleton.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"u-skeleton\">\r\n\t\t<view\r\n\t\t    class=\"u-skeleton__wrapper\"\r\n\t\t    ref=\"u-skeleton__wrapper\"\r\n\t\t    v-if=\"loading\"\r\n\t\t\tstyle=\"display: flex; flex-direction: row;\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t    class=\"u-skeleton__wrapper__avatar\"\r\n\t\t\t    v-if=\"avatar\"\r\n\t\t\t    :class=\"[`u-skeleton__wrapper__avatar--${avatarShape}`, animate && 'animate']\"\r\n\t\t\t    :style=\"{\r\n\t\t\t\t\t\theight: $u.addUnit(avatarSize),\r\n\t\t\t\t\t\twidth: $u.addUnit(avatarSize)\r\n\t\t\t\t\t}\"\r\n\t\t\t></view>\r\n\t\t\t<view\r\n\t\t\t    class=\"u-skeleton__wrapper__content\"\r\n\t\t\t    ref=\"u-skeleton__wrapper__content\"\r\n\t\t\t\tstyle=\"flex: 1;\"\r\n\t\t\t>\r\n\t\t\t\t<view\r\n\t\t\t\t    class=\"u-skeleton__wrapper__content__title\"\r\n\t\t\t\t    v-if=\"title\"\r\n\t\t\t\t    :style=\"{\r\n\t\t\t\t\t\t\twidth: uTitleWidth,\r\n\t\t\t\t\t\t\theight: $u.addUnit(titleHeight),\r\n\t\t\t\t\t\t}\"\r\n\t\t\t\t    :class=\"[animate && 'animate']\"\r\n\t\t\t\t></view>\r\n\t\t\t\t<view\r\n\t\t\t\t    class=\"u-skeleton__wrapper__content__rows\"\r\n\t\t\t\t    :class=\"[animate && 'animate']\"\r\n\t\t\t\t    v-for=\"(item, index) in rowsArray\"\r\n\t\t\t\t    :key=\"index\"\r\n\t\t\t\t    :style=\"{\r\n\t\t\t\t\t\t\t width: item.width,\r\n\t\t\t\t\t\t\t height: item.height,\r\n\t\t\t\t\t\t\t marginTop: item.marginTop\r\n\t\t\t\t\t\t}\"\r\n\t\t\t\t>\r\n\t\t\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<slot v-else />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t// #ifdef APP-NVUE\r\n\t// 由于weex为阿里的KPI业绩考核的产物，所以不支持百分比单位，这里需要通过dom查询组件的宽度\r\n\tconst dom = uni.requireNativePlugin('dom')\r\n\tconst animation = uni.requireNativePlugin('animation')\r\n\t// #endif\r\n\t/**\r\n\t * Skeleton 骨架屏\r\n\t * @description 骨架屏一般用于页面在请求远程数据尚未完成时，页面用灰色块预显示本来的页面结构，给用户更好的体验。\r\n\t * @tutorial https://www.uviewui.com/components/skeleton.html\r\n\t * @property {Boolean}\t\t\t\t\tloading\t\t是否显示骨架占位图，设置为false将会展示子组件内容 (默认 true )\r\n\t * @property {Boolean}\t\t\t\t\tanimate\t\t是否开启动画效果 (默认 true )\r\n\t * @property {String | Number}\t\t\trows\t\t段落占位图行数 (默认 0 )\r\n\t * @property {String | Number | Array}\trowsWidth\t段落占位图的宽度，可以为百分比，数值，带单位字符串等，可通过数组传入指定每个段落行的宽度 (默认 '100%' )\r\n\t * @property {String | Number | Array}\trowsHeight\t段落的高度 (默认 18 )\r\n\t * @property {Boolean}\t\t\t\t\ttitle\t\t是否展示标题占位图 (默认 true )\r\n\t * @property {String | Number}\t\t\ttitleWidth\t标题的宽度 (默认 '50%' )\r\n\t * @property {String | Number}\t\t\ttitleHeight\t标题的高度 (默认 18 )\r\n\t * @property {Boolean}\t\t\t\t\tavatar\t\t是否展示头像占位图 (默认 false )\r\n\t * @property {String | Number}\t\t\tavatarSize\t头像占位图大小 (默认 32 )\r\n\t * @property {String}\t\t\t\t\tavatarShape\t头像占位图的形状，circle-圆形，square-方形 (默认 'circle' )\r\n\t * @example <u-search placeholder=\"日照香炉生紫烟\" v-model=\"keyword\"></u-search>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-skeleton',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\twidth: 0,\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tloading() {\r\n\t\t\t\tthis.getComponentWidth()\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\trowsArray() {\r\n\t\t\t\tif (/%$/.test(this.rowsHeight)) {\r\n\t\t\t\t\tuni.$u.error('rowsHeight参数不支持百分比单位')\r\n\t\t\t\t}\r\n\t\t\t\tconst rows = []\r\n\t\t\t\tfor (let i = 0; i < this.rows; i++) {\r\n\t\t\t\t\tlet item = {},\r\n\t\t\t\t\t\t// 需要预防超出数组边界的情况\r\n\t\t\t\t\t\trowWidth = uni.$u.test.array(this.rowsWidth) ? (this.rowsWidth[i] || (i === this.row - 1 ? '70%' : '100%')) : i ===\r\n\t\t\t\t\t\tthis.rows - 1 ? '70%' : this.rowsWidth,\r\n\t\t\t\t\t\trowHeight = uni.$u.test.array(this.rowsHeight) ? (this.rowsHeight[i] || '18px') : this.rowsHeight\r\n\t\t\t\t\t// 如果有title占位图，第一个段落占位图的外边距需要大一些，如果没有title占位图，第一个段落占位图则无需外边距\r\n\t\t\t\t\t// 之所以需要这么做，是因为weex的无能，以提升性能为借口不支持css的一些伪类\r\n\t\t\t\t\titem.marginTop = !this.title && i === 0 ? 0 : this.title && i === 0 ? '20px' : '12px'\r\n\t\t\t\t\t// 如果设置的为百分比的宽度，转换为px值，因为nvue不支持百分比单位\r\n\t\t\t\t\tif (/%$/.test(rowWidth)) {\r\n\t\t\t\t\t\t// 通过parseInt提取出百分比单位中的数值部分，除以100得到百分比的小数值\r\n\t\t\t\t\t\titem.width = uni.$u.addUnit(this.width * parseInt(rowWidth) / 100)\r\n\t\t\t\t\t} else {\r\n\t\t\t\t\t\titem.width = uni.$u.addUnit(rowWidth)\r\n\t\t\t\t\t}\r\n\t\t\t\t\titem.height = uni.$u.addUnit(rowHeight)\r\n\t\t\t\t\trows.push(item)\r\n\t\t\t\t}\r\n\t\t\t\t// console.log(rows);\r\n\t\t\t\treturn rows\r\n\t\t\t},\r\n\t\t\tuTitleWidth() {\r\n\t\t\t\tlet tWidth = 0\r\n\t\t\t\tif (/%$/.test(this.titleWidth)) {\r\n\t\t\t\t\t// 通过parseInt提取出百分比单位中的数值部分，除以100得到百分比的小数值\r\n\t\t\t\t\ttWidth = uni.$u.addUnit(this.width * parseInt(this.titleWidth) / 100)\r\n\t\t\t\t} else {\r\n\t\t\t\t\ttWidth = uni.$u.addUnit(this.titleWidth)\r\n\t\t\t\t}\r\n\t\t\t\treturn uni.$u.addUnit(tWidth)\r\n\t\t\t},\r\n\t\t\t\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit() {\r\n\t\t\t\tthis.getComponentWidth()\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.loading && this.animate && this.setNvueAnimation()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tasync setNvueAnimation() {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 为了让opacity:1的状态保持一定时间，这里做一个延时\r\n\t\t\t\tawait uni.$u.sleep(500)\r\n\t\t\t\tconst skeleton = this.$refs['u-skeleton__wrapper'];\r\n\t\t\t\tskeleton && this.loading && this.animate && animation.transition(skeleton, {\r\n\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\topacity: 0.5\r\n\t\t\t\t\t},\r\n\t\t\t\t\tduration: 600,\r\n\t\t\t\t}, () => {\r\n\t\t\t\t\t// 这里无需判断是否loading和开启动画状态，因为最终的状态必须达到opacity: 1，否则可能\r\n\t\t\t\t\t// 会停留在opacity: 0.5的状态中\r\n\t\t\t\t\tanimation.transition(skeleton, {\r\n\t\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\t\topacity: 1\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t\tduration: 600,\r\n\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t// 只有在loading中时，才执行动画\r\n\t\t\t\t\t\tthis.loading && this.animate && this.setNvueAnimation()\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 获取组件的宽度\r\n\t\t\tasync getComponentWidth() {\r\n\t\t\t\t// 延时一定时间，以获取dom尺寸\r\n\t\t\t\tawait uni.$u.sleep(20)\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tthis.$uGetRect('.u-skeleton__wrapper__content').then(size => {\r\n\t\t\t\t\tthis.width = size.width\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tconst ref = this.$refs['u-skeleton__wrapper__content']\r\n\t\t\t\tref && dom.getComponentRect(ref, (res) => {\r\n\t\t\t\t\tthis.width = res.size.width\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t@mixin background {\r\n\t\t/* #ifdef APP-NVUE */\r\n\t\tbackground-color: #F1F2F4;\r\n\t\t/* #endif */\r\n\t\t/* #ifndef APP-NVUE */\r\n\t\tbackground: linear-gradient(90deg, #F1F2F4 25%, #e6e6e6 37%, #F1F2F4 50%);\r\n\t\tbackground-size: 400% 100%;\r\n\t\t/* #endif */\r\n\t}\r\n\r\n\t.u-skeleton {\r\n\t\tflex: 1;\r\n\t\t\r\n\t\t&__wrapper {\r\n\t\t\t@include flex(row);\r\n\t\t\t\r\n\t\t\t&__avatar {\r\n\t\t\t\t@include background;\r\n\t\t\t\tmargin-right: 15px;\r\n\t\t\t\r\n\t\t\t\t&--circle {\r\n\t\t\t\t\tborder-radius: 100px;\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\t&--square {\r\n\t\t\t\t\tborder-radius: 4px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\t&__content {\r\n\t\t\t\tflex: 1;\r\n\t\t\t\r\n\t\t\t\t&__rows,\r\n\t\t\t\t&__title {\r\n\t\t\t\t\t@include background;\r\n\t\t\t\t\tborder-radius: 3px;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n\r\n\t/* #ifndef APP-NVUE */\r\n\t.animate {\r\n\t\tanimation: skeleton 1.8s ease infinite\r\n\t}\r\n\r\n\t@keyframes skeleton {\r\n\t\t0% {\r\n\t\t\tbackground-position: 100% 50%\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\tbackground-position: 0 50%\r\n\t\t}\r\n\t}\r\n\r\n\t/* #endif */\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-skeleton.vue?vue&type=style&index=0&id=3bb5bf6e&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-skeleton.vue?vue&type=style&index=0&id=3bb5bf6e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688735785\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}