(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom"],{"0fcf":function(t,e,n){"use strict";n.r(e);var u=n("1258"),a=n("2088");for(var i in a)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return a[t]}))}(i);n("6f5b");var o=n("828b"),f=Object(o["a"])(a["default"],u["b"],u["c"],!1,null,"01127184",null,!1,u["a"],void 0);e["default"]=f.exports},1258:function(t,e,n){"use strict";n.d(e,"b",(function(){return u})),n.d(e,"c",(function(){return a})),n.d(e,"a",(function(){}));var u=function(){var t=this.$createElement,e=(this._self._c,this.__get_style([this.style]));this.$mp.data=Object.assign({},{$root:{s0:e}})},a=[]},2088:function(t,e,n){"use strict";n.r(e);var u=n("53ae"),a=n.n(u);for(var i in u)["default"].indexOf(i)<0&&function(t){n.d(e,t,(function(){return u[t]}))}(i);e["default"]=a.a},"53ae":function(t,e,n){"use strict";(function(t){var u=n("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var a=u(n("aa0c")),i={name:"u-safe-bottom",mixins:[t.$u.mpMixin,t.$u.mixin,a.default],data:function(){return{safeAreaBottomHeight:0,isNvue:!1}},computed:{style:function(){return t.$u.deepMerge({},t.$u.addStyle(this.customStyle))}},mounted:function(){}};e.default=i}).call(this,n("df3c")["default"])},"6f5b":function(t,e,n){"use strict";var u=n("a740"),a=n.n(u);a.a},a740:function(t,e,n){}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom-create-component',
    {
        'uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("0fcf"))
        })
    },
    [['uni_modules/uview-ui/components/u-safe-bottom/u-safe-bottom-create-component']]
]);
