(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/list/list3"],{"1f46":function(n,t,e){"use strict";var o=e("9344"),u=e.n(o);u.a},2477:function(n,t,e){"use strict";e.r(t);var o=e("9ad3"),u=e.n(o);for(var i in o)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return o[n]}))}(i);t["default"]=u.a},44324:function(n,t,e){"use strict";e.r(t);var o=e("c8e2"),u=e("2477");for(var i in u)["default"].indexOf(i)<0&&function(n){e.d(t,n,(function(){return u[n]}))}(i);e("1f46");var c=e("828b"),l=Object(c["a"])(u["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);t["default"]=l.exports},9344:function(n,t,e){},"9ad3":function(n,t,e){"use strict";(function(n){var o=e("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;o(e("bc37"));var u=e("0cdf"),i={components:{listGoodsItem3:function(){e.e("components/list-item/list-goods-item3").then(function(){return resolve(e("c5fc"))}.bind(null,e)).catch(e.oe)}},props:{list:{type:Array,default:[]},type:{type:String,default:""}},onReady:function(){},data:function(){return{}},watch:{list:function(n){console.log("watch list is",n)}},methods:{imageClick:function(t){[383898,383899,383900,383901,383902,383903,383904,383905].includes(t.categoryId)?u.softOpeningTip():n.navigateTo({url:"/pages/goods/detail?id="+t.id})}}};t.default=i}).call(this,e("df3c")["default"])},c8e2:function(n,t,e){"use strict";e.d(t,"b",(function(){return u})),e.d(t,"c",(function(){return i})),e.d(t,"a",(function(){return o}));var o={uRow:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-row/u-row")]).then(e.bind(null,"f632"))},uCol:function(){return Promise.all([e.e("common/vendor"),e.e("uni_modules/uview-ui/components/u-col/u-col")]).then(e.bind(null,"44b6"))}},u=function(){var n=this.$createElement;this._self._c},i=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/list/list3-create-component',
    {
        'components/list/list3-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("44324"))
        })
    },
    [['components/list/list3-create-component']]
]);
