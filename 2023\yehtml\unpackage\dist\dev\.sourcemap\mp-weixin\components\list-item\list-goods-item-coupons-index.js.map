{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-coupons-index.vue?a587", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-coupons-index.vue?2243", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-coupons-index.vue?8f8f", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-coupons-index.vue?f9ae", "uni-app:///components/list-item/list-goods-item-coupons-index.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-coupons-index.vue?5f91", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-coupons-index.vue?bae9", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-coupons-index.vue?35cb", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list-item/list-goods-item-coupons-index.vue?8a47"], "names": ["props", "item", "type", "default", "computed", "tags", "methods", "goto", "console", "uni", "url"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA0I;AAC1I;AACiF;AACL;AACa;AACC;;;AAG1F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,mGAAM;AACR,EAAE,wGAAM;AACR,EAAE,iHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,4GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAwrB,CAAgB,wsBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCmC5sB;EACAA;IACAC;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACvDA;AAAA;AAAA;AAAA;AAAo+B,CAAgB,u9BAAG,EAAC,C;;;;;;;;;;;ACAx/B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAuxC,CAAgB,2uCAAG,EAAC,C;;;;;;;;;;;ACA3yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/list-item/list-goods-item-coupons-index.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./list-goods-item-coupons-index.vue?vue&type=template&id=39d052a6&\"\nvar renderjs\nimport script from \"./list-goods-item-coupons-index.vue?vue&type=script&lang=js&\"\nexport * from \"./list-goods-item-coupons-index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list-goods-item-coupons-index.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./list-goods-item-coupons-index.vue?vue&type=style&index=1&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/list-item/list-goods-item-coupons-index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-coupons-index.vue?vue&type=template&id=39d052a6&\"", "var components\ntry {\n  components = {\n    uRow: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-row/u-row\" */ \"@/uni_modules/uview-ui/components/u-row/u-row.vue\"\n      )\n    },\n    uCol: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-col/u-col\" */ \"@/uni_modules/uview-ui/components/u-col/u-col.vue\"\n      )\n    },\n    uImage: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-image/u-image\" */ \"@/uni_modules/uview-ui/components/u-image/u-image.vue\"\n      )\n    },\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-coupons-index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-coupons-index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"goods-box-wrapper\">\r\n    <view class=\"goods-box\" @click=\"goto\">\r\n      <u-row justify=\"space-between\" gutter=\"10\">\r\n        <u-col span=\"4\">\r\n          <u-image width=\"200rpx\" height=\"200rpx\" :src=\"item.pic\" radius=\"10\"></u-image>\r\n        </u-col>\r\n        <u-col span=\"8\">\r\n          <view>\r\n            <view class=\"goods-title-box\">\r\n              <view class=\"goods-title\">{{ item.title }}</view>\r\n            </view>\r\n            <view class=\"goods-dsc-box\">\r\n              <view class=\"goods-dsc\">微信转发即赠，心意一键送达</view>\r\n            </view>\r\n            <view class=\"goods-tags-box\">\r\n              <view class=\"goods-tags\">\r\n                <span v-for=\"tag in tags\" :key=\"tag\" class=\"tag\">{{ tag }}</span>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"buy-wrapper\" style=\"display: flex;\">\r\n            <view class=\"price-score\">\r\n              <view>\r\n                <u-button type=\"success\" shape=\"circle\" text=\"立即购买 > \" size=\"small\"></u-button>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </u-col>\r\n      </u-row>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  export default {\r\n    props: {\r\n      item: {\r\n        type: Object,\r\n        default: {},\r\n      },\r\n    },\r\n    computed: {\r\n      tags() {\r\n        return this.item.tag.split(',');\r\n      },\r\n    },\r\n    methods: {\r\n      goto() {\r\n        console.log('item is', this.item)\r\n        uni.navigateTo({\r\n          url: `/packageFx/coupons/list?pic=${encodeURIComponent(this.item.pic)}&tag=${encodeURIComponent(this.item.tag)}&title=${encodeURIComponent(this.item.title)}&type=${encodeURIComponent(this.item.type)}`\r\n        })\r\n      }\r\n    },\r\n  }\r\n</script>\r\n<style>\r\n  .goods-box-wrapper {\r\n    margin-top: 24rpx;\r\n    border-radius: 5px;\r\n    padding-bottom: 10rpx;\r\n    text-align: left;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .goods-box {\r\n    padding: 0 10rpx;\r\n  }\r\n\r\n  .goods-box .goods-title-box {\r\n    line-height: 40rpx;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    word-break: break-all;\r\n  }\r\n\r\n  .goods-box .goods-title {\r\n    color: #03783F;\r\n    font-size: 40rpx;\r\n  }\r\n\r\n  .goods-box .goods-dsc-box {\r\n    margin-top: 20rpx;\r\n  }\r\n\r\n  .goods-box .goods-dsc {\r\n    color: #858996;\r\n    font-size: 24rpx;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    white-space: nowrap;\r\n  }\r\n\r\n  .goods-box .goods-tags span {\r\n    font-size: 22rpx;\r\n    font-weight: normal;\r\n    color: #858996;\r\n    border: 1px #D9DBDF solid;\r\n    margin-right: 10rpx;\r\n    border-radius: 2px;\r\n    padding: 0 4rpx;\r\n    line-height: 32rpx;\r\n  }\r\n\r\n  .goods-box .goods-price-container {\r\n    display: flex;\r\n    align-items: baseline;\r\n  }\r\n\r\n  .goods-box .goods-price {\r\n    overflow: hidden;\r\n    font-size: 34rpx;\r\n    color: #F20C32;\r\n    margin-left: 24rpx;\r\n  }\r\n\r\n  .goods-box .goods-price2 {\r\n    overflow: hidden;\r\n    font-size: 26rpx;\r\n    color: #aaa;\r\n    text-decoration: line-through;\r\n    margin-left: 20rpx;\r\n  }\r\n\r\n  .goods-box .buy-wrapper {\r\n    margin-top: 10rpx;\r\n    display: flex;\r\n    justify-content: space-between;\r\n  }\r\n\r\n  .goods-box .buy {\r\n    width: 52rpx;\r\n    height: 52rpx;\r\n    background-color: #34B764;\r\n    border-radius: 50%;\r\n    display: flex;\r\n    justify-content: center;\r\n    align-items: center;\r\n    padding: 4rpx;\r\n  }\r\n\r\n  .goods-box .original-price {\r\n    color: #858996;\r\n    font-size: 20rpx;\r\n    font-weight: 100;\r\n    line-height: 20rpx;\r\n  }\r\n\r\n  .goods-box .original-price .price {\r\n    text-decoration: line-through;\r\n  }\r\n\r\n  .goods-quota {\r\n    margin-top: 20rpx;\r\n    width: 300rpx;\r\n  }\r\n</style>\r\n<style lang=\"scss\">\r\n  .time {\r\n    &__custom {\r\n      background-color: #e64340;\r\n    }\r\n\r\n    &__doc {\r\n      color: #324A43;\r\n      padding: 0px 2px;\r\n      margin-top: 5px;\r\n    }\r\n\r\n    &__item {\r\n      color: #606266;\r\n      font-size: 10px;\r\n      margin-right: 2px;\r\n    }\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-coupons-index.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-coupons-index.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688719665\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-coupons-index.vue?vue&type=style&index=1&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list-goods-item-coupons-index.vue?vue&type=style&index=1&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688737012\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}