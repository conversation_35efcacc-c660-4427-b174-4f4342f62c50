{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow.vue?3408", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow.vue?c1dd", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow.vue?3ac8", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow.vue?c336", "uni-app:///uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow.vue?e7e6", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow.vue?6df2"], "names": ["props", "value", "column", "type", "default", "maxColumn", "columnSpace", "image<PERSON>ey", "hideImage<PERSON>ey", "seat", "listStyle", "data", "list", "msg", "listInitStyle", "adds", "isLoaded", "curIndex", "isRefresh", "flag", "refreshDatas", "computed", "w", "m", "s1", "created", "methods", "loadImages", "uni", "src", "complete", "count", "refresh", "setTimeout", "columnValue", "change", "getMin", "mo", "getMinColumnHeight", "i", "query", "heightArr", "height", "resolve", "initValue", "minHeightRes", "c", "cIndex", "index", "o", "imgLoad", "item", "imgError", "loaded", "wapper<PERSON><PERSON>", "imageClick", "watch", "deep", "handler"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA+I;AAC/I;AAC0E;AACL;AACsC;;;AAG3G;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,4FAAM;AACR,EAAE,6GAAM;AACR,EAAE,sHAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,iHAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5DA;AAAA;AAAA;AAAA;AAAirB,CAAgB,isBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eCoCrsB;EACAA;IACAC;IACAC;MAAA;MACAC;MACAC;IACA;IACAC;MAAA;MACAF;MACAC;IACA;IACAE;MAAA;MACAH;MACAC;IACA;IACAG;MAAA;MACAJ;MACAC;IACA;IACAI;MAAA;MACAL;MACAC;IACA;IACAK;MAAA;MACAN;MACAC;IACA;IACAM;MAAA;MACAP;IACA;EACA;EACAQ;IACA;MACAA;QACAC;QACAV;QACAI;QACAC;QACAE;MACA;MACAI;MACAC;QACA;QACA;QACA;MACA;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;EACA;EACAC;IACA;IACAC;MAAA;MAAA;MACA;MACA;QAAA;MAAA;MACA;QAEAC;UACAC;UACAC;YACAC;YACA;UACA;QACA;MAWA;IACA;IACA;IACAC;MAAA;MACA;QACA;QACA;MACA;MAAA;MACAC;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;UACA;UACA;QACA;QACA;UACA;QACA;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;QACA;QACA;UACA;YACA;YACA;YACA;UACA;QACA;MACA;IACA;IACAC;MACA;MACA;MACA;QACA;UACAb;QACA;MACA;MACAc;QAAA;MAAA;MACA;IACA;IACA;IACAC;MAAA;MACA;QACA;QAAA,2BACAC;UACA;UACAC;YACAC;cAAAvC;cAAAwC;YAAA;UACA;YACA;cACAC;YACA;UACA;QAAA;QARA;UAAA;QASA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBAAA,MACAL;kBAAA;kBAAA;gBAAA;gBACA;gBACA;gBAAA,iCACA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAM;gBACAC;gBACA;gBACAA;kBAAAC;kBAAAC;kBAAAC;gBAAA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACA;IACAC;MACA;MACAC;MACA;MACA;IACA;IACA;IACAC;MACA;MACAD;MACAA;MACA;MACA;IACA;IACA;IACAE;MACA;QACA;QACA;QACA;MACA;MACA;MACA;QACA;QACA;QACA;MACA;QACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;EACAC;IACAvD;MACAwD;MACAC;QAAA;QACAzB;UACA;YACA;YACA;cACA;cACA;cACA;cACA;gBACA;cACA;YACA;cACA;YACA;UACA;QACA;MACA;IACA;IACA/B;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5RA;AAAA;AAAA;AAAA;AAAwyC,CAAgB,4vCAAG,EAAC,C;;;;;;;;;;;ACA5zC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./custom-waterfalls-flow.vue?vue&type=template&id=ddfcbb1c&scoped=true&\"\nvar renderjs\nimport script from \"./custom-waterfalls-flow.vue?vue&type=script&lang=js&\"\nexport * from \"./custom-waterfalls-flow.vue?vue&type=script&lang=js&\"\nimport style0 from \"./custom-waterfalls-flow.vue?vue&type=style&index=0&id=ddfcbb1c&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ddfcbb1c\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./custom-waterfalls-flow.vue?vue&type=template&id=ddfcbb1c&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var l1 = _vm.__map(_vm.data.column, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s0 = _vm.__get_style([_vm.s1])\n    var l0 = _vm.columnValue(index)\n    return {\n      $orig: $orig,\n      s0: s0,\n      l0: l0,\n    }\n  })\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, item2) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        item2 = _temp2.item2\n      var _temp, _temp2\n      $event.stopPropagation()\n      return _vm.wapperClick(item2)\n    }\n    _vm.e1 = function ($event, item2, index) {\n      var _temp3 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp4 = _temp3.eventParams || _temp3[\"event-params\"],\n        item2 = _temp4.item2,\n        index = _temp4.index\n      var _temp3, _temp4\n      return _vm.imgLoad(item2, index + 1)\n    }\n    _vm.e2 = function ($event, item2, index) {\n      var _temp5 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp6 = _temp5.eventParams || _temp5[\"event-params\"],\n        item2 = _temp6.item2,\n        index = _temp6.index\n      var _temp5, _temp6\n      return _vm.imgError(item2, index + 1)\n    }\n    _vm.e3 = function ($event, item2) {\n      var _temp7 = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp8 = _temp7.eventParams || _temp7[\"event-params\"],\n        item2 = _temp8.item2\n      var _temp7, _temp8\n      $event.stopPropagation()\n      return _vm.imageClick(item2)\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        l1: l1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./custom-waterfalls-flow.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./custom-waterfalls-flow.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view class=\"waterfalls-flow\">\r\n\t\t<view v-for=\"(item,index) in data.column\" :key=\"index\" class=\"waterfalls-flow-column\" :id=\"`waterfalls_flow_column_${index+1}`\" :msg=\"msg\" :style=\"{'width':w,'margin-left':index==0?0:m}\">\r\n\t\t\t<view :class=\"['column-value',{'column-value-show':item2.o}]\" v-for=\"(item2,index2) in columnValue(index)\" :key=\"index2\" :style=\"[s1]\" @click.stop=\"wapperClick(item2)\">\r\n\t\t\t\t<view class=\"inner\" v-if=\"data.seat==1\">\r\n\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t\t\t<!-- #ifdef VUE2 -->\r\n\t\t\t\t\t<slot name=\"slot{{item2.index}}\"></slot>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifdef VUE3 -->\r\n\t\t\t\t\t<slot :name=\"`slot${item2.index}`\"></slot>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef MP-WEIXIN -->\r\n\t\t\t\t\t<slot v-bind=\"item2\"></slot>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</view>\r\n\t\t\t\t<image :class=\"['img',{'img-hide':item2[hideImageKey]==true||item2[hideImageKey]==1},{'img-error':!item2[data.imageKey]}]\" :src=\"item2[data.imageKey]\" mode=\"widthFix\" @load=\"imgLoad(item2,index+1)\" @error=\"imgError(item2,index+1)\" @click.stop=\"imageClick(item2)\"></image>\r\n\t\t\t\t<view class=\"inner\" v-if=\"data.seat==2\">\r\n\t\t\t\t\t<!-- #ifdef MP-WEIXIN -->\r\n\t\t\t\t\t<!-- #ifdef VUE2 -->\r\n\t\t\t\t\t<slot name=\"slot{{item2.index}}\"></slot>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifdef VUE3 -->\r\n\t\t\t\t\t<slot :name=\"`slot${item2.index}`\"></slot>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t<!-- #ifndef MP-WEIXIN -->\r\n\t\t\t\t\t<slot v-bind=\"item2\"></slot>\r\n\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\texport default {\r\n\t\tprops: {\r\n\t\t\tvalue: Array,\r\n\t\t\tcolumn: { // 列的数量 \r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 2\r\n\t\t\t},\r\n\t\t\tmaxColumn: { // 最大列数 \r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 5\r\n\t\t\t},\r\n\t\t\tcolumnSpace: { // 列之间的间距 百分比\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 2\r\n\t\t\t},\r\n\t\t\timageKey: { // 图片key\r\n\t\t\t\ttype: [String],\r\n\t\t\t\tdefault: 'image'\r\n\t\t\t},\r\n\t\t\thideImageKey: { // 隐藏图片key\r\n\t\t\t\ttype: [String],\r\n\t\t\t\tdefault: 'hide'\r\n\t\t\t},\r\n\t\t\tseat: { // 文本的位置，1图片之上 2图片之下\r\n\t\t\t\ttype: [String, Number],\r\n\t\t\t\tdefault: 2\r\n\t\t\t},\r\n\t\t\tlistStyle: { // 单个展示项的样式：eg:{'background':'red'}\r\n\t\t\t\ttype: Object\r\n\t\t\t}\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tdata: {\r\n\t\t\t\t\tlist: this.value ? this.value : [],\r\n\t\t\t\t\tcolumn: this.column < 2 ? 2 : this.column,\r\n\t\t\t\t\tcolumnSpace: this.columnSpace <= 5 ? this.columnSpace : 5,\r\n\t\t\t\t\timageKey: this.imageKey,\r\n\t\t\t\t\tseat: this.seat\r\n\t\t\t\t},\r\n\t\t\t\tmsg: 0,\r\n\t\t\t\tlistInitStyle: {\r\n\t\t\t\t\t'border-radius': '12rpx',\r\n\t\t\t\t\t'margin-bottom': '20rpx',\r\n\t\t\t\t\t'background-color': '#fff'\r\n\t\t\t\t},\r\n\t\t\t\tadds: [], //预置数据\r\n\t\t\t\tisLoaded: true,\r\n\t\t\t\tcurIndex: 0,\r\n\t\t\t\tisRefresh: true,\r\n\t\t\t\tflag: false,\r\n\t\t\t\trefreshDatas: []\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 计算列宽\r\n\t\t\tw() {\r\n\t\t\t\tconst column_rate = `${100 / this.data.column - (+this.data.columnSpace)}%`;\r\n\t\t\t\treturn column_rate;\r\n\t\t\t},\r\n\t\t\t// 计算margin\r\n\t\t\tm() {\r\n\t\t\t\tconst column_margin = `${(100-(100 / this.data.column - (+this.data.columnSpace)).toFixed(5)*this.data.column)/(this.data.column-1)}%`;\r\n\t\t\t\treturn column_margin;\r\n\t\t\t},\r\n\t\t\t// list样式\r\n\t\t\ts1() {\r\n\t\t\t\treturn { ...this.listInitStyle, ...this.listStyle };\r\n\t\t\t}\r\n\t\t},\r\n\t\tcreated() {\r\n\t\t\t// 初始化\r\n\t\t\tthis.refresh();\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 预加载图片\r\n\t\t\tloadImages(idx = 0) {\r\n\t\t\t\tlet count = 0;\r\n\t\t\t\tconst newList = this.data.list.filter((item, index) => index >= idx);\r\n\t\t\t\tfor (let i = 0; i < newList.length; i++) {\r\n\t\t\t\t\t// #ifndef APP-PLUS\r\n\t\t\t\t\tuni.getImageInfo({\r\n\t\t\t\t\t\tsrc: `${newList[i][this.imageKey]}.jpg`,\r\n\t\t\t\t\t\tcomplete: res => {\r\n\t\t\t\t\t\t\tcount++;\r\n\t\t\t\t\t\t\tif (count == newList.length) this.initValue(idx);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifdef APP-PLUS\r\n\t\t\t\t\tplus.io.getImageInfo({\r\n\t\t\t\t\t\tsrc: `${newList[i][this.imageKey]}.jpg`,\r\n\t\t\t\t\t\tcomplete: res => {\r\n\t\t\t\t\t\t\tcount++;\r\n\t\t\t\t\t\t\tif (count == newList.length) this.initValue(idx);\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t})\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 刷新\r\n\t\t\trefresh() {\r\n\t\t\t\tif (!this.isLoaded) {\r\n\t\t\t\t\tthis.refreshDatas = this.value;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t};\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.refreshDatas = [];\r\n\t\t\t\t\tthis.isRefresh = true;\r\n\t\t\t\t\tthis.adds = [];\r\n\t\t\t\t\tthis.data.list = this.value ? this.value : [];\r\n\t\t\t\t\tthis.data.column = this.column < 2 ? 2 : this.column >= this.maxColumn ? this.maxColumn : this.column;\r\n\t\t\t\t\tthis.data.columnSpace = this.columnSpace <= 5 ? this.columnSpace : 5;\r\n\t\t\t\t\tthis.data.imageKey = this.imageKey;\r\n\t\t\t\t\tthis.data.seat = this.seat;\r\n\t\t\t\t\tthis.curIndex = 0;\r\n\t\t\t\t\t// 每列的数据初始化\r\n\t\t\t\t\tfor (let i = 1; i <= this.data.column; i++) {\r\n\t\t\t\t\t\tthis.data[`column_${i}_values`] = [];\r\n\t\t\t\t\t\tthis.msg++;\r\n\t\t\t\t\t}\r\n\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\tthis.initValue(this.curIndex, 'refresh==>');\r\n\t\t\t\t\t})\r\n\t\t\t\t}, 1)\r\n\t\t\t},\r\n\t\t\tcolumnValue(index) {\r\n\t\t\t\treturn this.data[`column_${index+1}_values`];\r\n\t\t\t},\r\n\t\t\tchange(newValue) {\r\n\t\t\t\tfor (let i = 0; i < this.data.list.length; i++) {\r\n\t\t\t\t\tconst cv = this.data[`column_${this.data.list[i].column}_values`];\r\n\t\t\t\t\tfor (let j = 0; j < cv.length; j++) {\r\n\t\t\t\t\t\tif (newValue[i] && i === cv[j].index) {\r\n\t\t\t\t\t\t\tthis.data[`column_${this.data.list[i].column}_values`][j] = Object.assign(cv[j], newValue[i]);\r\n\t\t\t\t\t\t\tthis.msg++;\r\n\t\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tgetMin(a, s) {\r\n\t\t\t\tlet m = a[0][s];\r\n\t\t\t\tlet mo = a[0];\r\n\t\t\t\tfor (var i = a.length - 1; i >= 0; i--) {\r\n\t\t\t\t\tif (a[i][s] < m) {\r\n\t\t\t\t\t\tm = a[i][s];\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\tmo = a.filter(i => i[s] == m);\r\n\t\t\t\treturn mo[0];\r\n\t\t\t},\r\n\t\t\t// 计算每列的高度\r\n\t\t\tgetMinColumnHeight() {\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tconst heightArr = [];\r\n\t\t\t\t\tfor (let i = 1; i <= this.data.column; i++) {\r\n\t\t\t\t\t\tconst query = uni.createSelectorQuery().in(this);\r\n\t\t\t\t\t\tquery.select(`#waterfalls_flow_column_${i}`).boundingClientRect(data => {\r\n\t\t\t\t\t\t\theightArr.push({ column: i, height: data.height });\r\n\t\t\t\t\t\t}).exec(() => {\r\n\t\t\t\t\t\t\tif (this.data.column <= heightArr.length) {\r\n\t\t\t\t\t\t\t\tresolve(this.getMin(heightArr, 'height'));\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t});\r\n\t\t\t\t\t}\r\n\t\t\t\t})\r\n\t\t\t},\r\n\t\t\tasync initValue(i, from) {\r\n\t\t\t\tthis.isLoaded = false;\r\n\t\t\t\tif (i >= this.data.list.length || this.refreshDatas.length) {\r\n\t\t\t\t\tthis.msg++;\r\n\t\t\t\t\tthis.loaded();\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tconst minHeightRes = await this.getMinColumnHeight();\r\n\t\t\t\tconst c = this.data[`column_${minHeightRes.column}_values`];\r\n\t\t\t\tthis.data.list[i].column = minHeightRes.column;\r\n\t\t\t\tc.push({ ...this.data.list[i], cIndex: c.length, index: i, o: 0 });\r\n\t\t\t\tthis.msg++;\r\n\t\t\t},\r\n\t\t\t// 图片加载完成\r\n\t\t\timgLoad(item, c) {\r\n\t\t\t\tconst i = item.index;\r\n\t\t\t\titem.o = 1;\r\n\t\t\t\tthis.$set(this.data[`column_${c}_values`], item.cIndex, JSON.parse(JSON.stringify(item)));\r\n\t\t\t\tthis.initValue(i + 1);\r\n\t\t\t},\r\n\t\t\t// 图片加载失败\r\n\t\t\timgError(item, c) {\r\n\t\t\t\tconst i = item.index;\r\n\t\t\t\titem.o = 1;\r\n\t\t\t\titem[this.data.imageKey] = null;\r\n\t\t\t\tthis.$set(this.data[`column_${c}_values`], item.cIndex, JSON.parse(JSON.stringify(item)));\r\n\t\t\t\tthis.initValue(i + 1);\r\n\t\t\t},\r\n\t\t\t// 渲染结束\r\n\t\t\tloaded() {\r\n\t\t\t\tif (this.refreshDatas.length) {\r\n\t\t\t\t\tthis.isLoaded = true;\r\n\t\t\t\t\tthis.refresh();\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tthis.curIndex = this.data.list.length;\r\n\t\t\t\tif (this.adds.length) {\r\n\t\t\t\t\tthis.data.list = this.adds[0];\r\n\t\t\t\t\tthis.adds.splice(0, 1);\r\n\t\t\t\t\tthis.initValue(this.curIndex);\r\n\t\t\t\t} else {\r\n\t\t\t\t\tif (this.data.list.length) this.$emit('loaded');\r\n\t\t\t\t\tthis.isLoaded = true;\r\n\t\t\t\t\tthis.isRefresh = false;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 单项点击事件\r\n\t\t\twapperClick(item) {\r\n\t\t\t\tthis.$emit('wapperClick', item);\r\n\t\t\t},\r\n\t\t\t// 图片点击事件\r\n\t\t\timageClick(item) {\r\n\t\t\t\tthis.$emit('imageClick', item);\r\n\t\t\t}\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\tvalue: {\r\n\t\t\t\tdeep: true,\r\n\t\t\t\thandler(newValue, oldValue) {\r\n\t\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\tif (this.isRefresh) return false;\r\n\t\t\t\t\t\t\tif (this.isLoaded) {\r\n\t\t\t\t\t\t\t\t// if (newValue.length <= this.curIndex) return this.refresh();\r\n\t\t\t\t\t\t\t\tif (newValue.length <= this.curIndex) return this.change(newValue);\r\n\t\t\t\t\t\t\t\tthis.data.list = newValue;\r\n\t\t\t\t\t\t\t\tthis.$nextTick(() => {\r\n\t\t\t\t\t\t\t\t\tthis.initValue(this.curIndex, 'watch==>');\r\n\t\t\t\t\t\t\t\t})\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.adds.push(newValue);\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t})\r\n\t\t\t\t\t}, 10)\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tcolumn(newValue) {\r\n\t\t\t\tthis.refresh();\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n<style lang=\"scss\" scoped>\r\n\t.waterfalls-flow {\r\n\t\toverflow: hidden;\r\n\r\n\t\t&-column {\r\n\t\t\tfloat: left;\r\n\t\t}\r\n\t}\r\n\r\n\t.column-value {\r\n\t\twidth: 100%;\r\n\t\tfont-size: 0;\r\n\t\toverflow: hidden;\r\n\t\ttransition: opacity .4s;\r\n\t\topacity: 0;\r\n\r\n\t\t&-show {\r\n\t\t\topacity: 1;\r\n\t\t}\r\n\r\n\t\t.inner {\r\n\t\t\tfont-size: 30rpx;\r\n\t\t}\r\n\r\n\t\t.img {\r\n\t\t\twidth: 100%;\r\n\r\n\t\t\t&-hide {\r\n\t\t\t\tdisplay: none;\r\n\t\t\t}\r\n\r\n\t\t\t&-error {\r\n\t\t\t\tbackground: #f2f2f2 url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAC4AAAAiAQMAAAAatXkPAAAABlBMVEUAAADMzMzIT8AyAAAAAXRSTlMAQObYZgAAAIZJREFUCNdlzjEKwkAUBNAfEGyCuYBkLyLuxRYW2SKlV1JSeA2tUiZg4YrLjv9PGsHqNTPMSAQuyAJgRDHSyvBPwtZoSJXakeJI9iuRLGDygdl6V0yKDtyMAeMPZySj8yfD+UapvRPj2JOwkyAooSV5IwdDjPdCPspe8LyTl9IKJvDETKKRv6vnlUasgg0fAAAAAElFTkSuQmCC) no-repeat center center;\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./custom-waterfalls-flow.vue?vue&type=style&index=0&id=ddfcbb1c&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./custom-waterfalls-flow.vue?vue&type=style&index=0&id=ddfcbb1c&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692293789\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}