@charset "UTF-8";
/**
 * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量
 * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可
 * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 
 */
/* 变量 */
view.data-v-48634e29, scroll-view.data-v-48634e29, swiper-item.data-v-48634e29 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-tabs__wrapper.data-v-48634e29 {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.u-tabs__wrapper__scroll-view-wrapper.data-v-48634e29 {
  flex: 1;
  overflow: auto hidden;
}
.u-tabs__wrapper__scroll-view.data-v-48634e29 {
  display: flex;
  flex-direction: row;
  flex: 1;
}
.u-tabs__wrapper__nav.data-v-48634e29 {
  display: flex;
  flex-direction: row;
  position: relative;
}
.u-tabs__wrapper__nav__item.data-v-48634e29 {
  padding: 0 11px;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.u-tabs__wrapper__nav__item--disabled.data-v-48634e29 {
  cursor: not-allowed;
}
.u-tabs__wrapper__nav__item__text.data-v-48634e29 {
  font-size: 15px;
  color: #606266;
}
.u-tabs__wrapper__nav__item__text--disabled.data-v-48634e29 {
  color: #c8c9cc !important;
}
.u-tabs__wrapper__nav__line.data-v-48634e29 {
  height: 3px;
  background: #3c9cff;
  width: 30px;
  position: absolute;
  bottom: 2px;
  border-radius: 100px;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  transition-duration: 300ms;
}

