<view class="data-v-d5ffd2e6"><u-grid vue-id="42a99772-1" col="2" border="{{true}}" data-event-opts="{{[['^click',[['gridClick']]]]}}" bind:click="__e" class="data-v-d5ffd2e6" bind:__l="__l" vue-slots="{{['default']}}"><block wx:for="{{rechargeSendRules}}" wx:for-item="item" wx:for-index="index"><u-grid-item vue-id="{{('42a99772-2-'+index)+','+('42a99772-1')}}" name="{{index}}" class="data-v-d5ffd2e6" bind:__l="__l" vue-slots="{{['default']}}"><view class="grid-item data-v-d5ffd2e6"><text class="grid-text data-v-d5ffd2e6"><text class="data-v-d5ffd2e6">¥</text>{{item.confine}}</text><block wx:if="{{item.send}}"><u-badge vue-id="{{('42a99772-3-'+index)+','+('42a99772-2-'+index)}}" type="error" value="{{'赠送 ¥'+item.send}}" class="data-v-d5ffd2e6" bind:__l="__l"></u-badge></block></view></u-grid-item></block></u-grid><view class="submit-btn data-v-d5ffd2e6"><u-input bind:input="__e" vue-id="42a99772-4" placeholder="请输入充值金额" type="digit" fontSize="38rpx" clearable="{{true}}" value="{{amount}}" data-event-opts="{{[['^input',[['__set_model',['','amount','$event',[]]]]]]}}" class="data-v-d5ffd2e6" bind:__l="__l"></u-input></view><view class="submit-btn data-v-d5ffd2e6"><u-button vue-id="42a99772-5" type="success" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" class="data-v-d5ffd2e6" bind:__l="__l" vue-slots="{{['default']}}">立即充值</u-button></view></view>