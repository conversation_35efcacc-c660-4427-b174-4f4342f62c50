<view class="goods-box-wrapper"><view data-event-opts="{{[['tap',[['goto',['$event']]]]]}}" class="goods-box" bindtap="__e"><u-row vue-id="003a8320-1" justify="space-between" gutter="10" bind:__l="__l" vue-slots="{{['default']}}"><u-col vue-id="{{('003a8320-2')+','+('003a8320-1')}}" span="4" bind:__l="__l" vue-slots="{{['default']}}"><u-image vue-id="{{('003a8320-3')+','+('003a8320-2')}}" width="200rpx" height="200rpx" src="{{item.pic}}" radius="10" bind:__l="__l"></u-image></u-col><u-col vue-id="{{('003a8320-4')+','+('003a8320-1')}}" span="8" bind:__l="__l" vue-slots="{{['default']}}"><view><view class="goods-title-box"><view class="goods-title">{{item.title}}</view></view><view class="goods-dsc-box"><view class="goods-dsc">微信转发即赠，心意一键送达</view></view><view class="goods-tags-box"><view class="goods-tags"><block wx:for="{{tags}}" wx:for-item="tag" wx:for-index="__i0__" wx:key="*this"><label class="tag _span">{{tag}}</label></block></view></view></view><view class="buy-wrapper" style="display:flex;"><view class="price-score"><view><u-button vue-id="{{('003a8320-5')+','+('003a8320-4')}}" type="success" shape="circle" text="立即购买 > " size="small" bind:__l="__l"></u-button></view></view></view></u-col></u-row></view></view>