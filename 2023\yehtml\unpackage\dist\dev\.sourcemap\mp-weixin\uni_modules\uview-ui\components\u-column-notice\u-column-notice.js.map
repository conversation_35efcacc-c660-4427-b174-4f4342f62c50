{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-column-notice/u-column-notice.vue?d6f7", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-column-notice/u-column-notice.vue?5ba4", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-column-notice/u-column-notice.vue?e066", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-column-notice/u-column-notice.vue?d333", "uni-app:///uni_modules/uview-ui/components/u-column-notice/u-column-notice.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-column-notice/u-column-notice.vue?5f92", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-column-notice/u-column-notice.vue?8286"], "names": ["mixins", "watch", "text", "immediate", "handler", "uni", "computed", "textStyle", "style", "vertical", "data", "index", "methods", "noticeChange", "clickHandler", "close"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAwI;AACxI;AACmE;AACL;AACsC;;;AAGpG;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,qFAAM;AACR,EAAE,sGAAM;AACR,EAAE,+GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,0GAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC5CA;AAAA;AAAA;AAAA;AAA0qB,CAAgB,0rBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC2D9rB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,eAgBA;EACAA;EACAC;IACAC;MACAC;MACAC;QACA;UACAC;QACA;MACA;IACA;EACA;EACAC;IACA;IACAC;MACA;MACAC;MACAA;MACA;IACA;IACA;IACAC;MACA,iDACA;IACA;EACA;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxHA;AAAA;AAAA;AAAA;AAAiyC,CAAgB,qvCAAG,EAAC,C;;;;;;;;;;;ACArzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-column-notice/u-column-notice.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-column-notice.vue?vue&type=template&id=3bda0f19&scoped=true&\"\nvar renderjs\nimport script from \"./u-column-notice.vue?vue&type=script&lang=js&\"\nexport * from \"./u-column-notice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-column-notice.vue?vue&type=style&index=0&id=3bda0f19&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"3bda0f19\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-column-notice/u-column-notice.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-column-notice.vue?vue&type=template&id=3bda0f19&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.textStyle])\n  var g0 = [\"link\", \"closable\"].includes(_vm.mode)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-column-notice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-column-notice.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t\tclass=\"u-notice\"\r\n\t\t@tap=\"clickHandler\"\r\n\t>\r\n\t\t<slot name=\"icon\">\r\n\t\t\t<view\r\n\t\t\t\tclass=\"u-notice__left-icon\"\r\n\t\t\t\tv-if=\"icon\"\r\n\t\t\t>\r\n\t\t\t\t<u-icon\r\n\t\t\t\t\t:name=\"icon\"\r\n\t\t\t\t\t:color=\"color\"\r\n\t\t\t\t\tsize=\"19\"\r\n\t\t\t\t></u-icon>\r\n\t\t\t</view>\r\n\t\t</slot>\r\n\t\t<swiper\r\n\t\t\t:disable-touch=\"disableTouch\"\r\n\t\t\t:vertical=\"step ? false : true\"\r\n\t\t\tcircular\r\n\t\t\t:interval=\"duration\"\r\n\t\t\t:autoplay=\"true\"\r\n\t\t\tclass=\"u-notice__swiper\"\r\n\t\t\t@change=\"noticeChange\"\r\n\t\t>\r\n\t\t\t<swiper-item\r\n\t\t\t\tv-for=\"(item, index) in text\"\r\n\t\t\t\t:key=\"index\"\r\n\t\t\t\tclass=\"u-notice__swiper__item\"\r\n\t\t\t>\r\n\t\t\t\t<text\r\n\t\t\t\t\tclass=\"u-notice__swiper__item__text u-line-1\"\r\n\t\t\t\t\t:style=\"[textStyle]\"\r\n\t\t\t\t>{{ item }}</text>\r\n\t\t\t</swiper-item>\r\n\t\t</swiper>\r\n\t\t<view\r\n\t\t\tclass=\"u-notice__right-icon\"\r\n\t\t\tv-if=\"['link', 'closable'].includes(mode)\"\r\n\t\t>\r\n\t\t\t<u-icon\r\n\t\t\t\tv-if=\"mode === 'link'\"\r\n\t\t\t\tname=\"arrow-right\"\r\n\t\t\t\t:size=\"17\"\r\n\t\t\t\t:color=\"color\"\r\n\t\t\t></u-icon>\r\n\t\t\t<u-icon\r\n\t\t\t\tv-if=\"mode === 'closable'\"\r\n\t\t\t\tname=\"close\"\r\n\t\t\t\t:size=\"16\"\r\n\t\t\t\t:color=\"color\"\r\n\t\t\t\t@click=\"close\"\r\n\t\t\t></u-icon>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t/**\r\n\t * ColumnNotice 滚动通知中的垂直滚动 内部组件\r\n\t * @description 该组件用于滚动通告场景，是其中的垂直滚动方式\r\n\t * @tutorial https://www.uviewui.com/components/noticeBar.html\r\n\t * @property {Array}\t\t\ttext \t\t\t显示的内容，字符串\r\n\t * @property {String}\t\t\ticon \t\t\t是否显示左侧的音量图标 （ 默认 'volume' ）\r\n\t * @property {String}\t\t\tmode \t\t\t通告模式，link-显示右箭头，closable-显示右侧关闭图标\r\n\t * @property {String}\t\t\tcolor \t\t\t文字颜色，各图标也会使用文字颜色 （ 默认 '#f9ae3d' ）\r\n\t * @property {String}\t\t\tbgColor \t\t背景颜色 （ 默认 '#fdf6ec' ）\r\n\t * @property {String | Number}\tfontSize\t\t字体大小，单位px  （ 默认 14 ）\r\n\t * @property {String | Number}\tspeed\t\t\t水平滚动时的滚动速度，即每秒滚动多少px(rpx)，这有利于控制文字无论多少时，都能有一个恒定的速度 （ 默认 80 ）\r\n\t * @property {Boolean}\t\t\tstep\t\t\tdirection = row时，是否使用步进形式滚动 （ 默认 false ）\r\n\t * @property {String | Number}\tduration\t\t滚动一个周期的时间长，单位ms （ 默认 1500 ）\r\n\t * @property {Boolean}\t\t\tdisableTouch\t是否禁止用手滑动切换   目前HX2.6.11，只支持App 2.5.5+、H5 2.5.5+、支付宝小程序、字节跳动小程序 （ 默认 true ）\r\n\t * @example \r\n\t */\r\n\texport default {\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\twatch: {\r\n\t\t\ttext: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(newValue, oldValue) {\r\n\t\t\t\t\tif(!uni.$u.test.array(newValue)) {\r\n\t\t\t\t\t\tuni.$u.error('noticebar组件direction为column时，要求text参数为数组形式')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 文字内容的样式\r\n\t\t\ttextStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\tstyle.color = this.color\r\n\t\t\t\tstyle.fontSize = uni.$u.addUnit(this.fontSize)\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\t// 垂直或者水平滚动\r\n\t\t\tvertical() {\r\n\t\t\t\tif (this.mode == 'horizontal') return false\r\n\t\t\t\telse return true\r\n\t\t\t},\r\n\t\t},\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tindex:0\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tnoticeChange(e){\r\n\t\t\t\tthis.index = e.detail.current\r\n\t\t\t},\r\n\t\t\t// 点击通告栏\r\n\t\t\tclickHandler() {\r\n\t\t\t\tthis.$emit('click', this.index)\r\n\t\t\t},\r\n\t\t\t// 点击关闭按钮\r\n\t\t\tclose() {\r\n\t\t\t\tthis.$emit('close')\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-notice {\r\n\t\t@include flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\r\n\t\t&__left-icon {\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-right: 5px;\r\n\t\t}\r\n\r\n\t\t&__right-icon {\r\n\t\t\tmargin-left: 5px;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t&__swiper {\r\n\t\t\theight: 16px;\r\n\t\t\t@include flex;\r\n\t\t\talign-items: center;\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t&__item {\r\n\t\t\t\t@include flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\toverflow: hidden;\r\n\r\n\t\t\t\t&__text {\r\n\t\t\t\t\tfont-size: 14px;\r\n\t\t\t\t\tcolor: $u-warning;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-column-notice.vue?vue&type=style&index=0&id=3bda0f19&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-column-notice.vue?vue&type=style&index=0&id=3bda0f19&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692293423\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}