(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/str-autocomplete/str-autocomplete"],{"26b5":function(t,e,i){},"4ba9":function(t,e,i){"use strict";i.r(e);var n=i("b630"),l=i("9ab8");for(var s in l)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return l[t]}))}(s);i("7b38");var o=i("828b"),a=Object(o["a"])(l["default"],n["b"],n["c"],!1,null,null,null,!1,n["a"],void 0);e["default"]=a.exports},"7b38":function(t,e,i){"use strict";var n=i("26b5"),l=i.n(n);l.a},"9ab8":function(t,e,i){"use strict";i.r(e);var n=i("c386"),l=i.n(n);for(var s in n)["default"].indexOf(s)<0&&function(t){i.d(e,t,(function(){return n[t]}))}(s);e["default"]=l.a},b630:function(t,e,i){"use strict";i.d(e,"b",(function(){return n})),i.d(e,"c",(function(){return l})),i.d(e,"a",(function(){}));var n=function(){var t=this.$createElement;this._self._c},l=[]},c386:function(t,e,i){"use strict";(function(t){var n=i("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var l=n(i("3b2d")),s={props:{list:{type:Array},label:{type:String,default:"label"},importvalue:{type:String},lowerAndUp:{type:String,default:"true"},highlightColor:{type:String,default:"lightcoral"}},data:function(){return{showList:[],value:"",needShow:!1}},watch:{importvalue:function(t,e){this.value=t},list:{handler:function(t,e){this.filterList(this.value)},deep:!0}},computed:{isShow:function(){return this.needShow&&this.showList.length>0}},created:function(){this.list.sort(),this.showList=this.list,this.value=this.importvalue},methods:{onInput:function(t){this.filterList(t.detail.value),this.$set(this,"value",t.detail.value),this.$emit("change",t.detail.value),t.detail.value.length>0?this.needShow=!0:this.needShow=!1},hideList:function(){var t=this;setTimeout((function(){t.needShow=!1}),300)},filterList:function(e){for(var i=[],n=0;n<this.list.length;n++){var l=this.list[n],s=this.filterString(e,l);if(s&&s.number===e.length&&i.push(s),5===i.length)break}if(0===i.length&&0!==e.length)return t.showToast({title:"没有匹配的内容",mask:!1,icon:"none",duration:1500}),!1;this.showList=i},filterString:function(t,e){var i;"string"===typeof e&&(i=e),"object"===(0,l.default)(e)&&(i=e[this.label]);for(var n=i.split(""),s=[],o=t.split(""),a="<span style=color:"+this.highlightColor+">",r=0,u=0;u<n.length;u++){var h=n[u],f=o[r];if("no"!==this.lowerAndUp&&(h=h.toLowerCase(),f&&(f=f.toLowerCase())),h===f){var c=r;s.push(a);for(var d=0;d<o.length-c;d++){var p=n[u+d]||"",v=o[d+c];if("no"!==this.lowerAndUp&&(p=p.toLowerCase(),v&&(v=v.toLowerCase())),p===v)s.push(n[u+d]),r++;else if(p!==v){s.push("</span>"),s.push(n[u+d]),u+=d;break}d+c===o.length-1&&(u+=d,s.push("</span>"))}}else s.push(n[u])}if(r>0)return{label:i,originalEle:e,number:r,showString:s.join("")}},selectThisItem:function(t){this.$set(this,"value",t.label),this.needShow=!1,this.$emit("change",t.originalEle[this.label]||t.originalEle),this.$emit("select",t.originalEle)}}};e.default=s}).call(this,i("df3c")["default"])}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/str-autocomplete/str-autocomplete-create-component',
    {
        'components/str-autocomplete/str-autocomplete-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("4ba9"))
        })
    },
    [['components/str-autocomplete/str-autocomplete-create-component']]
]);
