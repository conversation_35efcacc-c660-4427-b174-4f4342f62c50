(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["components/list/list1"],{"07bc":function(t,n,e){"use strict";e.r(n);var o=e("6e8b"),i=e.n(o);for(var u in o)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return o[t]}))}(u);n["default"]=i.a},"18de":function(t,n,e){},"2afa":function(t,n,e){"use strict";e.r(n);var o=e("a827"),i=e("07bc");for(var u in i)["default"].indexOf(u)<0&&function(t){e.d(n,t,(function(){return i[t]}))}(u);e("401f");var l=e("828b"),a=Object(l["a"])(i["default"],o["b"],o["c"],!1,null,null,null,!1,o["a"],void 0);n["default"]=a.exports},"401f":function(t,n,e){"use strict";var o=e("18de"),i=e.n(o);i.a},"6e8b":function(t,n,e){"use strict";(function(t){var o=e("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var i=o(e("bc37")),u=e("0cdf"),l={components:{listGoodsItem1:function(){e.e("components/list-item/list-goods-item1").then(function(){return resolve(e("5474"))}.bind(null,e)).catch(e.oe)},listPromotion:function(){e.e("components/list-item/list-promotion").then(function(){return resolve(e("d7df"))}.bind(null,e)).catch(e.oe)}},props:{list:{type:Array,default:[]},type:{type:String,default:""}},onReady:function(){},data:function(){return{}},watch:{list:function(t){}},methods:{imageClick:function(n){if((0,i.default)(n.promotionInsert)){[383898,383899,383900,383901,383902,383903,383904,383905].includes(n.categoryId)?u.softOpeningTip():t.navigateTo({url:"/pages/goods/detail?id="+n.id})}else{t.navigateTo({url:n.path})}}}};n.default=l}).call(this,e("df3c")["default"])},a827:function(t,n,e){"use strict";e.d(n,"b",(function(){return i})),e.d(n,"c",(function(){return u})),e.d(n,"a",(function(){return o}));var o={customWaterfallsFlow:function(){return e.e("uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow").then(e.bind(null,"872e"))}},i=function(){var t=this.$createElement;this._self._c},u=[]}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'components/list/list1-create-component',
    {
        'components/list/list1-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("2afa"))
        })
    },
    [['components/list/list1-create-component']]
]);
