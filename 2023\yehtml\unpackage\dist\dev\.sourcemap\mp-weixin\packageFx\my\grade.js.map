{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/my/grade.vue?e949", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/my/grade.vue?f705", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/my/grade.vue?2068", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/my/grade.vue?01d2", "uni-app:///packageFx/my/grade.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/my/grade.vue?c15f", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/my/grade.vue?4864", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/my/grade.vue?824f", "webpack:///X:/Data/Web/ybn/2023/yehtml/packageFx/my/grade.vue?2016"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "zbTooltip", "data", "circular", "indicatorDots", "autoplay", "interval", "duration", "indicatorColor", "indicatorActiveColor", "leftRightMargin", "current", "memberLevel", "levelId", "userDetail", "balance", "memberLevelDetail", "activeColor", "memberName", "memberEqual", "equity", "icon", "text", "desc", "position", "open", "class", "faq", "onLoad", "methods", "_userDetail", "res", "console", "userLevelBuy", "priceId", "_userLevelBuy", "pass", "uni", "title", "_getUserAmount", "_userLevelDetail", "level", "tipClick", "changeItem"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACa;AACyB;;;AAG1F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACxBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,0QAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC1CA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,grBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;;;;eC8FprB;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACAC;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,GACA;QACAH;QACAC;QACAC;QACAC;MACA,EACA;MACAC;QACAJ;QACAC;QACAC;QACAG;MACA,GACA;QACAL;QACAC;QACAC;QACAG;MACA,EACA;MACAC;QACAN;QACAC;QACAC;QACAG;MACA,GACA;QACAL;QACAC;QACAC;QACAG;MACA;IAEA;EACA;EACAE;IACA;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACA;kBAEAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;QACA;UACAC;UACA;QACA;UACAA;UACA;QACA;UACAA;UACA;MAAA;MAGA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAJ;gBAEAK;gBAAA,eACAF;gBAAA,kCACA,2BAUA,2BAUA;gBAAA;cAAA;gBAnBA;kBACAG;oBACAC;oBACAjB;kBACA;kBAEAe;gBACA;gBAAA;cAAA;gBAGA;kBACAC;oBACAC;oBACAjB;kBACA;kBAEAe;gBACA;gBAAA;cAAA;gBAGA;kBACAC;oBACAC;oBACAjB;kBACA;kBAEAe;gBACA;gBAAA;cAAA;gBAIA;kBACA;oBACAC;sBACAC;sBACAjB;oBACA;kBACA;oBACA;sBACAgB;wBACAC;wBACAjB;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAkB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAR;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAS;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAT;gBACA;kBACA;kBACAC;kBAEAS;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC,gCAEA;IACAC;MACA;MACA;MAEA;QACA;QACA;UACAtB;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA,EACA;QAEA;UACAH;UACAC;UACAC;UACAG;QACA,GACA;UACAL;UACAC;UACAC;UACAG;QACA,EACA;MACA;MACA;QACA;QACA;UACAL;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA;UACAH;UACAC;UACAC;UACAC;QACA,EACA;QAEA;UACAH;UACAC;UACAC;UACAG;QACA,GACA;UACAL;UACAC;UACAC;UACAG;QACA,EACA;MACA;MACA;QACA;QACA;UACAL;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA;UACAH;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA,GACA;UACAH;UACAC;UACAC;UACAC;QACA,EACA;QAEA;UACAH;UACAC;UACAC;UACAG;QACA,GACA;UACAL;UACAC;UACAC;UACAG;QACA,EACA;MACA;;MAEA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5bA;AAAA;AAAA;AAAA;AAA48B,CAAgB,+7BAAG,EAAC,C;;;;;;;;;;;ACAh+B;AACA,OAAO,KAAU,EAAE,kBAKd;;;;;;;;;;;;;ACNL;AAAA;AAAA;AAAA;AAAuxC,CAAgB,2uCAAG,EAAC,C;;;;;;;;;;;ACA3yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "packageFx/my/grade.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './packageFx/my/grade.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./grade.vue?vue&type=template&id=e1f4ba8e&scoped=true&\"\nvar renderjs\nimport script from \"./grade.vue?vue&type=script&lang=js&\"\nexport * from \"./grade.vue?vue&type=script&lang=js&\"\nimport style0 from \"./grade.vue?vue&type=style&index=0&lang=css&\"\nimport style1 from \"./grade.vue?vue&type=style&index=1&id=e1f4ba8e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"e1f4ba8e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"packageFx/my/grade.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./grade.vue?vue&type=template&id=e1f4ba8e&scoped=true&\"", "var components\ntry {\n  components = {\n    zbTooltip: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/zb-tooltip/components/zb-tooltip/zb-tooltip\" */ \"@/uni_modules/zb-tooltip/components/zb-tooltip/zb-tooltip.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.equity.length\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./grade.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./grade.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view>\r\n    <view class=\"grade-flex-column\">\r\n      <swiper class=\"swiper\" :circular=\"circular\" :indicator-dots=\"indicatorDots\" :autoplay=\"autoplay\" :interval=\"interval\" :duration=\"duration\" :indicator-color=\"indicatorColor\" :indicator-active-color=\"activeColor[current]\" :previous-margin=\"leftRightMargin\" :next-margin=\"leftRightMargin\" @change=\"changeItem\">\r\n        <swiper-item v-for=\"x in 3\" :key=\"x\">\r\n          <view class=\"grade-member-card grade-flex-column\" :class=\"memberLevel + '-color'\">\r\n            <view class=\"member-info\">\r\n              <view class=\"grade-flex-column\">\r\n                <text class=\"member-name\">{{ memberName[current] }}会员</text>\r\n                <view v-if=\"memberEqual\">\r\n                  <text class=\"member-end\">会员到期时间：2025.12.30</text>\r\n                </view>\r\n              </view>\r\n              <text v-if=\"!memberEqual\" class=\"member-status\" :class=\"'status-' + (current + 1)\" @click=\"userLevelBuy(current)\">我要升级</text>\r\n            </view>\r\n            <view class=\"grow-up-info grade-flex-column\">\r\n              <view class=\"grow-up\">\r\n                <text></text>\r\n                <text v-if=\"memberEqual\">您已经是{{ memberName[current] }}会员</text>\r\n                <text v-if=\"!memberEqual\">您还不是{{ memberName[current] }}会员</text>\r\n              </view>\r\n              <!-- <view class=\"grow-up-progress\" :class=\"'progress-' + (current + 1)\"></view> -->\r\n            </view>\r\n          </view>\r\n        </swiper-item>\r\n      </swiper>\r\n      <view class=\"tip-swiper\">* 左右滑动卡片选择会员等级，更多的权益</view>\r\n\r\n      <view class=\"grade-list\">\r\n        <view class=\"grade-list-title\">\r\n          <text>当前已成功解锁<text class=\"red-strong\">{{ equity.length }}项</text>功能权益</text>\r\n        </view>\r\n        <view class=\"grade-list-content\">\r\n          <view class=\"grade-content-item\" v-for=\"(nav,index) in equity\" :key=\"index\">\r\n            <text @click=\"tipClick\" class=\"grade-content-item-icon\" :class=\"memberLevel + '-color'\">\r\n              <text class=\"iconfont\">{{ nav.icon }}</text></text>\r\n            <text class=\"grade-content-item-text\">{{ nav.text }}</text>\r\n            <zb-tooltip :placement=\"nav.position\" ref=\"tooltip\">\r\n              <view slot=\"content\">\r\n                <view class=\"desc-wrapper\">\r\n                  <text>{{ nav.desc }}</text>\r\n                </view>\r\n              </view>\r\n              <view class=\"tipBlock\"></view>\r\n            </zb-tooltip>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"grade-faq grade-flex-column\">\r\n        <view class=\"grade-faq-title\">如何成为{{ memberName[current] }}会员</view>\r\n        <view class=\"grade-faq-content grade-flex-column\">\r\n          <view class=\"grade-faq-content-item\" v-for=\"(nav,index) in open\" :key=\"index\">\r\n            <view class=\"grade-faq-item-info\">\r\n              <view>\r\n                <text class=\"grade-faq-item-icon\" :class=\"nav.class\">\r\n                  <text class=\"iconfont\">{{ nav.icon }}</text>\r\n                </text>\r\n              </view>\r\n              <view class=\"grade-flex-column\">\r\n                <text class=\"grade-faq-item-text\">{{ nav.text }}</text>\r\n                <text class=\"grade-faq-item-desc\">{{ nav.desc }}</text>\r\n              </view>\r\n            </view>\r\n            <view class=\"grade-faq-open\" @click=\"userLevelBuy(current)\">立即开通</view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n\r\n      <view class=\"grade-faq grade-flex-column\">\r\n        <view class=\"grade-faq-title\">常见问题</view>\r\n        <view class=\"grade-faq-content grade-flex-column\">\r\n          <view class=\"grade-faq-content-item\" v-for=\"(nav,index) in faq\" :key=\"index\">\r\n            <view class=\"grade-faq-item-info\" style=\"width: 100%;\">\r\n              <view>\r\n                <text class=\"grade-faq-item-icon\" :class=\"nav.class\">\r\n                  <text class=\"iconfont\">{{ nav.icon }}</text>\r\n                </text>\r\n              </view>\r\n              <view class=\"grade-flex-column\">\r\n                <text class=\"grade-faq-item-text\">{{ nav.text }}</text>\r\n                <text class=\"grade-faq-item-desc\">{{ nav.desc }}</text>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import zbTooltip from '@/uni_modules/zb-tooltip/components/zb-tooltip/zb-tooltip'\r\n\r\n  export default {\r\n    components: {\r\n      zbTooltip\r\n    },\r\n    data() {\r\n      return {\r\n        circular: false,\r\n        indicatorDots: false,\r\n        autoplay: false,\r\n        interval: 2000,\r\n        duration: 500,\r\n        indicatorColor: '#e9e9e9',\r\n        indicatorActiveColor: 'red',\r\n        leftRightMargin: '50rpx',\r\n        current: 0,\r\n        memberLevel: 'member1',\r\n        levelId: 5596,\r\n        userDetail: undefined,\r\n        balance: 0,\r\n        memberLevelDetail: [],\r\n        activeColor: ['#dbdfeb', '#ffe3cb', '#f7dbac'],\r\n        memberName: ['轻享', '心享', '尊享'],\r\n        memberEqual: false,\r\n        equity: [{\r\n            icon: \"\\u{e621}\",\r\n            text: '专属特价',\r\n            desc: '全场9.5折(预购、抢购等活动除外)',\r\n            position: 'bottom-start'\r\n          },\r\n          {\r\n            icon: \"\\u{e6c1}\",\r\n            text: '优先配送',\r\n            desc: '优先配送免排队，好食材快人一步',\r\n            position: 'bottom-start'\r\n          },\r\n          {\r\n            icon: \"\\u{e739}\",\r\n            text: '专属客服',\r\n            desc: '贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题',\r\n            position: 'bottom-start'\r\n          },\r\n          {\r\n            icon: \"\\u{e677}\",\r\n            text: '新品先购',\r\n            desc: '野贝农上市新品之前，优先获得选购权',\r\n            position: 'bottom-end'\r\n          }\r\n        ],\r\n        open: [{\r\n            icon: '\\u{e621}',\r\n            text: '购买轻享会员年卡',\r\n            desc: '费用为188元/年',\r\n            class: 'icon-color-1'\r\n          },\r\n          {\r\n            icon: '\\u{e621}',\r\n            text: '预存',\r\n            desc: '在野贝农小程序平台充值预存1000元',\r\n            class: 'icon-color-1'\r\n          }\r\n        ],\r\n        faq: [{\r\n            icon: '\\u{e739}',\r\n            text: '预存的费用有限制吗？',\r\n            desc: '没有限制，您可以购买野贝农平台所有商品',\r\n            class: 'icon-color-1'\r\n          },\r\n          {\r\n            icon: '\\u{e739}',\r\n            text: '预存的费用能提现吗？',\r\n            desc: '不能，根据国家法律，充值的费用是不允许提现的',\r\n            class: 'icon-color-4'\r\n          }\r\n        ]\r\n      }\r\n    },\r\n    onLoad(e) {\r\n      this._getUserAmount()\r\n      this._userLevelDetail()\r\n    },\r\n    methods: {\r\n      async _userDetail() {\r\n        // https://www.uviewui.com/components/form.html\r\n        const res = await this.$wxapi.userDetail(this.token)\r\n        if (res.code == 0) {\r\n          this.userDetail = res.data.base\r\n\r\n          console.log('this.userDetail is', this.userDetail)\r\n        }\r\n      },\r\n      userLevelBuy(current) {\r\n        let priceId\r\n        switch (current) {\r\n          case 0:\r\n            priceId = 553\r\n            break\r\n          case 1:\r\n            priceId = 554\r\n            break\r\n          case 2:\r\n            priceId = 555\r\n            break\r\n        }\r\n\r\n        this._userLevelBuy(priceId)\r\n      },\r\n      async _userLevelBuy(priceId) {\r\n        // https://www.yuque.com/apifm/nu0f75/svpnky\r\n        const res = await this.$wxapi.userLevelBuy(this.token, priceId)\r\n\r\n        let pass = true\r\n        switch (priceId) {\r\n          case 553:\r\n            if (this.balance < 1000) {\r\n              uni.showToast({\r\n                title: '余额不足1000',\r\n                icon: 'error'\r\n              })\r\n\r\n              pass = false\r\n            }\r\n            break\r\n          case 554:\r\n            if (this.balance < 3000) {\r\n              uni.showToast({\r\n                title: '余额不足3000',\r\n                icon: 'error'\r\n              })\r\n\r\n              pass = false\r\n            }\r\n            break\r\n          case 555:\r\n            if (this.balance < 6000) {\r\n              uni.showToast({\r\n                title: '余额不足6000',\r\n                icon: 'error'\r\n              })\r\n\r\n              pass = false\r\n            }\r\n            break\r\n        }\r\n\r\n        if (pass === true) {\r\n          if (res.code == 0) {\r\n            uni.showToast({\r\n              title: '升级成功',\r\n              icon: 'none'\r\n            })\r\n          } else {\r\n            if (res.code == 20000) {\r\n              uni.showToast({\r\n                title: res.msg,\r\n                icon: 'none'\r\n              })\r\n            }\r\n          }\r\n        }\r\n      },\r\n      async _getUserAmount() {\r\n        // https://www.yuque.com/apifm/nu0f75/wrqkcb\r\n        const res = await this.$wxapi.userAmount(this.token)\r\n        if (res.code == 0) {\r\n          this.balance = res.data.balance.toFixed(2)\r\n        }\r\n      },\r\n      async _userLevelDetail() {\r\n        // https://www.yuque.com/apifm/nu0f75/svpnky\r\n        const res = await this.$wxapi.userLevelDetail(this.levelId)\r\n        if (res.code == 0) {\r\n          this.memberLevelDetail = res.data\r\n          console.log('memberLevelDetail is', this.memberLevelDetail)\r\n\r\n          let level = res.data.info.level\r\n          this.memberEqual = false\r\n        }\r\n      },\r\n      tipClick(e) {\r\n\r\n      },\r\n      changeItem(e) {\r\n        this.current = e.detail.current\r\n        this.memberLevel = \"member\" + (e.detail.current + 1)\r\n\r\n        if (this.current === 0) {\r\n          this.levelId = 5596\r\n          this.equity = [{\r\n              icon: \"\\u{e621}\",\r\n              text: '专属特价',\r\n              desc: '全场9.5折(预购、抢购等活动除外)',\r\n              position: 'bottom-start'\r\n            },\r\n            {\r\n              icon: \"\\u{e6c1}\",\r\n              text: '优先配送',\r\n              desc: '优先配送免排队，好食材快人一步',\r\n              position: 'bottom-start'\r\n            },\r\n            {\r\n              icon: \"\\u{e739}\",\r\n              text: '专属客服',\r\n              desc: '贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题',\r\n              position: 'bottom-start'\r\n            },\r\n            {\r\n              icon: \"\\u{e677}\",\r\n              text: '新品先购',\r\n              desc: '野贝农上市新品之前，优先获得选购权',\r\n              position: 'bottom-end'\r\n            }\r\n          ]\r\n\r\n          this.open = [{\r\n              icon: '\\u{e621}',\r\n              text: '购买轻享会员年卡',\r\n              desc: '费用为188元/年',\r\n              class: 'icon-color-1'\r\n            },\r\n            {\r\n              icon: '\\u{e621}',\r\n              text: '预存',\r\n              desc: '在野贝农小程序平台充值预存1000元',\r\n              class: 'icon-color-1'\r\n            }\r\n          ]\r\n        }\r\n        if (this.current === 1) {\r\n          this.levelId = 5597\r\n          this.equity = [{\r\n              icon: \"\\u{e621}\",\r\n              text: '专属特价',\r\n              desc: '全场9折(预购、抢购等活动除外)',\r\n              position: 'bottom-start'\r\n            },\r\n            {\r\n              icon: \"\\u{e6c1}\",\r\n              text: '优先配送',\r\n              desc: '优先配送免排队，好食材快人一步',\r\n              position: 'bottom-start'\r\n            },\r\n            {\r\n              icon: \"\\u{e739}\",\r\n              text: '专属客服',\r\n              desc: '贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题',\r\n              position: 'bottom-start'\r\n            },\r\n            {\r\n              icon: \"\\u{e677}\",\r\n              text: '新品先购',\r\n              desc: '野贝农上市新品之前，优先获得选购权',\r\n              position: 'bottom-end'\r\n            },\r\n            {\r\n              icon: '\\u{e62f}',\r\n              text: '免费试吃',\r\n              desc: '野贝农新品上市前，都会邀请会员免费试吃',\r\n              position: 'bottom-start'\r\n            }, {\r\n              icon: '\\u{e631}',\r\n              text: '线下聚会',\r\n              desc: '有资格参加我们野贝农组织的线下活动：美食艺术家活动',\r\n              position: 'bottom-start'\r\n            }\r\n          ]\r\n\r\n          this.open = [{\r\n              icon: '\\u{e621}',\r\n              text: '购买轻享会员年卡',\r\n              desc: '费用为288元/年',\r\n              class: 'icon-color-1'\r\n            },\r\n            {\r\n              icon: '\\u{e621}',\r\n              text: '预存',\r\n              desc: '在野贝农小程序平台充值预存3000元',\r\n              class: 'icon-color-1'\r\n            }\r\n          ]\r\n        }\r\n        if (this.current === 2) {\r\n          this.levelId = 5598\r\n          this.equity = [{\r\n              icon: \"\\u{e621}\",\r\n              text: '专属特价',\r\n              desc: '全场8.8折(预购、抢购等活动除外)',\r\n              position: 'bottom-start'\r\n            },\r\n            {\r\n              icon: \"\\u{e6c1}\",\r\n              text: '优先配送',\r\n              desc: '优先配送免排队，好食材快人一步',\r\n              position: 'bottom-start'\r\n            },\r\n            {\r\n              icon: \"\\u{e739}\",\r\n              text: '专属客服',\r\n              desc: '贵宾绿色通道，不用排队。您在消费过程中遇到任何问题，均可以通过会员专属通道获得优先接入，解决疑问和处理问题',\r\n              position: 'bottom-start'\r\n            },\r\n            {\r\n              icon: \"\\u{e677}\",\r\n              text: '新品先购',\r\n              desc: '野贝农上市新品之前，优先获得选购权',\r\n              position: 'bottom-end'\r\n            },\r\n            {\r\n              icon: '\\u{e62f}',\r\n              text: '免费试吃',\r\n              desc: '野贝农新品上市前，都会邀请会员免费试吃',\r\n              position: 'bottom-start'\r\n            }, {\r\n              icon: '\\u{e631}',\r\n              text: '线下聚会',\r\n              desc: '有资格参加我们野贝农组织的线下活动：美食艺术家活动',\r\n              position: 'bottom-start'\r\n            },\r\n            {\r\n              icon: '\\u{e62a}',\r\n              text: '免费游玩',\r\n              desc: '作为我们的最高级别的会员，可以受邀免费参加我们深山养殖地游玩活动',\r\n              position: 'bottom-start'\r\n            },\r\n            {\r\n              icon: '\\u{e705}',\r\n              text: '成为合伙人',\r\n              desc: '做为我们的最高级别的会员，会获得成为我们野贝农渠道合伙人的资格',\r\n              position: 'bottom-end'\r\n            }\r\n          ]\r\n\r\n          this.open = [{\r\n              icon: '\\u{e621}',\r\n              text: '购买轻享会员年卡',\r\n              desc: '费用为388元/年',\r\n              class: 'icon-color-1'\r\n            },\r\n            {\r\n              icon: '\\u{e621}',\r\n              text: '预存',\r\n              desc: '在野贝农小程序平台充值预存6000元',\r\n              class: 'icon-color-1'\r\n            }\r\n          ]\r\n        }\r\n\r\n        // Load be the change\r\n        this._userLevelDetail()\r\n      }\r\n    }\r\n  }\r\n</script>\r\n\r\n<style>\r\n  @font-face {\r\n    font-family: 'iconfont';\r\n    /* Project id 4420575 */\r\n    src: url('//at.alicdn.com/t/c/font_4420575_45yzt13kqmb.woff2?t=1705988231706') format('woff2'),\r\n      url('//at.alicdn.com/t/c/font_4420575_45yzt13kqmb.woff?t=1705988231706') format('woff'),\r\n      url('//at.alicdn.com/t/c/font_4420575_45yzt13kqmb.ttf?t=1705988231706') format('truetype');\r\n  }\r\n\r\n  .iconfont {\r\n    font-family: iconfont;\r\n  }\r\n\r\n  page,\r\n  view {\r\n    display: flex;\r\n  }\r\n\r\n  page {\r\n    background-color: #fff;\r\n  }\r\n\r\n  .grade-flex-column {\r\n    flex-direction: column;\r\n  }\r\n</style>\r\n<style lang=\"scss\" scoped>\r\n  $head-color: #5b8cff; //5b8cff 4194fc\r\n  $white-color: #fff;\r\n  $radius: 5rpx;\r\n  $border-color: #efefef;\r\n  $color-1: #6eacfe;\r\n  $color-2: #52f0cf;\r\n  $color-3: #ffcd46;\r\n  $color-4: #ff727d;\r\n  $list-item-height: 100rpx;\r\n  $list-margin: 15rpx;\r\n\r\n  .swiper {\r\n    width: 100vw;\r\n    padding: 30rpx 0 0;\r\n    height: 390rpx;\r\n    box-sizing: border-box;\r\n  }\r\n\r\n  .tip-swiper {\r\n    margin-left: 80rpx;\r\n    margin-top: -50rpx;\r\n    margin-bottom: 30rpx;\r\n    font-size: 20rpx;\r\n    color: #999;\r\n  }\r\n\r\n  .grade-member-card {\r\n    height: calc(100% - 60rpx);\r\n    background-color: #d7dfeb;\r\n    color: #555d6e;\r\n    border-radius: 30rpx;\r\n    padding: 30rpx 35rpx 30rpx 40rpx;\r\n    box-sizing: border-box;\r\n    justify-content: space-between;\r\n\r\n    width: calc(100vw - 120rpx);\r\n    margin: 0 auto;\r\n  }\r\n\r\n  .member-info {\r\n    justify-content: space-between;\r\n    font-weight: bold;\r\n    font-size: 38rpx;\r\n\r\n    .member-end {\r\n      font-size: 22rpx;\r\n    }\r\n\r\n    .member-status {\r\n      background-color: #f3f7f7;\r\n      font-weight: bold;\r\n      font-size: 22rpx;\r\n      border-radius: 30rpx;\r\n      padding: 0 25rpx;\r\n      box-sizing: border-box;\r\n      height: 45rpx;\r\n      line-height: 45rpx;\r\n    }\r\n\r\n    .status-1 {\r\n      background-color: #f3f7f7;\r\n    }\r\n\r\n    .status-2 {\r\n      background-color: #fef6eb;\r\n    }\r\n\r\n    .status-3 {\r\n      background-color: #fef6eb;\r\n    }\r\n\r\n    .status-4 {\r\n      background-color: #424862;\r\n    }\r\n\r\n    .status-5 {\r\n      background-color: #625959;\r\n    }\r\n  }\r\n\r\n  .grow-up-info {\r\n    margin-bottom: 10rpx;\r\n  }\r\n\r\n  .grow-up {\r\n    justify-content: space-between;\r\n    font-weight: bold;\r\n    font-size: 24rpx;\r\n  }\r\n\r\n  .grow-up-progress {\r\n    margin-top: 10rpx;\r\n    width: 100%;\r\n    height: 11rpx;\r\n    background-color: #b8bdc7;\r\n    border-radius: 10rpx;\r\n    overflow: hidden;\r\n  }\r\n\r\n  .grow-up-progress::after {\r\n    content: '';\r\n    display: inline-block;\r\n    width: 30%;\r\n    height: 100%;\r\n    background-color: #f7fbfb;\r\n  }\r\n\r\n  .progress-1 {\r\n    background-color: #b8bdc7;\r\n  }\r\n\r\n  .progress-2 {\r\n    background-color: #e2bd81;\r\n  }\r\n\r\n  .progress-3 {\r\n    background-color: #f7cfa6;\r\n  }\r\n\r\n  .progress-4 {\r\n    background-color: #232b45;\r\n  }\r\n\r\n  .progress-5 {\r\n    background-color: #030303;\r\n  }\r\n\r\n  .grade-list {\r\n    width: 100%;\r\n    background-color: #FFFFFF;\r\n    margin: 0 auto 0;\r\n    padding-bottom: $list-margin;\r\n    flex-direction: column;\r\n    border-radius: $radius;\r\n  }\r\n\r\n  .grade-list-title {\r\n    padding: 0rpx 50rpx 30rpx;\r\n    font-size: 30rpx;\r\n    color: #333;\r\n    font-weight: 500;\r\n    justify-content: flex-start;\r\n  }\r\n\r\n  .red-strong {\r\n    color: red;\r\n  }\r\n\r\n  .grade-list-content {\r\n    box-sizing: border-box;\r\n    padding: 25rpx 40rpx 0 40rpx;\r\n    flex-wrap: wrap;\r\n\r\n    .grade-content-item {\r\n      position: relative;\r\n      width: 25%;\r\n      box-sizing: border-box;\r\n      padding: 0 0 35rpx 0rpx;\r\n      align-items: center;\r\n      flex-direction: column;\r\n\r\n      .grade-content-item-icon {\r\n        font-size: 50rpx;\r\n        font-weight: bold;\r\n        font-family: texticons;\r\n        border-radius: 50%;\r\n        padding: 15rpx;\r\n        width: 50rpx;\r\n        height: 50rpx;\r\n        display: flex;\r\n        justify-content: center;\r\n        align-items: center;\r\n        margin-bottom: 10rpx;\r\n      }\r\n\r\n      .grade-content-item-text {\r\n        color: #666;\r\n        font-size: 24rpx;\r\n        font-weight: 400;\r\n      }\r\n\r\n      .grade-content-item-desc {\r\n        color: #999;\r\n        font-size: 24rpx;\r\n        font-weight: 300;\r\n        margin-top: 0rpx;\r\n      }\r\n\r\n      .desc-wrapper {\r\n        display: flex;\r\n        flex-wrap: wrap;\r\n      }\r\n\r\n      .desc-wrapper text {\r\n        width: 200rpx;\r\n        word-break: break-all;\r\n        white-space: pre-wrap;\r\n      }\r\n    }\r\n\r\n    .tipBlock {\r\n      width: 100rpx;\r\n      height: 100rpx;\r\n      position: absolute;\r\n      top: -100rpx;\r\n      left: -50rpx;\r\n    }\r\n  }\r\n\r\n  .member1-color {\r\n    background-image: linear-gradient(#dbdfeb, #e3ebf3);\r\n    color: #555d6e;\r\n  }\r\n\r\n  .member2-color {\r\n    background-image: linear-gradient(to right, #f3d39a, #f7dfb5);\r\n    color: #a36a09;\r\n  }\r\n\r\n  .member3-color {\r\n    background-image: linear-gradient(to right, #ffe3cb, #ffebdc);\r\n    color: #b7734f;\r\n  }\r\n\r\n  .member4-color {\r\n    background-image: linear-gradient(#28304a, #4a4e67);\r\n    color: #ffe4de;\r\n  }\r\n\r\n  .member5-color {\r\n    background-image: linear-gradient(to right, #0d0b0b, #454546);\r\n    color: #ffe3cf;\r\n  }\r\n\r\n  .grade-faq {\r\n    background-color: #fff;\r\n    margin: 0 auto 40rpx;\r\n    width: calc(100vw - 80rpx);\r\n    box-sizing: border-box;\r\n\r\n    flex-direction: column;\r\n    border-radius: 30rpx;\r\n    border: solid 0rpx #eaf7ef;\r\n    box-shadow: 0rpx 1rpx 10rpx rgba(0, 0, 0, 0.1);\r\n    overflow: hidden;\r\n\r\n    .grade-faq-title {\r\n      background-image: linear-gradient(#eaf7ef, #fff);\r\n      padding: 30rpx 40rpx 0rpx;\r\n      box-sizing: border-box;\r\n      font-size: 30rpx;\r\n      color: #333;\r\n      font-weight: 600;\r\n    }\r\n  }\r\n\r\n  .grade-faq-content {\r\n    padding: 20rpx 30rpx 10rpx 40rpx;\r\n\r\n    .grade-faq-content-item {\r\n      align-items: center;\r\n      justify-content: space-between;\r\n      margin-top: 15rpx;\r\n      margin-bottom: 30rpx;\r\n    }\r\n\r\n    .grade-faq-item-info {\r\n      align-items: center;\r\n      width: 70%;\r\n    }\r\n\r\n    .grade-faq-item-icon {\r\n      font-size: 50rpx;\r\n      font-weight: bold;\r\n      font-family: texticons;\r\n      border-radius: 20rpx;\r\n      padding: 20rpx;\r\n      color: #fff;\r\n      margin-right: 20rpx;\r\n    }\r\n\r\n    .grade-faq-item-text {\r\n      color: #333;\r\n      font-size: 29rpx;\r\n      font-weight: 400;\r\n    }\r\n\r\n    .grade-faq-item-desc {\r\n      color: #999;\r\n      font-size: 23rpx;\r\n      font-weight: 300;\r\n      margin-top: 5rpx;\r\n    }\r\n  }\r\n\r\n  .grade-faq-open {\r\n    background-color: #10b575;\r\n    color: #fff;\r\n    font-weight: bold;\r\n    font-size: 24rpx;\r\n    border-radius: 30rpx;\r\n    padding: 0 30rpx;\r\n    box-sizing: border-box;\r\n    height: 60rpx;\r\n    line-height: 60rpx;\r\n  }\r\n\r\n  .icon-color-1 {\r\n    background-image: linear-gradient(#72aefe, #4896ff);\r\n  }\r\n\r\n  .icon-color-2 {\r\n    background-image: linear-gradient(#5ff9d7, #39debd);\r\n  }\r\n\r\n  .icon-color-3 {\r\n    background-image: linear-gradient(#ffd155, #ffbc04);\r\n  }\r\n\r\n  .icon-color-4 {\r\n    background-image: linear-gradient(#ff808b, #ff6872);\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./grade.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./grade.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692277256\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  ", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./grade.vue?vue&type=style&index=1&id=e1f4ba8e&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./grade.vue?vue&type=style&index=1&id=e1f4ba8e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692284089\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}