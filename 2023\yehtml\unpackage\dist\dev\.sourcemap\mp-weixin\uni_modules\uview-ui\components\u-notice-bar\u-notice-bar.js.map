{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?9df2", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?514a", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?8cca", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?82e4", "uni-app:///uni_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?c984", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue?c28a"], "names": ["name", "mixins", "data", "show", "methods", "click", "close"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wVAEN;AACP,KAAK;AACL;AACA,aAAa,sUAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS;AACT;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACtDA;AAAA;AAAA;AAAA;AAAuqB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACyC3rB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAtBA,eAuBA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA;QACA;MACA;IACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACzFA;AAAA;AAAA;AAAA;AAA8xC,CAAgB,kvCAAG,EAAC,C;;;;;;;;;;;ACAlzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-notice-bar/u-notice-bar.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-notice-bar.vue?vue&type=template&id=24c07869&scoped=true&\"\nvar renderjs\nimport script from \"./u-notice-bar.vue?vue&type=script&lang=js&\"\nexport * from \"./u-notice-bar.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-notice-bar.vue?vue&type=style&index=0&id=24c07869&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"24c07869\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-notice-bar.vue?vue&type=template&id=24c07869&scoped=true&\"", "var components\ntry {\n  components = {\n    uColumnNotice: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-column-notice/u-column-notice\" */ \"@/uni_modules/uview-ui/components/u-column-notice/u-column-notice.vue\"\n      )\n    },\n    uRowNotice: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-row-notice/u-row-notice\" */ \"@/uni_modules/uview-ui/components/u-row-notice/u-row-notice.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.show\n    ? _vm.__get_style([\n        {\n          backgroundColor: _vm.bgColor,\n        },\n        _vm.$u.addStyle(_vm.customStyle),\n      ])\n    : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-notice-bar.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-notice-bar.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t\tclass=\"u-notice-bar\"\r\n\t\tv-if=\"show\"\r\n\t\t:style=\"[{\r\n\t\t\tbackgroundColor: bgColor\r\n\t\t}, $u.addStyle(customStyle)]\"\r\n\t>\r\n\t\t<template v-if=\"direction === 'column' || (direction === 'row' && step)\">\r\n\t\t\t<u-column-notice\r\n\t\t\t\t:color=\"color\"\r\n\t\t\t\t:bgColor=\"bgColor\"\r\n\t\t\t\t:text=\"text\"\r\n\t\t\t\t:mode=\"mode\"\r\n\t\t\t\t:step=\"step\"\r\n\t\t\t\t:icon=\"icon\"\r\n\t\t\t\t:disable-touch=\"disableTouch\"\r\n\t\t\t\t:fontSize=\"fontSize\"\r\n\t\t\t\t:duration=\"duration\"\r\n\t\t\t\t@close=\"close\"\r\n\t\t\t\t@click=\"click\"\r\n\t\t\t></u-column-notice>\r\n\t\t</template>\r\n\t\t<template v-else>\r\n\t\t\t<u-row-notice\r\n\t\t\t\t:color=\"color\"\r\n\t\t\t\t:bgColor=\"bgColor\"\r\n\t\t\t\t:text=\"text\"\r\n\t\t\t\t:mode=\"mode\"\r\n\t\t\t\t:fontSize=\"fontSize\"\r\n\t\t\t\t:speed=\"speed\"\r\n\t\t\t\t:url=\"url\"\r\n\t\t\t\t:linkType=\"linkType\"\r\n\t\t\t\t:icon=\"icon\"\r\n\t\t\t\t@close=\"close\"\r\n\t\t\t\t@click=\"click\"\r\n\t\t\t></u-row-notice>\r\n\t\t</template>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport props from './props.js';\r\n\r\n\t/**\r\n\t * noticeBar 滚动通知\r\n\t * @description 该组件用于滚动通告场景，有多种模式可供选择\r\n\t * @tutorial https://www.uviewui.com/components/noticeBar.html\r\n\t * @property {Array | String}\ttext\t\t\t显示的内容，数组\r\n\t * @property {String}\t\t\tdirection\t\t通告滚动模式，row-横向滚动，column-竖向滚动 ( 默认 'row' )\r\n\t * @property {Boolean}\t\t\tstep\t\t\tdirection = row时，是否使用步进形式滚动  ( 默认 false )\r\n\t * @property {String}\t\t\ticon\t\t\t是否显示左侧的音量图标 ( 默认 'volume' )\r\n\t * @property {String}\t\t\tmode\t\t\t通告模式，link-显示右箭头，closable-显示右侧关闭图标\r\n\t * @property {String}\t\t\tcolor\t\t\t文字颜色，各图标也会使用文字颜色 ( 默认 '#f9ae3d' )\r\n\t * @property {String}\t\t\tbgColor\t\t\t背景颜色 ( 默认 '#fdf6ec' )\r\n\t * @property {String | Number}\tspeed\t\t\t水平滚动时的滚动速度，即每秒滚动多少px(px)，这有利于控制文字无论多少时，都能有一个恒定的速度 ( 默认 80 )\r\n\t * @property {String | Number}\tfontSize\t\t字体大小 ( 默认 14 )\r\n\t * @property {String | Number}\tduration\t\t滚动一个周期的时间长，单位ms ( 默认 2000 )\r\n\t * @property {Boolean}\t\t\tdisableTouch\t是否禁止用手滑动切换 目前HX2.6.11，只支持App 2.5.5+、H5 2.5.5+、支付宝小程序、字节跳动小程序（默认34） ( 默认 true )\r\n\t * @property {String}\t\t\turl\t\t\t\t跳转的页面路径\r\n\t * @property {String}\t\t\tlinkType\t\t页面跳转的类型 ( 默认 navigateTo )\r\n\t * @property {Object}\t\t\tcustomStyle\t\t定义需要用到的外部样式\r\n\t * \r\n\t * @event {Function}\t\t\tclick\t\t\t点击通告文字触发\r\n\t * @event {Function}\t\t\tclose\t\t\t点击右侧关闭图标触发\r\n\t * @example <u-notice-bar :more-icon=\"true\" :list=\"list\"></u-notice-bar>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-notice-bar\",\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshow: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 点击通告栏\r\n\t\t\tclick(index) {\r\n\t\t\t\tthis.$emit('click', index)\r\n\t\t\t\tif (this.url && this.linkType) {\r\n\t\t\t\t\t// 此方法写在mixin中，另外跳转的url和linkType参数也在mixin的props中\r\n\t\t\t\t\tthis.openPage()\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 点击关闭按钮\r\n\t\t\tclose() {\r\n\t\t\t\tthis.show = false\r\n\t\t\t\tthis.$emit('close')\r\n\t\t\t}\r\n\t\t}\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-notice-bar {\r\n\t\toverflow: hidden;\r\n\t\tpadding: 9px 12px;\r\n\t\tflex: 1;\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-notice-bar.vue?vue&type=style&index=0&id=24c07869&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-notice-bar.vue?vue&type=style&index=0&id=24c07869&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688735198\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}