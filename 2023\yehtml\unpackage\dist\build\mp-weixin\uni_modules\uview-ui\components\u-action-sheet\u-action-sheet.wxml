<u-popup vue-id="0f447b86-1" show="{{show}}" mode="bottom" safeAreaInsetBottom="{{safeAreaInsetBottom}}" round="{{round}}" data-event-opts="{{[['^close',[['closeH<PERSON>ler']]]]}}" bind:close="__e" class="data-v-269ff17c" bind:__l="__l" vue-slots="{{['default']}}"><view class="u-action-sheet data-v-269ff17c"><block wx:if="{{title}}"><view class="u-action-sheet__header data-v-269ff17c"><text class="u-action-sheet__header__title u-line-1 data-v-269ff17c">{{title}}</text><view data-event-opts="{{[['tap',[['cancel',['$event']]]]]}}" class="u-action-sheet__header__icon-wrap data-v-269ff17c" catchtap="__e"><u-icon vue-id="{{('0f447b86-2')+','+('0f447b86-1')}}" name="close" size="17" color="#c8c9cc" bold="{{true}}" class="data-v-269ff17c" bind:__l="__l"></u-icon></view></view></block><block wx:if="{{description}}"><text class="u-action-sheet__description data-v-269ff17c" style="{{'margin-top:'+(''+(title&&description?0:'18px'))+';'}}">{{description}}</text></block><block wx:if="{{$slots.default}}"><slot></slot></block><block wx:else><block wx:if="{{description}}"><u-line vue-id="{{('0f447b86-3')+','+('0f447b86-1')}}" class="data-v-269ff17c" bind:__l="__l"></u-line></block><view class="u-action-sheet__item-wrap data-v-269ff17c"><block wx:for="{{$root.l0}}" wx:for-item="item" wx:for-index="index" wx:key="index"><button class="u-reset-button data-v-269ff17c" openType="{{item.$orig.openType}}" lang="{{lang}}" session-from="{{sessionFrom}}" send-message-title="{{sendMessageTitle}}" send-message-path="{{sendMessagePath}}" send-message-img="{{sendMessageImg}}" show-message-card="{{showMessageCard}}" app-parameter="{{appParameter}}" hover-class="{{!item.$orig.disabled&&!item.$orig.loading?'u-action-sheet--hover':''}}" data-event-opts="{{[['getuserinfo',[['onGetUserInfo',['$event']]]],['contact',[['onContact',['$event']]]],['getphonenumber',[['onGetPhoneNumber',['$event']]]],['error',[['onError',['$event']]]],['launchapp',[['onLaunchApp',['$event']]]],['opensetting',[['onOpenSetting',['$event']]]],['tap',[['selectHandler',[index]]]]]}}" bindgetuserinfo="__e" bindcontact="__e" bindgetphonenumber="__e" binderror="__e" bindlaunchapp="__e" bindopensetting="__e" bindtap="__e"><view class="u-action-sheet__item-wrap__item data-v-269ff17c" hover-class="{{!item.$orig.disabled&&!item.$orig.loading?'u-action-sheet--hover':''}}" hover-stay-time="{{150}}" data-event-opts="{{[['tap',[['selectHandler',[index]]]]]}}" catchtap="__e"><block wx:if="{{!item.$orig.loading}}"><text class="u-action-sheet__item-wrap__item__name data-v-269ff17c" style="{{item.s0}}">{{item.$orig.name}}</text><block wx:if="{{item.$orig.subname}}"><text class="u-action-sheet__item-wrap__item__subname data-v-269ff17c">{{item.$orig.subname}}</text></block></block><block wx:else><u-loading-icon vue-id="{{('0f447b86-4-'+index)+','+('0f447b86-1')}}" custom-class="van-action-sheet__loading" size="18" mode="circle" class="data-v-269ff17c" bind:__l="__l"></u-loading-icon></block></view></button><block wx:if="{{index!==$root.g0-1}}"><u-line vue-id="{{('0f447b86-5-'+index)+','+('0f447b86-1')}}" class="data-v-269ff17c" bind:__l="__l"></u-line></block></block></view></block><block wx:if="{{cancelText}}"><u-gap vue-id="{{('0f447b86-6')+','+('0f447b86-1')}}" bgColor="#eaeaec" height="6" class="data-v-269ff17c" bind:__l="__l"></u-gap></block><view hover-class="u-action-sheet--hover" class="data-v-269ff17c"><block wx:if="{{cancelText}}"><text class="u-action-sheet__cancel-text data-v-269ff17c" hover-stay-time="{{150}}" data-event-opts="{{[['touchmove',[['',['$event']]]],['tap',[['cancel',['$event']]]]]}}" catchtouchmove="__e" bindtap="__e">{{cancelText}}</text></block></view></view></u-popup>