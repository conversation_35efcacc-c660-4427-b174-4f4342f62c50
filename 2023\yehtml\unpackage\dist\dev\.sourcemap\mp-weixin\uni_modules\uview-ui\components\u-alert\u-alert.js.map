{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-alert/u-alert.vue?d058", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-alert/u-alert.vue?2ef5", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-alert/u-alert.vue?4836", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-alert/u-alert.vue?8424", "uni-app:///uni_modules/uview-ui/components/u-alert/u-alert.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-alert/u-alert.vue?d7db", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-alert/u-alert.vue?3436"], "names": ["name", "mixins", "data", "show", "computed", "iconColor", "iconName", "methods", "clickHandler", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAgI;AAChI;AAC2D;AACL;AACsC;;;AAG5F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,8FAAM;AACR,EAAE,uGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,kGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sUAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAkqB,CAAgB,krBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC8DtrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,eAiBA;EACAA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;UACA;QACA;UACA;MAAA;IAEA;EACA;EACAC;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC7HA;AAAA;AAAA;AAAA;AAAyxC,CAAgB,6uCAAG,EAAC,C;;;;;;;;;;;ACA7yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-alert/u-alert.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-alert.vue?vue&type=template&id=ec4d7d1a&scoped=true&\"\nvar renderjs\nimport script from \"./u-alert.vue?vue&type=script&lang=js&\"\nexport * from \"./u-alert.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-alert.vue?vue&type=style&index=0&id=ec4d7d1a&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"ec4d7d1a\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-alert/u-alert.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-alert.vue?vue&type=template&id=ec4d7d1a&scoped=true&\"", "var components\ntry {\n  components = {\n    uTransition: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-transition/u-transition\" */ \"@/uni_modules/uview-ui/components/u-transition/u-transition.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.$u.addStyle(_vm.customStyle)])\n  var g0 = _vm.title ? _vm.$u.addUnit(_vm.fontSize) : null\n  var g1 = _vm.description ? _vm.$u.addUnit(_vm.fontSize) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        g0: g0,\n        g1: g1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-alert.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-alert.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<u-transition\r\n\t    mode=\"fade\"\r\n\t    :show=\"show\"\r\n\t>\r\n\t\t<view\r\n\t\t    class=\"u-alert\"\r\n\t\t    :class=\"[`u-alert--${type}--${effect}`]\"\r\n\t\t    @tap.stop=\"clickHandler\"\r\n\t\t    :style=\"[$u.addStyle(customStyle)]\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t    class=\"u-alert__icon\"\r\n\t\t\t    v-if=\"showIcon\"\r\n\t\t\t>\r\n\t\t\t\t<u-icon\r\n\t\t\t\t    :name=\"iconName\"\r\n\t\t\t\t    size=\"18\"\r\n\t\t\t\t    :color=\"iconColor\"\r\n\t\t\t\t></u-icon>\r\n\t\t\t</view>\r\n\t\t\t<view\r\n\t\t\t    class=\"u-alert__content\"\r\n\t\t\t    :style=\"[{\r\n\t\t\t\t\tpaddingRight: closable ? '20px' : 0\r\n\t\t\t\t}]\"\r\n\t\t\t>\r\n\t\t\t\t<text\r\n\t\t\t\t    class=\"u-alert__content__title\"\r\n\t\t\t\t    v-if=\"title\"\r\n\t\t\t\t\t:style=\"[{\r\n\t\t\t\t\t\tfontSize: $u.addUnit(fontSize),\r\n\t\t\t\t\t\ttextAlign: center ? 'center' : 'left'\r\n\t\t\t\t\t}]\"\r\n\t\t\t\t    :class=\"[effect === 'dark' ? 'u-alert__text--dark' : `u-alert__text--${type}--light`]\"\r\n\t\t\t\t>{{ title }}</text>\r\n\t\t\t\t<text\r\n\t\t\t\t    class=\"u-alert__content__desc\"\r\n\t\t\t\t\tv-if=\"description\"\r\n\t\t\t\t\t:style=\"[{\r\n\t\t\t\t\t\tfontSize: $u.addUnit(fontSize),\r\n\t\t\t\t\t\ttextAlign: center ? 'center' : 'left'\r\n\t\t\t\t\t}]\"\r\n\t\t\t\t    :class=\"[effect === 'dark' ? 'u-alert__text--dark' : `u-alert__text--${type}--light`]\"\r\n\t\t\t\t>{{ description }}</text>\r\n\t\t\t</view>\r\n\t\t\t<view\r\n\t\t\t    class=\"u-alert__close\"\r\n\t\t\t    v-if=\"closable\"\r\n\t\t\t    @tap.stop=\"closeHandler\"\r\n\t\t\t>\r\n\t\t\t\t<u-icon\r\n\t\t\t\t    name=\"close\"\r\n\t\t\t\t    :color=\"iconColor\"\r\n\t\t\t\t    size=\"15\"\r\n\t\t\t\t></u-icon>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</u-transition>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n\t/**\r\n\t * Alert  警告提示\r\n\t * @description 警告提示，展现需要关注的信息。\r\n\t * @tutorial https://www.uviewui.com/components/alertTips.html\r\n\t * \r\n\t * @property {String}\t\t\ttitle       显示的文字 \r\n\t * @property {String}\t\t\ttype        使用预设的颜色  （默认 'warning' ）\r\n\t * @property {String}\t\t\tdescription 辅助性文字，颜色比title浅一点，字号也小一点，可选  \r\n\t * @property {Boolean}\t\t\tclosable    关闭按钮(默认为叉号icon图标)  （默认 false ）\r\n\t * @property {Boolean}\t\t\tshowIcon    是否显示左边的辅助图标   （ 默认 false ）\r\n\t * @property {String}\t\t\teffect      多图时，图片缩放裁剪的模式  （默认 'light' ）\r\n\t * @property {Boolean}\t\t\tcenter\t\t文字是否居中  （默认 false ）\r\n\t * @property {String | Number}\tfontSize    字体大小  （默认 14 ）\r\n\t * @property {Object}\t\t\tcustomStyle\t定义需要用到的外部样式\r\n\t * @event    {Function}        click       点击组件时触发\r\n\t * @example  <u-alert :title=\"title\"  type = \"warning\" :closable=\"closable\" :description = \"description\"></u-alert>\r\n\t */\r\n\texport default {\r\n\t\tname: 'u-alert',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tshow: true\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\ticonColor() {\r\n\t\t\t\treturn this.effect === 'light' ? this.type : '#fff'\r\n\t\t\t},\r\n\t\t\t// 不同主题对应不同的图标\r\n\t\t\ticonName() {\r\n\t\t\t\tswitch (this.type) {\r\n\t\t\t\t\tcase 'success':\r\n\t\t\t\t\t\treturn 'checkmark-circle-fill';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'error':\r\n\t\t\t\t\t\treturn 'close-circle-fill';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'warning':\r\n\t\t\t\t\t\treturn 'error-circle-fill';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'info':\r\n\t\t\t\t\t\treturn 'info-circle-fill';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tcase 'primary':\r\n\t\t\t\t\t\treturn 'more-circle-fill';\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\tdefault: \r\n\t\t\t\t\t\treturn 'error-circle-fill';\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\t// 点击内容\r\n\t\t\tclickHandler() {\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t},\r\n\t\t\t// 点击关闭按钮\r\n\t\t\tcloseHandler() {\r\n\t\t\t\tthis.show = false\r\n\t\t\t}\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-alert {\r\n\t\tposition: relative;\r\n\t\tbackground-color: $u-primary;\r\n\t\tpadding: 8px 10px;\r\n\t\t@include flex(row);\r\n\t\talign-items: center;\r\n\t\tborder-top-left-radius: 4px;\r\n\t\tborder-top-right-radius: 4px;\r\n\t\tborder-bottom-left-radius: 4px;\r\n\t\tborder-bottom-right-radius: 4px;\r\n\r\n\t\t&--primary--dark {\r\n\t\t\tbackground-color: $u-primary;\r\n\t\t}\r\n\r\n\t\t&--primary--light {\r\n\t\t\tbackground-color: #ecf5ff;\r\n\t\t}\r\n\r\n\t\t&--error--dark {\r\n\t\t\tbackground-color: $u-error;\r\n\t\t}\r\n\r\n\t\t&--error--light {\r\n\t\t\tbackground-color: #FEF0F0;\r\n\t\t}\r\n\r\n\t\t&--success--dark {\r\n\t\t\tbackground-color: $u-success;\r\n\t\t}\r\n\r\n\t\t&--success--light {\r\n\t\t\tbackground-color: #f5fff0;\r\n\t\t}\r\n\r\n\t\t&--warning--dark {\r\n\t\t\tbackground-color: $u-warning;\r\n\t\t}\r\n\r\n\t\t&--warning--light {\r\n\t\t\tbackground-color: #FDF6EC;\r\n\t\t}\r\n\r\n\t\t&--info--dark {\r\n\t\t\tbackground-color: $u-info;\r\n\t\t}\r\n\r\n\t\t&--info--light {\r\n\t\t\tbackground-color: #f4f4f5;\r\n\t\t}\r\n\r\n\t\t&__icon {\r\n\t\t\tmargin-right: 5px;\r\n\t\t}\r\n\r\n\t\t&__content {\r\n\t\t\t@include flex(column);\r\n\t\t\tflex: 1;\r\n\r\n\t\t\t&__title {\r\n\t\t\t\tcolor: $u-main-color;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t\tmargin-bottom: 2px;\r\n\t\t\t}\r\n\r\n\t\t\t&__desc {\r\n\t\t\t\tcolor: $u-main-color;\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tflex-wrap: wrap;\r\n\t\t\t\tcolor: #fff;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__title--dark,\r\n\t\t&__desc--dark {\r\n\t\t\tcolor: #FFFFFF;\r\n\t\t}\r\n\r\n\t\t&__text--primary--light,\r\n\t\t&__text--primary--light {\r\n\t\t\tcolor: $u-primary;\r\n\t\t}\r\n\r\n\t\t&__text--success--light,\r\n\t\t&__text--success--light {\r\n\t\t\tcolor: $u-success;\r\n\t\t}\r\n\r\n\t\t&__text--warning--light,\r\n\t\t&__text--warning--light {\r\n\t\t\tcolor: $u-warning;\r\n\t\t}\r\n\r\n\t\t&__text--error--light,\r\n\t\t&__text--error--light {\r\n\t\t\tcolor: $u-error;\r\n\t\t}\r\n\r\n\t\t&__text--info--light,\r\n\t\t&__text--info--light {\r\n\t\t\tcolor: $u-info;\r\n\t\t}\r\n\r\n\t\t&__close {\r\n\t\t\tposition: absolute;\r\n\t\t\ttop: 11px;\r\n\t\t\tright: 10px;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-alert.vue?vue&type=style&index=0&id=ec4d7d1a&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-alert.vue?vue&type=style&index=0&id=ec4d7d1a&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692292641\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}