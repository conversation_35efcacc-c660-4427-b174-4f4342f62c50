(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/login/reg"],{"05bc":function(e,n,t){"use strict";(function(e,n){var u=t("47a9");t("96bd");u(t("3240"));var o=u(t("f0c0"));e.__webpack_require_UNI_MP_PLUGIN__=t,n(o.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},"23c0":function(e,n,t){"use strict";t.d(n,"b",(function(){return o})),t.d(n,"c",(function(){return r})),t.d(n,"a",(function(){return u}));var u={uEmpty:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-empty/u-empty")]).then(t.bind(null,"c57e"))},uForm:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-form/u-form")]).then(t.bind(null,"29b8"))},uFormItem:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-form-item/u-form-item")]).then(t.bind(null,"218e"))},uInput:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-input/u-input")]).then(t.bind(null,"17d5"))},uImage:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-image/u-image")]).then(t.bind(null,"73f7"))},uToast:function(){return t.e("uni_modules/uview-ui/components/u-toast/u-toast").then(t.bind(null,"8d91"))},uCode:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-code/u-code")]).then(t.bind(null,"8b6d"))},uButton:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-button/u-button")]).then(t.bind(null,"9edc"))}},o=function(){var e=this.$createElement;this._self._c},r=[]},"2f6c":function(e,n,t){"use strict";t.r(n);var u=t("375e"),o=t.n(u);for(var r in u)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return u[e]}))}(r);n["default"]=o.a},"375e":function(e,n,t){"use strict";(function(e){var u=t("47a9");Object.defineProperty(n,"__esModule",{value:!0}),n.default=void 0;var o=u(t("7eb4")),r=u(t("ee10")),i={data:function(){return{imgkey:void 0,imgsrc:void 0,seconds:60,tips:void 0,rules:{mobile:[{required:!0,message:"不能为空",trigger:["change","blur"]}],imgcode:[{required:!0,message:"不能为空",trigger:["change","blur"]}],code:[{required:!0,message:"不能为空",trigger:["change","blur"]}],pwd:[{required:!0,message:"不能为空",trigger:["change","blur"]}]},form:{mobile:void 0,imgcode:void 0,code:void 0,pwd:void 0}}},created:function(){this.changeImgCode()},mounted:function(){},onReady:function(){this.$refs.uForm.setRules(this.rules)},onLoad:function(e){},onShow:function(){},methods:{changeImgCode:function(){this.imgkey=Math.random(),this.imgsrc=this.$wxapi.graphValidateCodeUrl(this.imgkey)},codeChange:function(e){this.tips=e},getCode:function(){var n=this;return(0,r.default)(o.default.mark((function t(){var u;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(!n.$refs.uCode.canGetCode){t.next=19;break}if(n.form.mobile){t.next=4;break}return e.$u.toast("请输入手机号码"),t.abrupt("return");case 4:if(n.form.imgcode){t.next=7;break}return e.$u.toast("请输入图形验证码"),t.abrupt("return");case 7:return e.showLoading({title:"正在获取验证码"}),t.next=10,n.$wxapi.smsValidateCode(n.form.mobile,n.imgkey,n.form.imgcode);case 10:if(u=t.sent,0==u.code){t.next=14;break}return e.$u.toast(u.msg),t.abrupt("return");case 14:e.hideLoading(),e.$u.toast("验证码已发送"),n.$refs.uCode.start(),t.next=20;break;case 19:e.$u.toast("倒计时结束后再发送");case 20:case"end":return t.stop()}}),t)})))()},end:function(){},start:function(){},goLogin:function(){e.redirectTo({url:"/pages/login/login"})},submit:function(){var n=this;return(0,r.default)(o.default.mark((function t(){var u;return o.default.wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(n.form.mobile){t.next=3;break}return e.$u.toast("请输入手机号码"),t.abrupt("return");case 3:if(n.form.code){t.next=6;break}return e.$u.toast("请输入短信验证码"),t.abrupt("return");case 6:return t.next=8,n.$wxapi.register_mobile({mobile:n.form.mobile,code:n.form.code,pwd:n.form.pwd,autoLogin:!0,referrer:n.referrer?n.referrer:""});case 8:if(u=t.sent,0==u.code){t.next=12;break}return e.showToast({title:u.msg,icon:"none"}),t.abrupt("return");case 12:if(1e4!=u.code){t.next=15;break}return e.showModal({showCancel:!1,title:"提示",content:"当前手机号码已存在，无需重新注册",confirmText:"知道了"}),t.abrupt("return");case 15:n.$u.vuex("token",u.data.token),n.$u.vuex("uid",u.data.uid),setTimeout((function(){e.$emit("loginOK",{}),e.navigateBack()}),500);case 18:case"end":return t.stop()}}),t)})))()}}};n.default=i}).call(this,t("df3c")["default"])},"5d55":function(e,n,t){"use strict";var u=t("e112"),o=t.n(u);o.a},e112:function(e,n,t){},f0c0:function(e,n,t){"use strict";t.r(n);var u=t("23c0"),o=t("2f6c");for(var r in o)["default"].indexOf(r)<0&&function(e){t.d(n,e,(function(){return o[e]}))}(r);t("5d55");var i=t("828b"),a=Object(i["a"])(o["default"],u["b"],u["c"],!1,null,"384de68a",null,!1,u["a"],void 0);n["default"]=a.exports}},[["05bc","common/runtime","common/vendor"]]]);