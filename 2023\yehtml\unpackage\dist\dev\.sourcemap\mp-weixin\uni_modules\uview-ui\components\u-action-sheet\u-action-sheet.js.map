{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.vue?83bb", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.vue?1248", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.vue?9ce8", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.vue?6a6d", "uni-app:///uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.vue?19aa", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.vue?c0f4"], "names": ["name", "mixins", "data", "computed", "itemStyle", "methods", "<PERSON><PERSON><PERSON><PERSON>", "cancel", "<PERSON><PERSON><PERSON><PERSON>"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAuI;AACvI;AACkE;AACL;AACsC;;;AAGnG;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,oFAAM;AACR,EAAE,qGAAM;AACR,EAAE,8GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,yGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,wSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,kVAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA,MAAM;AACN;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACvEA;AAAA;AAAA;AAAA;AAAyqB,CAAgB,yrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC6G7rB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhCA,eAiCA;EACAA;EACA;EACAC;EACAC;IACA,QAEA;EACA;EACAC;IACA;IACAC;MAAA;MACA;QACA;QACA;QACA;QACA;QACA;QACA;MACA;IACA;EACA;EACAC;IACAC;MACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACA;QACA;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC5LA;AAAA;AAAA;AAAA;AAAgyC,CAAgB,ovCAAG,EAAC,C;;;;;;;;;;;ACApzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-action-sheet.vue?vue&type=template&id=b62b882e&scoped=true&\"\nvar renderjs\nimport script from \"./u-action-sheet.vue?vue&type=script&lang=js&\"\nexport * from \"./u-action-sheet.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-action-sheet.vue?vue&type=style&index=0&id=b62b882e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"b62b882e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-action-sheet/u-action-sheet.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-action-sheet.vue?vue&type=template&id=b62b882e&scoped=true&\"", "var components\ntry {\n  components = {\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uLine: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-line/u-line\" */ \"@/uni_modules/uview-ui/components/u-line/u-line.vue\"\n      )\n    },\n    uLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-loading-icon/u-loading-icon\" */ \"@/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue\"\n      )\n    },\n    uGap: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-gap/u-gap\" */ \"@/uni_modules/uview-ui/components/u-gap/u-gap.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.actions.length\n  var l0 = _vm.__map(_vm.actions, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var s0 = !item.loading ? _vm.__get_style([_vm.itemStyle(index)]) : null\n    return {\n      $orig: $orig,\n      s0: s0,\n    }\n  })\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-action-sheet.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-action-sheet.vue?vue&type=script&lang=js&\"", "\r\n<template>\r\n\t<u-popup\r\n\t    :show=\"show\"\r\n\t    mode=\"bottom\"\r\n\t    @close=\"closeHandler\"\r\n\t    :safeAreaInsetBottom=\"safeAreaInsetBottom\"\r\n\t    :round=\"round\"\r\n\t>\r\n\t\t<view class=\"u-action-sheet\">\r\n\t\t\t<view\r\n\t\t\t    class=\"u-action-sheet__header\"\r\n\t\t\t    v-if=\"title\"\r\n\t\t\t>\r\n\t\t\t\t<text class=\"u-action-sheet__header__title u-line-1\">{{title}}</text>\r\n\t\t\t\t<view\r\n\t\t\t\t    class=\"u-action-sheet__header__icon-wrap\"\r\n\t\t\t\t    @tap.stop=\"cancel\"\r\n\t\t\t\t>\r\n\t\t\t\t\t<u-icon\r\n\t\t\t\t\t    name=\"close\"\r\n\t\t\t\t\t    size=\"17\"\r\n\t\t\t\t\t    color=\"#c8c9cc\"\r\n\t\t\t\t\t    bold\r\n\t\t\t\t\t></u-icon>\r\n\t\t\t\t</view>\r\n\t\t\t</view>\r\n\t\t\t<text\r\n\t\t\t    class=\"u-action-sheet__description\"\r\n\t\t\t\t:style=\"[{\r\n\t\t\t\t\tmarginTop: `${title && description ? 0 : '18px'}`\r\n\t\t\t\t}]\"\r\n\t\t\t    v-if=\"description\"\r\n\t\t\t>{{description}}</text>\r\n\t\t\t<slot>\r\n\t\t\t\t<u-line v-if=\"description\"></u-line>\r\n\t\t\t\t<view class=\"u-action-sheet__item-wrap\">\r\n\t\t\t\t\t<template v-for=\"(item, index) in actions\">\r\n\t\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t\t<button\r\n\t\t\t\t\t\t    :key=\"index\"\r\n\t\t\t\t\t\t    class=\"u-reset-button\"\r\n\t\t\t\t\t\t    :openType=\"item.openType\"\r\n\t\t\t\t\t\t    @getuserinfo=\"onGetUserInfo\"\r\n\t\t\t\t\t\t    @contact=\"onContact\"\r\n\t\t\t\t\t\t    @getphonenumber=\"onGetPhoneNumber\"\r\n\t\t\t\t\t\t    @error=\"onError\"\r\n\t\t\t\t\t\t    @launchapp=\"onLaunchApp\"\r\n\t\t\t\t\t\t    @opensetting=\"onOpenSetting\"\r\n\t\t\t\t\t\t    :lang=\"lang\"\r\n\t\t\t\t\t\t    :session-from=\"sessionFrom\"\r\n\t\t\t\t\t\t    :send-message-title=\"sendMessageTitle\"\r\n\t\t\t\t\t\t    :send-message-path=\"sendMessagePath\"\r\n\t\t\t\t\t\t    :send-message-img=\"sendMessageImg\"\r\n\t\t\t\t\t\t    :show-message-card=\"showMessageCard\"\r\n\t\t\t\t\t\t    :app-parameter=\"appParameter\"\r\n\t\t\t\t\t\t    @tap=\"selectHandler(index)\"\r\n\t\t\t\t\t\t    :hover-class=\"!item.disabled && !item.loading ? 'u-action-sheet--hover' : ''\"\r\n\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t\t<view\r\n\t\t\t\t\t\t\t    class=\"u-action-sheet__item-wrap__item\"\r\n\t\t\t\t\t\t\t    @tap.stop=\"selectHandler(index)\"\r\n\t\t\t\t\t\t\t    :hover-class=\"!item.disabled && !item.loading ? 'u-action-sheet--hover' : ''\"\r\n\t\t\t\t\t\t\t    :hover-stay-time=\"150\"\r\n\t\t\t\t\t\t\t>\r\n\t\t\t\t\t\t\t\t<template v-if=\"!item.loading\">\r\n\t\t\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t\t    class=\"u-action-sheet__item-wrap__item__name\"\r\n\t\t\t\t\t\t\t\t\t    :style=\"[itemStyle(index)]\"\r\n\t\t\t\t\t\t\t\t\t>{{ item.name }}</text>\r\n\t\t\t\t\t\t\t\t\t<text\r\n\t\t\t\t\t\t\t\t\t    v-if=\"item.subname\"\r\n\t\t\t\t\t\t\t\t\t    class=\"u-action-sheet__item-wrap__item__subname\"\r\n\t\t\t\t\t\t\t\t\t>{{ item.subname }}</text>\r\n\t\t\t\t\t\t\t\t</template>\r\n\t\t\t\t\t\t\t\t<u-loading-icon\r\n\t\t\t\t\t\t\t\t    v-else\r\n\t\t\t\t\t\t\t\t    custom-class=\"van-action-sheet__loading\"\r\n\t\t\t\t\t\t\t\t    size=\"18\"\r\n\t\t\t\t\t\t\t\t    mode=\"circle\"\r\n\t\t\t\t\t\t\t\t/>\r\n\t\t\t\t\t\t\t</view>\r\n\t\t\t\t\t\t\t<!-- #ifdef MP -->\r\n\t\t\t\t\t\t</button>\r\n\t\t\t\t\t\t<!-- #endif -->\r\n\t\t\t\t\t\t<u-line v-if=\"index !== actions.length - 1\"></u-line>\r\n\t\t\t\t\t</template>\r\n\t\t\t\t</view>\r\n\t\t\t</slot>\r\n\t\t\t<u-gap\r\n\t\t\t    bgColor=\"#eaeaec\"\r\n\t\t\t    height=\"6\"\r\n\t\t\t    v-if=\"cancelText\"\r\n\t\t\t></u-gap>\r\n\t\t\t<view hover-class=\"u-action-sheet--hover\">\r\n\t\t\t\t<text\r\n\t\t\t\t    @touchmove.stop.prevent\r\n\t\t\t\t    :hover-stay-time=\"150\"\r\n\t\t\t\t    v-if=\"cancelText\"\r\n\t\t\t\t    class=\"u-action-sheet__cancel-text\"\r\n\t\t\t\t    @tap=\"cancel\"\r\n\t\t\t\t>{{cancelText}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t</u-popup>\r\n</template>\r\n\r\n<script>\r\n\timport openType from '../../libs/mixin/openType'\r\n\timport button from '../../libs/mixin/button'\r\n\timport props from './props.js';\r\n\t/**\r\n\t * ActionSheet 操作菜单\r\n\t * @description 本组件用于从底部弹出一个操作菜单，供用户选择并返回结果。本组件功能类似于uni的uni.showActionSheetAPI，配置更加灵活，所有平台都表现一致。\r\n\t * @tutorial https://www.uviewui.com/components/actionSheet.html\r\n\t * \r\n\t * @property {Boolean}\t\t\tshow\t\t\t\t操作菜单是否展示 （默认 false ）\r\n\t * @property {String}\t\t\ttitle\t\t\t\t操作菜单标题\r\n\t * @property {String}\t\t\tdescription\t\t\t选项上方的描述信息\r\n\t * @property {Array<Object>}\tactions\t\t\t\t按钮的文字数组，见官方文档示例\r\n\t * @property {String}\t\t\tcancelText\t\t\t取消按钮的提示文字,不为空时显示按钮\r\n\t * @property {Boolean}\t\t\tcloseOnClickAction\t点击某个菜单项时是否关闭弹窗 （默认 true ）\r\n\t * @property {Boolean}\t\t\tsafeAreaInsetBottom\t处理底部安全区 （默认 true ）\r\n\t * @property {String}\t\t\topenType\t\t\t小程序的打开方式 (contact | launchApp | getUserInfo | openSetting ｜getPhoneNumber ｜error )\r\n\t * @property {Boolean}\t\t\tcloseOnClickOverlay\t点击遮罩是否允许关闭  (默认 true )\r\n\t * @property {Number|String}\tround\t\t\t\t圆角值，默认无圆角  (默认 0 )\r\n\t * @property {String}\t\t\tlang\t\t\t\t指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文\r\n\t * @property {String}\t\t\tsessionFrom\t\t\t会话来源，openType=\"contact\"时有效\r\n\t * @property {String}\t\t\tsendMessageTitle\t会话内消息卡片标题，openType=\"contact\"时有效\r\n\t * @property {String}\t\t\tsendMessagePath\t\t会话内消息卡片点击跳转小程序路径，openType=\"contact\"时有效\r\n\t * @property {String}\t\t\tsendMessageImg\t\t会话内消息卡片图片，openType=\"contact\"时有效\r\n\t * @property {Boolean}\t\t\tshowMessageCard\t\t是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示\"可能要发送的小程序\"提示，用户点击后可以快速发送小程序消息，openType=\"contact\"时有效 （默认 false ）\r\n\t * @property {String}\t\t\tappParameter\t\t打开 APP 时，向 APP 传递的参数，openType=launchApp 时有效\r\n\t * \r\n\t * @event {Function} select\t\t\t点击ActionSheet列表项时触发 \r\n\t * @event {Function} close\t\t\t点击取消按钮时触发\r\n\t * @event {Function} getuserinfo\t用户点击该按钮时，会返回获取到的用户信息，回调的 detail 数据与 wx.getUserInfo 返回的一致，openType=\"getUserInfo\"时有效\r\n\t * @event {Function} contact\t\t客服消息回调，openType=\"contact\"时有效\r\n\t * @event {Function} getphonenumber\t获取用户手机号回调，openType=\"getPhoneNumber\"时有效\r\n\t * @event {Function} error\t\t\t当使用开放能力时，发生错误的回调，openType=\"error\"时有效\r\n\t * @event {Function} launchapp\t\t打开 APP 成功的回调，openType=\"launchApp\"时有效\r\n\t * @event {Function} opensetting\t在打开授权设置页后回调，openType=\"openSetting\"时有效\r\n\t * @example <u-action-sheet :actions=\"list\" :title=\"title\" :show=\"show\"></u-action-sheet>\r\n\t */\r\n\texport default {\r\n\t\tname: \"u-action-sheet\",\r\n\t\t// 一些props参数和methods方法，通过mixin混入，因为其他文件也会用到\r\n\t\tmixins: [openType, button, uni.$u.mixin, props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 操作项目的样式\r\n\t\t\titemStyle() {\r\n\t\t\t\treturn (index) => {\r\n\t\t\t\t\tlet style = {};\r\n\t\t\t\t\tif (this.actions[index].color) style.color = this.actions[index].color\r\n\t\t\t\t\tif (this.actions[index].fontSize) style.fontSize = uni.$u.addUnit(this.actions[index].fontSize)\r\n\t\t\t\t\t// 选项被禁用的样式\r\n\t\t\t\t\tif (this.actions[index].disabled) style.color = '#c0c4cc'\r\n\t\t\t\t\treturn style;\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tcloseHandler() {\r\n\t\t\t\t// 允许点击遮罩关闭时，才发出close事件\r\n\t\t\t\tif(this.closeOnClickOverlay) {\r\n\t\t\t\t\tthis.$emit('close')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// 点击取消按钮\r\n\t\t\tcancel() {\r\n\t\t\t\tthis.$emit('close')\r\n\t\t\t},\r\n\t\t\tselectHandler(index) {\r\n\t\t\t\tconst item = this.actions[index]\r\n\t\t\t\tif (item && !item.disabled && !item.loading) {\r\n\t\t\t\t\tthis.$emit('select', item)\r\n\t\t\t\t\tif (this.closeOnClickAction) {\r\n\t\t\t\t\t\tthis.$emit('close')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t}\r\n\t}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\t$u-action-sheet-reset-button-width:100% !default;\r\n\t$u-action-sheet-title-font-size: 16px !default;\r\n\t$u-action-sheet-title-padding: 12px 30px !default;\r\n\t$u-action-sheet-title-color: $u-main-color !default;\r\n\t$u-action-sheet-header-icon-wrap-right:15px !default;\r\n\t$u-action-sheet-header-icon-wrap-top:15px !default;\r\n\t$u-action-sheet-description-font-size:13px !default;\r\n\t$u-action-sheet-description-color:14px !default;\r\n\t$u-action-sheet-description-margin: 18px 15px !default;\r\n\t$u-action-sheet-item-wrap-item-padding:15px !default;\r\n\t$u-action-sheet-item-wrap-name-font-size:16px !default;\r\n\t$u-action-sheet-item-wrap-subname-font-size:13px !default;\r\n\t$u-action-sheet-item-wrap-subname-color: #c0c4cc !default;\r\n\t$u-action-sheet-item-wrap-subname-margin-top:10px !default;\r\n\t$u-action-sheet-cancel-text-font-size:16px !default;\r\n\t$u-action-sheet-cancel-text-color:$u-content-color !default;\r\n\t$u-action-sheet-cancel-text-font-size:15px !default;\r\n\t$u-action-sheet-cancel-text-hover-background-color:rgb(242, 243, 245) !default;\r\n\r\n\t.u-reset-button {\r\n\t\twidth: $u-action-sheet-reset-button-width;\r\n\t}\r\n\r\n\t.u-action-sheet {\r\n\t\ttext-align: center;\r\n\t\t&__header {\r\n\t\t\tposition: relative;\r\n\t\t\tpadding: $u-action-sheet-title-padding;\r\n\t\t\t&__title {\r\n\t\t\t\tfont-size: $u-action-sheet-title-font-size;\r\n\t\t\t\tcolor: $u-action-sheet-title-color;\r\n\t\t\t\tfont-weight: bold;\r\n\t\t\t\ttext-align: center;\r\n\t\t\t}\r\n\r\n\t\t\t&__icon-wrap {\r\n\t\t\t\tposition: absolute;\r\n\t\t\t\tright: $u-action-sheet-header-icon-wrap-right;\r\n\t\t\t\ttop: $u-action-sheet-header-icon-wrap-top;\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__description {\r\n\t\t\tfont-size: $u-action-sheet-description-font-size;\r\n\t\t\tcolor: $u-tips-color;\r\n\t\t\tmargin: $u-action-sheet-description-margin;\r\n\t\t\ttext-align: center;\r\n\t\t}\r\n\r\n\t\t&__item-wrap {\r\n\r\n\t\t\t&__item {\r\n\t\t\t\tpadding: $u-action-sheet-item-wrap-item-padding;\r\n\t\t\t\t@include flex;\r\n\t\t\t\talign-items: center;\r\n\t\t\t\tjustify-content: center;\r\n\t\t\t\tflex-direction: column;\r\n\r\n\t\t\t\t&__name {\r\n\t\t\t\t\tfont-size: $u-action-sheet-item-wrap-name-font-size;\r\n\t\t\t\t\tcolor: $u-main-color;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\r\n\t\t\t\t&__subname {\r\n\t\t\t\t\tfont-size: $u-action-sheet-item-wrap-subname-font-size;\r\n\t\t\t\t\tcolor: $u-action-sheet-item-wrap-subname-color;\r\n\t\t\t\t\tmargin-top: $u-action-sheet-item-wrap-subname-margin-top;\r\n\t\t\t\t\ttext-align: center;\r\n\t\t\t\t}\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t\t&__cancel-text {\r\n\t\t\tfont-size: $u-action-sheet-cancel-text-font-size;\r\n\t\t\tcolor: $u-action-sheet-cancel-text-color;\r\n\t\t\ttext-align: center;\r\n\t\t\tpadding: $u-action-sheet-cancel-text-font-size;\r\n\t\t}\r\n\r\n\t\t&--hover {\r\n\t\t\tbackground-color: $u-action-sheet-cancel-text-hover-background-color;\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-action-sheet.vue?vue&type=style&index=0&id=b62b882e&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-action-sheet.vue?vue&type=style&index=0&id=b62b882e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692293555\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}