(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["pages/score/index"],{"0a9f":function(n,e,t){"use strict";var u=t("47a9");Object.defineProperty(e,"__esModule",{value:!0}),e.default=void 0;var r=u(t("7eb4")),o=u(t("ee10")),c={data:function(){return{score:0}},created:function(){},mounted:function(){},onReady:function(){},onLoad:function(n){},onShow:function(){this._userAmount()},methods:{_userAmount:function(){var n=this;return(0,o.default)(r.default.mark((function e(){var t;return r.default.wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,n.$wxapi.userAmount(n.token);case 2:t=e.sent,0==t.code&&(n.score=t.data.score);case 4:case"end":return e.stop()}}),e)})))()}}};e.default=c},2911:function(n,e,t){"use strict";(function(n,e){var u=t("47a9");t("96bd");u(t("3240"));var r=u(t("95f6"));n.__webpack_require_UNI_MP_PLUGIN__=t,e(r.default)}).call(this,t("3223")["default"],t("df3c")["createPage"])},"2b0c":function(n,e,t){"use strict";t.r(e);var u=t("0a9f"),r=t.n(u);for(var o in u)["default"].indexOf(o)<0&&function(n){t.d(e,n,(function(){return u[n]}))}(o);e["default"]=r.a},"5de8":function(n,e,t){"use strict";t.d(e,"b",(function(){return r})),t.d(e,"c",(function(){return o})),t.d(e,"a",(function(){return u}));var u={uCell:function(){return Promise.all([t.e("common/vendor"),t.e("uni_modules/uview-ui/components/u-cell/u-cell")]).then(t.bind(null,"819c"))}},r=function(){var n=this.$createElement;this._self._c},o=[]},7400:function(n,e,t){"use strict";var u=t("d6ab"),r=t.n(u);r.a},"95f6":function(n,e,t){"use strict";t.r(e);var u=t("5de8"),r=t("2b0c");for(var o in r)["default"].indexOf(o)<0&&function(n){t.d(e,n,(function(){return r[n]}))}(o);t("7400");var c=t("828b"),a=Object(c["a"])(r["default"],u["b"],u["c"],!1,null,"604d1255",null,!1,u["a"],void 0);e["default"]=a.exports},d6ab:function(n,e,t){}},[["2911","common/runtime","common/vendor"]]]);