<view><input class="uni-input" placeholder="请输入内容" data-event-opts="{{[['input',[['onInput',['$event']]]],['blur',[['hideList',['$event']]]]]}}" value="{{value}}" bindinput="__e" bindblur="__e"/><block wx:if="{{isShow}}"><view class="str-auto-complete-container"><block wx:for="{{showList}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view data-event-opts="{{[['tap',[['selectThisItem',['$0'],[[['showList','',index]]]]]]]}}" class="str-auto-complete-item" bindtap="__e"><rich-text nodes="{{item.showString}}"></rich-text></view></block></view></block></view>