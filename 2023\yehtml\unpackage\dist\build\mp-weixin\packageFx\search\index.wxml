<view class="data-v-1451c234"><u-sticky vue-id="10e27256-1" bgColor="#ffffff" class="data-v-1451c234" bind:__l="__l" vue-slots="{{['default']}}"><view class="search-box data-v-1451c234"><u-search vue-id="{{('10e27256-2')+','+('10e27256-1')}}" placeholder="输入关键词搜索" showAction="{{false}}" value="{{kw}}" data-event-opts="{{[['^search',[['search']]],['^input',[['__set_model',['','kw','$event',[]]]]]]}}" bind:search="__e" bind:input="__e" class="data-v-1451c234" bind:__l="__l"></u-search></view></u-sticky><block wx:if="{{$root.g0}}"><page-box-empty vue-id="10e27256-3" title="暂无记录" sub-title="试着搜一搜你感兴趣的吧～" show-btn="{{false}}" class="data-v-1451c234" bind:__l="__l"></page-box-empty></block><view class="tags data-v-1451c234"><block wx:for="{{list}}" wx:for-item="item" wx:for-index="index" wx:key="index"><u-tag vue-id="{{'10e27256-4-'+index}}" text="{{item}}" type="success" closable="{{true}}" name="{{index}}" data-event-opts="{{[['^click',[['tagclick']]],['^close',[['tagclose']]]]}}" bind:click="__e" bind:close="__e" class="data-v-1451c234" bind:__l="__l"></u-tag></block></view></view>