{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-parse/u-parse.vue?5b5c", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-parse/u-parse.vue?0191", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-parse/u-parse.vue?54be", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-parse/u-parse.vue?d558", "uni-app:///uni_modules/uview-ui/components/u-parse/u-parse.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-parse/u-parse.vue?8f91", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-parse/u-parse.vue?8f5d"], "names": ["name", "data", "nodes", "mixins", "components", "node", "watch", "content", "created", "mounted", "<PERSON><PERSON><PERSON><PERSON>", "clearInterval", "methods", "in", "page", "selector", "scrollTop", "navigateTo", "offset", "deep", "select", "uni", "duration", "resolve", "getText", "text", "traversal", "getRect", "<PERSON><PERSON><PERSON><PERSON>", "height", "_hook"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAoH;AACpH;AAC2D;AACL;AACa;;;AAGnE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,6EAAM;AACR,EAAE,kFAAM;AACR,EAAE,2FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,sFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAkqB,CAAgB,krBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACatrB;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eAOA;EACAA;EACAC;IACA;MACAC;IAIA;EACA;EACAC;EAEAC;IACAC;EACA;EAEAC;IACAC;MACA;IACA;EACA;EACAC;IACA;IACA;MACA;IAAA;EACA;EACAC;IACA,wCACA;EACA;EACAC;IACA;IACAC;EACA;EACAC;IACA;AACA;AACA;AACA;AACA;AACA;IACAC;MAEA,mCACA;QACAC;QACAC;QACAC;MACA;IAEA;IAEA;AACA;AACA;AACA;AACA;AACA;IACAC;MAAA;MACA;QACA,sBACA;QACAC;QAiBA;QAEAC;QAEA,yCAEAN,uCAEAO;QACA,eACAL,mDACAK;QAAA,KAEAL;QACAA;UACA,aACA;UACA;UACA;YACA;YACA;YAEA;YACAM;cACAL;cACAM;YACA;UACAC;QACA;MAEA;IACA;IAEA;AACA;AACA;AACA;IACAC;MACA;MACA;QACA;UACA;UACA,yBACAC,8CACA,uBACAA,kBACA;YACA;YACA;YACA,sDACAA;YACA;YACA,mBACAC;YACA,8CACAD,kBACA,4CACAA;UACA;QACA;MACA;MACA;IACA;IAEA;AACA;AACA;AACA;IACAE;MAAA;MACA;QACAN,0BAEAR,WAEAO;UAAA;QAAA;MACA;IACA;IAEA;AACA;AACA;AACA;AACA;IACAQ;MAAA;MACA,8BACA;MACA;MAKA;MAGA;MACA;QACA;QACA;MACA;;MAEA;MACA;MACAjB;MACA;QACA;UACA;UACA;YACA;YACAA;UACA;UACAkB;QACA;MACA;IAEA;IAEA;AACA;AACA;IACAC;MACA;QACA,2BACA;MAAA;IACA;EAkGA;AACA;AAAA,2B;;;;;;;;;;;;;AC7VA;AAAA;AAAA;AAAA;AAA88B,CAAgB,i8BAAG,EAAC,C;;;;;;;;;;;ACAl+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-parse/u-parse.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-parse.vue?vue&type=template&id=1674d3be&\"\nvar renderjs\nimport script from \"./u-parse.vue?vue&type=script&lang=js&\"\nexport * from \"./u-parse.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-parse.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-parse/u-parse.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-parse.vue?vue&type=template&id=1674d3be&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-parse.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-parse.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view id=\"_root\" :class=\"(selectable?'_select ':'')+'_root'\">\r\n    <slot v-if=\"!nodes[0]\" />\r\n    <!-- #ifndef APP-PLUS-NVUE -->\r\n    <node v-else :childs=\"nodes\" :opts=\"[lazyLoad,loadingImg,errorImg,showImgMenu]\" />\r\n    <!-- #endif -->\r\n    <!-- #ifdef APP-PLUS-NVUE -->\r\n    <web-view ref=\"web\" src=\"/static/app-plus/mp-html/local.html\" :style=\"'margin-top:-2px;height:' + height + 'px'\" @onPostMessage=\"_onMessage\" />\r\n    <!-- #endif -->\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n\timport props from './props.js';\r\n/**\r\n * mp-html v2.0.4\r\n * @description 富文本组件\r\n * @tutorial https://github.com/jin-yufeng/mp-html\r\n * @property {String}\t\t\tbgColor\t\t背景颜色，只适用与APP-PLUS-NVUE\r\n * @property {String}\t\t\tcontent\t\t用于渲染的富文本字符串（默认 true ）\r\n * @property {Boolean}\t\t\tcopyLink\t是否允许外部链接被点击时自动复制\r\n * @property {String}\t\t\tdomain\t\t主域名，用于拼接链接\r\n * @property {String}\t\t\terrorImg\t图片出错时的占位图链接\r\n * @property {Boolean}\t\t\tlazyLoad\t是否开启图片懒加载（默认 true ）\r\n * @property {string}\t\t\tloadingImg\t图片加载过程中的占位图链接\r\n * @property {Boolean}\t\t\tpauseVideo\t是否在播放一个视频时自动暂停其它视频（默认 true ）\r\n * @property {Boolean}\t\t\tpreviewImg\t是否允许图片被点击时自动预览（默认 true ）\r\n * @property {Boolean}\t\t\tscrollTable\t是否给每个表格添加一个滚动层使其能单独横向滚动\r\n * @property {Boolean}\t\t\tselectable\t是否开启长按复制\r\n * @property {Boolean}\t\t\tsetTitle\t是否将 title 标签的内容设置到页面标题（默认 true ）\r\n * @property {Boolean}\t\t\tshowImgMenu\t是否允许图片被长按时显示菜单（默认 true ）\r\n * @property {Object}\t\t\ttagStyle\t标签的默认样式\r\n * @property {Boolean | Number}\tuseAnchor\t是否使用锚点链接\r\n * \r\n * @event {Function}\tload\tdom 结构加载完毕时触发\r\n * @event {Function}\tready\t所有图片加载完毕时触发\r\n * @event {Function}\timgTap\t图片被点击时触发\r\n * @event {Function}\tlinkTap\t链接被点击时触发\r\n * @event {Function}\terror\t媒体加载出错时触发\r\n */\r\nconst plugins=[]\r\nconst parser = require('./parser')\r\n// #ifndef APP-PLUS-NVUE\r\nimport node from './node/node'\r\n// #endif\r\n// #ifdef APP-PLUS-NVUE\r\nconst dom = weex.requireModule('dom')\r\n// #endif\r\nexport default {\r\n  name: 'mp-html',\r\n  data() {\r\n    return {\r\n      nodes: [],\r\n      // #ifdef APP-PLUS-NVUE\r\n      height: 0\r\n      // #endif\r\n    }\r\n  },\r\n  mixins:[props],\r\n  // #ifndef APP-PLUS-NVUE\r\n  components: {\r\n    node\r\n  },\r\n  // #endif\r\n  watch: {\r\n    content(content) {\r\n      this.setContent(content)\r\n    }\r\n  },\r\n  created() {\r\n    this.plugins = []\r\n    for (let i = plugins.length; i--;)\r\n      this.plugins.push(new plugins[i](this))\r\n  },\r\n  mounted() {\r\n    if (this.content && !this.nodes.length)\r\n      this.setContent(this.content)\r\n  },\r\n  beforeDestroy() {\r\n    this._hook('onDetached')\r\n    clearInterval(this._timer)\r\n  },\r\n  methods: {\r\n    /**\r\n     * @description 将锚点跳转的范围限定在一个 scroll-view 内\r\n     * @param {Object} page scroll-view 所在页面的示例\r\n     * @param {String} selector scroll-view 的选择器\r\n     * @param {String} scrollTop scroll-view scroll-top 属性绑定的变量名\r\n     */\r\n    in(page, selector, scrollTop) {\r\n      // #ifndef APP-PLUS-NVUE\r\n      if (page && selector && scrollTop)\r\n        this._in = {\r\n          page,\r\n          selector,\r\n          scrollTop\r\n        }\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 锚点跳转\r\n     * @param {String} id 要跳转的锚点 id\r\n     * @param {Number} offset 跳转位置的偏移量\r\n     * @returns {Promise}\r\n     */\r\n    navigateTo(id, offset) {\r\n      return new Promise((resolve, reject) => {\r\n        if (!this.useAnchor)\r\n          return reject('Anchor is disabled')\r\n        offset = offset || parseInt(this.useAnchor) || 0\r\n        // #ifdef APP-PLUS-NVUE\r\n        if (!id) {\r\n          dom.scrollToElement(this.$refs.web, {\r\n            offset\r\n          })\r\n          resolve()\r\n        } else {\r\n          this._navigateTo = {\r\n            resolve,\r\n            reject,\r\n            offset\r\n          }\r\n          this.$refs.web.evalJs('uni.postMessage({data:{action:\"getOffset\",offset:(document.getElementById(' + id + ')||{}).offsetTop}})')\r\n        }\r\n        // #endif\r\n        // #ifndef APP-PLUS-NVUE\r\n        let deep = ' '\r\n        // #ifdef MP-WEIXIN || MP-QQ || MP-TOUTIAO\r\n        deep = '>>>'\r\n        // #endif\r\n        const selector = uni.createSelectorQuery()\r\n          // #ifndef MP-ALIPAY\r\n          .in(this._in ? this._in.page : this)\r\n          // #endif\r\n          .select((this._in ? this._in.selector : '._root') + (id ? `${deep}#${id}` : '')).boundingClientRect()\r\n        if (this._in)\r\n          selector.select(this._in.selector).scrollOffset()\r\n            .select(this._in.selector).boundingClientRect() // 获取 scroll-view 的位置和滚动距离\r\n        else\r\n          selector.selectViewport().scrollOffset() // 获取窗口的滚动距离\r\n        selector.exec(res => {\r\n          if (!res[0])\r\n            return reject('Label not found')\r\n          const scrollTop = res[1].scrollTop + res[0].top - (res[2] ? res[2].top : 0) + offset\r\n          if (this._in)\r\n            // scroll-view 跳转\r\n            this._in.page[this._in.scrollTop] = scrollTop\r\n          else\r\n            // 页面跳转\r\n            uni.pageScrollTo({\r\n              scrollTop,\r\n              duration: 300\r\n            })\r\n          resolve()\r\n        })\r\n        // #endif\r\n      })\r\n    },\r\n\r\n    /**\r\n     * @description 获取文本内容\r\n     * @return {String}\r\n     */\r\n    getText() {\r\n      let text = '';\r\n      (function traversal(nodes) {\r\n        for (let i = 0; i < nodes.length; i++) {\r\n          const node = nodes[i]\r\n          if (node.type == 'text')\r\n            text += node.text.replace(/&amp;/g, '&')\r\n          else if (node.name == 'br')\r\n            text += '\\n'\r\n          else {\r\n            // 块级标签前后加换行\r\n            const isBlock = node.name == 'p' || node.name == 'div' || node.name == 'tr' || node.name == 'li' || (node.name[0] == 'h' && node.name[1] > '0' && node.name[1] < '7')\r\n            if (isBlock && text && text[text.length - 1] != '\\n')\r\n              text += '\\n'\r\n            // 递归获取子节点的文本\r\n            if (node.children)\r\n              traversal(node.children)\r\n            if (isBlock && text[text.length - 1] != '\\n')\r\n              text += '\\n'\r\n            else if (node.name == 'td' || node.name == 'th')\r\n              text += '\\t'\r\n          }\r\n        }\r\n      })(this.nodes)\r\n      return text\r\n    },\r\n\r\n    /**\r\n     * @description 获取内容大小和位置\r\n     * @return {Promise}\r\n     */\r\n    getRect() {\r\n      return new Promise((resolve, reject) => {\r\n        uni.createSelectorQuery()\r\n          // #ifndef MP-ALIPAY\r\n          .in(this)\r\n          // #endif\r\n          .select('#_root').boundingClientRect().exec(res => res[0] ? resolve(res[0]) : reject('Root label not found'))\r\n      })\r\n    },\r\n\r\n    /**\r\n     * @description 设置内容\r\n     * @param {String} content html 内容\r\n     * @param {Boolean} append 是否在尾部追加\r\n     */\r\n    setContent(content, append) {\r\n      if (!append || !this.imgList)\r\n        this.imgList = []\r\n      const nodes = new parser(this).parse(content)\r\n      // #ifdef APP-PLUS-NVUE\r\n      if (this._ready)\r\n        this._set(nodes, append)\r\n      // #endif\r\n      this.$set(this, 'nodes', append ? (this.nodes || []).concat(nodes) : nodes)\r\n\r\n      // #ifndef APP-PLUS-NVUE\r\n      this._videos = []\r\n      this.$nextTick(() => {\r\n        this._hook('onLoad')\r\n        this.$emit('load')\r\n      })\r\n\r\n      // 等待图片加载完毕\r\n      let height\r\n      clearInterval(this._timer)\r\n      this._timer = setInterval(() => {\r\n        this.getRect().then(rect => {\r\n          // 350ms 总高度无变化就触发 ready 事件\r\n          if (rect.height == height) {\r\n            this.$emit('ready', rect)\r\n            clearInterval(this._timer)\r\n          }\r\n          height = rect.height\r\n        }).catch(() => { })\r\n      }, 350)\r\n      // #endif\r\n    },\r\n\r\n    /**\r\n     * @description 调用插件钩子函数\r\n     */\r\n    _hook(name) {\r\n      for (let i = plugins.length; i--;)\r\n        if (this.plugins[i][name])\r\n          this.plugins[i][name]()\r\n    },\r\n\r\n    // #ifdef APP-PLUS-NVUE\r\n    /**\r\n     * @description 设置内容\r\n     */\r\n    _set(nodes, append) {\r\n      this.$refs.web.evalJs('setContent(' + JSON.stringify(nodes) + ',' + JSON.stringify([this.bgColor, this.errorImg, this.loadingImg, this.pauseVideo, this.scrollTable, this.selectable]) + ',' + append + ')')\r\n    },\r\n\r\n    /**\r\n     * @description 接收到 web-view 消息\r\n     */\r\n    _onMessage(e) {\r\n      const message = e.detail.data[0]\r\n      switch (message.action) {\r\n        // web-view 初始化完毕\r\n        case 'onJSBridgeReady':\r\n          this._ready = true\r\n          if (this.nodes)\r\n            this._set(this.nodes)\r\n          break\r\n        // 内容 dom 加载完毕\r\n        case 'onLoad':\r\n          this.height = message.height\r\n          this._hook('onLoad')\r\n          this.$emit('load')\r\n          break\r\n        // 所有图片加载完毕\r\n        case 'onReady':\r\n          this.getRect().then(res => {\r\n            this.$emit('ready', res)\r\n          }).catch(() => { })\r\n          break\r\n        // 总高度发生变化\r\n        case 'onHeightChange':\r\n          this.height = message.height\r\n          break\r\n        // 图片点击\r\n        case 'onImgTap':\r\n          this.$emit('imgTap', message.attrs)\r\n          if (this.previewImg)\r\n            uni.previewImage({\r\n              current: parseInt(message.attrs.i),\r\n              urls: this.imgList\r\n            })\r\n          break\r\n        // 链接点击\r\n        case 'onLinkTap':\r\n          const href = message.attrs.href\r\n          this.$emit('linkTap', message.attrs)\r\n          if (href) {\r\n            // 锚点跳转\r\n            if (href[0] == '#') {\r\n              if (this.useAnchor)\r\n                dom.scrollToElement(this.$refs.web, {\r\n                  offset: message.offset\r\n                })\r\n            }\r\n            // 打开外链\r\n            else if (href.includes('://')) {\r\n              if (this.copyLink)\r\n                plus.runtime.openWeb(href)\r\n            }\r\n            else\r\n              uni.navigateTo({\r\n                url: href,\r\n                fail() {\r\n                  wx.switchTab({\r\n                    url: href\r\n                  })\r\n                }\r\n              })\r\n          }\r\n          break\r\n        // 获取到锚点的偏移量\r\n        case 'getOffset':\r\n          if (typeof message.offset == 'number') {\r\n            dom.scrollToElement(this.$refs.web, {\r\n              offset: message.offset + this._navigateTo.offset\r\n            })\r\n            this._navigateTo.resolve()\r\n          } else\r\n            this._navigateTo.reject('Label not found')\r\n          break\r\n        // 点击\r\n        case 'onClick':\r\n          this.$emit('tap')\r\n          break\r\n        // 出错\r\n        case 'onError':\r\n          this.$emit('error', {\r\n            source: message.source,\r\n            attrs: message.attrs\r\n          })\r\n      }\r\n    }\r\n    // #endif\r\n  }\r\n}\r\n</script>\r\n\r\n<style>\r\n/* #ifndef APP-PLUS-NVUE */\r\n/* 根节点样式 */\r\n._root {\r\n  overflow: auto;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n\r\n/* 长按复制 */\r\n._select {\r\n  user-select: text;\r\n}\r\n/* #endif */\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-parse.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-parse.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692280968\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}