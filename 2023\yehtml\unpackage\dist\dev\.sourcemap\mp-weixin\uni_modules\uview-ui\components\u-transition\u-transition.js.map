{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-transition/u-transition.vue?cec6", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-transition/u-transition.vue?e818", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-transition/u-transition.vue?4ce5", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-transition/u-transition.vue?1215", "uni-app:///uni_modules/uview-ui/components/u-transition/u-transition.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-transition/u-transition.vue?c237", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-transition/u-transition.vue?703b"], "names": ["name", "data", "inited", "viewStyle", "status", "transitionEnded", "display", "classes", "computed", "mergeStyle", "customStyle", "transitionDuration", "transitionTimingFunction", "uni", "mixins", "watch", "show", "handler", "newVal", "immediate"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjBA;AAAA;AAAA;AAAA;AAAuqB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;ACe3rB;AAEA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,eAiBA;EACAA;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;MAAA;MACAC;IACA;EACA;;EACAC;IACAC;MACA;QAAAC;MACA;QAEAC;QACA;QACAC;MAAA,GAGAC,+BACAV;IAEA;EACA;EACA;EACAW;EACAC;IACAC;MACAC;QACA;;QAKAC;MAEA;MACA;MACAC;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/EA;AAAA;AAAA;AAAA;AAA8xC,CAAgB,kvCAAG,EAAC,C;;;;;;;;;;;ACAlzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-transition/u-transition.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-transition.vue?vue&type=template&id=8e60ec6e&scoped=true&\"\nvar renderjs\nimport script from \"./u-transition.vue?vue&type=script&lang=js&\"\nexport * from \"./u-transition.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-transition.vue?vue&type=style&index=0&id=8e60ec6e&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"8e60ec6e\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-transition/u-transition.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-transition.vue?vue&type=template&id=8e60ec6e&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.inited ? _vm.__get_style([_vm.mergeStyle]) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-transition.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-transition.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t\tv-if=\"inited\"\r\n\t\tclass=\"u-transition\"\r\n\t\tref=\"u-transition\"\r\n\t\t@tap=\"clickHandler\"\r\n\t\t:class=\"classes\"\r\n\t\t:style=\"[mergeStyle]\"\r\n\t\t@touchmove=\"noop\"\r\n\t>\r\n\t\t<slot />\r\n\t</view>\r\n</template>\r\n\r\n<script>\r\nimport props from './props.js';\r\n// 组件的methods方法，由于内容较长，写在外部文件中通过mixin引入\r\nimport transition from \"./transition.js\";\r\n/**\r\n * transition  动画组件\r\n * @description\r\n * @tutorial\r\n * @property {String}\t\t\tshow\t\t\t是否展示组件 （默认 false ）\r\n * @property {String}\t\t\tmode\t\t\t使用的动画模式 （默认 'fade' ）\r\n * @property {String | Number}\tduration\t\t动画的执行时间，单位ms （默认 '300' ）\r\n * @property {String}\t\t\ttimingFunction\t使用的动画过渡函数 （默认 'ease-out' ）\r\n * @property {Object}\t\t\tcustomStyle\t\t自定义样式\r\n * @event {Function} before-enter\t进入前触发\r\n * @event {Function} enter\t\t\t进入中触发\r\n * @event {Function} after-enter\t进入后触发\r\n * @event {Function} before-leave\t离开前触发\r\n * @event {Function} leave\t\t\t离开中触发\r\n * @event {Function} after-leave\t离开后触发\r\n * @example\r\n */\r\nexport default {\r\n\tname: 'u-transition',\r\n\tdata() {\r\n\t\treturn {\r\n\t\t\tinited: false, // 是否显示/隐藏组件\r\n\t\t\tviewStyle: {}, // 组件内部的样式\r\n\t\t\tstatus: '', // 记录组件动画的状态\r\n\t\t\ttransitionEnded: false, // 组件是否结束的标记\r\n\t\t\tdisplay: false, // 组件是否展示\r\n\t\t\tclasses: '', // 应用的类名\r\n\t\t}\r\n\t},\r\n\tcomputed: {\r\n\t    mergeStyle() {\r\n\t        const { viewStyle, customStyle } = this\r\n\t        return {\r\n\t            // #ifndef APP-NVUE\r\n\t            transitionDuration: `${this.duration}ms`,\r\n\t            // display: `${this.display ? '' : 'none'}`,\r\n\t\t\t\ttransitionTimingFunction: this.timingFunction,\r\n\t            // #endif\r\n\t\t\t\t// 避免自定义样式影响到动画属性，所以写在viewStyle前面\r\n\t            ...uni.$u.addStyle(customStyle),\r\n\t            ...viewStyle\r\n\t        }\r\n\t    }\r\n\t},\r\n\t// 将mixin挂在到组件中，uni.$u.mixin实际上为一个vue格式对象\r\n\tmixins: [uni.$u.mpMixin, uni.$u.mixin, transition, props],\r\n\twatch: {\r\n\t\tshow: {\r\n\t\t\thandler(newVal) {\r\n\t\t\t\t// vue和nvue分别执行不同的方法\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tnewVal ? this.nvueEnter() : this.nvueLeave()\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tnewVal ? this.vueEnter() : this.vueLeave()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 表示同时监听初始化时的props的show的意思\r\n\t\t\timmediate: true\r\n\t\t}\r\n\t}\r\n}\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import '../../libs/css/components.scss';\r\n\r\n/* #ifndef APP-NVUE */\r\n// vue版本动画相关的样式抽离在外部文件\r\n@import './vue.ani-style.scss';\r\n/* #endif */\r\n\r\n.u-transition {}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-transition.vue?vue&type=style&index=0&id=8e60ec6e&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-transition.vue?vue&type=style&index=0&id=8e60ec6e&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692293525\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}