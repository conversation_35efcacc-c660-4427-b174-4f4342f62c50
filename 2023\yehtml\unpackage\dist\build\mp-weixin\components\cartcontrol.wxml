<view style="display:flex;flex-direction:row;height:27px;" class="data-v-689b239a"><view class="food-control data-v-689b239a"><view data-event-opts="{{[['tap',[['decreaseCart',['$0'],['food']]]]]}}" hidden="{{!(food.count>0)}}" class="cont data-v-689b239a" style="margin-top:2px;" bindtap="__e"><image style="width:20px;height:20px;" src="/static/des.png" mode class="data-v-689b239a"></image></view><input hidden="{{!(food.count>0)}}" style="margin:0 2px;width:36px;padding:0 1px;border:1px solid #C8C7CC;border-radius:3px;" type="number" maxlength="3" data-event-opts="{{[['input',[['__set_model',['$0','count','$event',[]],['food']],['inputCart',['$0'],['food']]]]]}}" value="{{food.count}}" bindinput="__e" class="data-v-689b239a"/></view><view data-event-opts="{{[['tap',[['addCart',['$0'],['food']]]]]}}" class="cont data-v-689b239a" style="flex:1;margin-top:2px;" bindtap="__e"><image style="width:20px;height:20px;color:#CCCCCC;" src="/static/add2.png" mode class="data-v-689b239a"></image></view></view>