(global["webpackJsonp"]=global["webpackJsonp"]||[]).push([["uni_modules/uview-ui/components/u-count-down/u-count-down"],{"149a":function(i,t,e){"use strict";(function(i){var n=e("47a9");Object.defineProperty(t,"__esModule",{value:!0}),t.default=void 0;var a=n(e("9f81")),u=e("fcbd"),r={name:"u-count-down",mixins:[i.$u.mpMixin,i.$u.mixin,a.default],data:function(){return{timer:null,timeData:(0,u.parseTimeData)(0),formattedTime:"0",runing:!1,endTime:0,remainTime:0}},watch:{time:function(i){this.reset()}},mounted:function(){this.init()},methods:{init:function(){this.reset()},start:function(){this.runing||(this.runing=!0,this.endTime=Date.now()+this.remainTime,this.toTick())},toTick:function(){this.millisecond?this.microTick():this.macroTick()},macroTick:function(){var i=this;this.clearTimeout(),this.timer=setTimeout((function(){var t=i.getRemainTime();(0,u.isSameSecond)(t,i.remainTime)&&0!==t||i.setRemainTime(t),0!==i.remainTime&&i.macroTick()}),30)},microTick:function(){var i=this;this.clearTimeout(),this.timer=setTimeout((function(){i.setRemainTime(i.getRemainTime()),0!==i.remainTime&&i.microTick()}),50)},getRemainTime:function(){return Math.max(this.endTime-Date.now(),0)},setRemainTime:function(i){this.remainTime=i;var t=(0,u.parseTimeData)(i);this.$emit("change",t),this.formattedTime=(0,u.parseFormat)(this.format,t),i<=0&&(this.pause(),this.$emit("finish"))},reset:function(){this.pause(),this.remainTime=this.time,this.setRemainTime(this.remainTime),this.autoStart&&this.start()},pause:function(){this.runing=!1,this.clearTimeout()},clearTimeout:function(i){function t(){return i.apply(this,arguments)}return t.toString=function(){return i.toString()},t}((function(){clearTimeout(this.timer),this.timer=null}))},beforeDestroy:function(){this.clearTimeout()}};t.default=r}).call(this,e("df3c")["default"])},"20bc":function(i,t,e){},4921:function(i,t,e){"use strict";e.d(t,"b",(function(){return n})),e.d(t,"c",(function(){return a})),e.d(t,"a",(function(){}));var n=function(){var i=this.$createElement;this._self._c},a=[]},5979:function(i,t,e){"use strict";var n=e("20bc"),a=e.n(n);a.a},"7dea":function(i,t,e){"use strict";e.r(t);var n=e("4921"),a=e("9a73");for(var u in a)["default"].indexOf(u)<0&&function(i){e.d(t,i,(function(){return a[i]}))}(u);e("5979");var r=e("828b"),o=Object(r["a"])(a["default"],n["b"],n["c"],!1,null,"f147a924",null,!1,n["a"],void 0);t["default"]=o.exports},"9a73":function(i,t,e){"use strict";e.r(t);var n=e("149a"),a=e.n(n);for(var u in n)["default"].indexOf(u)<0&&function(i){e.d(t,i,(function(){return n[i]}))}(u);t["default"]=a.a}}]);
;(global["webpackJsonp"] = global["webpackJsonp"] || []).push([
    'uni_modules/uview-ui/components/u-count-down/u-count-down-create-component',
    {
        'uni_modules/uview-ui/components/u-count-down/u-count-down-create-component':(function(module, exports, __webpack_require__){
            __webpack_require__('df3c')['createComponent'](__webpack_require__("7dea"))
        })
    },
    [['uni_modules/uview-ui/components/u-count-down/u-count-down-create-component']]
]);
