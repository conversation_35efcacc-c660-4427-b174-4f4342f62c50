@charset "UTF-8";
/**
 * 下方引入的为uView UI的集成样式文件，为scss预处理器，其中包含了一些"u-"开头的自定义变量
 * 使用的时候，请将下面的一行复制到您的uniapp项目根目录的uni.scss中即可
 * uView自定义的css类名和scss变量，均以"u-"开头，不会造成冲突，请放心使用 
 */
/* 变量 */
view.data-v-3a8aa7a9, scroll-view.data-v-3a8aa7a9, swiper-item.data-v-3a8aa7a9 {
  display: flex;
  flex-direction: column;
  flex-shrink: 0;
  flex-grow: 0;
  flex-basis: auto;
  align-items: stretch;
  align-content: flex-start;
}
.u-switch.data-v-3a8aa7a9 {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  position: relative;
  background-color: #fff;
  border-width: 1px;
  border-radius: 100px;
  transition: background-color 0.4s;
  border-color: rgba(0, 0, 0, 0.12);
  border-style: solid;
  justify-content: flex-end;
  align-items: center;
  overflow: hidden;
}
.u-switch__node.data-v-3a8aa7a9 {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  border-radius: 100px;
  background-color: #fff;
  border-radius: 100px;
  box-shadow: 1px 1px 1px 0 rgba(0, 0, 0, 0.25);
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  transition-duration: 0.4s;
  transition-timing-function: cubic-bezier(0.3, 1.05, 0.4, 1.05);
}
.u-switch__bg.data-v-3a8aa7a9 {
  position: absolute;
  border-radius: 100px;
  background-color: #FFFFFF;
  transition-property: -webkit-transform;
  transition-property: transform;
  transition-property: transform, -webkit-transform;
  transition-duration: 0.4s;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
  transition-timing-function: ease;
}
.u-switch--disabled.data-v-3a8aa7a9 {
  opacity: 0.6;
}

