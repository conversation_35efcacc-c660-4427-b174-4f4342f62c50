<template>
  <view class="cart">
    <view class="tip">* 向左滑动可以删除项目</view>
    <page-box-empty v-if="(!shippingCarInfo || shippingCarInfo.number == 0)" title="您还没有挑选任何商品" sub-title="可以去看看有那些想买的～" :show-btn="true" />
    <view v-if="shippingCarInfo" class="order">
      <u-swipe-action>
        <u-swipe-action-item v-for="(item, index) in shippingCarInfo.items" :key="index" :show="item.show" :index="index" :options="options" @click="deleterecord0">
          <view class="item">
            <view class="left">
              <image :src="item.pic" mode="aspectFill"></image>
            </view>
            <view class="content">
              <view class="title">{{ item.name }}</view>
              <view class="type">
                <text v-for="(item2, index2) in item.sku" :key="'b' + index2">{{ item2.optionName }}:{{ item2.optionValueName }}/</text>
                <text v-for="(item3, index3) in item.additions" :key="'c' + index3">{{ item3.pname }}:{{ item3.name }}/</text>
              </view>
              <view class="delivery-time">
                <u-number-box v-model="item.number" :name="index" :min="item.minBuyNumber" :max="item.stores" @change="numberChange0"></u-number-box>
              </view>
            </view>
            <view class="right">
              <view class="price">
                <text>￥</text>{{ item.price }}
              </view>
            </view>
          </view>
        </u-swipe-action-item>
      </u-swipe-action>
      <!-- <view class="total">
        共 <text class="number">{{ shippingCarInfo.number }}</text> 件商品 合计:
        <text class="total-price">
          <text>￥</text>{{ shippingCarInfo.price }}
        </text>
      </view>
      <view v-if="shippingCarInfo && shippingCarInfo.number > 0" class="submit0">
        <u-button type="error" @click="submit0">提交订单</u-button>
      </view> -->

      <view class="settlement-box">
        <view class="left-price">
          <view class="total">
            共 <text class="number">{{ shippingCarInfo.number }}</text> 件商品 合计
            <text class="total-price">
              <text>￥</text>{{ shippingCarInfo.price }}
            </text>
          </view>
        </view>
        <view v-if="shippingCarInfo && shippingCarInfo.number > 0" @click="submit0" class="to-pay-btn">结算</view>
      </view>
    </view>

    <view class="recommend">
      <view class="goods-container">
        <view class="recommend-title">
          <img style="height: 36px;" src="/static/images/cart/recommend-title.jpg">
        </view>
        <view v-if="goodsRecommend" class="goodsRecommend">
          <view class="goods-container">
            <list1 :list="goodsRecommend" type="goods"></list1>
          </view>
        </view>
      </view>
    </view>

  </view>
</template>

<script>
  import empty from 'empty-value'
  import list1 from '@/components/list/list1'

  const TOOLS = require('@/common/tools')
  export default {
    components: {
      list1,
    },
    data() {
      return {
        tabIndex: 3,
        tabs: [{
            name: '自营商品'
          },
          {
            name: '京东商品'
          }
        ],
        shippingCarInfo: undefined,
        jdvopCartInfo: undefined,
        options: [{
          text: '删除',
          style: {
            backgroundColor: '#e64340',
          }
        }],
        adPosition: {},
        goodsRecommend: undefined,
      }
    },
    created() {

    },
    mounted() {

    },
    onReady() {

    },
    onLoad(e) {
      this._goodsRecommend()
      this._adPosition()
    },
    onShow() {
      this._shippingCarInfo()
    },
    methods: {
      async _shippingCarInfo() {
        // https://www.yuque.com/apifm/nu0f75/awql14
        const res = await this.$wxapi.shippingCarInfo(this.token)
        if (res.code == 0) {
          res.data.items.forEach(ele => {
            ele.show = false
          })
          this.shippingCarInfo = res.data
        } else {
          this.shippingCarInfo = null
        }
      },
      async _jdvopCartInfo() {
        // https://www.yuque.com/apifm/nu0f75/gwat37
        const res = await this.$wxapi.jdvopCartInfo(this.token)
        if (res.code == 0) {
          res.data.items.forEach(ele => {
            ele.show = false
          })
          this.jdvopCartInfo = res.data
        } else {
          this.jdvopCartInfo = null
        }
      },
      async deleterecord0(e) {
        // 删除购物车记录 https://www.yuque.com/apifm/nu0f75/pndgyc
        const item = this.shippingCarInfo.items[e.index]
        const res = await this.$wxapi.shippingCarInfoRemoveItem(this.token, item.key)
        if (res.code == 0) {
          uni.showToast({
            title: '已删除'
          })
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        }
        this._shippingCarInfo()
        TOOLS.showTabBarBadge()
      },
      async deleterecord1(e) {
        // 删除购物车记录 https://www.yuque.com/apifm/nu0f75/syqlot
        const item = this.jdvopCartInfo.items[e.index]
        const res = await this.$wxapi.jdvopCartRemove(this.token, item.key)
        if (res.code == 0) {
          uni.showToast({
            title: '已删除'
          })
        } else {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        }
        this._jdvopCartInfo()
        TOOLS.showTabBarBadge()
      },
      async numberChange0(e) {
        const item = this.shippingCarInfo.items[e.name]
        // https://www.yuque.com/apifm/nu0f75/kbi5b0
        const res = await this.$wxapi.shippingCarInfoModifyNumber(this.token, item.key, e.value)
        if (res.code != 0) {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        } else {
          this._shippingCarInfo()
          TOOLS.showTabBarBadge()
        }
      },
      async numberChange1(e) {
        // https://www.yuque.com/apifm/nu0f75/vkd6q5
        const item = this.jdvopCartInfo.items[e.name]
        const res = await this.$wxapi.jdvopCartModifyNumber(this.token, item.key, e.value)
        if (res.code != 0) {
          uni.showToast({
            title: res.msg,
            icon: 'none'
          })
        } else {
          this._jdvopCartInfo()
          TOOLS.showTabBarBadge()
        }
      },
      open(index) {
        this.shippingCarInfo.items.forEach(ele => {
          ele.show = false
        })
        this.shippingCarInfo.items[index].show = true
      },
      submit0() {
        uni.navigateTo({
          url: '../pay/order?mod=cart&cartType=apifm'
        })
      },
      submit1() {
        uni.navigateTo({
          url: '../pay/order?mod=cart&cartType=jdvop'
        })
      },
      async _adPosition() {
        // https://www.yuque.com/apifm/nu0f75/ypi79p
        const res = await this.$wxapi.adPositionBatch('cart_banner')
        if (res.code == 0) {
          res.data.forEach(ele => {
            this.adPosition[ele.key] = ele
            if (ele.key == 'indexPop') {
              this.adPositionIndexPop = true
            }
          })
        }
      },
      goUrl(url) {
        this.adPositionIndexPop = false
        if (url) {
          uni.navigateTo({
            url
          })
        }
      },
      async _goodsRecommend() {
        // https://www.yuque.com/apifm/nu0f75/wg5t98
        const res = await this.$wxapi.goodsv2({
          recommendStatus: 1
        })
        if (res.code == 0) {
          let _goods = []
          let goods = []

          _goods = res.data.result

          if (!empty(_goods)) {
            _goods.forEach(async (good, index) => {
              good.image = good.pic
              good.title = good.name
              goods.push(good)
            })
          }

          this.goodsRecommend = goods
        }
      },
      goDetail(item) {
        uni.navigateTo({
          url: '/pages/goods/detail?id=' + item.id
        })
      },
    }
  }
</script>
<style scoped lang="scss">
  .cart {
    padding-top: 30rpx;
    background: #F3F3F3;
  }

  .tip {
    width: 660rpx;
    margin: 16rpx auto;
    font-size: 24rpx;
    color: #858996;
  }

  .title {
    font-size: 90rpx;
    color: #2b2b2b;
    position: relative;

    text {
      width: 7px;
      height: 7px;
      position: absolute;
      border: 2px solid #a78845;
      border-radius: 50%;
    }
  }

  .title-sub {
    margin-left: 25px;
    color: #2b2b2b;
    font-size: 36rpx;
    font-weight: 300;
  }

  .banner-wrapper .banner {
    margin: 20rpx;
  }

  .order {
    width: 660rpx;
    background-color: #ffffff;
    margin: 0 auto;
    border-radius: 20rpx;
    box-sizing: border-box;
    padding: 20rpx;
    font-size: 28rpx;

    .item {
      display: flex;
      margin: 0;
      margin-bottom: 30rpx;

      .left {
        margin-right: 20rpx;

        image {
          width: 160rpx;
          height: 160rpx;
          border-radius: 20rpx;
        }
      }

      .content {
        flex: 1;

        .title {
          line-height: 50rpx;
          font-size: 30rpx;
        }

        .type {
          margin: 14rpx 0;
          font-size: 24rpx;
          color: $u-tips-color;
        }

        .delivery-time {
          color: #e5d001;
          font-size: 24rpx;
        }
      }

      .right {
        margin-left: 10rpx;
        padding-top: 20rpx;
        text-align: right;

        .decimal {
          font-size: 24rpx;
          margin-top: 4rpx;
        }

        .number {
          color: $u-tips-color;
          font-size: 24rpx;
        }

        .price {
          text {
            font-size: 26rpx;
          }

          font-size: 38rpx;
          color: #e64340;
          margin-right: 20rpx;
        }
      }
    }

    .total {
      text-align: right;
      font-size: 24rpx;

      .number {
        color: #e64340;
        padding: 0 8rpx;
        font-size: 30rpx;
      }

      .total-price {
        text {
          font-size: 26rpx;
          padding: 0 8rpx;
        }

        font-size: 38rpx;
        color: #e64340;
      }
    }

    .bottom {
      display: flex;
      margin-top: 40rpx;
      padding: 0 10rpx;
      justify-content: space-between;
      align-items: center;

      .btn {
        line-height: 52rpx;
        width: 160rpx;
        border-radius: 26rpx;
        border: 2rpx solid $u-border-color;
        font-size: 26rpx;
        text-align: center;
        color: $u-info-dark;
      }

      .evaluate {
        color: $u-warning-dark;
        border-color: $u-warning-dark;
      }
    }
  }

  .submit {
    margin-top: 64rpx;
  }

  .goods-container {
    padding: 0 24rpx 60rpx 24rpx;
  }

  .recommend {
    background: #F3F3F3;
  }

  .recommend-title {
    padding: 40rpx 0 20rpx 0;
    text-align: center;
  }


  .settlement-box {
    display: flex;
    justify-content: space-between;
    width: 100%;
    height: 100rpx;
    position: fixed;
    bottom: 0;
    left: 0;
    background-color: #FAFAFA;
    z-index: 999;
  }

  .settlement-box .to-pay-btn {
    text-align: center;
    line-height: 76rpx;
    width: 200rpx;
    height: 76rpx;
    background: linear-gradient(270deg, #FF972A 0%, #FF444A 100%);
    border-radius: 38rpx;
    margin-top: 12rpx;
    margin-right: 20rpx;
    color: white;
  }

  .settlement-box .to-pay-btn.no-select {
    background-color: #ccc;
  }

  .settlement-box .left-price {
    display: flex;
    width: 510rpx;
    justify-content: space-between;
    line-height: 100rpx;
    padding: 0 30rpx 0 32rpx;
    font-size: 28rpx;
    box-sizing: border-box;
  }

  .settlement-box .total {
    color: #606266;
  }
</style>