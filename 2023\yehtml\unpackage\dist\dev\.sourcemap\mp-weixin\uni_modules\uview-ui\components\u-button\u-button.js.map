{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-button/u-button.vue?cb6f", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-button/u-button.vue?c281", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-button/u-button.vue?9e34", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-button/u-button.vue?7c44", "uni-app:///uni_modules/uview-ui/components/u-button/u-button.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-button/u-button.vue?1efd", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-button/u-button.vue?736f"], "names": ["name", "mixins", "data", "computed", "bemClass", "loadingColor", "uni", "iconColorCom", "baseColor", "style", "nvueTextStyle", "textSize", "size", "methods", "clickHandler", "getphonenumber", "getuserinfo", "error", "opensetting", "launchapp"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAiI;AACjI;AAC4D;AACL;AACsC;;;AAG7F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,8EAAM;AACR,EAAE,+FAAM;AACR,EAAE,wGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,mGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,kVAEN;AACP,KAAK;AACL;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACnDA;AAAA;AAAA;AAAA;AAAmqB,CAAgB,mrBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;AC+GvrB;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AA1CA,eA2CA;EACAA;EAEAC;EAKAC;IACA;EACA;EACAC;IACA;IACAC;MACA;MACA;QACA,gBACA,UACA,2BACA,kCACA;MACA;QACA;QACA,gBACA,UACA,mBACA,kCACA;MACA;IACA;IACAC;MACA;QACA;QACA,oBACA,aACAC;MACA;MACA;QACA;MACA;MACA;IACA;IACAC;MACA;MACA;MACA;MACA;QACA;MACA;QACA;MACA;IACA;IACAC;MACA;MACA;QACA;QACAC;QACA;UACA;UACAA;QACA;QACA;UACA;UACA;UACA;UACAA;UACAA;UACAA;UACAA;UACA;YACAA;UACA;QACA;UACA;UACAA;UACAA;UACAA;QACA;MACA;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;QACAD;MACA;MACA;QACAA;MACA;MACAA;MACA;IACA;IACA;IACAE;MACA;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;EACA;EACAC;IACAC;MAAA;MACA;MACA;QACA;QACAR;UACA;QACA;MACA;IACA;IACA;IACAS;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACjSA;AAAA;AAAA;AAAA;AAA0xC,CAAgB,8uCAAG,EAAC,C;;;;;;;;;;;ACA9yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-button/u-button.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-button.vue?vue&type=template&id=2bf0e569&scoped=true&\"\nvar renderjs\nimport script from \"./u-button.vue?vue&type=script&lang=js&\"\nexport * from \"./u-button.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-button.vue?vue&type=style&index=0&id=2bf0e569&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"2bf0e569\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-button/u-button.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-button.vue?vue&type=template&id=2bf0e569&scoped=true&\"", "var components\ntry {\n  components = {\n    uLoadingIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-loading-icon/u-loading-icon\" */ \"@/uni_modules/uview-ui/components/u-loading-icon/u-loading-icon.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.baseColor, _vm.$u.addStyle(_vm.customStyle)])\n  var m0 = Number(_vm.hoverStartTime)\n  var m1 = Number(_vm.hoverStayTime)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        m0: m0,\n        m1: m1,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-button.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-button.vue?vue&type=script&lang=js&\"", "<template>\r\n    <!-- #ifndef APP-NVUE -->\r\n    <button\r\n        :hover-start-time=\"Number(hoverStartTime)\"\r\n        :hover-stay-time=\"Number(hoverStayTime)\"\r\n        :form-type=\"formType\"\r\n        :open-type=\"openType\"\r\n        :app-parameter=\"appParameter\"\r\n        :hover-stop-propagation=\"hoverStopPropagation\"\r\n        :send-message-title=\"sendMessageTitle\"\r\n        :send-message-path=\"sendMessagePath\"\r\n        :lang=\"lang\"\r\n        :data-name=\"dataName\"\r\n        :session-from=\"sessionFrom\"\r\n        :send-message-img=\"sendMessageImg\"\r\n        :show-message-card=\"showMessageCard\"\r\n        @getphonenumber=\"getphonenumber\"\r\n        @getuserinfo=\"getuserinfo\"\r\n        @error=\"error\"\r\n        @opensetting=\"opensetting\"\r\n        @launchapp=\"launchapp\"\r\n        :hover-class=\"!disabled && !loading ? 'u-button--active' : ''\"\r\n        class=\"u-button u-reset-button\"\r\n        :style=\"[baseColor, $u.addStyle(customStyle)]\"\r\n        @tap=\"clickHandler\"\r\n        :class=\"bemClass\"\r\n    >\r\n        <template v-if=\"loading\">\r\n            <u-loading-icon\r\n                :mode=\"loadingMode\"\r\n                :size=\"loadingSize * 1.15\"\r\n                :color=\"loadingColor\"\r\n            ></u-loading-icon>\r\n            <text\r\n                class=\"u-button__loading-text\"\r\n                :style=\"[{ fontSize: textSize + 'px' }]\"\r\n                >{{ loadingText || text }}</text\r\n            >\r\n        </template>\r\n        <template v-else>\r\n            <u-icon\r\n                v-if=\"icon\"\r\n                :name=\"icon\"\r\n                :color=\"iconColorCom\"\r\n                :size=\"textSize * 1.35\"\r\n                :customStyle=\"{ marginRight: '2px' }\"\r\n            ></u-icon>\r\n            <slot>\r\n                <text\r\n                    class=\"u-button__text\"\r\n                    :style=\"[{ fontSize: textSize + 'px' }]\"\r\n                    >{{ text }}</text\r\n                >\r\n            </slot>\r\n        </template>\r\n    </button>\r\n    <!-- #endif -->\r\n\r\n    <!-- #ifdef APP-NVUE -->\r\n    <view\r\n        :hover-start-time=\"Number(hoverStartTime)\"\r\n        :hover-stay-time=\"Number(hoverStayTime)\"\r\n        class=\"u-button\"\r\n        :hover-class=\"\r\n            !disabled && !loading && !color && (plain || type === 'info')\r\n                ? 'u-button--active--plain'\r\n                : !disabled && !loading && !plain\r\n                ? 'u-button--active'\r\n                : ''\r\n        \"\r\n        @tap=\"clickHandler\"\r\n        :class=\"bemClass\"\r\n        :style=\"[baseColor, $u.addStyle(customStyle)]\"\r\n    >\r\n        <template v-if=\"loading\">\r\n            <u-loading-icon\r\n                :mode=\"loadingMode\"\r\n                :size=\"loadingSize * 1.15\"\r\n                :color=\"loadingColor\"\r\n            ></u-loading-icon>\r\n            <text\r\n                class=\"u-button__loading-text\"\r\n                :style=\"[nvueTextStyle]\"\r\n                :class=\"[plain && `u-button__text--plain--${type}`]\"\r\n                >{{ loadingText || text }}</text\r\n            >\r\n        </template>\r\n        <template v-else>\r\n            <u-icon\r\n                v-if=\"icon\"\r\n                :name=\"icon\"\r\n                :color=\"iconColorCom\"\r\n                :size=\"textSize * 1.35\"\r\n            ></u-icon>\r\n            <text\r\n                class=\"u-button__text\"\r\n                :style=\"[\r\n                    {\r\n                        marginLeft: icon ? '2px' : 0,\r\n                    },\r\n                    nvueTextStyle,\r\n                ]\"\r\n                :class=\"[plain && `u-button__text--plain--${type}`]\"\r\n                >{{ text }}</text\r\n            >\r\n        </template>\r\n    </view>\r\n    <!-- #endif -->\r\n</template>\r\n\r\n<script>\r\nimport button from \"../../libs/mixin/button.js\";\r\nimport openType from \"../../libs/mixin/openType.js\";\r\nimport props from \"./props.js\";\r\n/**\r\n * button 按钮\r\n * @description Button 按钮\r\n * @tutorial https://www.uviewui.com/components/button.html\r\n *\r\n * @property {Boolean}\t\t\thairline\t\t\t\t是否显示按钮的细边框 (默认 true )\r\n * @property {String}\t\t\ttype\t\t\t\t\t按钮的预置样式，info，primary，error，warning，success (默认 'info' )\r\n * @property {String}\t\t\tsize\t\t\t\t\t按钮尺寸，large，normal，mini （默认 normal）\r\n * @property {String}\t\t\tshape\t\t\t\t\t按钮形状，circle（两边为半圆），square（带圆角） （默认 'square' ）\r\n * @property {Boolean}\t\t\tplain\t\t\t\t\t按钮是否镂空，背景色透明 （默认 false）\r\n * @property {Boolean}\t\t\tdisabled\t\t\t\t是否禁用 （默认 false）\r\n * @property {Boolean}\t\t\tloading\t\t\t\t\t按钮名称前是否带 loading 图标(App-nvue 平台，在 ios 上为雪花，Android上为圆圈) （默认 false）\r\n * @property {String | Number}\tloadingText\t\t\t\t加载中提示文字\r\n * @property {String}\t\t\tloadingMode\t\t\t\t加载状态图标类型 （默认 'spinner' ）\r\n * @property {String | Number}\tloadingSize\t\t\t\t加载图标大小 （默认 15 ）\r\n * @property {String}\t\t\topenType\t\t\t\t开放能力，具体请看uniapp稳定关于button组件部分说明\r\n * @property {String}\t\t\tformType\t\t\t\t用于 <form> 组件，点击分别会触发 <form> 组件的 submit/reset 事件\r\n * @property {String}\t\t\tappParameter\t\t\t打开 APP 时，向 APP 传递的参数，open-type=launchApp时有效 （注：只微信小程序、QQ小程序有效）\r\n * @property {Boolean}\t\t\thoverStopPropagation\t指定是否阻止本节点的祖先节点出现点击态，微信小程序有效（默认 true ）\r\n * @property {String}\t\t\tlang\t\t\t\t\t指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文（默认 en ）\r\n * @property {String}\t\t\tsessionFrom\t\t\t\t会话来源，openType=\"contact\"时有效\r\n * @property {String}\t\t\tsendMessageTitle\t\t会话内消息卡片标题，openType=\"contact\"时有效\r\n * @property {String}\t\t\tsendMessagePath\t\t\t会话内消息卡片点击跳转小程序路径，openType=\"contact\"时有效\r\n * @property {String}\t\t\tsendMessageImg\t\t\t会话内消息卡片图片，openType=\"contact\"时有效\r\n * @property {Boolean}\t\t\tshowMessageCard\t\t\t是否显示会话内消息卡片，设置此参数为 true，用户进入客服会话会在右下角显示\"可能要发送的小程序\"提示，用户点击后可以快速发送小程序消息，openType=\"contact\"时有效（默认false）\r\n * @property {String}\t\t\tdataName\t\t\t\t额外传参参数，用于小程序的data-xxx属性，通过target.dataset.name获取\r\n * @property {String | Number}\tthrottleTime\t\t\t节流，一定时间内只能触发一次 （默认 0 )\r\n * @property {String | Number}\thoverStartTime\t\t\t按住后多久出现点击态，单位毫秒 （默认 0 )\r\n * @property {String | Number}\thoverStayTime\t\t\t手指松开后点击态保留时间，单位毫秒 （默认 200 )\r\n * @property {String | Number}\ttext\t\t\t\t\t按钮文字，之所以通过props传入，是因为slot传入的话（注：nvue中无法控制文字的样式）\r\n * @property {String}\t\t\ticon\t\t\t\t\t按钮图标\r\n * @property {String}\t\t\ticonColor\t\t\t\t按钮图标颜色\r\n * @property {String}\t\t\tcolor\t\t\t\t\t按钮颜色，支持传入linear-gradient渐变色\r\n * @property {Object}\t\t\tcustomStyle\t\t\t\t定义需要用到的外部样式\r\n *\r\n * @event {Function}\tclick\t\t\t非禁止并且非加载中，才能点击\r\n * @event {Function}\tgetphonenumber\topen-type=\"getPhoneNumber\"时有效\r\n * @event {Function}\tgetuserinfo\t\t用户点击该按钮时，会返回获取到的用户信息，从返回参数的detail中获取到的值同uni.getUserInfo\r\n * @event {Function}\terror\t\t\t当使用开放能力时，发生错误的回调\r\n * @event {Function}\topensetting\t\t在打开授权设置页并关闭后回调\r\n * @event {Function}\tlaunchapp\t\t打开 APP 成功的回调\r\n * @example <u-button>月落</u-button>\r\n */\r\nexport default {\r\n    name: \"u-button\",\r\n    // #ifdef MP\r\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, button, openType, props],\r\n    // #endif\r\n    // #ifndef MP\r\n    mixins: [uni.$u.mpMixin, uni.$u.mixin, props],\r\n    // #endif\r\n    data() {\r\n        return {};\r\n    },\r\n    computed: {\r\n        // 生成bem风格的类名\r\n        bemClass() {\r\n            // this.bem为一个computed变量，在mixin中\r\n            if (!this.color) {\r\n                return this.bem(\r\n                    \"button\",\r\n                    [\"type\", \"shape\", \"size\"],\r\n                    [\"disabled\", \"plain\", \"hairline\"]\r\n                );\r\n            } else {\r\n                // 由于nvue的原因，在有color参数时，不需要传入type，否则会生成type相关的类型，影响最终的样式\r\n                return this.bem(\r\n                    \"button\",\r\n                    [\"shape\", \"size\"],\r\n                    [\"disabled\", \"plain\", \"hairline\"]\r\n                );\r\n            }\r\n        },\r\n        loadingColor() {\r\n            if (this.plain) {\r\n                // 如果有设置color值，则用color值，否则使用type主题颜色\r\n                return this.color\r\n                    ? this.color\r\n                    : uni.$u.config.color[`u-${this.type}`];\r\n            }\r\n            if (this.type === \"info\") {\r\n                return \"#c9c9c9\";\r\n            }\r\n            return \"rgb(200, 200, 200)\";\r\n        },\r\n        iconColorCom() {\r\n            // 如果是镂空状态，设置了color就用color值，否则使用主题颜色，\r\n            // u-icon的color能接受一个主题颜色的值\r\n\t\t\tif (this.iconColor) return this.iconColor;\r\n\t\t\tif (this.plain) {\r\n                return this.color ? this.color : this.type;\r\n            } else {\r\n                return this.type === \"info\" ? \"#000000\" : \"#ffffff\";\r\n            }\r\n        },\r\n        baseColor() {\r\n            let style = {};\r\n            if (this.color) {\r\n                // 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色\r\n                style.color = this.plain ? this.color : \"white\";\r\n                if (!this.plain) {\r\n                    // 非镂空，背景色使用自定义的颜色\r\n                    style[\"background-color\"] = this.color;\r\n                }\r\n                if (this.color.indexOf(\"gradient\") !== -1) {\r\n                    // 如果自定义的颜色为渐变色，不显示边框，以及通过backgroundImage设置渐变色\r\n                    // weex文档说明可以写borderWidth的形式，为什么这里需要分开写？\r\n                    // 因为weex是阿里巴巴为了部门业绩考核而做的你懂的东西，所以需要这么写才有效\r\n                    style.borderTopWidth = 0;\r\n                    style.borderRightWidth = 0;\r\n                    style.borderBottomWidth = 0;\r\n                    style.borderLeftWidth = 0;\r\n                    if (!this.plain) {\r\n                        style.backgroundImage = this.color;\r\n                    }\r\n                } else {\r\n                    // 非渐变色，则设置边框相关的属性\r\n                    style.borderColor = this.color;\r\n                    style.borderWidth = \"1px\";\r\n                    style.borderStyle = \"solid\";\r\n                }\r\n            }\r\n            return style;\r\n        },\r\n        // nvue版本按钮的字体不会继承父组件的颜色，需要对每一个text组件进行单独的设置\r\n        nvueTextStyle() {\r\n            let style = {};\r\n            // 针对自定义了color颜色的情况，镂空状态下，就是用自定义的颜色\r\n            if (this.type === \"info\") {\r\n                style.color = \"#323233\";\r\n            }\r\n            if (this.color) {\r\n                style.color = this.plain ? this.color : \"white\";\r\n            }\r\n            style.fontSize = this.textSize + \"px\";\r\n            return style;\r\n        },\r\n        // 字体大小\r\n        textSize() {\r\n            let fontSize = 14,\r\n                { size } = this;\r\n            if (size === \"large\") fontSize = 16;\r\n            if (size === \"normal\") fontSize = 14;\r\n            if (size === \"small\") fontSize = 12;\r\n            if (size === \"mini\") fontSize = 10;\r\n            return fontSize;\r\n        },\r\n    },\r\n    methods: {\r\n        clickHandler() {\r\n            // 非禁止并且非加载中，才能点击\r\n            if (!this.disabled && !this.loading) {\r\n\t\t\t\t// 进行节流控制，每this.throttle毫秒内，只在开始处执行\r\n\t\t\t\tuni.$u.throttle(() => {\r\n\t\t\t\t\tthis.$emit(\"click\");\r\n\t\t\t\t}, this.throttleTime);\r\n            }\r\n        },\r\n        // 下面为对接uniapp官方按钮开放能力事件回调的对接\r\n        getphonenumber(res) {\r\n            this.$emit(\"getphonenumber\", res);\r\n        },\r\n        getuserinfo(res) {\r\n            this.$emit(\"getuserinfo\", res);\r\n        },\r\n        error(res) {\r\n            this.$emit(\"error\", res);\r\n        },\r\n        opensetting(res) {\r\n            this.$emit(\"opensetting\", res);\r\n        },\r\n        launchapp(res) {\r\n            this.$emit(\"launchapp\", res);\r\n        },\r\n    },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n@import \"../../libs/css/components.scss\";\r\n\r\n/* #ifndef APP-NVUE */\r\n@import \"./vue.scss\";\r\n/* #endif */\r\n\r\n/* #ifdef APP-NVUE */\r\n@import \"./nvue.scss\";\r\n/* #endif */\r\n\r\n$u-button-u-button-height: 40px !default;\r\n$u-button-text-font-size: 15px !default;\r\n$u-button-loading-text-font-size: 15px !default;\r\n$u-button-loading-text-margin-left: 4px !default;\r\n$u-button-large-width: 100% !default;\r\n$u-button-large-height: 50px !default;\r\n$u-button-normal-padding: 0 12px !default;\r\n$u-button-large-padding: 0 15px !default;\r\n$u-button-normal-font-size: 14px !default;\r\n$u-button-small-min-width: 60px !default;\r\n$u-button-small-height: 30px !default;\r\n$u-button-small-padding: 0px 8px !default;\r\n$u-button-mini-padding: 0px 8px !default;\r\n$u-button-small-font-size: 12px !default;\r\n$u-button-mini-height: 22px !default;\r\n$u-button-mini-font-size: 10px !default;\r\n$u-button-mini-min-width: 50px !default;\r\n$u-button-disabled-opacity: 0.5 !default;\r\n$u-button-info-color: #323233 !default;\r\n$u-button-info-background-color: #fff !default;\r\n$u-button-info-border-color: #ebedf0 !default;\r\n$u-button-info-border-width: 1px !default;\r\n$u-button-info-border-style: solid !default;\r\n$u-button-success-color: #fff !default;\r\n$u-button-success-background-color: $u-success !default;\r\n$u-button-success-border-color: $u-button-success-background-color !default;\r\n$u-button-success-border-width: 1px !default;\r\n$u-button-success-border-style: solid !default;\r\n$u-button-primary-color: #fff !default;\r\n$u-button-primary-background-color: $u-primary !default;\r\n$u-button-primary-border-color: $u-button-primary-background-color !default;\r\n$u-button-primary-border-width: 1px !default;\r\n$u-button-primary-border-style: solid !default;\r\n$u-button-error-color: #fff !default;\r\n$u-button-error-background-color: $u-error !default;\r\n$u-button-error-border-color: $u-button-error-background-color !default;\r\n$u-button-error-border-width: 1px !default;\r\n$u-button-error-border-style: solid !default;\r\n$u-button-warning-color: #fff !default;\r\n$u-button-warning-background-color: $u-warning !default;\r\n$u-button-warning-border-color: $u-button-warning-background-color !default;\r\n$u-button-warning-border-width: 1px !default;\r\n$u-button-warning-border-style: solid !default;\r\n$u-button-block-width: 100% !default;\r\n$u-button-circle-border-top-right-radius: 100px !default;\r\n$u-button-circle-border-top-left-radius: 100px !default;\r\n$u-button-circle-border-bottom-left-radius: 100px !default;\r\n$u-button-circle-border-bottom-right-radius: 100px !default;\r\n$u-button-square-border-top-right-radius: 3px !default;\r\n$u-button-square-border-top-left-radius: 3px !default;\r\n$u-button-square-border-bottom-left-radius: 3px !default;\r\n$u-button-square-border-bottom-right-radius: 3px !default;\r\n$u-button-icon-min-width: 1em !default;\r\n$u-button-plain-background-color: #fff !default;\r\n$u-button-hairline-border-width: 0.5px !default;\r\n\r\n.u-button {\r\n    height: $u-button-u-button-height;\r\n    position: relative;\r\n    align-items: center;\r\n    justify-content: center;\r\n    @include flex;\r\n    /* #ifndef APP-NVUE */\r\n    box-sizing: border-box;\r\n    /* #endif */\r\n    flex-direction: row;\r\n\r\n    &__text {\r\n        font-size: $u-button-text-font-size;\r\n    }\r\n\r\n    &__loading-text {\r\n        font-size: $u-button-loading-text-font-size;\r\n        margin-left: $u-button-loading-text-margin-left;\r\n    }\r\n\r\n    &--large {\r\n        /* #ifndef APP-NVUE */\r\n        width: $u-button-large-width;\r\n        /* #endif */\r\n        height: $u-button-large-height;\r\n        padding: $u-button-large-padding;\r\n    }\r\n\r\n    &--normal {\r\n        padding: $u-button-normal-padding;\r\n        font-size: $u-button-normal-font-size;\r\n    }\r\n\r\n    &--small {\r\n        /* #ifndef APP-NVUE */\r\n        min-width: $u-button-small-min-width;\r\n        /* #endif */\r\n        height: $u-button-small-height;\r\n        padding: $u-button-small-padding;\r\n        font-size: $u-button-small-font-size;\r\n    }\r\n\r\n    &--mini {\r\n        height: $u-button-mini-height;\r\n        font-size: $u-button-mini-font-size;\r\n        /* #ifndef APP-NVUE */\r\n        min-width: $u-button-mini-min-width;\r\n        /* #endif */\r\n        padding: $u-button-mini-padding;\r\n    }\r\n\r\n    &--disabled {\r\n        opacity: $u-button-disabled-opacity;\r\n    }\r\n\r\n    &--info {\r\n        color: $u-button-info-color;\r\n        background-color: $u-button-info-background-color;\r\n        border-color: $u-button-info-border-color;\r\n        border-width: $u-button-info-border-width;\r\n        border-style: $u-button-info-border-style;\r\n    }\r\n\r\n    &--success {\r\n        color: $u-button-success-color;\r\n        background-color: $u-button-success-background-color;\r\n        border-color: $u-button-success-border-color;\r\n        border-width: $u-button-success-border-width;\r\n        border-style: $u-button-success-border-style;\r\n    }\r\n\r\n    &--primary {\r\n        color: $u-button-primary-color;\r\n        background-color: $u-button-primary-background-color;\r\n        border-color: $u-button-primary-border-color;\r\n        border-width: $u-button-primary-border-width;\r\n        border-style: $u-button-primary-border-style;\r\n    }\r\n\r\n    &--error {\r\n        color: $u-button-error-color;\r\n        background-color: $u-button-error-background-color;\r\n        border-color: $u-button-error-border-color;\r\n        border-width: $u-button-error-border-width;\r\n        border-style: $u-button-error-border-style;\r\n    }\r\n\r\n    &--warning {\r\n        color: $u-button-warning-color;\r\n        background-color: $u-button-warning-background-color;\r\n        border-color: $u-button-warning-border-color;\r\n        border-width: $u-button-warning-border-width;\r\n        border-style: $u-button-warning-border-style;\r\n    }\r\n\r\n    &--block {\r\n        @include flex;\r\n        width: $u-button-block-width;\r\n    }\r\n\r\n    &--circle {\r\n        border-top-right-radius: $u-button-circle-border-top-right-radius;\r\n        border-top-left-radius: $u-button-circle-border-top-left-radius;\r\n        border-bottom-left-radius: $u-button-circle-border-bottom-left-radius;\r\n        border-bottom-right-radius: $u-button-circle-border-bottom-right-radius;\r\n    }\r\n\r\n    &--square {\r\n        border-bottom-left-radius: $u-button-square-border-top-right-radius;\r\n        border-bottom-right-radius: $u-button-square-border-top-left-radius;\r\n        border-top-left-radius: $u-button-square-border-bottom-left-radius;\r\n        border-top-right-radius: $u-button-square-border-bottom-right-radius;\r\n    }\r\n\r\n    &__icon {\r\n        /* #ifndef APP-NVUE */\r\n        min-width: $u-button-icon-min-width;\r\n        line-height: inherit !important;\r\n        vertical-align: top;\r\n        /* #endif */\r\n    }\r\n\r\n    &--plain {\r\n        background-color: $u-button-plain-background-color;\r\n    }\r\n\r\n    &--hairline {\r\n        border-width: $u-button-hairline-border-width !important;\r\n    }\r\n}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-button.vue?vue&type=style&index=0&id=2bf0e569&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-button.vue?vue&type=style&index=0&id=2bf0e569&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688737326\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}