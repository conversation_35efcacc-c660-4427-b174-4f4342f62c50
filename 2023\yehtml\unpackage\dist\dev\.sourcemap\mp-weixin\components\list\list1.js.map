{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list1.vue?318f", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list1.vue?feb4", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list1.vue?a287", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list1.vue?721b", "uni-app:///components/list/list1.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list1.vue?c314", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/list1.vue?7d07"], "names": ["components", "listGoodsItem1", "listPromotion", "props", "list", "type", "default", "onReady", "data", "watch", "methods", "imageClick", "uni", "url", "TOOLS"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACa;;;AAGjE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,sXAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACjCA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,grBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACwBprB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAIA;AAAA,eACA;EACAA;IACAC;IACAC;EACA;EACAC;IACAC;MACAC;MACAC;IACA;IACAD;MACAA;MACAC;IACA;EACA;EACAC;EACAC;IACA;EACA;EACAC;IACAL;MACA;IAAA;EAEA;EACAM;IACAC;MACA;QACA;QACAC;UACAC;QACA;MACA;QACA;QACA;UACAC;QACA;UACAF;YACAC;UACA;QACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;ACxEA;AAAA;AAAA;AAAA;AAA48B,CAAgB,+7BAAG,EAAC,C;;;;;;;;;;;ACAh+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/list/list1.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./list1.vue?vue&type=template&id=8f993dc6&\"\nvar renderjs\nimport script from \"./list1.vue?vue&type=script&lang=js&\"\nexport * from \"./list1.vue?vue&type=script&lang=js&\"\nimport style0 from \"./list1.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/list/list1.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list1.vue?vue&type=template&id=8f993dc6&\"", "var components\ntry {\n  components = {\n    customWaterfallsFlow: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow\" */ \"@/uni_modules/custom-waterfalls-flow/components/custom-waterfalls-flow/custom-waterfalls-flow.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list1.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list1.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"list1\">\r\n    <view>\r\n      <custom-waterfalls-flow ref=\"waterfallsFlowRef\" :value=\"list\" :column=\"2\" :columnSpace=\"1.5\" :seat=\"2\" @imageClick=\"imageClick()\">\r\n        <view v-for=\"(item, index) in list\" :key=\"index\" slot=\"slot{{index}}\">\r\n          <view class=\"badge-box\">\r\n            <view v-if=\"(item.categoryId === 383898 || item.categoryId === 383899 || item.categoryId === 383900 || item.categoryId === 383901 || item.categoryId === 383902 || item.categoryId === 383903 || item.categoryId === 383904 || item.categoryId === 383905) && item.promotionInsert !== 1\" class=\"badge\"></view>\r\n          </view>\r\n          <view v-if=\"item.deliveryTypeUrl\" class=\"delivery-type\">\r\n            <img style=\"width: 45px;height: 25px;\" :src=\"item.deliveryTypeUrl\">\r\n          </view>\r\n          <view v-if=\"item.promotionInsert === 1\">\r\n            <listPromotion :item=\"item\"></listPromotion>\r\n          </view>\r\n          <view v-if=\"item.promotionInsert !== 1\">\r\n            <listGoodsItem1 :item=\"item\"></listGoodsItem1>\r\n          </view>\r\n        </view>\r\n      </custom-waterfalls-flow>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import empty from 'empty-value'\r\n  import listGoodsItem1 from '@/components/list-item/list-goods-item1.vue'\r\n  import listPromotion from '@/components/list-item/list-promotion.vue'\r\n\r\n  const TOOLS = require('@/common/tools')\r\n  export default {\r\n    components: {\r\n      listGoodsItem1,\r\n      listPromotion\r\n    },\r\n    props: {\r\n      list: {\r\n        type: Array,\r\n        default: [],\r\n      },\r\n      type: {\r\n        type: String,\r\n        default: '',\r\n      },\r\n    },\r\n    onReady() {},\r\n    data() {\r\n      return {}\r\n    },\r\n    watch: {\r\n      list: function(val) {\r\n        //console.log('watch list is', val)\r\n      }\r\n    },\r\n    methods: {\r\n      imageClick(item) {\r\n        if (!empty(item.promotionInsert)) {\r\n          let that = this\r\n          uni.navigateTo({\r\n            url: item.path\r\n          })\r\n        } else {\r\n          const categoryIds = [383898, 383899, 383900, 383901, 383902, 383903, 383904, 383905]\r\n          if (categoryIds.includes(item.categoryId)) {\r\n            TOOLS.softOpeningTip()\r\n          } else {\r\n            uni.navigateTo({\r\n              url: '/pages/goods/detail?id=' + item.id\r\n            })\r\n          }\r\n        }\r\n      }\r\n    },\r\n  }\r\n</script>\r\n<style>\r\n  .list1 {\r\n    padding: 20rpx 0;\r\n  }\r\n\r\n  .badge-box {\r\n    position: relative;\r\n    top: -180px;\r\n    left: 70%;\r\n    margin-bottom: -50px;\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .badge {\r\n    background: url(\"https://ye.niutouren.vip/static/images/list/stock-up.png\") no-repeat;\r\n    background-size: 50px 50px;\r\n    background-position: center;\r\n    width: 50px;\r\n    height: 50px;\r\n  }\r\n\r\n  .delivery-type {\r\n    position: relative;\r\n    bottom: 0;\r\n    left: 0;\r\n    margin-top: -136rpx;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list1.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./list1.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688728695\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}