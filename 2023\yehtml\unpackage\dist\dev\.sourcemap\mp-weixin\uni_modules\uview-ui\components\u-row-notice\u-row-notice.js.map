{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-row-notice/u-row-notice.vue?2241", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-row-notice/u-row-notice.vue?6e1c", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-row-notice/u-row-notice.vue?1f72", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-row-notice/u-row-notice.vue?24c3", "uni-app:///uni_modules/uview-ui/components/u-row-notice/u-row-notice.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-row-notice/u-row-notice.vue?ff39", "webpack:///X:/Data/Web/ybn/2023/yehtml/uni_modules/uview-ui/components/u-row-notice/u-row-notice.vue?1643"], "names": ["name", "mixins", "data", "animationDuration", "animationPlayState", "nvueInit", "show", "watch", "text", "immediate", "handler", "uni", "fontSize", "speed", "computed", "textStyle", "style", "animationStyle", "innerText", "len", "result", "mounted", "methods", "init", "vue", "boxWidth", "textWidth", "setTimeout", "nvue", "loopAnimation", "getNvueRect", "clickHandler", "close"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAqI;AACrI;AACgE;AACL;AACsC;;;AAGjG;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,kFAAM;AACR,EAAE,mGAAM;AACR,EAAE,4GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,uGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AC9CA;AAAA;AAAA;AAAA;AAAuqB,CAAgB,urBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACsD3rB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,eAgBA;EACAA;EACAC;EACAC;IACA;MACAC;MAAA;MACAC;MAAA;MACA;MACA;MACAC;MACAC;IACA;EACA;EACAC;IACAC;MACAC;MACAC;QAKA;QAGA;UACAC;QACA;MACA;IACA;IACAC;MAKA;IAEA;IACAC;MAKA;IAEA;EACA;EACAC;IACA;IACAC;MACA;MACAC;MACAA;MACA;IACA;IACAC;MACA;MACAD;MACAA;MACA;IACA;IACA;IACA;IACAE;MACA;QACA;QACAC;MACA;MACA;QACA;QACAC;MACA;MACA;IACA;EACA;EACAC;IAeA;EACA;EACAC;IACAC;MAMA;MAGA;QACAZ;MACA;IACA;IACA;IACAa;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAEAC,cACAC,eACA;gBAAA;gBAAA,OACAf;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAe;gBAAA;gBAAA,OACA;cAAA;gBAAAD;gBACA;gBACA;gBACA;gBACA;gBACA;gBACAE;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAC;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAoBA;IACAC,4DA6BA;IACAC,uCASA;IACA;IACAC;MACA;IACA;IACA;IACAC;MACA;IACA;EACA;AAMA;AAAA,2B;;;;;;;;;;;;;ACpRA;AAAA;AAAA;AAAA;AAA8xC,CAAgB,kvCAAG,EAAC,C;;;;;;;;;;;ACAlzC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "uni_modules/uview-ui/components/u-row-notice/u-row-notice.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./u-row-notice.vue?vue&type=template&id=9adf94ee&scoped=true&\"\nvar renderjs\nimport script from \"./u-row-notice.vue?vue&type=script&lang=js&\"\nexport * from \"./u-row-notice.vue?vue&type=script&lang=js&\"\nimport style0 from \"./u-row-notice.vue?vue&type=style&index=0&id=9adf94ee&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"9adf94ee\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"uni_modules/uview-ui/components/u-row-notice/u-row-notice.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-row-notice.vue?vue&type=template&id=9adf94ee&scoped=true&\"", "var components\ntry {\n  components = {\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-icon/u-icon\" */ \"@/uni_modules/uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var s0 = _vm.__get_style([_vm.animationStyle])\n  var s1 = _vm.__get_style([_vm.textStyle])\n  var g0 = [\"link\", \"closable\"].includes(_vm.mode)\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        s0: s0,\n        s1: s1,\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-row-notice.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-row-notice.vue?vue&type=script&lang=js&\"", "<template>\r\n\t<view\r\n\t\tclass=\"u-notice\"\r\n\t\t@tap=\"clickHandler\"\r\n\t>\r\n\t\t<slot name=\"icon\">\r\n\t\t\t<view\r\n\t\t\t\tclass=\"u-notice__left-icon\"\r\n\t\t\t\tv-if=\"icon\"\r\n\t\t\t>\r\n\t\t\t\t<u-icon\r\n\t\t\t\t\t:name=\"icon\"\r\n\t\t\t\t\t:color=\"color\"\r\n\t\t\t\t\tsize=\"19\"\r\n\t\t\t\t></u-icon>\r\n\t\t\t</view>\r\n\t\t</slot>\r\n\t\t<view\r\n\t\t\tclass=\"u-notice__content\"\r\n\t\t\tref=\"u-notice__content\"\r\n\t\t>\r\n\t\t\t<view\r\n\t\t\t\tref=\"u-notice__content__text\"\r\n\t\t\t\tclass=\"u-notice__content__text\"\r\n\t\t\t\t:style=\"[animationStyle]\"\r\n\t\t\t>\r\n\t\t\t\t<text\r\n\t\t\t\t\tv-for=\"(item, index) in innerText\"\r\n\t\t\t\t\t:key=\"index\"\r\n\t\t\t\t\t:style=\"[textStyle]\"\r\n\t\t\t\t>{{item}}</text>\r\n\t\t\t</view>\r\n\t\t</view>\r\n\t\t<view\r\n\t\t\tclass=\"u-notice__right-icon\"\r\n\t\t\tv-if=\"['link', 'closable'].includes(mode)\"\r\n\t\t>\r\n\t\t\t<u-icon\r\n\t\t\t\tv-if=\"mode === 'link'\"\r\n\t\t\t\tname=\"arrow-right\"\r\n\t\t\t\t:size=\"17\"\r\n\t\t\t\t:color=\"color\"\r\n\t\t\t></u-icon>\r\n\t\t\t<u-icon\r\n\t\t\t\tv-if=\"mode === 'closable'\"\r\n\t\t\t\t@click=\"close\"\r\n\t\t\t\tname=\"close\"\r\n\t\t\t\t:size=\"16\"\r\n\t\t\t\t:color=\"color\"\r\n\t\t\t></u-icon>\r\n\t\t</view>\r\n\t</view>\r\n</template>\r\n<script>\r\n\timport props from './props.js';\r\n\t// #ifdef APP-NVUE\r\n\tconst animation = uni.requireNativePlugin('animation')\r\n\tconst dom = uni.requireNativePlugin('dom')\r\n\t// #endif\r\n\t/**\r\n\t * RowNotice 滚动通知中的水平滚动模式\r\n\t * @description 水平滚动\r\n\t * @tutorial https://www.uviewui.com/components/noticeBar.html\r\n\t * @property {String | Number}\ttext\t\t\t显示的内容，字符串\r\n\t * @property {String}\t\t\ticon\t\t\t是否显示左侧的音量图标 (默认 'volume' )\r\n\t * @property {String}\t\t\tmode\t\t\t通告模式，link-显示右箭头，closable-显示右侧关闭图标\r\n\t * @property {String}\t\t\tcolor\t\t\t文字颜色，各图标也会使用文字颜色 (默认 '#f9ae3d' )\r\n\t * @property {String}\t\t\tbgColor\t\t\t背景颜色 (默认 ''#fdf6ec' )\r\n\t * @property {String | Number}\tfontSize\t\t字体大小，单位px (默认 14 )\r\n\t * @property {String | Number}\tspeed\t\t\t水平滚动时的滚动速度，即每秒滚动多少px(rpx)，这有利于控制文字无论多少时，都能有一个恒定的速度  (默认 80 )\r\n\t * \r\n\t * @event {Function} click 点击通告文字触发\r\n\t * @event {Function} close 点击右侧关闭图标触发\r\n\t * @example \r\n\t */\r\n\texport default {\r\n\t\tname: 'u-row-notice',\r\n\t\tmixins: [uni.$u.mpMixin, uni.$u.mixin,props],\r\n\t\tdata() {\r\n\t\t\treturn {\r\n\t\t\t\tanimationDuration: '0', // 动画执行时间\r\n\t\t\t\tanimationPlayState: 'paused', // 动画的开始和结束执行\r\n\t\t\t\t// nvue下，内容发生变化，导致滚动宽度也变化，需要标志为是否需要重新计算宽度\r\n\t\t\t\t// 不能在内容变化时直接重新计算，因为nvue的animation模块上一次的滚动不是刚好结束，会有影响\r\n\t\t\t\tnvueInit: true,\r\n\t\t\t\tshow: true\r\n\t\t\t};\r\n\t\t},\r\n\t\twatch: {\r\n\t\t\ttext: {\r\n\t\t\t\timmediate: true,\r\n\t\t\t\thandler(newValue, oldValue) {\r\n\t\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t\tthis.nvueInit = true\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\t\tthis.vue()\r\n\t\t\t\t\t// #endif\r\n\t\t\t\t\t\r\n\t\t\t\t\tif(!uni.$u.test.string(newValue)) {\r\n\t\t\t\t\t\tuni.$u.error('noticebar组件direction为row时，要求text参数为字符串形式')\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\tfontSize() {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.nvueInit = true\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tthis.vue()\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tspeed() {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.nvueInit = true\r\n\t\t\t\t// #endif\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tthis.vue()\r\n\t\t\t\t// #endif\r\n\t\t\t}\r\n\t\t},\r\n\t\tcomputed: {\r\n\t\t\t// 文字内容的样式\r\n\t\t\ttextStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\tstyle.color = this.color\r\n\t\t\t\tstyle.fontSize = uni.$u.addUnit(this.fontSize)\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\tanimationStyle() {\r\n\t\t\t\tlet style = {}\r\n\t\t\t\tstyle.animationDuration = this.animationDuration\r\n\t\t\t\tstyle.animationPlayState = this.animationPlayState\r\n\t\t\t\treturn style\r\n\t\t\t},\r\n\t\t\t// 内部对用户传入的数据进一步分割，放到多个text标签循环，否则如果用户传入的字符串很长（100个字符以上）\r\n\t\t\t// 放在一个text标签中进行滚动，在低端安卓机上，动画可能会出现抖动现象，需要分割到多个text中可解决此问题\r\n\t\t\tinnerText() {\r\n\t\t\t\tlet result = [],\r\n\t\t\t\t\t// 每组text标签的字符长度\r\n\t\t\t\t\tlen = 20\r\n\t\t\t\tconst textArr = this.text.split('')\r\n\t\t\t\tfor (let i = 0; i < textArr.length; i += len) {\r\n\t\t\t\t\t// 对拆分的后的text进行slice分割，得到的为数组再进行join拼接为字符串\r\n\t\t\t\t\tresult.push(textArr.slice(i, i + len).join(''))\r\n\t\t\t\t}\r\n\t\t\t\treturn result\r\n\t\t\t}\r\n\t\t},\r\n\t\tmounted() {\r\n\t\t\t// #ifdef APP-PLUS\r\n\t\t\t// 在APP上(含nvue)，监听当前webview是否处于隐藏状态(进入下一页时即为hide状态)\r\n\t\t\t// 如果webivew隐藏了，为了节省性能的损耗，应停止动画的执行，同时也是为了保持进入下一页返回后，滚动位置保持不变\r\n\t\t\tvar pages = getCurrentPages()\r\n\t\t\tvar page = pages[pages.length - 1]\r\n\t\t\tvar currentWebview = page.$getAppWebview()\r\n\t\t\tcurrentWebview.addEventListener('hide', () => {\r\n\t\t\t\tthis.webviewHide = true\r\n\t\t\t})\r\n\t\t\tcurrentWebview.addEventListener('show', () => {\r\n\t\t\t\tthis.webviewHide = false\r\n\t\t\t})\r\n\t\t\t// #endif\r\n\r\n\t\t\tthis.init()\r\n\t\t},\r\n\t\tmethods: {\r\n\t\t\tinit() {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.nvue()\r\n\t\t\t\t// #endif\r\n\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tthis.vue()\r\n\t\t\t\t// #endif\r\n\t\t\t\t\r\n\t\t\t\tif(!uni.$u.test.string(this.text)) {\r\n\t\t\t\t\tuni.$u.error('noticebar组件direction为row时，要求text参数为字符串形式')\r\n\t\t\t\t}\r\n\t\t\t},\r\n\t\t\t// vue版处理\r\n\t\t\tasync vue() {\r\n\t\t\t\t// #ifndef APP-NVUE\r\n\t\t\t\tlet boxWidth = 0,\r\n\t\t\t\t\ttextWidth = 0\r\n\t\t\t\t// 进行一定的延时\r\n\t\t\t\tawait uni.$u.sleep()\r\n\t\t\t\t// 查询盒子和文字的宽度\r\n\t\t\t\ttextWidth = (await this.$uGetRect('.u-notice__content__text')).width\r\n\t\t\t\tboxWidth = (await this.$uGetRect('.u-notice__content')).width\r\n\t\t\t\t// 根据t=s/v(时间=路程/速度)，这里为何不需要加上#u-notice-box的宽度，因为中设置了.u-notice-content样式中设置了padding-left: 100%\r\n\t\t\t\t// 恰巧计算出来的结果中已经包含了#u-notice-box的宽度\r\n\t\t\t\tthis.animationDuration = `${textWidth / uni.$u.getPx(this.speed)}s`\r\n\t\t\t\t// 这里必须这样开始动画，否则在APP上动画速度不会改变\r\n\t\t\t\tthis.animationPlayState = 'paused'\r\n\t\t\t\tsetTimeout(() => {\r\n\t\t\t\t\tthis.animationPlayState = 'running'\r\n\t\t\t\t}, 10)\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// nvue版处理\r\n\t\t\tasync nvue() {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tthis.nvueInit = false\r\n\t\t\t\tlet boxWidth = 0,\r\n\t\t\t\t\ttextWidth = 0\r\n\t\t\t\t// 进行一定的延时\r\n\t\t\t\tawait uni.$u.sleep()\r\n\t\t\t\t// 查询盒子和文字的宽度\r\n\t\t\t\ttextWidth = (await this.getNvueRect('u-notice__content__text')).width\r\n\t\t\t\tboxWidth = (await this.getNvueRect('u-notice__content')).width\r\n\t\t\t\t// 将文字移动到盒子的右边沿，之所以需要这么做，是因为nvue不支持100%单位，否则可以通过css设置\r\n\t\t\t\tanimation.transition(this.$refs['u-notice__content__text'], {\r\n\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\ttransform: `translateX(${boxWidth}px)`\r\n\t\t\t\t\t},\r\n\t\t\t\t}, () => {\r\n\t\t\t\t\t// 如果非禁止动画，则开始滚动\r\n\t\t\t\t\t!this.stopAnimation && this.loopAnimation(textWidth, boxWidth)\r\n\t\t\t\t});\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tloopAnimation(textWidth, boxWidth) {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\tanimation.transition(this.$refs['u-notice__content__text'], {\r\n\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\t// 目标移动终点为-textWidth，也即当文字的最右边贴到盒子的左边框的位置\r\n\t\t\t\t\t\ttransform: `translateX(-${textWidth}px)`\r\n\t\t\t\t\t},\r\n\t\t\t\t\t// 滚动时间的计算为，时间 = 路程(boxWidth + textWidth) / 速度，最后转为毫秒\r\n\t\t\t\t\tduration: (boxWidth + textWidth) / uni.$u.getPx(this.speed) * 1000,\r\n\t\t\t\t\tdelay: 10\r\n\t\t\t\t}, () => {\r\n\t\t\t\t\tanimation.transition(this.$refs['u-notice__content__text'], {\r\n\t\t\t\t\t\tstyles: {\r\n\t\t\t\t\t\t\t// 重新将文字移动到盒子的右边沿\r\n\t\t\t\t\t\t\ttransform: `translateX(${this.stopAnimation ? 0 : boxWidth}px)`\r\n\t\t\t\t\t\t},\r\n\t\t\t\t\t}, () => {\r\n\t\t\t\t\t\t// 如果非禁止动画，则继续下一轮滚动\r\n\t\t\t\t\t\tif (!this.stopAnimation) {\r\n\t\t\t\t\t\t\t// 判断是否需要初始化计算尺寸\r\n\t\t\t\t\t\t\tif (this.nvueInit) {\r\n\t\t\t\t\t\t\t\tthis.nvue()\r\n\t\t\t\t\t\t\t} else {\r\n\t\t\t\t\t\t\t\tthis.loopAnimation(textWidth, boxWidth)\r\n\t\t\t\t\t\t\t}\r\n\t\t\t\t\t\t}\r\n\t\t\t\t\t});\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\tgetNvueRect(el) {\r\n\t\t\t\t// #ifdef APP-NVUE\r\n\t\t\t\t// 返回一个promise\r\n\t\t\t\treturn new Promise(resolve => {\r\n\t\t\t\t\tdom.getComponentRect(this.$refs[el], (res) => {\r\n\t\t\t\t\t\tresolve(res.size)\r\n\t\t\t\t\t})\r\n\t\t\t\t})\r\n\t\t\t\t// #endif\r\n\t\t\t},\r\n\t\t\t// 点击通告栏\r\n\t\t\tclickHandler(index) {\r\n\t\t\t\tthis.$emit('click')\r\n\t\t\t},\r\n\t\t\t// 点击右侧按钮，需要判断点击的是关闭图标还是箭头图标\r\n\t\t\tclose() {\r\n\t\t\t\tthis.$emit('close')\r\n\t\t\t}\r\n\t\t},\r\n\t\t// #ifdef APP-NVUE\r\n\t\tbeforeDestroy() {\r\n\t\t\tthis.stopAnimation = true\r\n\t\t},\r\n\t\t// #endif\r\n\t};\r\n</script>\r\n\r\n<style lang=\"scss\" scoped>\r\n\t@import \"../../libs/css/components.scss\";\r\n\r\n\t.u-notice {\r\n\t\t@include flex;\r\n\t\talign-items: center;\r\n\t\tjustify-content: space-between;\r\n\r\n\t\t&__left-icon {\r\n\t\t\talign-items: center;\r\n\t\t\tmargin-right: 5px;\r\n\t\t}\r\n\r\n\t\t&__right-icon {\r\n\t\t\tmargin-left: 5px;\r\n\t\t\talign-items: center;\r\n\t\t}\r\n\r\n\t\t&__content {\r\n\t\t\ttext-align: right;\r\n\t\t\tflex: 1;\r\n\t\t\t@include flex;\r\n\t\t\tflex-wrap: nowrap;\r\n\t\t\toverflow: hidden;\r\n\r\n\t\t\t&__text {\r\n\t\t\t\tfont-size: 14px;\r\n\t\t\t\tcolor: $u-warning;\r\n\t\t\t\t/* #ifndef APP-NVUE */\r\n\t\t\t\t// 这一句很重要，为了能让滚动左右连接起来\r\n\t\t\t\tpadding-left: 100%;\r\n\t\t\t\tword-break: keep-all;\r\n\t\t\t\twhite-space: nowrap;\r\n\t\t\t\tanimation: u-loop-animation 10s linear infinite both;\r\n\t\t\t\t/* #endif */\r\n\t\t\t\t@include flex(row);\r\n\t\t\t}\r\n\t\t}\r\n\r\n\t}\r\n\r\n\t@keyframes u-loop-animation {\r\n\t\t0% {\r\n\t\t\ttransform: translate3d(0, 0, 0);\r\n\t\t}\r\n\r\n\t\t100% {\r\n\t\t\ttransform: translate3d(-100%, 0, 0);\r\n\t\t}\r\n\t}\r\n</style>\r\n", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-row-notice.vue?vue&type=style&index=0&id=9adf94ee&lang=scss&scoped=true&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./u-row-notice.vue?vue&type=style&index=0&id=9adf94ee&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692293500\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}