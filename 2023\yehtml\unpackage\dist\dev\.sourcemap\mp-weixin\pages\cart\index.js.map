{"version": 3, "sources": ["uni-app:///main.js", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/cart/index.vue?a04f", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/cart/index.vue?1753", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/cart/index.vue?1a0a", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/cart/index.vue?4efe", "uni-app:///pages/cart/index.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/cart/index.vue?af57", "webpack:///X:/Data/Web/ybn/2023/yehtml/pages/cart/index.vue?39b5"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "list1", "data", "tabIndex", "tabs", "name", "shippingCarInfo", "jdvopCartInfo", "options", "text", "style", "backgroundColor", "adPosition", "goodsRecommend", "created", "mounted", "onReady", "onLoad", "onShow", "methods", "_shippingCarInfo", "res", "ele", "_jdvopCartInfo", "deleterecord0", "item", "uni", "title", "icon", "TOOLS", "deleterecord1", "numberChange0", "numberChange1", "open", "submit0", "url", "submit1", "_adPosition", "goUrl", "_goodsRecommend", "recommendStatus", "_goods", "goods", "good", "goDetail"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAA8H;AAC9H;AACyD;AACL;AACsC;;;AAG1F;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,4FAAM;AACR,EAAE,qGAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,gGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,4NAEN;AACP,KAAK;AACL;AACA,aAAa,iVAEN;AACP,KAAK;AACL;AACA,aAAa,+WAEN;AACP,KAAK;AACL;AACA,aAAa,qUAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;AChDA;AAAA;AAAA;AAAA;AAAgqB,CAAgB,grBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;ACqEprB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;QACAC;MACA,GACA;QACAA;MACA,EACA;MACAC;MACAC;MACAC;QACAC;QACAC;UACAC;QACA;MACA;MACAC;MACAC;IACA;EACA;EACAC,6BAEA;EACAC,6BAEA;EACAC,6BAEA;EACAC;IACA;IACA;EACA;EACAC;IACA;EACA;EACAC;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACA;kBACAA;oBACAC;kBACA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAF;gBACA;kBACAA;oBACAC;kBACA;kBACA;gBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC;gBAAA;gBAAA,OACA;cAAA;gBAAAJ;gBACA;kBACAK;oBACAC;kBACA;gBACA;kBACAD;oBACAC;oBACAC;kBACA;gBACA;gBACA;gBACAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAL;gBAAA;gBAAA,OACA;cAAA;gBAAAJ;gBACA;kBACAK;oBACAC;kBACA;gBACA;kBACAD;oBACAC;oBACAC;kBACA;gBACA;gBACA;gBACAC;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAN,6CACA;gBAAA;gBAAA,OACA;cAAA;gBAAAJ;gBACA;kBACAK;oBACAC;oBACAC;kBACA;gBACA;kBACA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAG;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAP;gBAAA;gBAAA,OACA;cAAA;gBAAAJ;gBACA;kBACAK;oBACAC;oBACAC;kBACA;gBACA;kBACA;kBACAC;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAI;MACA;QACAX;MACA;MACA;IACA;IACAY;MACAR;QACAS;MACA;IACA;IACAC;MACAV;QACAS;MACA;IACA;IACAE;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAhB;gBACA;kBACAA;oBACA;oBACA;sBACA;oBACA;kBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAiB;MACA;MACA;QACAZ;UACAS;QACA;MACA;IACA;IACAI;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OAEA;kBACAC;gBACA;cAAA;gBAFAnB;gBAGA;kBACAoB;kBACAC;kBAEAD;kBAEA;oBACAA;sBAAA;wBAAA;0BAAA;4BAAA;8BAAA;gCACAE;gCACAA;gCACAD;8BAAA;8BAAA;gCAAA;4BAAA;0BAAA;wBAAA;sBAAA,CACA;sBAAA;wBAAA;sBAAA;oBAAA;kBACA;kBAEA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAE;MACAlB;QACAS;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC1QA;AAAA;AAAA;AAAA;AAAuxC,CAAgB,2uCAAG,EAAC,C;;;;;;;;;;;ACA3yC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/cart/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/cart/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=457cfe48&scoped=true&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&id=457cfe48&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"457cfe48\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/cart/index.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=template&id=457cfe48&scoped=true&\"", "var components\ntry {\n  components = {\n    pageBoxEmpty: function () {\n      return import(\n        /* webpackChunkName: \"components/page-box-empty/page-box-empty\" */ \"@/components/page-box-empty/page-box-empty.vue\"\n      )\n    },\n    uSwipeAction: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-swipe-action/u-swipe-action\" */ \"@/uni_modules/uview-ui/components/u-swipe-action/u-swipe-action.vue\"\n      )\n    },\n    uSwipeActionItem: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-swipe-action-item/u-swipe-action-item\" */ \"@/uni_modules/uview-ui/components/u-swipe-action-item/u-swipe-action-item.vue\"\n      )\n    },\n    uNumberBox: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-number-box/u-number-box\" */ \"@/uni_modules/uview-ui/components/u-number-box/u-number-box.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"cart\">\r\n    <view class=\"tip\">* 向左滑动可以删除项目</view>\r\n    <page-box-empty v-if=\"(!shippingCarInfo || shippingCarInfo.number == 0)\" title=\"您还没有挑选任何商品\" sub-title=\"可以去看看有那些想买的～\" :show-btn=\"true\" />\r\n    <view v-if=\"shippingCarInfo\" class=\"order\">\r\n      <u-swipe-action>\r\n        <u-swipe-action-item v-for=\"(item, index) in shippingCarInfo.items\" :key=\"index\" :show=\"item.show\" :index=\"index\" :options=\"options\" @click=\"deleterecord0\">\r\n          <view class=\"item\">\r\n            <view class=\"left\">\r\n              <image :src=\"item.pic\" mode=\"aspectFill\"></image>\r\n            </view>\r\n            <view class=\"content\">\r\n              <view class=\"title\">{{ item.name }}</view>\r\n              <view class=\"type\">\r\n                <text v-for=\"(item2, index2) in item.sku\" :key=\"'b' + index2\">{{ item2.optionName }}:{{ item2.optionValueName }}/</text>\r\n                <text v-for=\"(item3, index3) in item.additions\" :key=\"'c' + index3\">{{ item3.pname }}:{{ item3.name }}/</text>\r\n              </view>\r\n              <view class=\"delivery-time\">\r\n                <u-number-box v-model=\"item.number\" :name=\"index\" :min=\"item.minBuyNumber\" :max=\"item.stores\" @change=\"numberChange0\"></u-number-box>\r\n              </view>\r\n            </view>\r\n            <view class=\"right\">\r\n              <view class=\"price\">\r\n                <text>￥</text>{{ item.price }}\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </u-swipe-action-item>\r\n      </u-swipe-action>\r\n      <!-- <view class=\"total\">\r\n        共 <text class=\"number\">{{ shippingCarInfo.number }}</text> 件商品 合计:\r\n        <text class=\"total-price\">\r\n          <text>￥</text>{{ shippingCarInfo.price }}\r\n        </text>\r\n      </view>\r\n      <view v-if=\"shippingCarInfo && shippingCarInfo.number > 0\" class=\"submit0\">\r\n        <u-button type=\"error\" @click=\"submit0\">提交订单</u-button>\r\n      </view> -->\r\n\r\n      <view class=\"settlement-box\">\r\n        <view class=\"left-price\">\r\n          <view class=\"total\">\r\n            共 <text class=\"number\">{{ shippingCarInfo.number }}</text> 件商品 合计\r\n            <text class=\"total-price\">\r\n              <text>￥</text>{{ shippingCarInfo.price }}\r\n            </text>\r\n          </view>\r\n        </view>\r\n        <view v-if=\"shippingCarInfo && shippingCarInfo.number > 0\" @click=\"submit0\" class=\"to-pay-btn\">结算</view>\r\n      </view>\r\n    </view>\r\n\r\n    <view class=\"recommend\">\r\n      <view class=\"goods-container\">\r\n        <view class=\"recommend-title\">\r\n          <img style=\"height: 36px;\" src=\"/static/images/cart/recommend-title.jpg\">\r\n        </view>\r\n        <view v-if=\"goodsRecommend\" class=\"goodsRecommend\">\r\n          <view class=\"goods-container\">\r\n            <list1 :list=\"goodsRecommend\" type=\"goods\"></list1>\r\n          </view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import empty from 'empty-value'\r\n  import list1 from '@/components/list/list1'\r\n\r\n  const TOOLS = require('@/common/tools')\r\n  export default {\r\n    components: {\r\n      list1,\r\n    },\r\n    data() {\r\n      return {\r\n        tabIndex: 3,\r\n        tabs: [{\r\n            name: '自营商品'\r\n          },\r\n          {\r\n            name: '京东商品'\r\n          }\r\n        ],\r\n        shippingCarInfo: undefined,\r\n        jdvopCartInfo: undefined,\r\n        options: [{\r\n          text: '删除',\r\n          style: {\r\n            backgroundColor: '#e64340',\r\n          }\r\n        }],\r\n        adPosition: {},\r\n        goodsRecommend: undefined,\r\n      }\r\n    },\r\n    created() {\r\n\r\n    },\r\n    mounted() {\r\n\r\n    },\r\n    onReady() {\r\n\r\n    },\r\n    onLoad(e) {\r\n      this._goodsRecommend()\r\n      this._adPosition()\r\n    },\r\n    onShow() {\r\n      this._shippingCarInfo()\r\n    },\r\n    methods: {\r\n      async _shippingCarInfo() {\r\n        // https://www.yuque.com/apifm/nu0f75/awql14\r\n        const res = await this.$wxapi.shippingCarInfo(this.token)\r\n        if (res.code == 0) {\r\n          res.data.items.forEach(ele => {\r\n            ele.show = false\r\n          })\r\n          this.shippingCarInfo = res.data\r\n        } else {\r\n          this.shippingCarInfo = null\r\n        }\r\n      },\r\n      async _jdvopCartInfo() {\r\n        // https://www.yuque.com/apifm/nu0f75/gwat37\r\n        const res = await this.$wxapi.jdvopCartInfo(this.token)\r\n        if (res.code == 0) {\r\n          res.data.items.forEach(ele => {\r\n            ele.show = false\r\n          })\r\n          this.jdvopCartInfo = res.data\r\n        } else {\r\n          this.jdvopCartInfo = null\r\n        }\r\n      },\r\n      async deleterecord0(e) {\r\n        // 删除购物车记录 https://www.yuque.com/apifm/nu0f75/pndgyc\r\n        const item = this.shippingCarInfo.items[e.index]\r\n        const res = await this.$wxapi.shippingCarInfoRemoveItem(this.token, item.key)\r\n        if (res.code == 0) {\r\n          uni.showToast({\r\n            title: '已删除'\r\n          })\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n        }\r\n        this._shippingCarInfo()\r\n        TOOLS.showTabBarBadge()\r\n      },\r\n      async deleterecord1(e) {\r\n        // 删除购物车记录 https://www.yuque.com/apifm/nu0f75/syqlot\r\n        const item = this.jdvopCartInfo.items[e.index]\r\n        const res = await this.$wxapi.jdvopCartRemove(this.token, item.key)\r\n        if (res.code == 0) {\r\n          uni.showToast({\r\n            title: '已删除'\r\n          })\r\n        } else {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n        }\r\n        this._jdvopCartInfo()\r\n        TOOLS.showTabBarBadge()\r\n      },\r\n      async numberChange0(e) {\r\n        const item = this.shippingCarInfo.items[e.name]\r\n        // https://www.yuque.com/apifm/nu0f75/kbi5b0\r\n        const res = await this.$wxapi.shippingCarInfoModifyNumber(this.token, item.key, e.value)\r\n        if (res.code != 0) {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n        } else {\r\n          this._shippingCarInfo()\r\n          TOOLS.showTabBarBadge()\r\n        }\r\n      },\r\n      async numberChange1(e) {\r\n        // https://www.yuque.com/apifm/nu0f75/vkd6q5\r\n        const item = this.jdvopCartInfo.items[e.name]\r\n        const res = await this.$wxapi.jdvopCartModifyNumber(this.token, item.key, e.value)\r\n        if (res.code != 0) {\r\n          uni.showToast({\r\n            title: res.msg,\r\n            icon: 'none'\r\n          })\r\n        } else {\r\n          this._jdvopCartInfo()\r\n          TOOLS.showTabBarBadge()\r\n        }\r\n      },\r\n      open(index) {\r\n        this.shippingCarInfo.items.forEach(ele => {\r\n          ele.show = false\r\n        })\r\n        this.shippingCarInfo.items[index].show = true\r\n      },\r\n      submit0() {\r\n        uni.navigateTo({\r\n          url: '../pay/order?mod=cart&cartType=apifm'\r\n        })\r\n      },\r\n      submit1() {\r\n        uni.navigateTo({\r\n          url: '../pay/order?mod=cart&cartType=jdvop'\r\n        })\r\n      },\r\n      async _adPosition() {\r\n        // https://www.yuque.com/apifm/nu0f75/ypi79p\r\n        const res = await this.$wxapi.adPositionBatch('cart_banner')\r\n        if (res.code == 0) {\r\n          res.data.forEach(ele => {\r\n            this.adPosition[ele.key] = ele\r\n            if (ele.key == 'indexPop') {\r\n              this.adPositionIndexPop = true\r\n            }\r\n          })\r\n        }\r\n      },\r\n      goUrl(url) {\r\n        this.adPositionIndexPop = false\r\n        if (url) {\r\n          uni.navigateTo({\r\n            url\r\n          })\r\n        }\r\n      },\r\n      async _goodsRecommend() {\r\n        // https://www.yuque.com/apifm/nu0f75/wg5t98\r\n        const res = await this.$wxapi.goodsv2({\r\n          recommendStatus: 1\r\n        })\r\n        if (res.code == 0) {\r\n          let _goods = []\r\n          let goods = []\r\n\r\n          _goods = res.data.result\r\n\r\n          if (!empty(_goods)) {\r\n            _goods.forEach(async (good, index) => {\r\n              good.image = good.pic\r\n              good.title = good.name\r\n              goods.push(good)\r\n            })\r\n          }\r\n\r\n          this.goodsRecommend = goods\r\n        }\r\n      },\r\n      goDetail(item) {\r\n        uni.navigateTo({\r\n          url: '/pages/goods/detail?id=' + item.id\r\n        })\r\n      },\r\n    }\r\n  }\r\n</script>\r\n<style scoped lang=\"scss\">\r\n  .cart {\r\n    padding-top: 30rpx;\r\n    background: #F3F3F3;\r\n  }\r\n\r\n  .tip {\r\n    width: 660rpx;\r\n    margin: 16rpx auto;\r\n    font-size: 24rpx;\r\n    color: #858996;\r\n  }\r\n\r\n  .title {\r\n    font-size: 90rpx;\r\n    color: #2b2b2b;\r\n    position: relative;\r\n\r\n    text {\r\n      width: 7px;\r\n      height: 7px;\r\n      position: absolute;\r\n      border: 2px solid #a78845;\r\n      border-radius: 50%;\r\n    }\r\n  }\r\n\r\n  .title-sub {\r\n    margin-left: 25px;\r\n    color: #2b2b2b;\r\n    font-size: 36rpx;\r\n    font-weight: 300;\r\n  }\r\n\r\n  .banner-wrapper .banner {\r\n    margin: 20rpx;\r\n  }\r\n\r\n  .order {\r\n    width: 660rpx;\r\n    background-color: #ffffff;\r\n    margin: 0 auto;\r\n    border-radius: 20rpx;\r\n    box-sizing: border-box;\r\n    padding: 20rpx;\r\n    font-size: 28rpx;\r\n\r\n    .item {\r\n      display: flex;\r\n      margin: 0;\r\n      margin-bottom: 30rpx;\r\n\r\n      .left {\r\n        margin-right: 20rpx;\r\n\r\n        image {\r\n          width: 160rpx;\r\n          height: 160rpx;\r\n          border-radius: 20rpx;\r\n        }\r\n      }\r\n\r\n      .content {\r\n        flex: 1;\r\n\r\n        .title {\r\n          line-height: 50rpx;\r\n          font-size: 30rpx;\r\n        }\r\n\r\n        .type {\r\n          margin: 14rpx 0;\r\n          font-size: 24rpx;\r\n          color: $u-tips-color;\r\n        }\r\n\r\n        .delivery-time {\r\n          color: #e5d001;\r\n          font-size: 24rpx;\r\n        }\r\n      }\r\n\r\n      .right {\r\n        margin-left: 10rpx;\r\n        padding-top: 20rpx;\r\n        text-align: right;\r\n\r\n        .decimal {\r\n          font-size: 24rpx;\r\n          margin-top: 4rpx;\r\n        }\r\n\r\n        .number {\r\n          color: $u-tips-color;\r\n          font-size: 24rpx;\r\n        }\r\n\r\n        .price {\r\n          text {\r\n            font-size: 26rpx;\r\n          }\r\n\r\n          font-size: 38rpx;\r\n          color: #e64340;\r\n          margin-right: 20rpx;\r\n        }\r\n      }\r\n    }\r\n\r\n    .total {\r\n      text-align: right;\r\n      font-size: 24rpx;\r\n\r\n      .number {\r\n        color: #e64340;\r\n        padding: 0 8rpx;\r\n        font-size: 30rpx;\r\n      }\r\n\r\n      .total-price {\r\n        text {\r\n          font-size: 26rpx;\r\n          padding: 0 8rpx;\r\n        }\r\n\r\n        font-size: 38rpx;\r\n        color: #e64340;\r\n      }\r\n    }\r\n\r\n    .bottom {\r\n      display: flex;\r\n      margin-top: 40rpx;\r\n      padding: 0 10rpx;\r\n      justify-content: space-between;\r\n      align-items: center;\r\n\r\n      .btn {\r\n        line-height: 52rpx;\r\n        width: 160rpx;\r\n        border-radius: 26rpx;\r\n        border: 2rpx solid $u-border-color;\r\n        font-size: 26rpx;\r\n        text-align: center;\r\n        color: $u-info-dark;\r\n      }\r\n\r\n      .evaluate {\r\n        color: $u-warning-dark;\r\n        border-color: $u-warning-dark;\r\n      }\r\n    }\r\n  }\r\n\r\n  .submit {\r\n    margin-top: 64rpx;\r\n  }\r\n\r\n  .goods-container {\r\n    padding: 0 24rpx 60rpx 24rpx;\r\n  }\r\n\r\n  .recommend {\r\n    background: #F3F3F3;\r\n  }\r\n\r\n  .recommend-title {\r\n    padding: 40rpx 0 20rpx 0;\r\n    text-align: center;\r\n  }\r\n\r\n\r\n  .settlement-box {\r\n    display: flex;\r\n    justify-content: space-between;\r\n    width: 100%;\r\n    height: 100rpx;\r\n    position: fixed;\r\n    bottom: 0;\r\n    left: 0;\r\n    background-color: #FAFAFA;\r\n    z-index: 999;\r\n  }\r\n\r\n  .settlement-box .to-pay-btn {\r\n    text-align: center;\r\n    line-height: 76rpx;\r\n    width: 200rpx;\r\n    height: 76rpx;\r\n    background: linear-gradient(270deg, #FF972A 0%, #FF444A 100%);\r\n    border-radius: 38rpx;\r\n    margin-top: 12rpx;\r\n    margin-right: 20rpx;\r\n    color: white;\r\n  }\r\n\r\n  .settlement-box .to-pay-btn.no-select {\r\n    background-color: #ccc;\r\n  }\r\n\r\n  .settlement-box .left-price {\r\n    display: flex;\r\n    width: 510rpx;\r\n    justify-content: space-between;\r\n    line-height: 100rpx;\r\n    padding: 0 30rpx 0 32rpx;\r\n    font-size: 28rpx;\r\n    box-sizing: border-box;\r\n  }\r\n\r\n  .settlement-box .total {\r\n    color: #606266;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=457cfe48&scoped=true&lang=scss&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./index.vue?vue&type=style&index=0&id=457cfe48&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753692294047\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}