<view><u-sticky vue-id="4547129c-1" bind:__l="__l" vue-slots="{{['default']}}"><u-subsection vue-id="{{('4547129c-2')+','+('4547129c-1')}}" list="{{list}}" current="{{activeIndex}}" data-event-opts="{{[['^change',[['tabChange']]]]}}" bind:change="__e" bind:__l="__l"></u-subsection></u-sticky><block wx:if="{{$root.g0}}"><u-empty vue-id="4547129c-3" text="暂无团队" mode="car" icon="http://cdn.uviewui.com/uview/empty/car.png" bind:__l="__l"></u-empty></block><block wx:for="{{members}}" wx:for-item="item" wx:for-index="index"><view class="list"><image class="l" src="{{item.avatarUrls}}" mode="aspectFill"></image><view class="r"><u-cell vue-id="{{'4547129c-4-'+index}}" title="{{item.nicks}}" label="{{item.mobileMasks}}" isLink="{{true}}" url="{{'myusers-detail?id='+item.uids}}" bind:__l="__l"></u-cell><u-cell vue-id="{{'4547129c-5-'+index}}" title="成交额" value="{{'¥'+item.totalPayAmount?item.totalPayAmount:0}}" titleStyle="flex:2" bind:__l="__l"></u-cell><u-cell vue-id="{{'4547129c-6-'+index}}" title="订单数" value="{{item.totalPayNumber?item.totalPayNumber:0+'笔'}}" label="{{'最近下单:'+item.lastOrderDate?item.lastOrderDate:'-'}}" titleStyle="flex:2" bind:__l="__l"></u-cell></view></view></block></view>