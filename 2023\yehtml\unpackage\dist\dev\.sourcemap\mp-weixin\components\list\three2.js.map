{"version": 3, "sources": ["webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/three2.vue?d486", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/three2.vue?66e4", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/three2.vue?8b15", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/three2.vue?cc27", "uni-app:///components/list/three2.vue", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/three2.vue?4119", "webpack:///X:/Data/Web/ybn/2023/yehtml/components/list/three2.vue?414f"], "names": ["components", "props", "list", "type", "default", "onReady", "data", "timeData", "watch", "methods", "goDetail", "TOOLS", "onChangeTimeData", "handleTags"], "mappings": ";;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmH;AACnH;AAC0D;AACL;AACa;;;AAGlE;AACkL;AAClL,gBAAgB,0LAAU;AAC1B,EAAE,4EAAM;AACR,EAAE,iFAAM;AACR,EAAE,0FAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,0TAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpDA;AAAA;AAAA;AAAA;AAAiqB,CAAgB,irBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;ACmErrB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA;AAAA,eAEA;EACAA;EACAC;IACAC;MACAC;MACAC;IACA;IACAD;MACAA;MACAC;IACA;EACA;EACAC;EACAC;IACA;MACAC;IACA;EACA;EACAC;IACAN;MACA;IAAA;EAEA;EACAO;IACAC;MACAC;;MAEA;AACA;AACA;AACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;AC9GA;AAAA;AAAA;AAAA;AAA68B,CAAgB,g8BAAG,EAAC,C;;;;;;;;;;;ACAj+B;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "components/list/three2.js", "sourcesContent": ["import { render, staticRenderFns, recyclableRender, components } from \"./three2.vue?vue&type=template&id=33b4e07a&\"\nvar renderjs\nimport script from \"./three2.vue?vue&type=script&lang=js&\"\nexport * from \"./three2.vue?vue&type=script&lang=js&\"\nimport style0 from \"./three2.vue?vue&type=style&index=0&lang=css&\"\n\n\n/* normalize component */\nimport normalizer from \"!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"components/list/three2.vue\"\nexport default component.exports", "export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./three2.vue?vue&type=template&id=33b4e07a&\"", "var components\ntry {\n  components = {\n    uRow: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-row/u-row\" */ \"@/uni_modules/uview-ui/components/u-row/u-row.vue\"\n      )\n    },\n    uCol: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-col/u-col\" */ \"@/uni_modules/uview-ui/components/u-col/u-col.vue\"\n      )\n    },\n    uSkeleton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-skeleton/u-skeleton\" */ \"@/uni_modules/uview-ui/components/u-skeleton/u-skeleton.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var m0 = _vm.list[1].name ? _vm.handleTags(_vm.list[0].tags) : null\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        m0: m0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./three2.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./three2.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"three1\">\r\n    <view>\r\n      <u-row v-if=\"list[1].name\" customStyle=\"padding: 12px;margin-bottom: 10px;\" gutter=\"12\">\r\n        <u-col span=\"6\">\r\n          <view class=\"right\" @click=\"goDetail(list[1])\">\r\n            <view class=\"right-top\" :style=\"{ backgroundImage: 'url(' + list[1].pic + ')' }\"></view>\r\n            <view class=\"right-bottom\">\r\n              <view class=\"title\">{{ list[1].name }}</view>\r\n              <view class=\"price\">\r\n                <view class=\"price-right\">\r\n                  <span class=\"prefix\">￥</span>\r\n                  <span>38</span>\r\n                </view>\r\n                <view class=\"price-left\">\r\n                  <view class=\"original-price\"><span>原价￥{{ list[1].originalPrice }}</span> / {{ list[1].unit }}</view>\r\n                  <view class=\"only\">仅剩<span>{{ list[1].stores }}</span>只</view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n          <view class=\"right\" style=\"margin-top: 20rpx;\" @click=\"goDetail(list[2])\">\r\n            <view class=\"right-top\" :style=\"{ backgroundImage: 'url(' + list[2].pic + ')' }\"></view>\r\n            <view class=\"right-bottom\">\r\n              <view class=\"title\">{{ list[2].name }}</view>\r\n              <view class=\"price\">\r\n                <view class=\"price-right\">\r\n                  <span class=\"prefix\">￥</span>\r\n                  <span>38</span>\r\n                </view>\r\n                <view class=\"price-left\">\r\n                  <view class=\"original-price\"><span>原价￥{{ list[2].originalPrice }}</span> / {{ list[2].minPrice }}\r\n                  </view>\r\n                  <view class=\"only\">仅剩<span>{{ list[2].stores }}</span>只</view>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </u-col>\r\n        <u-col span=\"6\">\r\n          <view class=\"left\" @click=\"goDetail(list[0])\">\r\n            <view class=\"left-top\" :style=\"{ backgroundImage: 'url(' + list[0].pic + ')' }\"></view>\r\n            <view class=\"left-bottom\">\r\n              <view class=\"title\">{{ list[0].name }}</view>\r\n              <view class=\"dsc\">{{ handleTags(list[0].tags) }}</view>\r\n              <view class=\"price\">\r\n                <view class=\"price-left\">\r\n                  <view class=\"price-tip\">抢购价</view>\r\n                  <view class=\"only\">仅剩<span>{{ list[0].stores }}</span>只</view>\r\n                </view>\r\n                <view class=\"price-right\">\r\n                  <span class=\"prefix\">￥</span>\r\n                  <span>{{ list[0].minPrice }}</span>\r\n                </view>\r\n              </view>\r\n            </view>\r\n          </view>\r\n        </u-col>\r\n      </u-row>\r\n      <view v-if=\"!list[0].name\" style=\"padding: 12px;margin-bottom: 10px;\">\r\n        <u-skeleton rows=\"3\"></u-skeleton>\r\n      </view>\r\n    </view>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\n  import empty from 'empty-value'\r\n  const TOOLS = require('@/common/tools')\r\n\r\n  export default {\r\n    components: {},\r\n    props: {\r\n      list: {\r\n        type: Array,\r\n        default: [],\r\n      },\r\n      type: {\r\n        type: String,\r\n        default: '',\r\n      }\r\n    },\r\n    onReady() {},\r\n    data() {\r\n      return {\r\n        timeData: {}\r\n      }\r\n    },\r\n    watch: {\r\n      list: function(val) {\r\n        //console.log('watch list is', val)\r\n      }\r\n    },\r\n    methods: {\r\n      goDetail(item) {\r\n        TOOLS.softOpeningTip()\r\n        \r\n        /* let that = this\r\n        uni.navigateTo({\r\n          url: '/pages/goods/detail?id=' + item.id\r\n        }) */\r\n      },\r\n      onChangeTimeData(e) {\r\n        this.timeData = e\r\n      },\r\n      handleTags(tag) {\r\n        var result = tag.replace(/[,，]/g, ' ')\r\n        return result\r\n      }\r\n    },\r\n  }\r\n</script>\r\n<style>\r\n  .only {\r\n    font-size: 22rpx;\r\n    line-height: 24rpx;\r\n  }\r\n\r\n  .only span {\r\n    background-color: #324A43;\r\n    border-radius: 4px;\r\n    display: inline-block;\r\n    text-align: center;\r\n    width: 16px;\r\n    height: 16px;\r\n    line-height: 16px;\r\n    margin: 0 8rpx;\r\n  }\r\n\r\n  .left {\r\n    border-radius: 8rpx;\r\n    overflow: hidden;\r\n    text-align: center;\r\n  }\r\n\r\n  .left-top {\r\n    height: 546rpx;\r\n    background-size: cover;\r\n    background-position: center;\r\n  }\r\n\r\n  .left-bottom {\r\n    line-height: 50rpx;\r\n    background: #87C232;\r\n    background: radial-gradient(circle, #eead4d, #f2933a);\r\n    color: #FFF;\r\n    padding: 20rpx 10rpx;\r\n  }\r\n\r\n  .left-bottom .count-down {\r\n    font-size: 22rpx;\r\n    color: #324A43;\r\n  }\r\n\r\n  .left-bottom .title {\r\n    font-size: 46rpx;\r\n    font-weight: bold;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n  }\r\n\r\n  .left-bottom .dsc {\r\n    font-size: 26rpx;\r\n    color: #FFFFFF;\r\n  }\r\n\r\n  .left-bottom .price-tip {\r\n    font-size: 22rpx;\r\n    color: #345409;\r\n    background: #f5f9c3;\r\n    border-radius: 20rpx;\r\n    padding: 0 8rpx;\r\n    display: inline-block;\r\n    line-height: 32rpx;\r\n  }\r\n\r\n  .left-bottom .price {\r\n    font-weight: bolder;\r\n  }\r\n\r\n  .left-bottom .price .price-left {\r\n    display: inline-block;\r\n    font-weight: normal;\r\n    text-align: right;\r\n  }\r\n\r\n  .left-bottom .price .price-right {\r\n    font-size: 80rpx;\r\n    display: inline-block;\r\n    letter-spacing: -2px;\r\n  }\r\n\r\n  .left-bottom .price .prefix,\r\n  .left-bottom .price .suffix {\r\n    font-size: 40rpx;\r\n  }\r\n\r\n  .right {\r\n    color: #FFFFFF;\r\n    border-radius: 8rpx;\r\n    overflow: hidden;\r\n    text-align: center;\r\n  }\r\n\r\n  .right-top {\r\n    height: 200rpx;\r\n    background-size: cover;\r\n    background-position: top;\r\n  }\r\n\r\n  .right-bottom {\r\n    line-height: 50rpx;\r\n    background: #87C232;\r\n    background: radial-gradient(circle, #eead4d, #f2933a);\r\n    color: #FFF;\r\n    padding: 20rpx 10rpx;\r\n  }\r\n\r\n  .right-bottom .title {\r\n    font-size: 46rpx;\r\n    font-weight: bold;\r\n    white-space: nowrap;\r\n    overflow: hidden;\r\n    text-overflow: ellipsis;\r\n    margin-bottom: 20rpx;\r\n  }\r\n\r\n  .right-bottom .price-tip {\r\n    font-size: 20rpx;\r\n    color: #345409;\r\n    background: #f5f9c3;\r\n    border-radius: 20rpx;\r\n    padding: 0 8rpx;\r\n    display: inline-block;\r\n    line-height: 32rpx;\r\n  }\r\n\r\n  .right-bottom .price {\r\n    font-weight: bolder;\r\n  }\r\n\r\n  .right-bottom .price .price-left {\r\n    display: inline-block;\r\n    font-weight: normal;\r\n    text-align: left;\r\n  }\r\n\r\n  .right-bottom .price .price-right {\r\n    font-size: 70rpx;\r\n    display: inline-block;\r\n    letter-spacing: -2px;\r\n    margin-right: 20rpx;\r\n  }\r\n\r\n  .right-bottom .price .prefix,\r\n  .right-bottom .price .suffix {\r\n    font-size: 32rpx;\r\n  }\r\n\r\n  .right-bottom .price .original-price {\r\n    font-size: 20rpx;\r\n    text-decoration: line-through;\r\n    line-height: 32rpx;\r\n  }\r\n</style>", "import mod from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./three2.vue?vue&type=style&index=0&lang=css&\"; export default mod; export * from \"-!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--6-oneOf-1-0!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--6-oneOf-1-1!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--6-oneOf-1-2!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--6-oneOf-1-3!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!C:\\\\Files\\\\Soft\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./three2.vue?vue&type=style&index=0&lang=css&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1753688728627\n      var cssReload = require(\"C:/Files/Soft/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}