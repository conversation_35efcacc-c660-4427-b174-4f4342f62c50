<view class="index container-wrapper data-v-5a49a3e8"><view class="category-page-box data-v-5a49a3e8"><view class="category-page data-v-5a49a3e8"><view class="search data-v-5a49a3e8"><u-search vue-id="9d275500-1" placeholder="输入关键词搜索" searchIconSize="28.6" height="41.6" showAction="{{false}}" value="{{kw}}" data-event-opts="{{[['^search',[['search']]],['^input',[['__set_model',['','kw','$event',[]]]]]]}}" bind:search="__e" bind:input="__e" class="data-v-5a49a3e8" bind:__l="__l"></u-search></view><view class="_main data-v-5a49a3e8"><view class="tab1 data-v-5a49a3e8"><u-row vue-id="9d275500-2" customStyle="margin-bottom: 10px" class="data-v-5a49a3e8" bind:__l="__l" vue-slots="{{['default']}}"><u-col vue-id="{{('9d275500-3')+','+('9d275500-2')}}" span="4" justify="center" textAlign="center" class="data-v-5a49a3e8" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['changeTab',['tab1']]]]]}}" class="{{['tab','line','_div','data-v-5a49a3e8',(activeTab==='tab1')?'active':'']}}" bindtap="__e"><view style="height:30rpx;" class="_div data-v-5a49a3e8"></view><view class="title _div data-v-5a49a3e8">土鲜多</view></view></u-col><u-col vue-id="{{('9d275500-4')+','+('9d275500-2')}}" span="4" justify="center" textAlign="center" class="data-v-5a49a3e8" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['changeTab',['tab2']]]]]}}" class="{{['tab','line','_div','data-v-5a49a3e8',(activeTab==='tab2')?'active':'']}}" bindtap="__e"><view style="height:30rpx;" class="_div data-v-5a49a3e8"></view><view class="title _div data-v-5a49a3e8">山泉多</view></view></u-col><u-col vue-id="{{('9d275500-5')+','+('9d275500-2')}}" span="4" justify="center" textAlign="center" class="data-v-5a49a3e8" bind:__l="__l" vue-slots="{{['default']}}"><view data-event-opts="{{[['tap',[['changeTab',['tab3']]]]]}}" class="{{['tab','_div','data-v-5a49a3e8',(activeTab==='tab3')?'active':'']}}" bindtap="__e"><view style="height:30rpx;" class="_div data-v-5a49a3e8"></view><view class="title _div data-v-5a49a3e8">好礼多</view></view></u-col></u-row></view><view class="main data-v-5a49a3e8"><scroll-view class="u-tab-view menu-scroll-view data-v-5a49a3e8" scroll-y="true" scroll-with-animation="true"><block wx:for="{{firstCategories}}" wx:for-item="item" wx:for-index="index" wx:key="index"><view class="{{['u-tab-item','data-v-5a49a3e8',current==index?'u-tab-item-active':'']}}" data-current="{{index}}" data-event-opts="{{[['tap',[['swichMenu',[index]]]]]}}" catchtap="__e"><text class="u-line-1 data-v-5a49a3e8">{{item.name}}</text></view></block></scroll-view><scroll-view class="goods-container data-v-5a49a3e8" scroll-y="true" scroll-top="{{scrolltop}}"><block wx:if="{{!goodsList}}"><u-empty vue-id="9d275500-6" mode="list" text="暂无商品" marginTop="200rpx" class="data-v-5a49a3e8" bind:__l="__l"></u-empty></block><list3 vue-id="9d275500-7" list="{{goodsList}}" class="data-v-5a49a3e8" bind:__l="__l"></list3></scroll-view></view></view></view><goods-pop vue-id="9d275500-8" show="{{showGoodsPop}}" goodsDetail="{{goodsDetail}}" data-event-opts="{{[['^close',[['e0']]]]}}" bind:close="__e" class="data-v-5a49a3e8" bind:__l="__l"></goods-pop></view></view>