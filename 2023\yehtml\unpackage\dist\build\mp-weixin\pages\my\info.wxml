<view class="data-v-93244a1a"><u-cell vue-id="5004dfbe-1" title="头像" border="{{false}}" class="data-v-93244a1a" bind:__l="__l" vue-slots="{{['value']}}"><view slot="value" class="data-v-93244a1a"><image class="avatarUrl data-v-93244a1a" src="{{userDetail.avatarUrl}}" mode="aspectFill"></image></view></u-cell><view class="form-box data-v-93244a1a"><u-form vue-id="5004dfbe-2" label-width="130rpx" model="{{form}}" data-ref="uForm" class="data-v-93244a1a vue-ref" bind:__l="__l" vue-slots="{{['default']}}"><u-form-item vue-id="{{('5004dfbe-3')+','+('5004dfbe-2')}}" label="昵称" prop="nick" required="{{true}}" class="data-v-93244a1a" bind:__l="__l" vue-slots="{{['default']}}"><u-input bind:input="__e" vue-id="{{('5004dfbe-4')+','+('5004dfbe-3')}}" type="text" clearable="{{true}}" placeholder="请输入昵称" value="{{form.nick}}" data-event-opts="{{[['^input',[['__set_model',['$0','nick','$event',[]],['form']]]]]}}" class="data-v-93244a1a" bind:__l="__l"></u-input></u-form-item><u-form-item vue-id="{{('5004dfbe-5')+','+('5004dfbe-2')}}" label="性别" required="{{true}}" data-event-opts="{{[['^click',[['e0']]]]}}" bind:click="__e" class="data-v-93244a1a" bind:__l="__l" vue-slots="{{['default','right']}}"><u-input bind:input="__e" vue-id="{{('5004dfbe-6')+','+('5004dfbe-5')}}" disabled="{{true}}" disabledColor="#ffffff" placeholder="请选择性别" border="none" value="{{sex}}" data-event-opts="{{[['^input',[['__set_model',['','sex','$event',[]]]]]]}}" class="data-v-93244a1a" bind:__l="__l"></u-input><u-icon vue-id="{{('5004dfbe-7')+','+('5004dfbe-5')}}" slot="right" name="arrow-right" class="data-v-93244a1a" bind:__l="__l"></u-icon></u-form-item><u-form-item vue-id="{{('5004dfbe-8')+','+('5004dfbe-2')}}" label="手机号" required="{{true}}" class="data-v-93244a1a" bind:__l="__l" vue-slots="{{['default','right']}}"><u-input bind:input="__e" vue-id="{{('5004dfbe-9')+','+('5004dfbe-8')}}" disabled="{{true}}" disabledColor="#ffffff" placeholder="未绑定" border="none" value="{{form.mobile}}" data-event-opts="{{[['^input',[['__set_model',['$0','mobile','$event',[]],['form']]]]]}}" class="data-v-93244a1a" bind:__l="__l"></u-input><u-button vue-id="{{('5004dfbe-10')+','+('5004dfbe-8')}}" slot="right" type="success" text="{{form.mobile?'重新绑定':'立即绑定'}}" size="mini" open-type="getPhoneNumber" data-event-opts="{{[['^getphonenumber',[['getPhoneNumber']]]]}}" bind:getphonenumber="__e" class="data-v-93244a1a" bind:__l="__l"></u-button></u-form-item></u-form></view><view class="submit-btn data-v-93244a1a"><u-button vue-id="5004dfbe-11" type="success" data-event-opts="{{[['^click',[['submit']]]]}}" bind:click="__e" class="data-v-93244a1a" bind:__l="__l" vue-slots="{{['default']}}">保存</u-button></view><u-action-sheet vue-id="5004dfbe-12" show="{{showSex}}" actions="{{actions}}" title="请选择性别" description="如果选择保密会报错" data-event-opts="{{[['^close',[['e1']]],['^select',[['sexSelect']]]]}}" bind:close="__e" bind:select="__e" class="data-v-93244a1a" bind:__l="__l"></u-action-sheet></view>