(global["webpackJsonp"] = global["webpackJsonp"] || []).push([["pages/index/index"],{

/***/ 334:
/*!****************************************************************************!*\
  !*** X:/Data/Web/ybn/2023/yehtml/main.js?{"page":"pages%2Findex%2Findex"} ***!
  \****************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(wx, createPage) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
__webpack_require__(/*! uni-pages */ 26);
var _vue = _interopRequireDefault(__webpack_require__(/*! vue */ 25));
var _index = _interopRequireDefault(__webpack_require__(/*! ./pages/index/index.vue */ 335));
// @ts-ignore
wx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;
createPage(_index.default);
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/wx.js */ 1)["default"], __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["createPage"]))

/***/ }),

/***/ 335:
/*!*********************************************************!*\
  !*** X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue ***!
  \*********************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./index.vue?vue&type=template&id=57280228&scoped=true& */ 336);
/* harmony import */ var _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./index.vue?vue&type=script&lang=js& */ 338);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__[key]; }) }(__WEBPACK_IMPORT_KEY__));
/* harmony import */ var _index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss& */ 342);
/* harmony import */ var _index_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./index.vue?vue&type=style&index=1&lang=scss& */ 344);
/* harmony import */ var _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js */ 183);

var renderjs






/* normalize component */

var component = Object(_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_runtime_componentNormalizer_js__WEBPACK_IMPORTED_MODULE_4__["default"])(
  _index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_1__["default"],
  _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"],
  _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"],
  false,
  null,
  "57280228",
  null,
  false,
  _index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"],
  renderjs
)

component.options.__file = "pages/index/index.vue"
/* harmony default export */ __webpack_exports__["default"] = (component.exports);

/***/ }),

/***/ 336:
/*!****************************************************************************************************!*\
  !*** X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?vue&type=template&id=57280228&scoped=true& ***!
  \****************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&scoped=true& */ 337);
/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "render", function() { return _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["render"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["staticRenderFns"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["recyclableRender"]; });

/* harmony reexport (safe) */ __webpack_require__.d(__webpack_exports__, "components", function() { return _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_templateLoader_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_17_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_template_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_uni_app_loader_page_meta_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_template_id_57280228_scoped_true___WEBPACK_IMPORTED_MODULE_0__["components"]; });



/***/ }),

/***/ 337:
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--17-0!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?vue&type=template&id=57280228&scoped=true& ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! exports provided: render, staticRenderFns, recyclableRender, components */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "render", function() { return render; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "staticRenderFns", function() { return staticRenderFns; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "recyclableRender", function() { return recyclableRender; });
/* harmony export (binding) */ __webpack_require__.d(__webpack_exports__, "components", function() { return components; });
var components
try {
  components = {
    uSearch: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-search/u-search */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-search/u-search")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-search/u-search.vue */ 817))
    },
    uSwiper: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-swiper/u-swiper */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-swiper/u-swiper")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-swiper/u-swiper.vue */ 799))
    },
    uNoticeBar: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-notice-bar/u-notice-bar */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-notice-bar/u-notice-bar")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-notice-bar/u-notice-bar.vue */ 841))
    },
    uRow: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-row/u-row */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-row/u-row")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-row/u-row.vue */ 849))
    },
    uCol: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-col/u-col */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-col/u-col")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-col/u-col.vue */ 857))
    },
    uIcon: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-icon/u-icon */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-icon/u-icon")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-icon/u-icon.vue */ 865))
    },
    uOverlay: function () {
      return Promise.all(/*! import() | uni_modules/uview-ui/components/u-overlay/u-overlay */[__webpack_require__.e("common/vendor"), __webpack_require__.e("uni_modules/uview-ui/components/u-overlay/u-overlay")]).then(__webpack_require__.bind(null, /*! @/uni_modules/uview-ui/components/u-overlay/u-overlay.vue */ 874))
    },
  }
} catch (e) {
  if (
    e.message.indexOf("Cannot find module") !== -1 &&
    e.message.indexOf(".vue") !== -1
  ) {
    console.error(e.message)
    console.error("1. 排查组件名称拼写是否正确")
    console.error(
      "2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom"
    )
    console.error(
      "3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件"
    )
  } else {
    throw e
  }
}
var render = function () {
  var _vm = this
  var _h = _vm.$createElement
  var _c = _vm._self._c || _h
  if (!_vm._isMounted) {
    _vm.e0 = function ($event) {
      _vm.adPositionIndexPop = false
    }
  }
}
var recyclableRender = false
var staticRenderFns = []
render._withStripped = true



/***/ }),

/***/ 338:
/*!**********************************************************************************!*\
  !*** X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?vue&type=script&lang=js& ***!
  \**********************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js& */ 339);
/* harmony import */ var _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_babel_loader_lib_index_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_13_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_script_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_script_lang_js___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 339:
/*!*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/babel-loader/lib!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--13-1!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?vue&type=script&lang=js& ***!
  \*****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

"use strict";
/* WEBPACK VAR INJECTION */(function(uni) {

var _interopRequireDefault = __webpack_require__(/*! @babel/runtime/helpers/interopRequireDefault */ 4);
Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.default = void 0;
var _regenerator = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/regenerator */ 30));
var _asyncToGenerator2 = _interopRequireDefault(__webpack_require__(/*! @babel/runtime/helpers/asyncToGenerator */ 32));
var _emptyValue = _interopRequireDefault(__webpack_require__(/*! empty-value */ 340));
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
//
var tabbar = function tabbar() {
  __webpack_require__.e(/*! require.ensure | components/tabbar */ "components/tabbar").then((function () {
    return resolve(__webpack_require__(/*! @/components/tabbar */ 882));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var list1 = function list1() {
  Promise.all(/*! require.ensure | components/list/list1 */[__webpack_require__.e("common/vendor"), __webpack_require__.e("components/list/list1")]).then((function () {
    return resolve(__webpack_require__(/*! @/components/list/list1 */ 887));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var three1 = function three1() {
  __webpack_require__.e(/*! require.ensure | components/list/three1 */ "components/list/three1").then((function () {
    return resolve(__webpack_require__(/*! @/components/list/three1 */ 894));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var three2 = function three2() {
  __webpack_require__.e(/*! require.ensure | components/list/three2 */ "components/list/three2").then((function () {
    return resolve(__webpack_require__(/*! @/components/list/three2 */ 901));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var three3 = function three3() {
  __webpack_require__.e(/*! require.ensure | components/list/three3 */ "components/list/three3").then((function () {
    return resolve(__webpack_require__(/*! @/components/list/three3 */ 908));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var tab1 = function tab1() {
  __webpack_require__.e(/*! require.ensure | components/tabs/tab1 */ "components/tabs/tab1").then((function () {
    return resolve(__webpack_require__(/*! @/components/tabs/tab1 */ 915));
  }).bind(null, __webpack_require__)).catch(__webpack_require__.oe);
};
var TOOLS = __webpack_require__(/*! @/common/tools */ 341);
var _default = {
  components: {
    tabbar: tabbar,
    list1: list1,
    three1: three1,
    three2: three2,
    three3: three3,
    tab1: tab1
  },
  data: function data() {
    return {
      tabIndex: 2,
      apiUserInfoMap: undefined,
      background: ['color1', 'color2', 'color3'],
      indicatorDots: true,
      autoplay: true,
      interval: 2000,
      duration: 500,
      headerMarginTopStyle: 'margin-top:0',
      kw: '',
      menuButtonInfoStyle: '',
      shopInfo: undefined,
      banners: undefined,
      goodsDynamic: undefined,
      categories: undefined,
      categories2: undefined,
      categories3: undefined,
      notice: undefined,
      adPosition: {},
      miaoshaGoods: undefined,
      shichiGoods: undefined,
      goodsRecommend: undefined,
      goodsHLD: undefined,
      goodsSQS: undefined,
      goodsYBC: undefined,
      kanjiaList: undefined,
      pingtuanList: undefined,
      page: 1,
      goods: [],
      adPositionIndexPop: false,
      timeData: {},
      activeTab: 'tab1',
      promotionGoods: [],
      promotionGoodsBak: [],
      promotionInsert: true
    };
  },
  onLoad: function onLoad(e) {
    uni.showShareMenu({
      withShareTicket: true
    });
    var systemInfo = uni.getSystemInfoSync();
    var menuButtonInfo = uni.getMenuButtonBoundingClientRect();
    this.menuButtonInfoStyle = "width: ".concat(systemInfo.screenWidth - menuButtonInfo.left, "px;height: ").concat(menuButtonInfo.height, "px");
    this.headerMarginTopStyle = "margin-top:".concat(menuButtonInfo.top, "px");

    // 读取小程序码中的邀请人编号
    if (e && e.scene) {
      var scene = decodeURIComponent(e.scene);
      if (scene) {
        this.$u.vuex('referrer', scene.substring(11));
      }
    }
    uni.setNavigationBarTitle({
      title: this.sysconfigMap.mallName
    });
    this._userDetail();
    this._banners();
    this._categories();
    this._notice();
    this._adPosition();
    this._miaoshaGoods();
    this._shichiGoods();
    this._goodsRecommend();
    this._kanjiaList();
    this._pingtuanList();
    this._goodsPromotion();
    this._goods();
    this._goodsHLD();
    this._goodsSQS();
  },
  onShow: function onShow() {
    this.shopInfo = uni.getStorageSync('shopInfo');
    this._goodsDynamic();
    TOOLS.showTabBarBadge();
    if (this.activeTab === 'tab1') {
      var refreshIndex = uni.getStorageSync('refreshIndex');
      if (refreshIndex) {
        this.onPullDownRefresh();
        uni.removeStorageSync('refreshIndex');
      }
    }
  },
  created: function created() {},
  onShareAppMessage: function onShareAppMessage() {
    return {
      title: '"' + this.sysconfigMap.mallName + '" ' + this.sysconfigMap.share_profile,
      path: '/pages/index/index?inviter_id=' + this.uid
    };
  },
  onReachBottom: function onReachBottom() {
    this.page += 1;
    this._goods();
  },
  onPullDownRefresh: function onPullDownRefresh() {
    this.page = 1;
    this._banners();
    this._categories();
    this._notice();
    this._adPosition();
    this._miaoshaGoods();
    this._goodsRecommend();
    this._kanjiaList();
    this._pingtuanList();
    this._goods();
    uni.stopPullDownRefresh();
  },
  methods: {
    _goodsPromotion: function _goodsPromotion() {
      var _this = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee() {
        return _regenerator.default.wrap(function _callee$(_context) {
          while (1) {
            switch (_context.prev = _context.next) {
              case 0:
                _context.next = 2;
                return uni.$u.http.post('https://ye.niutouren.vip/api/order', {
                  'type': 'index_good_promotion'
                }).then(function (res) {
                  if (!(0, _emptyValue.default)(res)) {
                    _this.promotionGoods = uni.$u.deepClone(res);
                  }

                  //console.log('long is', this.longList)
                }).catch(function (err) {
                  //
                });
              case 2:
              case "end":
                return _context.stop();
            }
          }
        }, _callee);
      }))();
    },
    _userDetail: function _userDetail() {
      var _this2 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee2() {
        var res;
        return _regenerator.default.wrap(function _callee2$(_context2) {
          while (1) {
            switch (_context2.prev = _context2.next) {
              case 0:
                _context2.next = 2;
                return _this2.$wxapi.userDetail(_this2.token);
              case 2:
                res = _context2.sent;
                if (res.code == 0) {
                  _this2.apiUserInfoMap = res.data;
                }
              case 4:
              case "end":
                return _context2.stop();
            }
          }
        }, _callee2);
      }))();
    },
    jssdkSign: function jssdkSign() {
      var _this3 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee3() {
        var res;
        return _regenerator.default.wrap(function _callee3$(_context3) {
          while (1) {
            switch (_context3.prev = _context3.next) {
              case 0:
                _context3.next = 2;
                return _this3.$wxapi.jssdkSign(window.location.href);
              case 2:
                res = _context3.sent;
                if (res.code === 0) {
                  jweixin.config({
                    debug: false,
                    // 开启调试模式,调用的所有api的返回值会在客户端alert出来，若要查看传入的参数，可以在pc端打开，参数信息会通过log打出，仅在pc端时才会打印。
                    appId: res.data.appid,
                    // 必填，公众号的唯一标识
                    timestamp: res.data.timestamp,
                    // 必填，生成签名的时间戳
                    nonceStr: res.data.noncestr,
                    // 必填，生成签名的随机串
                    signature: res.data.sign,
                    // 必填，签名
                    jsApiList: ['updateAppMessageShareData', 'updateTimelineShareData'] // 必填，需要使用的JS接口列表
                  });
                }
              case 4:
              case "end":
                return _context3.stop();
            }
          }
        }, _callee3);
      }))();
    },
    goSearch: function goSearch() {
      uni.navigateTo({
        url: '/packageFx/search/index'
      });
    },
    _banners: function _banners() {
      var _this4 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee4() {
        var res;
        return _regenerator.default.wrap(function _callee4$(_context4) {
          while (1) {
            switch (_context4.prev = _context4.next) {
              case 0:
                _context4.next = 2;
                return _this4.$wxapi.banners({
                  type: 'index'
                });
              case 2:
                res = _context4.sent;
                if (res.code == 0) {
                  _this4.banners = res.data;
                } else {
                  uni.showToast({
                    title: '后台未维护Banner数据',
                    icon: 'none'
                  });
                }
              case 4:
              case "end":
                return _context4.stop();
            }
          }
        }, _callee4);
      }))();
    },
    tapBanner: function tapBanner(index) {
      var linkUrl = this.banners[index].linkUrl;
      if (linkUrl) {
        uni.navigateTo({
          url: linkUrl
        });
      }
    },
    _goodsDynamic: function _goodsDynamic() {
      var _this5 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee5() {
        var res;
        return _regenerator.default.wrap(function _callee5$(_context5) {
          while (1) {
            switch (_context5.prev = _context5.next) {
              case 0:
                _context5.next = 2;
                return _this5.$wxapi.goodsDynamic(0);
              case 2:
                res = _context5.sent;
                if (res.code == 0) {
                  _this5.goodsDynamic = [];
                  res.data.forEach(function (ele) {
                    _this5.goodsDynamic.push("".concat(ele.nick, "\u8D2D\u4E70\u4E86").concat(ele.goodsName));
                  });
                }
              case 4:
              case "end":
                return _context5.stop();
            }
          }
        }, _callee5);
      }))();
    },
    _categories: function _categories() {
      var _this6 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee6() {
        var res, categorizedData;
        return _regenerator.default.wrap(function _callee6$(_context6) {
          while (1) {
            switch (_context6.prev = _context6.next) {
              case 0:
                _context6.next = 2;
                return _this6.$wxapi.goodsCategory();
              case 2:
                res = _context6.sent;
                if (res.code === 0) {
                  categorizedData = res.data.reduce(function (acc, curr) {
                    if (curr.key === 'YBN1') {
                      acc.categories.push(curr);
                    } else if (curr.key === 'YBN2') {
                      acc.categories2.push(curr);
                    } else if (curr.key === 'YBN3') {
                      acc.categories3.push(curr);
                    }
                    return acc;
                  }, {
                    categories: [],
                    categories2: [],
                    categories3: []
                  });
                  _this6.categories = categorizedData.categories;
                  _this6.categories2 = categorizedData.categories2;
                  _this6.categories3 = categorizedData.categories3;
                }
              case 4:
              case "end":
                return _context6.stop();
            }
          }
        }, _callee6);
      }))();
    },
    // 移除套餐、活动等
    removeItemsFromArray: function removeItemsFromArray(arr) {
      var idsToRemove = [390765, 390766, 391088, 391089, 455910, 412599, 417010, 424167];
      var filteredArr = arr.filter(function (item) {
        return !idsToRemove.includes(item.id);
      });
      return filteredArr;
    },
    categoryClick: function categoryClick(category) {
      if (category.vopCid1 || category.vopCid2) {
        uni.navigateTo({
          url: '/pages/goods/list-vop?cid1=' + (category.vopCid1 ? category.vopCid1 : '') + '&cid2=' + (category.vopCid2 ? category.vopCid2 : '')
        });
      } else {
        uni.navigateTo({
          url: '/pages/goods/list?categoryId=' + category.id
        });
      }
    },
    _notice: function _notice() {
      var _this7 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee7() {
        var res;
        return _regenerator.default.wrap(function _callee7$(_context7) {
          while (1) {
            switch (_context7.prev = _context7.next) {
              case 0:
                _context7.next = 2;
                return _this7.$wxapi.noticeLastOne();
              case 2:
                res = _context7.sent;
                if (res.code == 0) {
                  _this7.notice = res.data;
                }
              case 4:
              case "end":
                return _context7.stop();
            }
          }
        }, _callee7);
      }))();
    },
    _adPosition: function _adPosition() {
      var _this8 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee8() {
        var res;
        return _regenerator.default.wrap(function _callee8$(_context8) {
          while (1) {
            switch (_context8.prev = _context8.next) {
              case 0:
                _context8.next = 2;
                return _this8.$wxapi.adPositionBatch('indexPop,index-live-pic');
              case 2:
                res = _context8.sent;
                if (res.code == 0) {
                  res.data.forEach(function (ele) {
                    _this8.adPosition[ele.key] = ele;
                    if (ele.key == 'indexPop') {
                      _this8.adPositionIndexPop = true;
                    }
                  });
                }
              case 4:
              case "end":
                return _context8.stop();
            }
          }
        }, _callee8);
      }))();
    },
    gotoActivity: function gotoActivity(type) {
      uni.navigateTo({
        url: '/pages/promotion/list?type=' + type
      });
    },
    goUrl: function goUrl(url) {
      this.adPositionIndexPop = false;
      if (url) {
        uni.navigateTo({
          url: url
        });
      }
    },
    noticeclick: function noticeclick(e) {
      console.log(e);
    },
    _shichiGoods: function _shichiGoods() {
      var _this9 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee9() {
        var res, shichiGoods;
        return _regenerator.default.wrap(function _callee9$(_context9) {
          while (1) {
            switch (_context9.prev = _context9.next) {
              case 0:
                _context9.next = 2;
                return _this9.$wxapi.goodsv2({
                  categoryId: '463740',
                  showExtJson: true
                });
              case 2:
                res = _context9.sent;
                if (res.code == 0) {
                  res.data.result.forEach(function (ele) {
                    var _now = new Date().getTime();
                    if (ele.dateStart) {
                      ele.dateStartInt = new Date(ele.dateStart.replace(/-/g, '/')).getTime() - _now;
                    }
                    if (ele.dateEnd) {
                      ele.dateEndInt = new Date(ele.dateEnd.replace(/-/g, '/')).getTime() - _now;
                    }
                    if (!(0, _emptyValue.default)(res.data.extJsonMap)) {
                      if (ele.id in res.data.extJsonMap) {
                        ele.ext = res.data.extJsonMap[ele.id];
                      }
                    }
                  });
                  shichiGoods = [];
                  shichiGoods = _this9.rotateArray(res.data.result);
                  _this9.shichiGoods = shichiGoods;
                }
              case 4:
              case "end":
                return _context9.stop();
            }
          }
        }, _callee9);
      }))();
    },
    _miaoshaGoods: function _miaoshaGoods() {
      var _this10 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee10() {
        var res, miaoshaGoods;
        return _regenerator.default.wrap(function _callee10$(_context10) {
          while (1) {
            switch (_context10.prev = _context10.next) {
              case 0:
                _context10.next = 2;
                return _this10.$wxapi.goodsv2({
                  categoryId: '391089'
                });
              case 2:
                res = _context10.sent;
                if (res.code == 0) {
                  res.data.result.forEach(function (ele) {
                    var _now = new Date().getTime();
                    if (ele.dateStart) {
                      ele.dateStartInt = new Date(ele.dateStart.replace(/-/g, '/')).getTime() - _now;
                    }
                    if (ele.dateEnd) {
                      ele.dateEndInt = new Date(ele.dateEnd.replace(/-/g, '/')).getTime() - _now;
                    }
                  });
                  miaoshaGoods = [];
                  miaoshaGoods = _this10.rotateArray(res.data.result);
                  _this10.miaoshaGoods = miaoshaGoods;
                }
              case 4:
              case "end":
                return _context10.stop();
            }
          }
        }, _callee10);
      }))();
    },
    _kanjiaList: function _kanjiaList() {
      var _this11 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee11() {
        var res, kanjiaGoodsIds, goodsKanjiaSetRes;
        return _regenerator.default.wrap(function _callee11$(_context11) {
          while (1) {
            switch (_context11.prev = _context11.next) {
              case 0:
                _context11.next = 2;
                return _this11.$wxapi.goodsv2({
                  kanjia: true
                });
              case 2:
                res = _context11.sent;
                if (!(res.code == 0)) {
                  _context11.next = 10;
                  break;
                }
                kanjiaGoodsIds = [];
                res.data.result.forEach(function (ele) {
                  kanjiaGoodsIds.push(ele.id);
                });
                // https://www.yuque.com/apifm/nu0f75/xs42ih
                _context11.next = 8;
                return _this11.$wxapi.kanjiaSet(kanjiaGoodsIds.join());
              case 8:
                goodsKanjiaSetRes = _context11.sent;
                if (goodsKanjiaSetRes.code == 0) {
                  res.data.result.forEach(function (ele) {
                    var _process = goodsKanjiaSetRes.data.find(function (_set) {
                      return _set.goodsId == ele.id;
                    });
                    if (_process) {
                      ele.process = 100 * _process.numberBuy / _process.number;
                      ele.process = ele.process.toFixed(0);
                    }
                  });
                  _this11.kanjiaList = res.data.result;
                }
              case 10:
              case "end":
                return _context11.stop();
            }
          }
        }, _callee11);
      }))();
    },
    _pingtuanList: function _pingtuanList() {
      var _this12 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee12() {
        var res;
        return _regenerator.default.wrap(function _callee12$(_context12) {
          while (1) {
            switch (_context12.prev = _context12.next) {
              case 0:
                _context12.next = 2;
                return _this12.$wxapi.goodsv2({
                  pingtuan: true
                });
              case 2:
                res = _context12.sent;
                if (res.code == 0) {
                  _this12.pingtuanList = res.data.result;
                }
              case 4:
              case "end":
                return _context12.stop();
            }
          }
        }, _callee12);
      }))();
    },
    _goods: function _goods() {
      var _this13 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee14() {
        var res, _goods, goods, goodsNew, promotionGoods;
        return _regenerator.default.wrap(function _callee14$(_context14) {
          while (1) {
            switch (_context14.prev = _context14.next) {
              case 0:
                if (_this13.page > 1) {
                  uni.showToast({
                    title: '正在加载',
                    icon: 'none'
                  });
                }

                // https://www.yuque.com/apifm/nu0f75/wg5t98
                _context14.next = 3;
                return _this13.$wxapi.goodsv2({
                  page: 1,
                  pageSize: 10,
                  categoryId: '383896,383897',
                  showExtJson: true,
                  orderBy: 'ordersDown,addedDown' // 按订单数量降序，然后按发布时间降序
                });
              case 3:
                res = _context14.sent;
                if (res.code == 0) {
                  _goods = [];
                  goods = [];
                  goodsNew = [];
                  _goods = res.data.result;
                  _goods = _this13.shuffle(_goods);
                  if ((0, _emptyValue.default)(_this13.promotionGoods)) {
                    _this13.promotionGoods = uni.$u.deepClone(_this13.promotionGoodsBak);
                  }
                  promotionGoods = _this13.promotionGoods;
                  if (!(0, _emptyValue.default)(_goods) && !(0, _emptyValue.default)(promotionGoods)) {
                    goodsNew = _this13.insertPromotionGoods(_goods, promotionGoods);
                  }
                  if (_this13.page > 1) {
                    _goods = _this13.goods.concat(goodsNew);
                  } else {
                    _goods = goodsNew;
                  }
                  if (!(0, _emptyValue.default)(_goods)) {
                    _goods.forEach( /*#__PURE__*/function () {
                      var _ref = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee13(good, index) {
                        return _regenerator.default.wrap(function _callee13$(_context13) {
                          while (1) {
                            switch (_context13.prev = _context13.next) {
                              case 0:
                                if (!(0, _emptyValue.default)(good)) {
                                  good.image = good.pic;
                                  good.title = good.name;
                                  if (!(0, _emptyValue.default)(res.data.extJsonMap)) {
                                    if (good.id in res.data.extJsonMap) {
                                      good.ext = res.data.extJsonMap[good.id];
                                      if (!(0, _emptyValue.default)(good.ext['delivery_type'])) {
                                        if (good.ext['delivery_type'] === '预购') {
                                          good.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-yg.png';
                                        }
                                        if (good.ext['delivery_type'] === '当日达') {
                                          good.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-drd.png';
                                        }
                                        if (good.ext['delivery_type'] === '次日达') {
                                          good.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-crd.png';
                                        }
                                        if (good.ext['delivery_type'] === '后日达') {
                                          good.deliveryTypeUrl = 'https://ye.niutouren.vip/static/images/goods/delivery-type-hrd.png';
                                        }
                                      }
                                    }
                                  }
                                  goods.push(good);
                                }
                              case 1:
                              case "end":
                                return _context13.stop();
                            }
                          }
                        }, _callee13);
                      }));
                      return function (_x, _x2) {
                        return _ref.apply(this, arguments);
                      };
                    }());
                  }
                  _this13.goods = goods;
                } else {
                  /* if (this.page != 1) {
                    uni.showToast({
                      title: '没有更多了～',
                      icon: 'none'
                    })
                  } */
                }
              case 5:
              case "end":
                return _context14.stop();
            }
          }
        }, _callee14);
      }))();
    },
    insertPromotionGoods: function insertPromotionGoods(goods, promotionGoods) {
      var first = promotionGoods.shift();
      goods.unshift(first);
      var second = promotionGoods.shift();
      goods.splice(5, 0, second);
      var third = promotionGoods.shift();
      goods.splice(9, 0, third);
      return goods;
    },
    _goodsRecommend: function _goodsRecommend() {
      var _this14 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee16() {
        var res, _goods, goods, extJsonMap;
        return _regenerator.default.wrap(function _callee16$(_context16) {
          while (1) {
            switch (_context16.prev = _context16.next) {
              case 0:
                _context16.next = 2;
                return _this14.$wxapi.goodsv2({
                  categoryId: '391088',
                  showExtJson: true
                });
              case 2:
                res = _context16.sent;
                if (res.code == 0) {
                  _goods = [];
                  goods = [];
                  extJsonMap = [];
                  _goods = res.data.result;
                  if (!(0, _emptyValue.default)(_goods)) {
                    _goods.forEach( /*#__PURE__*/function () {
                      var _ref2 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee15(good, index) {
                        return _regenerator.default.wrap(function _callee15$(_context15) {
                          while (1) {
                            switch (_context15.prev = _context15.next) {
                              case 0:
                                good.image = good.pic;
                                good.title = good.name;
                                if (!(0, _emptyValue.default)(res.data.extJsonMap)) {
                                  if (good.id in res.data.extJsonMap) {
                                    good.ext = res.data.extJsonMap[good.id];
                                    if (!(0, _emptyValue.default)(good.ext['deadline'])) {
                                      good.ext['deadlineDifference'] = TOOLS.translateTimeDifference(good.ext['deadline']);
                                    }
                                  }
                                }
                                goods.push(good);
                              case 4:
                              case "end":
                                return _context15.stop();
                            }
                          }
                        }, _callee15);
                      }));
                      return function (_x3, _x4) {
                        return _ref2.apply(this, arguments);
                      };
                    }());
                  }
                  _this14.goodsRecommend = _this14.rotateArray(goods);
                }
              case 4:
              case "end":
                return _context16.stop();
            }
          }
        }, _callee16);
      }))();
    },
    _goodsHLD: function _goodsHLD() {
      var _this15 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee18() {
        var res, goods, goodsNew, _goods, promotionGoods;
        return _regenerator.default.wrap(function _callee18$(_context18) {
          while (1) {
            switch (_context18.prev = _context18.next) {
              case 0:
                _context18.next = 2;
                return _this15.$wxapi.goodsv2({
                  categoryId: '455908,455909,455910,455911,455912,455913,489436,455913,455912',
                  orderBy: 'ordersDown,addedDown' // 按订单数量降序，然后按发布时间降序
                });
              case 2:
                res = _context18.sent;
                if (res.code == 0) {
                  goods = [];
                  goodsNew = [];
                  _goods = [];
                  _goods = res.data.result;
                  _goods = _this15.shuffle(_goods);
                  if ((0, _emptyValue.default)(_this15.promotionGoods)) {
                    _this15.promotionGoods = uni.$u.deepClone(_this15.promotionGoodsBak);
                  }
                  promotionGoods = _this15.promotionGoods;
                  if (!(0, _emptyValue.default)(_goods) && !(0, _emptyValue.default)(promotionGoods)) {
                    goodsNew = _this15.insertPromotionGoods(_goods, promotionGoods);
                  }
                  if (!(0, _emptyValue.default)(_goods)) {
                    _goods.forEach( /*#__PURE__*/function () {
                      var _ref3 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee17(good, index) {
                        return _regenerator.default.wrap(function _callee17$(_context17) {
                          while (1) {
                            switch (_context17.prev = _context17.next) {
                              case 0:
                                if (!(0, _emptyValue.default)(good)) {
                                  good.image = good.pic;
                                  good.title = good.name;
                                  goods.push(good);
                                }
                              case 1:
                              case "end":
                                return _context17.stop();
                            }
                          }
                        }, _callee17);
                      }));
                      return function (_x5, _x6) {
                        return _ref3.apply(this, arguments);
                      };
                    }());
                  }
                  _this15.goodsHLD = goods;
                }
              case 4:
              case "end":
                return _context18.stop();
            }
          }
        }, _callee18);
      }))();
    },
    _goodsSQS: function _goodsSQS() {
      var _this16 = this;
      return (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee20() {
        var res, goods, goodsNew, _goods, promotionGoods;
        return _regenerator.default.wrap(function _callee20$(_context20) {
          while (1) {
            switch (_context20.prev = _context20.next) {
              case 0:
                _context20.next = 2;
                return _this16.$wxapi.goodsv2({
                  categoryId: '424167,463423',
                  orderBy: 'ordersDown,addedDown' // 按订单数量降序，然后按发布时间降序
                });
              case 2:
                res = _context20.sent;
                if (res.code == 0) {
                  goods = [];
                  goodsNew = [];
                  _goods = [];
                  _goods = res.data.result;
                  _goods = _this16.shuffle(_goods);
                  if ((0, _emptyValue.default)(_this16.promotionGoods)) {
                    _this16.promotionGoods = uni.$u.deepClone(_this16.promotionGoodsBak);
                  }
                  promotionGoods = _this16.promotionGoods;
                  if (!(0, _emptyValue.default)(_goods) && !(0, _emptyValue.default)(promotionGoods)) {
                    goodsNew = _this16.insertPromotionGoods(_goods, promotionGoods);
                  }
                  if (!(0, _emptyValue.default)(_goods)) {
                    _goods.forEach( /*#__PURE__*/function () {
                      var _ref4 = (0, _asyncToGenerator2.default)( /*#__PURE__*/_regenerator.default.mark(function _callee19(good, index) {
                        return _regenerator.default.wrap(function _callee19$(_context19) {
                          while (1) {
                            switch (_context19.prev = _context19.next) {
                              case 0:
                                if (!(0, _emptyValue.default)(good)) {
                                  good.image = good.pic;
                                  good.title = good.name;
                                  goods.push(good);
                                }
                              case 1:
                              case "end":
                                return _context19.stop();
                            }
                          }
                        }, _callee19);
                      }));
                      return function (_x7, _x8) {
                        return _ref4.apply(this, arguments);
                      };
                    }());
                  }
                  _this16.goodsSQS = goods;
                }
              case 4:
              case "end":
                return _context20.stop();
            }
          }
        }, _callee20);
      }))();
    },
    goCoupons: function goCoupons() {
      uni.switchTab({
        url: '/packageFx/coupons/index'
      });
    },
    shuffle: function shuffle(array) {
      var currentIndex = array.length,
        randomIndex;
      while (currentIndex != 0) {
        randomIndex = Math.floor(Math.random() * currentIndex);
        currentIndex--;
        var _ref5 = [array[randomIndex], array[currentIndex]];
        array[currentIndex] = _ref5[0];
        array[randomIndex] = _ref5[1];
      }
      return array;
    },
    rotateArray: function rotateArray(arr) {
      var timer = setInterval(function () {
        var firstElement = arr.shift();
        arr.push(firstElement);
      }, 10000);
      return arr;
    },
    changeTab: function changeTab(tab) {
      this.activeTab = tab;
    },
    alertTab: function alertTab() {
      uni.showToast({
        title: '敬请期待',
        icon: 'error'
      });
    },
    featureClick: function featureClick(key) {
      var aboutPage = true;
      if (key === 'sw') {
        aboutPage = false;
        // 实体礼品卡 - 直接跳转到实物礼品卡列表
        var physicalParams = {
          pic: encodeURIComponent('https://ye.niutouren.vip/static/images/coupons/card2.jpg'),
          tag: encodeURIComponent('指定抵扣,可自用,可赠送'),
          title: encodeURIComponent('实物礼品卡'),
          type: encodeURIComponent('physical')
        };
        uni.navigateTo({
          url: "/packageFx/coupons/list?pic=".concat(physicalParams.pic, "&tag=").concat(physicalParams.tag, "&title=").concat(physicalParams.title, "&type=").concat(physicalParams.type)
        });
      }
      if (key === 'sz') {
        aboutPage = false;
        // 数字礼品卡 - 直接跳转到消费礼品卡列表
        var virtualParams = {
          pic: encodeURIComponent('https://ye.niutouren.vip/static/images/coupons/card1.jpg'),
          tag: encodeURIComponent('全品通用,可自用,可赠送'),
          title: encodeURIComponent('消费礼品卡'),
          type: encodeURIComponent('virtual')
        };
        uni.navigateTo({
          url: "/packageFx/coupons/list?pic=".concat(virtualParams.pic, "&tag=").concat(virtualParams.tag, "&title=").concat(virtualParams.title, "&type=").concat(virtualParams.type)
        });
      }
      if (key === 'sq') {
        aboutPage = false;
        // 山泉礼品卡 - 直接跳转到水卡列表
        var waterParams = {
          pic: encodeURIComponent('https://dcdn.it120.cc/2024/05/05/77302a3c-066e-426d-a36e-da545df8fccf.png'),
          tag: encodeURIComponent('购水抵扣,可自用,可赠送'),
          title: encodeURIComponent('水卡'),
          type: encodeURIComponent('water')
        };
        uni.navigateTo({
          url: "/packageFx/coupons/list?pic=".concat(waterParams.pic, "&tag=").concat(waterParams.tag, "&title=").concat(waterParams.title, "&type=").concat(waterParams.type)
        });
      }
      if (aboutPage) {
        uni.navigateTo({
          url: '/packageFx/about/about?key=' + key
        });
      }
    }
  },
  computed: {
    // 计算 span 值
    tabSpan: function tabSpan() {
      var _this$apiUserInfoMap, _this$apiUserInfoMap$;
      return ((_this$apiUserInfoMap = this.apiUserInfoMap) === null || _this$apiUserInfoMap === void 0 ? void 0 : (_this$apiUserInfoMap$ = _this$apiUserInfoMap.userLevel) === null || _this$apiUserInfoMap$ === void 0 ? void 0 : _this$apiUserInfoMap$.id) === 33080 ? 3 : 4;
    },
    // 判断是否显示企业购标签
    showEnterpriseTab: function showEnterpriseTab() {
      var _this$apiUserInfoMap2, _this$apiUserInfoMap3;
      return ((_this$apiUserInfoMap2 = this.apiUserInfoMap) === null || _this$apiUserInfoMap2 === void 0 ? void 0 : (_this$apiUserInfoMap3 = _this$apiUserInfoMap2.userLevel) === null || _this$apiUserInfoMap3 === void 0 ? void 0 : _this$apiUserInfoMap3.id) === 33080;
    }
  }
};
exports.default = _default;
/* WEBPACK VAR INJECTION */}.call(this, __webpack_require__(/*! ./node_modules/@dcloudio/uni-mp-weixin/dist/index.js */ 2)["default"]))

/***/ }),

/***/ 342:
/*!*******************************************************************************************************************!*\
  !*** X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss& ***!
  \*******************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss& */ 343);
/* harmony import */ var _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_0_id_57280228_scoped_true_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 343:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?vue&type=style&index=0&id=57280228&scoped=true&lang=scss& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ }),

/***/ 344:
/*!*******************************************************************************************!*\
  !*** X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?vue&type=style&index=1&lang=scss& ***!
  \*******************************************************************************************/
/*! no static exports found */
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony import */ var _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! -!./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=1&lang=scss& */ 345);
/* harmony import */ var _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__);
/* harmony reexport (unknown) */ for(var __WEBPACK_IMPORT_KEY__ in _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__) if(["default"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) (function(key) { __webpack_require__.d(__webpack_exports__, key, function() { return _C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0__[key]; }) }(__WEBPACK_IMPORT_KEY__));
 /* harmony default export */ __webpack_exports__["default"] = (_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_mini_css_extract_plugin_dist_loader_js_ref_8_oneOf_1_0_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_css_loader_dist_cjs_js_ref_8_oneOf_1_1_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_loaders_stylePostLoader_js_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_2_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_postcss_loader_src_index_js_ref_8_oneOf_1_3_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_sass_loader_dist_cjs_js_ref_8_oneOf_1_4_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_webpack_preprocess_loader_index_js_ref_8_oneOf_1_5_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_vue_cli_plugin_uni_packages_vue_loader_lib_index_js_vue_loader_options_C_Files_Soft_HBuilderX_plugins_uniapp_cli_node_modules_dcloudio_webpack_uni_mp_loader_lib_style_js_index_vue_vue_type_style_index_1_lang_scss___WEBPACK_IMPORTED_MODULE_0___default.a); 

/***/ }),

/***/ 345:
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!./node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-2!./node_modules/postcss-loader/src??ref--8-oneOf-1-3!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader??ref--8-oneOf-1-5!./node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib??vue-loader-options!./node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!X:/Data/Web/ybn/2023/yehtml/pages/index/index.vue?vue&type=style&index=1&lang=scss& ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/*! no static exports found */
/***/ (function(module, exports, __webpack_require__) {

// extracted by mini-css-extract-plugin
    if(false) { var cssReload; }
  

/***/ })

},[[334,"common/runtime","common/vendor"]]]);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/index/index.js.map